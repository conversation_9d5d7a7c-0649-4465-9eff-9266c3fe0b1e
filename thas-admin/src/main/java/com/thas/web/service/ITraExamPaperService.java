package com.thas.web.service;

import com.thas.web.domain.TraExamPaper;
import com.thas.web.domain.vo.TraExamDetailsVO;
import com.thas.web.dto.TraExamPaperDTO;

import java.util.List;

/**
 * 考卷Service接口
 *
 * <AUTHOR>
 * @date 2022-01-25
 */
public interface ITraExamPaperService {
    /**
     * 查询考卷
     *
     * @param id 考卷主键
     * @return 考卷
     */
    TraExamDetailsVO selectTraExamPaperById(Long id);


    TraExamDetailsVO editTraExamPaperById(Long id);

    /**
     * 查询考卷列表
     *
     * @param traExamPaper 考卷
     * @return 考卷集合
     */
    List<TraExamPaper> selectTraExamPaperList(TraExamPaper traExamPaper);

    /**
     * 新增考卷
     *
     * @param traExamPaperDTO 考卷
     * @return 结果
     */
    int insertTraExamPaper(TraExamPaperDTO traExamPaperDTO);

    /**
     * 修改考卷
     *
     * @param traExamPaperDTO 考卷
     * @return 结果
     */
    int updateTraExamPaper(TraExamPaperDTO traExamPaperDTO);

    /**
     * 批量删除考卷
     *
     * @param ids 需要删除的考卷主键集合
     * @return 结果
     */
    int deleteTraExamPaperByIds(Long[] ids);

    /**
     * 修改考卷的答卷数
     *
     * @param id     考卷id
     * @param action 行为
     */
    void updateAnswerSheetNum(Long id, String action);

    /**
     * 修改考卷状态
     *
     * @param id     考卷id
     * @param status 状态
     * @return 是否修改成功
     */
    int updateStatus(Long id, Long learnResourceId, Integer status);
}
