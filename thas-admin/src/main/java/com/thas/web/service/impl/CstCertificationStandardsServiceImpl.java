package com.thas.web.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ZipUtil;
import com.alibaba.fastjson.JSON;

import static com.thas.common.constant.Constants.STANDARDS_DETAIL;

import com.thas.common.core.domain.CstCertificationStandardsExcel;
import com.thas.common.core.domain.CstEvaluationCriterionExcel;
import com.thas.common.core.redis.RedisCache;
import com.thas.common.exception.ServiceException;
import com.thas.common.utils.DateUtils;
import com.thas.web.domain.CstCertificationStandards;
import com.thas.web.domain.CstEvaluationCriterion;
import com.thas.web.domain.CstVersioning;
import com.thas.web.domain.dto.CstCertificationStandardsDetailQueryDTO;
import com.thas.web.domain.dto.SelectStandardsByClauseIdsDTO;
import com.thas.web.domain.vo.ArticleVo;
import com.thas.web.domain.vo.ChapterVo;
import com.thas.web.domain.vo.ClauseVo;
import com.thas.web.domain.vo.SectionVo;
import com.thas.web.domain.vo.SelectByVersionIdVo;
import com.thas.web.dto.CstCertificationStandardVO;
import com.thas.web.mapper.CstCertificationStandardsMapper;
import com.thas.web.mapper.CstVersioningMapper;
import com.thas.web.service.ICstCertificationStandardsService;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 认证标准模板Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-21
 */
@Service
@Slf4j
public class CstCertificationStandardsServiceImpl implements ICstCertificationStandardsService {
    @Autowired
    private CstCertificationStandardsMapper cstCertificationStandardsMapper;

    @Autowired
    private CstVersioningMapper cstVersioningMapper;

    @Autowired
    private RedisCache redisCache;

    /**
     * 查询认证标准模板
     *
     * @param id 认证标准模板主键
     * @return 认证标准模板
     */
    @Override
    public CstCertificationStandards selectCstCertificationStandardsById(Long id) {
        return cstCertificationStandardsMapper.selectCstCertificationStandardsById(id);
    }

    /**
     * 查询认证标准模板列表
     *
     * @param cstCertificationStandards 认证标准模板
     * @return 认证标准模板
     */
    @Override
    public List<CstCertificationStandards> selectCstCertificationStandardsList(CstCertificationStandards cstCertificationStandards) {
        return cstCertificationStandardsMapper.selectCstCertificationStandardsList(cstCertificationStandards);
    }

    /**
     * 新增认证标准模板
     *
     * @param cstCertificationStandards 认证标准模板
     * @return 结果
     */
    @Override
    public int insertCstCertificationStandards(CstCertificationStandards cstCertificationStandards) {
        cstCertificationStandards.setCreateTime(DateUtils.getNowDate());
        return cstCertificationStandardsMapper.insertCstCertificationStandards(cstCertificationStandards);
    }

    /**
     * 修改认证标准模板
     *
     * @param cstCertificationStandards 认证标准模板
     * @return 结果
     */
    @Override
    public int updateCstCertificationStandards(CstCertificationStandards cstCertificationStandards) {
        cstCertificationStandards.setUpdateTime(DateUtils.getNowDate());
        return cstCertificationStandardsMapper.updateCstCertificationStandards(cstCertificationStandards);
    }

    /**
     * 批量删除认证标准模板
     *
     * @param ids 需要删除的认证标准模板主键
     * @return 结果
     */
    @Override
    public int deleteCstCertificationStandardsByIds(Long[] ids) {
        return cstCertificationStandardsMapper.deleteCstCertificationStandardsByIds(ids);
    }

    /**
     * 删除认证标准模板信息
     *
     * @param id 认证标准模板主键
     * @return 结果
     */
    @Override
    public int deleteCstCertificationStandardsById(Long id) {
        return cstCertificationStandardsMapper.deleteCstCertificationStandardsById(id);
    }

//    @Override
//    public SelectByVersionIdVo selectByVersionId(Long versionId) {
//        String versionName = "";
////         versionId = 22022317L;
////        long begin1 = System.currentTimeMillis();
////        long end1 = System.currentTimeMillis();
//        if(ObjectUtils.isEmpty(versionId)){
//            CstVersioning cstVersioning = new CstVersioning();
//            cstVersioning.setStatus("1");
//            List<CstVersioning> cstVersionings = cstVersioningMapper.selectCstVersioningList(cstVersioning);
////            end1 = System.currentTimeMillis();
////            log.info("---------------1cstVersionings-SQL花费时间：{}ms-------------", end1-begin1);
//            if (ObjectUtils.isNotEmpty(cstVersionings)){
//                versionId = cstVersionings.get(0).getVersionId();
//                versionName=cstVersionings.get(0).getVersionName();
//            }else {
//                throw new ServiceException("没有版本id");
//            }
//        }else {
//            CstVersioning cstVersioning = cstVersioningMapper.selectCstVersioningByVersionId(versionId);
//            versionName=cstVersioning.getVersionName();
////            log.info("---------------2cstVersioning：{}-------------", versionName);
//        }
////        end1 = System.currentTimeMillis();
////        log.info("---------------2cstVersioning-SQL花费时间：{}ms-------------", end1-begin1);
//
//
//        //从缓存里获取
//        long begin = System.currentTimeMillis();
//        log.info("---------------标准详情获取缓存开始时间：{}-------------", begin);
//        List<ChapterVo> chapterVosList = redisCache.getCacheObject(STANDARDS_DETAIL + versionId);
////        Object cacheObject = redisCache.getCacheObject(STANDARDS_DETAIL + versionId);
////        List<ChapterVo> chapterVosList = new ArrayList<>();
////        if(ObjectUtil.isNotNull(cacheObject)){
////             chapterVosList =  JSON.parseArray(JSON.toJSONString(JSON.parse(ZipUtil.unGzip((byte[]) cacheObject))), ChapterVo.class);
////        }
//
//        long end = System.currentTimeMillis();
//        log.info("---------------标准详情获取缓存结束时间：{}-------------", end);
//        log.info("---------------标准详情获取缓存总共花费时间：{}ms-------------", end-begin);
//
//        if (ObjectUtils.isEmpty(chapterVosList)){
// //           synchronized (this){
//                if (ObjectUtils.isEmpty(chapterVosList)){
//                     begin = System.currentTimeMillis();
//                    log.info("---------------标准详情数据库获取开始时间：{}-------------", begin);
//                    chapterVosList = cstCertificationStandardsMapper.selectByVersionId(versionId);
//                     end = System.currentTimeMillis();
//                    log.info("---------------标准详情数据库获取结束时间：{}-------------", end);
//                    log.info("---------------标准详情数据库获取总共花费时间：{}ms-------------", end-begin);
//
////                    redisCache.setCacheObject(STANDARDS_DETAIL+versionId,ZipUtil.gzip(JSON.toJSONBytes(chapterVosList)));
//
//                    redisCache.setCacheObject(STANDARDS_DETAIL+versionId,chapterVosList);
//                }
// //           }
//        }
//        SelectByVersionIdVo selectByVersionIdVo = new SelectByVersionIdVo();
//        selectByVersionIdVo.setVersionId(versionId);
//        selectByVersionIdVo.setChapterVoList(chapterVosList);
//        selectByVersionIdVo.setVersionName(versionName);
//        return selectByVersionIdVo;
//    }

    @Override
    public SelectByVersionIdVo selectByVersionId(Long versionId) {

        Long begin = System.currentTimeMillis();
        CstVersioning cstVersioning = this.getCstVersioningByVersionId(versionId, false);
        // 从缓存获取评估信息
        SelectByVersionIdVo selectByVersionIdVo = this.getVersionInfoVoFromRedis(cstVersioning);
        if (selectByVersionIdVo == null) {
            log.info("缓存中数据不存在，需要重新初始化该版本信息到缓存");
            List<SelectByVersionIdVo> selectByVersionIdVos = this.processCstVersionings(Arrays.asList(cstVersioning));
            if (CollectionUtil.isNotEmpty(selectByVersionIdVos)) {
                selectByVersionIdVo = selectByVersionIdVos.get(0);
            }
        }
        Long end = System.currentTimeMillis();
        log.info("---------------selectByVersionId方法总共花费时间：{}ms-------------", end - begin);
        return selectByVersionIdVo;
    }

    @Override
    public List<ChapterVo> selectDetailByVersionIdAndClauseId(Long versionId, Long articleId) {
        if(articleId == null ){
            throw new ServiceException("没有条id");
        }

        //参数版本id为空，查询最新版本
        if (ObjectUtils.isEmpty(versionId)) {
            CstVersioning cstVersioning = new CstVersioning();
            cstVersioning.setStatus("1");
            List<CstVersioning> cstVersionings = cstVersioningMapper.selectCstVersioningList(cstVersioning);

            if (ObjectUtils.isNotEmpty(cstVersionings)) {
                versionId = cstVersionings.get(0).getVersionId();
            } else {
                throw new ServiceException("没有版本id");
            }
        }
        return cstCertificationStandardsMapper.selectClauseDetailByVersionId(versionId, articleId);
    }

    /**
     * 根据版本号更新缓存
     *
     * @param versionId
     */
    @Override
    public List<ChapterVo> getChapterVoListFromRedis(Long versionId) {
        // 从缓存获取评估信息
        SelectByVersionIdVo selectByVersionIdVo = this.getVersionInfoVoFromRedis(this.getCstVersioningByVersionId(versionId, true));
        return (selectByVersionIdVo != null) ? (selectByVersionIdVo.getChapterVoList()) : null;
    }

    /**
     * 获取版本号对应的认证标准模板数据
     *
     * @param versionId
     * @return
     */
    @Override
    public List<ChapterVo> selectCstCertificationStandardsByVersionId(String versionId) {
        return cstCertificationStandardsMapper.selectCstCertificationStandardsByVersionId(versionId);
    }

    /**
     * 获取款id对应的款数
     *
     * @param clauseIds
     * @param autCsId
     * @return
     */
    @Override
    public List<String> selectClauseNosByClauseIds(List<String> clauseIds, String autCsId) {
        return cstCertificationStandardsMapper.selectClauseNosByClauseIds(clauseIds,autCsId);
    }

    /**
     * 根据版本号获取评估版本信息
     *
     * @param versionId
     * @param checkVersionId 是否校验版本号
     * @return
     */
    private CstVersioning getCstVersioningByVersionId(Long versionId, boolean checkVersionId) {
        log.info("入参参数版本号 versionId：{}, checkVersionId:{}", versionId, checkVersionId);
        if (!checkVersionId && versionId == null) {
            CstVersioning cstVersioning = new CstVersioning();
            cstVersioning.setStatus("1");
            List<CstVersioning> stVersionings = cstVersioningMapper.selectCstVersioningList(new CstVersioning());
            if (CollectionUtil.isNotEmpty(stVersionings)) {
                return stVersionings.get(0);
            }
        }

        if (versionId == null) {
            log.info("入参参数版本号不能为空 ");
            throw new ServiceException("入参参数版本号不能为空");
        }
        //删除状态不能看详情
        CstVersioning cstVersioning = cstVersioningMapper.selectCstVersioningByVersionId(versionId);
        if (cstVersioning == null) {
            log.info("版本号:{}没有查询到对应版本信息",versionId);
            throw new ServiceException("版本号没有查询到对应版本信息");
        }
        return cstVersioning;
    }

    /**
     * 从缓存获取版本信息
     *
     * @return
     */
    private SelectByVersionIdVo getVersionInfoVoFromRedis(CstVersioning cstVersioning) {
        long begin = System.currentTimeMillis();
        byte[] chapterVoByltes = redisCache.getCacheObject(STANDARDS_DETAIL + cstVersioning.getVersionId());
        SelectByVersionIdVo selectByVersionIdVo = null;
        if (null != chapterVoByltes) {
            List<ChapterVo> chapterVosList = JSON.parseArray(JSON.toJSONString(JSON.parse(ZipUtil.unGzip(chapterVoByltes))), ChapterVo.class);
            selectByVersionIdVo = new SelectByVersionIdVo();
            selectByVersionIdVo.setVersionId(cstVersioning.getVersionId());
            selectByVersionIdVo.setChapterVoList(chapterVosList);
            selectByVersionIdVo.setVersionName(cstVersioning.getVersionName());
        }
        log.info("从缓存获取版本信息耗时：{}ms-------------", System.currentTimeMillis() - begin);
        return selectByVersionIdVo;
    }

    /**
     * 初始化评估标准详情
     */
    @Override
    public void initStandardsVersions() {
        long begin = System.currentTimeMillis();
        log.info("初始化 评估标准详情开始时间：{}------------", begin);
        // 获取所有未删除的评估标准信息
        List<CstVersioning> cstVersionings = cstVersioningMapper.selectCstVersioningList(new CstVersioning());
        log.info("初始化 评估标准版本信息：{}", cstVersionings);
        this.processCstVersionings(cstVersionings);
        long end = System.currentTimeMillis();
        log.info("初始化 评估标准详情完成结束时间：{}------------", end);
        log.info("初始化 评估标准详情总共花费时间：{}ms-------------", end - begin);
    }

    /**
     * 查表获取版本列表对对应的信息
     *
     * @param cstVersionings
     */
    public List<SelectByVersionIdVo> processCstVersionings(List<CstVersioning> cstVersionings) {
        long begin = System.currentTimeMillis();
        log.info("processCstVersionings开始时间：{}ms------------", begin);
        List<SelectByVersionIdVo> selectByVersionIdVos = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(cstVersionings)) {
            for (CstVersioning ctVersioning : cstVersionings) {
                log.info("processCstVersionings 初始化版本号：{} 对应版本信息开始------------", ctVersioning.getVersionId());
                String versionId = ctVersioning.getVersionId().toString();
                long begin1 = System.currentTimeMillis();

                // 通过sql直接查询相关信息后在解析封装为版本信息
//                List<CstCertificationStandardsExcel> standards = cstCertificationStandardsMapper.selectCstCertificationStandardsByVersionId(versionId);
//                List<CstEvaluationCriterionExcel> evaluations = cstCertificationStandardsMapper.selectCstEvaluationCriterionByVersionId(versionId);
                List<ChapterVo> chapterVosList = cstCertificationStandardsMapper.selectCstCertificationStandardsByVersionId(versionId);
                long end = System.currentTimeMillis();
                log.info("查表总共花费时间：{}ms-------------", end - begin1);
//                if (CollectionUtil.isEmpty(standards) || CollectionUtil.isEmpty(evaluations)) {
//                    log.info("版本：{} 对应数据信息不全", ctVersioning);
//                    continue;
//                }
                // 解析章节条款信息
//                List<ChapterVo> chapterVosList = this.parseChapterVosList(standards, evaluations);
                log.info("chapterVosList总共花费时间：{}ms-------------", System.currentTimeMillis() - begin1);

                // 通过sql直接查询版本信息
//                long end111 = System.currentTimeMillis();
//                List<ChapterVo> chapterVosList = cstCertificationStandardsMapper.selectByVersionId(ctVersioning.getVersionId());
//                log.info("chapterVosList总共花费时间：{}ms-------------", System.currentTimeMillis()-end111);

                SelectByVersionIdVo selectByVersionIdVo = new SelectByVersionIdVo();
                selectByVersionIdVo.setVersionId(Long.valueOf(versionId));
                selectByVersionIdVo.setChapterVoList(chapterVosList);
                selectByVersionIdVo.setVersionName(ctVersioning.getVersionName());
                redisCache.setCacheObject(STANDARDS_DETAIL + versionId, ZipUtil.gzip(JSON.toJSONBytes(chapterVosList)), 60, TimeUnit.MINUTES);
                selectByVersionIdVos.add(selectByVersionIdVo);
            }
        }
        log.info("processCstVersionings完成时间：{}ms------------", System.currentTimeMillis() - begin);
        return selectByVersionIdVos;
    }

    /**
     * 解析章节条款信息
     *
     * @param standards   章节信息
     * @param evaluations 评估信息
     * @return 封装后的章节信息
     */
    private List<ChapterVo> parseChapterVosList(List<CstCertificationStandardsExcel> standards, List<CstEvaluationCriterionExcel> evaluations) {
        long begin = System.currentTimeMillis();
        Map<String, List<CstEvaluationCriterionExcel>> evaluationMap = evaluations.stream().collect(Collectors.groupingBy(CstEvaluationCriterionExcel::getCertificationStandardsId));
        Map<String, List<CstCertificationStandardsExcel>> chapterMap = standards.stream().collect(Collectors.groupingBy(CstCertificationStandardsExcel::getChapterId));
        List<ChapterVo> chapterVosList = chapterMap.entrySet().stream().map(chapter -> {
            // 节列表
            List<CstCertificationStandardsExcel> sectionVoList = chapter.getValue();
            // 章信息
            ChapterVo chapterVo = new ChapterVo();
            chapterVo.setChapterId(Long.valueOf(chapter.getKey()));
            chapterVo.setChapterNo(sectionVoList.get(0).getChapterNo());
            chapterVo.setChapter(sectionVoList.get(0).getChapter());
            Map<String, List<CstCertificationStandardsExcel>> sectionMap = sectionVoList.stream().collect(Collectors.groupingBy(CstCertificationStandardsExcel::getSectionId));
            chapterVo.setSectionVoList(sectionMap.entrySet().stream().sorted(Comparator.comparing(section -> Integer.valueOf(section.getKey()))).map(section -> {
                // 条列表
                List<CstCertificationStandardsExcel> articleVoList = section.getValue();
                // 节信息
                SectionVo sectionVo = new SectionVo();
                sectionVo.setSection(articleVoList.get(0).getSection());
                sectionVo.setSectionId(Long.valueOf(section.getKey()));
                sectionVo.setSectionNo(articleVoList.get(0).getSectionNo());
                Map<String, List<CstCertificationStandardsExcel>> articleMap = articleVoList.stream().collect(Collectors.groupingBy(CstCertificationStandardsExcel::getArticleId));
                sectionVo.setArticleVoList(articleMap.entrySet().stream().sorted(Comparator.comparing(article -> Integer.valueOf(article.getKey()))).map(article -> {
                    // 款列表
                    List<CstCertificationStandardsExcel> clauseVoList = article.getValue();
                    // 条信息
                    ArticleVo articleVo = new ArticleVo();
                    articleVo.setArticle(clauseVoList.get(0).getArticle());
                    articleVo.setArticleId(Long.valueOf(article.getKey()));
                    articleVo.setArticleNo(clauseVoList.get(0).getArticleNo());
                    articleVo.setClauseVoList(clauseVoList.stream().sorted(Comparator.comparing(clause -> Integer.valueOf(clause.getClauseId()))).map(clause -> {
                        ClauseVo clauseVo = new ClauseVo();
                        clauseVo.setClause(clause.getClause());
                        clauseVo.setClauseId(Long.valueOf(clause.getClauseId()));
                        clauseVo.setClauseNo(clause.getClauseNo());
                        clauseVo.setIsStar(clause.getIsStar());
                        clauseVo.setArticleId(Long.valueOf(clause.getArticleId()));
                        clauseVo.setVersionId(clause.getVersionId());
                        clauseVo.setDetailRulesDesc(clause.getDetailRulesDesc());
                        clauseVo.setDetailRulesTitle(clause.getDetailRulesTitle());
                        clauseVo.setEvidenceMaterial(clause.getEvidenceMaterial());
                        clauseVo.setRegulationFile(clause.getRegulationFile());
                        clauseVo.setInternationalReference(clause.getInternationalReference());
                        clauseVo.setChapterId(Long.valueOf(clause.getChapterId()));
                        clauseVo.setDomainId(Long.valueOf(clause.getDomainId()));
                        clauseVo.setSectionId(Long.valueOf(clause.getSectionId()));
                        List<CstEvaluationCriterionExcel> evaluationList = evaluationMap.get(clause.getClauseId());
                        if (CollectionUtil.isNotEmpty(evaluationList)) {
                            clauseVo.setCstEvaluationCriterionList(evaluationList.stream().sorted(Comparator.comparing(evaluation -> evaluation.getId())).map(evaluation -> {
                                CstEvaluationCriterion cstEvaluationCriterion = new CstEvaluationCriterion();
                                cstEvaluationCriterion.setCertificationStandardsId(Long.valueOf(evaluation.getCertificationStandardsId()));
                                cstEvaluationCriterion.setEvaluate(evaluation.getEvaluate());
                                cstEvaluationCriterion.setStandard(evaluation.getStandard());
                                cstEvaluationCriterion.setId(Long.valueOf(evaluation.getId()));
                                cstEvaluationCriterion.setVersionId(Long.valueOf(evaluation.getVersionId()));
                                return cstEvaluationCriterion;
                            }).collect(Collectors.toList()));
                        }
                        return clauseVo;
                    }).collect(Collectors.toList()));
                    return articleVo;
                }).collect(Collectors.toList()));
                return sectionVo;
            }).collect(Collectors.toList()));
            return chapterVo;
        }).collect(Collectors.toList());
        long end = System.currentTimeMillis();
        log.info("解析耗时：{}ms------------", end - begin);
        return chapterVosList;
    }

    @Override
    public CstCertificationStandards selectDetailByVersionIdAndClauseId(CstCertificationStandardsDetailQueryDTO cstCertificationStandardsDetailQueryDTO) {
        return cstCertificationStandardsMapper.selectDetailByVersionIdAndClauseId(cstCertificationStandardsDetailQueryDTO);
    }

    @Override
    public int updateByClauseIdAndVersionId(CstCertificationStandards cstCertificationStandards) {
        Long clauseId = cstCertificationStandards.getClauseId();
        Long versionId = cstCertificationStandards.getVersionId();
        if (ObjectUtils.isEmpty(clauseId) || ObjectUtils.isEmpty(versionId)) {
            throw new ServiceException("款id/版本id不能为空！");
        }
        int result = cstCertificationStandardsMapper.updateByClauseIdAndVersionId(cstCertificationStandards);
        //修改缓存的数据
//        List<ChapterVo> chapterVosList = redisCache.getCacheObject(STANDARDS_DETAIL + versionId);
        List<ChapterVo> chapterVosList = this.getChapterVoListFromRedis(versionId);
        if (ObjectUtils.isNotEmpty(chapterVosList)) {
            //因不存 款项详情到缓存，固先注释掉，如果存在缓存，就删除缓存，以后让查询接口去获取缓存
            redisCache.deleteObject(STANDARDS_DETAIL + versionId);
//            chapterVosList.forEach(chapterVo -> {
//                chapterVo.getSectionVoList().forEach(sectionVo -> {
//                    sectionVo.getArticleVoList().forEach(articleVo -> {
//                        List<ClauseVo> clauseVoList = articleVo.getClauseVoList();
//                        ClauseVo clauseVo = new ClauseVo();
//                        BeanUtils.copyProperties(cstCertificationStandards, clauseVo);
//                        for (int i = 0; i < clauseVoList.size(); i++) {
//                            ClauseVo clauseVo1 = clauseVoList.get(i);
//                            if (clauseVo1.getClauseId().equals(cstCertificationStandards.getClauseId())) {
//                                clauseVoList.remove(i);
//                                clauseVoList.add(clauseVo);
//                            }
//                        }
//                    });
//                });
//            });
//            redisCache.setCacheObject(STANDARDS_DETAIL + versionId, ZipUtil.gzip(JSON.toJSONBytes(chapterVosList)), 60, TimeUnit.MINUTES);
        }
//        redisCache.setCacheObject(STANDARDS_DETAIL+versionId,chapterVosList);
        return result;
    }

    @Override
    public int updateByClauseIdAndVersionIds(Map paramMap) {
        List<String> clauseIds = MapUtil.get(paramMap, "clauseIds", List.class);
        Long versionId = MapUtil.getLong(paramMap, "versionId");
        Long domainId = MapUtil.getLong(paramMap, "domainId");
        if (CollectionUtil.isEmpty(clauseIds) || ObjectUtils.isEmpty(versionId)) {
            throw new ServiceException("款id列表/版本id不能为空！");
        }
        int result = cstCertificationStandardsMapper.updateByClauseIdAndVersionIds(paramMap);
        //修改缓存的数据
        //线程池异步处理
        threadPoolsHandleCache(clauseIds, domainId, versionId);

//        List<ChapterVo> chapterVosList = redisCache.getCacheObject(STANDARDS_DETAIL + versionId );
//        if (ObjectUtils.isNotEmpty(chapterVosList)){
//            chapterVosList.forEach(chapterVo -> {
//                chapterVo.getSectionVoList().forEach(sectionVo -> {
//                    sectionVo.getArticleVoList().forEach(articleVo -> {
//                        articleVo.getClauseVoList().forEach(clauseVo -> {
//                           clauseIds.forEach(clauseId->{
//                               if (clauseVo.getClauseId().equals( Long.valueOf(clauseId))){
//                                    clauseVo.setDomainId(domainId);
//                               }
//                           });
//                        });
//                    });
//                });
//            });
//        }
//        redisCache.setCacheObject(STANDARDS_DETAIL+versionId,chapterVosList);

        return result;
    }

    private void threadPoolsHandleCache(List<String> clauseIds, Long domainId, Long versionId) {
        // new ThreadPoolExecutor(1, 1, 60, TimeUnit.MICROSECONDS, new LinkedBlockingQueue<Runnable>());
        ExecutorService service = Executors.newSingleThreadExecutor();
        service.execute(
                new Runnable() {
                    @Override
                    public void run() {
                        try {
//                            List<ChapterVo> chapterVosList = redisCache.getCacheObject(STANDARDS_DETAIL + versionId);
                            List<ChapterVo> chapterVosList = getChapterVoListFromRedis(versionId);
                            if (ObjectUtils.isNotEmpty(chapterVosList)) {
                                chapterVosList.forEach(chapterVo -> {
                                    chapterVo.getSectionVoList().forEach(sectionVo -> {
                                        sectionVo.getArticleVoList().forEach(articleVo -> {
                                            articleVo.getClauseVoList().forEach(clauseVo -> {
                                                clauseIds.forEach(clauseId -> {
                                                    if (clauseVo.getClauseId().equals(Long.valueOf(clauseId))) {
                                                        clauseVo.setDomainId(domainId);
                                                    }
                                                });
                                            });
                                        });
                                    });
                                });
                                redisCache.setCacheObject(STANDARDS_DETAIL + versionId, ZipUtil.gzip(JSON.toJSONBytes(chapterVosList)), 60, TimeUnit.MINUTES);
                            }
//                            redisCache.setCacheObject(STANDARDS_DETAIL+versionId,chapterVosList);
                            log.info("领域详情缓存完成，Key为：{}", STANDARDS_DETAIL + versionId);
                        } catch (Exception e) {
                            e.printStackTrace();
                            log.error("领域详情缓存异常信息：{}", e.getMessage());
                        } /*finally {
                            service.shutdown();
                        }*/
                    }
                }
        );
    }

    @Override
    public int updateByDomainIdAndVersionId(CstCertificationStandards cstCertificationStandards) {
        Long domainId = cstCertificationStandards.getDomainId();
        Long versionId = cstCertificationStandards.getVersionId();
        if (ObjectUtils.isEmpty(domainId) || ObjectUtils.isEmpty(versionId)) {
            throw new ServiceException("领域id列表/版本id不能为空！");
        }
        int result = cstCertificationStandardsMapper.updateByDomainIdAndVersionId(cstCertificationStandards);
        log.info("修改领域-更新对应款项表结果：{}",result);
        //修改缓存的数据
//        List<ChapterVo> chapterVosList = redisCache.getCacheObject(STANDARDS_DETAIL + versionId );
        List<ChapterVo> chapterVosList = getChapterVoListFromRedis(versionId);
        if (ObjectUtils.isNotEmpty(chapterVosList)) {
            chapterVosList.forEach(chapterVo -> {
                chapterVo.getSectionVoList().forEach(sectionVo -> {
                    sectionVo.getArticleVoList().forEach(articleVo -> {
                        articleVo.getClauseVoList().forEach(clauseVo -> {
                            if (domainId.equals(clauseVo.getDomainId())) {
                                clauseVo.setDomainId(0L);
                            }
                        });
                    });
                });
            });
            redisCache.setCacheObject(STANDARDS_DETAIL + versionId, ZipUtil.gzip(JSON.toJSONBytes(chapterVosList)), 60, TimeUnit.MINUTES);
        }
//        redisCache.setCacheObject(STANDARDS_DETAIL+versionId,chapterVosList);
        return result;
    }

    @Override
    public Map<String, Integer> selectCountByVersionId(String versionId) {
        return cstCertificationStandardsMapper.selectCountByVersionId(versionId);
    }

    @Override
    public List<CstCertificationStandards> selectByClauseIdsAndVersionId(SelectStandardsByClauseIdsDTO selectStandardsByClauseIdsDTO) {
        List<CstCertificationStandards> cstCertificationStandards = cstCertificationStandardsMapper.selectByClauseIdsAndVersionId(selectStandardsByClauseIdsDTO.getVersionId(), selectStandardsByClauseIdsDTO.getClauseIds());
        return cstCertificationStandards;
    }

    @Override
    public List<CstCertificationStandardVO> selectCstCertificationStandardVOByIds(String ids) {
        return cstCertificationStandardsMapper.selectCstCertificationStandardVOByIds(ids);
    }

    @Override
    public List<CstCertificationStandardVO> selectCstCertificationStandardVONoInIds(String ids, Long versionId) {

        return cstCertificationStandardsMapper.selectCstCertificationStandardVONoInIds(ids, versionId);
    }

    @Override
    public List<CstCertificationStandardVO> selectCstCertificationStandardsByDomainId(String domainIds, Long versionId) {
        return cstCertificationStandardsMapper.selectCstCertificationStandardsByDomainId(domainIds, versionId);
    }

    @Override
    public List<CstCertificationStandardVO> selectCstCertificationStandardsNotInDomainId(String domainIds, Long versionId) {
        return cstCertificationStandardsMapper.selectCstCertificationStandardsNotInDomainId(domainIds, versionId);
    }

    @Override
    public Integer selectCstCertificationStandardsCountByVersionId(Long versionId) {
        return cstCertificationStandardsMapper.selectCstCertificationStandardsCountByVersionId(versionId);
    }

    @Override
    public Integer selectCstCertificationStandardsCountByDomainIds(String fieldIdList, Long versionId) {
        return cstCertificationStandardsMapper.selectCstCertificationStandardsCountByDomainIds(fieldIdList, versionId);
    }

    /**
     * 根据版本号查询所有的款id
     *
     * @param versionId
     * @return
     */
    @Override
    public List<CstCertificationStandards> selectAllClauseIdByVersionId(String versionId) {
        return cstCertificationStandardsMapper.selectAllClauseIdByVersionId(versionId);
    }

    @Override
    public List<String> selectDomainIdListByVersionId(Long versionId) {
        return cstCertificationStandardsMapper.selectDomainIdListByVersionId(versionId);
    }

}
