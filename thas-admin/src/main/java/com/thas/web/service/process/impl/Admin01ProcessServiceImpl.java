package com.thas.web.service.process.impl;

import com.thas.common.constant.Constants;
import com.thas.common.enums.*;
import com.thas.common.exception.ServiceException;
import com.thas.common.utils.StringUtils;
import com.thas.web.domain.*;
import com.thas.web.domain.vo.AutSaAudDetailVO;
import com.thas.web.mapper.AutSaAudBusinessDataMapper;
import com.thas.web.mapper.AutSaAudMapper;
import com.thas.web.service.process.BaseProcessService;
import com.thas.web.service.process.CommonProcessService;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * 管理员流程服务 Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-04-01
 */
@Component("admin01ProcessService")
@Slf4j
public class Admin01ProcessServiceImpl implements BaseProcessService {

    @Resource
    private CommonProcessService commonProcessService;

    @Resource
    private AutSaAudBusinessDataMapper autSaAudBusinessDataMapper;

    @Resource
    private AutSaAudMapper autSaAudMapper;

    @Override
    public void process(AutSaAudSaveDTO req) {
        long startTime = System.currentTimeMillis();
        log.info("admin01ProcessService.process ------ 开始");
        if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.SR_REPORT_CONFIRM, AutSaAudStatusEnum.SR_REPORT_CONFIRM_MEET)) {
            // 评审报告确认
            if (CollectionUtils.isNotEmpty(req.getAutSaAudLists()) && StringUtils.isNotBlank(req.getAutSaAudLists().get(0).getAutResult())) {
//                //上会节点，判断是否填写上会纪要文件，有才能操作驳回或结束流程
//                this.checkMeet(req);
                commonProcessService.processSummary(req);
                StatusProcessEnum statusProcessEnum = StringUtils.equals(req.getAutSaAudLists().get(0).getAutResult(), AutSaAudResultEnum.FIRST_TRIAL_PASS.getCode().toString()) ? StatusProcessEnum.PS : StatusProcessEnum.RJ;
                String nextStatus = commonProcessService.getNextStatus(req.getAutSaAudStatusConfig().getNextStatusConfig(), statusProcessEnum);
                // 更新关联表 aut_sa_relation
                commonProcessService.updateAutSaRelation(nextStatus, req.getAutCode());
                // 驳回时，失效驳回款数据
                if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.SR_REPORT_CONFIRM_MEET) &&
                        statusProcessEnum.equals(StatusProcessEnum.RJ)
                ) {
                    List<AutSaAud> autSaAudList = new ArrayList<>();
                    AutSaAud autSaAud = new AutSaAud();
                    autSaAud.setAutCode(req.getAutSaRelation().getAutCode());
                    autSaAud.setSubmitType(AutSaAudSubmitTypeEnum.SR_REPORT_M.getSubmitType());
                    autSaAud.setStatus(Constants.INT_ZERO);
                    autSaAudList.add(autSaAud);
                    Integer num = autSaAudMapper.batchUpdateAutSaAud(autSaAudList);
                    log.info("提交类型为：{}，自评编码为：{}， 更新失效数据共：{}条", AutSaAudSubmitTypeEnum.SR_REPORT_M.getSubmitType(), req.getAutSaRelation().getAutCode(), num);

                    //失效会议纪要文件业务数据
                    autSaAudBusinessDataMapper.deleteAutSaAudBusinessData(req.getAutSaRelation().getAutCode(), AutSaAudBusinessCodeEnum.MEET_REPORT.getCode());
                    log.info("当前自评编码为：{}当前节点：{}，已删除上会纪要文件", req.getAutSaRelation().getAutCode(),req.getAutSaRelation().getAutCode());
                }
            } else {
                log.error("评审报告确认，入参错误");
                throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000002);
            }
        }
        log.info("admin01ProcessService.process ------ 结束 耗时：{}", System.currentTimeMillis() - startTime);
    }

    private void checkMeet(AutSaAudSaveDTO req) {
        if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.SR_REPORT_CONFIRM_MEET)) {
            if (!AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(req.getSubmitType(), AutSaAudSubmitTypeEnum.SR_E_REPORT_CONFIRM_MEET)) {
                throw new ServiceException("上会节点入参提交类型错误，应为：" + AutSaAudSubmitTypeEnum.SR_E_REPORT_CONFIRM_MEET.getSubmitType());
            }
            //上会节点，判断是否填写上会纪要文件，有才能操作驳回或结束流程
            List<AutSaAudBusinessData> autSaAudBusinessData = autSaAudBusinessDataMapper.selectAutSaAudBusinessData(req.getAutSaRelation().getAutCode(), AutSaAudBusinessCodeEnum.MEET_REPORT.getCode());
            if (CollectionUtils.isEmpty(autSaAudBusinessData) || StringUtils.isEmpty(autSaAudBusinessData.get(0).getData())) {
                throw new ServiceException("未上传上会纪要文件，操作失败！");
            }
        }
    }

    @Override
    public List<AutSaAudList> queryList(AutSaAudQueryDTO req) {
        return null;
    }

    @Override
    public AutSaAudDetailVO queryDetail(AutSaAudQueryDTO req) {
        return null;
    }

}
