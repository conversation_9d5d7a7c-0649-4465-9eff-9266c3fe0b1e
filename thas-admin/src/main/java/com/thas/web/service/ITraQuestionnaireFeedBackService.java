package com.thas.web.service;


import com.thas.system.domain.vo.ReviewerInfoVo;
import com.thas.system.domain.vo.TraineesAssessorListVo;
import com.thas.web.domain.TraQuestionnaireFeedBack;
import com.thas.web.domain.dto.TraQuestionnaireFeedBackDTO;

import java.util.List;

/**
 * 反馈问卷Service接口
 * 
 * <AUTHOR>
 * @date 2022-10-26
 */
public interface ITraQuestionnaireFeedBackService 
{

    /**
     * 查询反馈问卷列表
     * 
     * @param questionnaireId 调查问卷id
     * @param feedBackType
     * @return 反馈问卷集合
     */
    public TraineesAssessorListVo selectTraQuestionnaireFeedBackList(Long questionnaireId, Long feedBackType);

    /**
     * 新增反馈问卷
     * 
     * @param req 反馈问卷
     * @return 结果
     */
    public int insertTraQuestionnaireFeedBack(TraQuestionnaireFeedBackDTO req);

    TraineesAssessorListVo traineesAssessorList(Long feedBackType, Long questionnaireId);

    List<TraQuestionnaireFeedBack> details(Long feedBackType, Long questionnaireId);

    ReviewerInfoVo selectReviewerInfo(String autCode);
}
