package com.thas.web.service;

import com.thas.common.core.domain.entity.SysRole;
import com.thas.common.core.domain.entity.SysUser;
import com.thas.common.properties.AbstractSftpProperties;
import com.thas.web.domain.FileInfoDTO;
import com.thas.web.domain.vo.FileInfoVO;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.thas.web.domain.vo.ReviewInterestVO;
import com.thas.web.dto.SysUserBaseInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 认证记录Service接口
 *
 * <AUTHOR>
 * @date 2022-01-17
 */
public interface CommonService {

    /**
     * 通用文件上传
     *
     * @param files
     * @param fileNameSuffix 文件后缀名（可为空）
     * @param downLoadFileName 下载文件名(用于下载文件的命名)
     * @return
     */
    FileInfoVO uploadFile(MultipartFile files,  String type,String fileNameSuffix,String downLoadFileName);

//    /**
//     * 批量文件上传
//     *
//     * @param files
//     * @return
//     */
//    List<FileInfoVO> uploadFiles(MultipartFile[] files);

    /**
     * 下载文件
     *
     * @param fileId 文件id
     */
    void downloadFile(String fileId, HttpServletResponse response);

    void fileTemplateDownLoad(String fileTemplate, HttpServletResponse response);

    /**
     * 通用文件上传
     *
     * @param files
     * @return
     */
    FileInfoVO uploadFile1(MultipartFile files);

    /**
     * 下载文件
     *
     * @param fileId 文件id
     */
    void downloadFile1(String fileId, HttpServletResponse response);


    /**
     * 根据来源拼接url
     *
     * @param paltForm 来源
     * @param path 相对路径
     * @return 全路径
     */
    String getFileUrl(String paltForm, String path);

    /**
     * 文件FileInfoDTO类型转为FileInfoVO类型
     *
     * @param fileInfoDTO Dto格式
     * @return VO格式
     */
    FileInfoVO fileInfoDtoToVo(FileInfoDTO fileInfoDTO);

    /**
     * 文件FileInfoDTO类型转为FileInfoVO类型
     *
     * @param fileInfoDtos Dto格式 列表
     * @return VO格式 列表
     */
    List<FileInfoVO> fileInfoDtoToVo(List<FileInfoDTO> fileInfoDtos);


    /**
     * 获取当前角色信息
     */
    SysRole selectSysRole();

    /**
     * 通过roleKey查询对应角色的所有用户
     *
     * @param roleKey 角色标识
     * @param flag 标识为true过滤掉请假的用户
     * @return 用户列表
     */
    List<SysUser> selectUserByRoleKey(String roleKey, boolean flag);

    List<SysUserBaseInfo> getBaseInfo(List<SysUser> sysUserList);

    AbstractSftpProperties getSftpProperties(String type);

    void reviewInterestVOFilePathToUrl(List<ReviewInterestVO> reviewInterestVOList);

    /**
     * 读取json文件
     *
     * @param path path
     * @return map
     */
    <T> T readerJsonFile(String path, Class<T> clz);
}
