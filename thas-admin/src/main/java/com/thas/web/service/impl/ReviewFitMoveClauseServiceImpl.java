package com.thas.web.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.thas.common.constant.Constants;
import com.thas.common.constant.Constants.HospitalConstants;
import com.thas.common.exception.ServiceException;
import com.thas.common.utils.DateUtils;
import com.thas.common.utils.StringUtils;
import com.thas.web.domain.AutSaRelation;
import com.thas.web.domain.CstCertificationStandards;
import com.thas.web.domain.HospitalPlannedDistribution;
import com.thas.web.domain.ReviewFitMoveClause;
import com.thas.web.domain.dto.MoveClauseRequest;
import com.thas.web.domain.dto.MoveClauseResponse;
import com.thas.web.domain.dto.MoveClauseResponse.MoveClauseItem;
import com.thas.web.domain.dto.ReviewClauseMoveRequest;
import com.thas.web.domain.dto.ReviewClauseMoveRequest.ReviewClauseMoveInfo;
import com.thas.web.domain.dto.ReviewFitMoveClauseReq;
import com.thas.web.domain.vo.DomainGroupNode;
import com.thas.web.dto.CstCertificationStandardVO;
import com.thas.web.dto.GroupClauseInfo;
import com.thas.web.dto.HosPlanUserInfoVO;
import com.thas.web.mapper.*;
import com.thas.web.service.IAutSaRelationService;
import com.thas.web.service.ICstDomainService;
import com.thas.web.service.IHospitalDomainGroupService;
import com.thas.web.service.IReviewFitMoveClauseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 测试评审不适用与挪动款Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-03-28
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class ReviewFitMoveClauseServiceImpl implements IReviewFitMoveClauseService {
    @Autowired
    private ReviewFitMoveClauseMapper reviewFitMoveClauseMapper;
    @Autowired
    private IAutSaRelationService autSaRelationService;
    @Autowired
    private HospitalReviewerMapper hospitalReviewerMapper;
    @Autowired
    private CstCertificationStandardsMapper cstCertificationStandardsMapper;
    @Autowired
    private HospitalPlannedDistributionMapper hospitalPlannedDistributionMapper;
    @Autowired
    private IHospitalDomainGroupService hospitalDomainGroupService;
    @Autowired
    private ICstDomainService cstDomainService;
    @Autowired
    private CstDomainMapper cstDomainMapper;

    /**
     * 查询测试评审不适用与挪动款
     *
     * @param id 测试评审不适用与挪动款主键
     * @return 测试评审不适用与挪动款
     */
    @Override
    public ReviewFitMoveClause selectReviewFitMoveClauseById(Long id) {
        return reviewFitMoveClauseMapper.selectReviewFitMoveClauseById(id);
    }

    /**
     * 查询测试评审不适用与挪动款列表
     *
     * @param reviewFitMoveClause 测试评审不适用与挪动款
     * @return 测试评审不适用与挪动款
     */
    @Override
    public List<ReviewFitMoveClause> selectReviewFitMoveClauseList(ReviewFitMoveClause reviewFitMoveClause) {
        return reviewFitMoveClauseMapper.selectReviewFitMoveClauseList(reviewFitMoveClause);
    }

    /**
     * 新增测试评审不适用与挪动款
     *
     * @param reviewFitMoveClause 测试评审不适用与挪动款
     * @return 结果
     */
    @Override
    public int insertReviewFitMoveClause(ReviewFitMoveClause reviewFitMoveClause) {
        reviewFitMoveClause.setCreateTime(DateUtils.getNowDate());
        return reviewFitMoveClauseMapper.insertReviewFitMoveClause(reviewFitMoveClause);
    }

    /**
     * 修改测试评审不适用与挪动款
     *
     * @param reviewFitMoveClause 测试评审不适用与挪动款
     * @return 结果
     */
    @Override
    public int updateReviewFitMoveClause(ReviewFitMoveClause reviewFitMoveClause) {
        reviewFitMoveClause.setUpdateTime(DateUtils.getNowDate());
        return reviewFitMoveClauseMapper.updateReviewFitMoveClause(reviewFitMoveClause);
    }

    /**
     * 批量删除测试评审不适用与挪动款
     *
     * @param ids 需要删除的测试评审不适用与挪动款主键
     * @return 结果
     */
    @Override
    public int deleteReviewFitMoveClauseByIds(Long[] ids) {
        return reviewFitMoveClauseMapper.deleteReviewFitMoveClauseByIds(ids);
    }

    /**
     * 删除测试评审不适用与挪动款信息
     *
     * @param id 测试评审不适用与挪动款主键
     * @return 结果
     */
    @Override
    public int deleteReviewFitMoveClauseById(Long id) {
        return reviewFitMoveClauseMapper.deleteReviewFitMoveClauseById(id);
    }

    @Override
    public void submitReviewFitMoveClause(ReviewFitMoveClauseReq reviewFitMoveClauseReq) {
        // 判断操作类型 操作标识：不适用为0，更换分组为1（挪动）
        if (ObjectUtil.equal(reviewFitMoveClauseReq.getOperationFlag(), Constants.STR_NUM_0)) {
            // 不适用
            // 校验：勾选时未保存不允许挪动；未分配评审员时可进行不适用操作；基本款不能小于3款；非基本款不能小于3款；六组每一组必须至少有1款；（临床服务条件校验规则待提供）
            checkNotApplicable(reviewFitMoveClauseReq);
            // 提交数据落表保存
            this.batchInsertOrUpdateReviewFitMoveClause(reviewFitMoveClauseReq);
        } else {
            throw new ServiceException("不支持当前操作");
        }
    }

    @Override
    public List<String> groupAllNotApplicableTheme(String autCode, List<String> groupIdList, String versionId) {
        String groupIds = String.join(",", groupIdList);
        List<DomainGroupNode> domainGroupList = hospitalDomainGroupService.selectDomainGroup(groupIds, versionId);
        if (CollectionUtils.isEmpty(domainGroupList)) {
            return null;
        }

        List<String> result = new ArrayList<>();
        for (DomainGroupNode domainGroupNode : domainGroupList) {
            // 大主题节点
            List<DomainGroupNode> themeNodeList = domainGroupNode.getChildren();
            if (CollectionUtils.isEmpty(themeNodeList)) {
                continue;
            }
            themeNotApplicableClause(autCode, themeNodeList, result);
        }
        return result;
    }

    @Override
    public void clauseMove(ReviewClauseMoveRequest reviewClauseMoveRequest) {
        // 校验当前是否可挪动
        checkClauseMove(reviewClauseMoveRequest);

        // 组装参数插入或者修改
        doInsertClauseMove(reviewClauseMoveRequest);
    }

    @Override
    public Set<String> moveClause(String autCode, HosPlanUserInfoVO hosPlanUserInfoVO) {
        List<CstCertificationStandardVO> cstCertificationStandardVOList = hosPlanUserInfoVO.getCstCertificationStandardVOList();
        if (CollectionUtils.isEmpty(cstCertificationStandardVOList)) {
            return new HashSet<>();
        }

        Map<String, Set<String>> groupClauseIdSetMap = cstCertificationStandardVOList
                .stream()
                .collect(Collectors.groupingBy(CstCertificationStandardVO::getDomainId,
                        Collectors.mapping(cst -> String.valueOf(cst.getClauseId()), Collectors.toSet()))
                );

        moveClause(autCode, groupClauseIdSetMap);
        Set<String> result = new HashSet<>();
        for (Set<String> clauseIdSet : groupClauseIdSetMap.values()) {
            if (CollectionUtils.isNotEmpty(clauseIdSet)) {
                result.addAll(clauseIdSet);
            }
        }
        return result;
    }

    @Override
    public void moveClause(String autCode, Map<String, Set<String>> groupClauseIdSetMap) {
        if (MapUtils.isEmpty(groupClauseIdSetMap)) {
            return;
        }

        // 查询所有的挪动的款项
        List<ReviewFitMoveClause> reviewFitMoveClauseList = queryAllMoveClause(autCode);

        if (CollectionUtils.isEmpty(reviewFitMoveClauseList)) {
            return;
        }
        // 所有挪动的款
        Set<String> allMoveClauseIdSet = reviewFitMoveClauseList
                .stream()
                .map(ReviewFitMoveClause::getClauseId)
                .collect(Collectors.toSet());

        // 根据groupId分组
        Map<String, Set<String>> groupIdMoveClauseIdSetMap = reviewFitMoveClauseList
                .stream()
                .collect(Collectors.groupingBy(ReviewFitMoveClause::getGroupId,
                        Collectors.mapping(ReviewFitMoveClause::getClauseId, Collectors.toSet())));

        // 增加或者减少
        doMoveClause(groupClauseIdSetMap, allMoveClauseIdSet, groupIdMoveClauseIdSetMap);
    }

    /**
     * 增加或者减少款
     *
     * @param groupClauseIdSetMap       原分组款映射集合
     * @param allMoveClauseIdSet        所有挪动款id集合
     * @param groupIdMoveClauseIdSetMap 所有挪动分组款id集合
     */
    private void doMoveClause(Map<String, Set<String>> groupClauseIdSetMap, Set<String> allMoveClauseIdSet, Map<String, Set<String>> groupIdMoveClauseIdSetMap) {
        // 挪动逻辑，判断每组下个款有哪些。
        for (Entry<String, Set<String>> entry : groupClauseIdSetMap.entrySet()) {
            String groupId = entry.getKey();
            Set<String> clauseIdSet = entry.getValue();

            if (CollectionUtils.isEmpty(clauseIdSet)) {
                continue;
            }

            // 去除
            clauseIdSet.removeAll(allMoveClauseIdSet);

            // 增加
            Set<String> inputClauseIdSet = groupIdMoveClauseIdSetMap.get(groupId);
            if (CollectionUtils.isNotEmpty(inputClauseIdSet)) {
                clauseIdSet.addAll(inputClauseIdSet);
            }
        }
    }

    @Override
    public void moveClause(String autCode, List<DomainGroupNode> domainGroups) {
        if (CollectionUtils.isEmpty(domainGroups)) {
            return;
        }

        // 查询所有的挪动的款项
        List<ReviewFitMoveClause> reviewFitMoveClauseList = queryAllMoveClause(autCode);
        if (CollectionUtils.isEmpty(reviewFitMoveClauseList)) {
            return;
        }

        // 根据小主题id分组
        Map<String, Set<String>> smallThmeIdMoveClauseIdSetMap = reviewFitMoveClauseList
                .stream()
                .collect(Collectors.groupingBy(ReviewFitMoveClause::getSmallThemeId,
                        Collectors.mapping(ReviewFitMoveClause::getClauseId, Collectors.toSet())));

        // 所有挪动的款
        Set<String> allMoveClauseIdSet = reviewFitMoveClauseList
                .stream()
                .map(ReviewFitMoveClause::getClauseId)
                .collect(Collectors.toSet());

        // 扁平化所有的款对象，并且组装成map
        Map<String, DomainGroupNode> clauseIdGroupNodeMap = getAllClauseGroupNode(domainGroups);

        for (DomainGroupNode groupNode : domainGroups) {
            // 获取大主题
            List<DomainGroupNode> themeGroupNodeList = groupNode.getChildren();
            if (CollectionUtils.isEmpty(themeGroupNodeList)) {
                continue;
            }

            for (DomainGroupNode themeNode : themeGroupNodeList) {

                // 获取小主题
                List<DomainGroupNode> smallThemeNodeList = themeNode.getChildren();
                if (CollectionUtils.isEmpty(smallThemeNodeList)) {
                    continue;
                }

                for (DomainGroupNode smallThemeNode : smallThemeNodeList) {
                    String smallThemeId = smallThemeNode.getGroupId();

                    // 获取款
                    List<DomainGroupNode> clauseNodeList = smallThemeNode.getChildren();

                    if (CollectionUtils.isEmpty(clauseNodeList)) {
                        continue;
                    }

                    // 先删除
                    clauseNodeList = clauseNodeList.stream()
                            .filter(clauseNode -> !allMoveClauseIdSet.contains(clauseNode.getGroupDetail()))
                            .collect(Collectors.toList());

                    smallThemeNode.setChildren(clauseNodeList);

                    Set<String> clauseIdSet = smallThmeIdMoveClauseIdSetMap.get(smallThemeId);

                    if (CollectionUtils.isEmpty(clauseIdSet)) {
                        continue;
                    }

                    for (String clauseId : clauseIdSet) {
                        clauseNodeList.add(clauseIdGroupNodeMap.get(clauseId));
                    }
                }
            }
        }
    }

    private Map<String, DomainGroupNode> getAllClauseGroupNode(List<DomainGroupNode> domainGroups) {
        List<DomainGroupNode> clauseNodeList = new ArrayList<>();
        for (DomainGroupNode groupNode : domainGroups) {
            // 获取大主题
            List<DomainGroupNode> themeGroupNodeList = groupNode.getChildren();
            if (CollectionUtils.isEmpty(themeGroupNodeList)) {
                continue;
            }

            for (DomainGroupNode themeNode : themeGroupNodeList) {

                // 获取小主题
                List<DomainGroupNode> smallThemeNodeList = themeNode.getChildren();
                if (CollectionUtils.isEmpty(smallThemeNodeList)) {
                    continue;
                }

                for (DomainGroupNode smallThemeNode : smallThemeNodeList) {
                    if (CollectionUtils.isNotEmpty(smallThemeNode.getChildren())) {
                        clauseNodeList.addAll(smallThemeNode.getChildren());
                    }
                }
            }
        }
        return clauseNodeList.stream()
                .collect(Collectors.toMap(DomainGroupNode::getGroupDetail, v -> v, (v1, v2) -> v2));
    }


    private List<ReviewFitMoveClause> queryAllMoveClause(String autCode) {
        ReviewFitMoveClause queryParam = new ReviewFitMoveClause();
        queryParam.setMoveStatus(HospitalConstants.LONG_NUM_1);
        queryParam.setAutCode(autCode);
        return reviewFitMoveClauseMapper.selectReviewFitMoveClauseList(queryParam);
    }


    @Override
    public Integer notApplicable(String autCode, Map<String, Set<String>> groupClauseIdSetMap) {
        // 查询所有的不适用的款项
        ReviewFitMoveClause queryParam = new ReviewFitMoveClause();
        queryParam.setAutCode(autCode);
        queryParam.setFitStatus(HospitalConstants.LONG_NUM_1);
        List<ReviewFitMoveClause> reviewFitMoveClauseList = reviewFitMoveClauseMapper.selectReviewFitMoveClauseList(queryParam);

        if (CollectionUtils.isEmpty(reviewFitMoveClauseList)) {
            return 0;
        }

        if (MapUtils.isEmpty(groupClauseIdSetMap)) {
            return reviewFitMoveClauseList.size();
        }

        // 所有不适用的款
        Set<String> allNotApplicableClauseIdSet = reviewFitMoveClauseList
                .stream()
                .map(ReviewFitMoveClause::getClauseId)
                .collect(Collectors.toSet());

        for (Entry<String, Set<String>> entry : groupClauseIdSetMap.entrySet()) {
            Set<String> clauseIdSet = entry.getValue();
            clauseIdSet.removeAll(allNotApplicableClauseIdSet);
        }
        return reviewFitMoveClauseList.size();
    }

    @Override
    public MoveClauseResponse moveClauseInfo(MoveClauseRequest moveClauseRequest) {
        MoveClauseResponse moveClauseResponse = new MoveClauseResponse();
        List<MoveClauseItem> moveClauseItemList = new ArrayList<>();
        moveClauseResponse.setMoveClauseItemList(moveClauseItemList);

        // 查询所有挪动记录
        List<ReviewFitMoveClause> reviewFitMoveClauseList = queryAllMoveClause(moveClauseRequest.getAutCode());
        if (CollectionUtils.isEmpty(reviewFitMoveClauseList)) {
            return moveClauseResponse;
        }

        // 通过自评编码拿到版本号
        AutSaRelation autSaRelation = autSaRelationService.selectAutSaRelationByAutCode(moveClauseRequest.getAutCode(), Constants.HospitalConstants.NUM_1);

        // 根绝autCode查询现有的款记录。
        List<GroupClauseInfo> groupClauseInfos = cstCertificationStandardsMapper.selectGroupClause(null, Long.valueOf(autSaRelation.getAutCsId()));

        if (CollectionUtils.isEmpty(groupClauseInfos)) {
            return moveClauseResponse;
        }

        // 根据groupId分组
        Map<String, Set<String>> groupIdMoveClauseIdSetMap = reviewFitMoveClauseList
                .stream()
                .collect(Collectors.groupingBy(ReviewFitMoveClause::getGroupId,
                        Collectors.mapping(ReviewFitMoveClause::getClauseId, Collectors.toSet())));

        // 所有挪动的款
        Set<String> allMoveClauseIdSet = reviewFitMoveClauseList
                .stream()
                .map(ReviewFitMoveClause::getClauseId)
                .collect(Collectors.toSet());

        Map<String, Set<String>> oriGroupIdClauseIdSetMap = groupClauseInfos.stream()
                .collect(Collectors.groupingBy(GroupClauseInfo::getGroupId,
                        Collectors.mapping(GroupClauseInfo::getClauseId, Collectors.toSet())));

        for (Entry<String, Set<String>> entry : oriGroupIdClauseIdSetMap.entrySet()) {
            String groupId = entry.getKey();
            Set<String> clauseIdSet = entry.getValue();

            boolean noneMoveClauseFlag = clauseIdSet.stream().noneMatch(allMoveClauseIdSet::contains);

            if (!groupIdMoveClauseIdSetMap.containsKey(groupId) && noneMoveClauseFlag) {
                continue;
            }

            MoveClauseItem moveClauseItem = new MoveClauseItem();
            Set<String> inputClauseIds = new HashSet<>();
            Set<String> outputClauseIds = new HashSet<>();
            moveClauseItem.setGroupId(groupId);
            moveClauseItem.setInputClauseIds(inputClauseIds);
            moveClauseItem.setOutputClauseIds(outputClauseIds);

            // 拿到挪动到当前组的款
            Set<String> input = groupIdMoveClauseIdSetMap.get(groupId);

            // 获取当前组挪出去的款
            Set<String> out = clauseIdSet.stream()
                    .filter(allMoveClauseIdSet::contains)
                    .collect(Collectors.toSet());

            if (CollectionUtils.isNotEmpty(input)) {
                out.removeAll(input);

                // 获取挪到当前组的款，存在第一次从A挪动到B，第二次又从B挪回A，需要删除原本就属于A的款id记录
                inputClauseIds.addAll(input);
                inputClauseIds.removeAll(clauseIdSet);
            }
            outputClauseIds.addAll(out);

            moveClauseItemList.add(moveClauseItem);
        }
        return moveClauseResponse;
    }

    private void doInsertClauseMove(ReviewClauseMoveRequest reviewClauseMoveRequest) {
        // 查询数据，判断入参数据是新增还是更新
        List<String> clauseIdList = reviewClauseMoveRequest.getClauseMoveInfoList()
                .stream()
                .map(ReviewClauseMoveInfo::getClauseId)
                .collect(Collectors.toList());
        ReviewFitMoveClauseReq reviewFitMoveClauseReq = new ReviewFitMoveClauseReq();
        reviewFitMoveClauseReq.setAutCode(reviewClauseMoveRequest.getAutCode());
        reviewFitMoveClauseReq.setClauseIdList(clauseIdList);
        List<ReviewFitMoveClause> oldReviewFitMoveClauseList = reviewFitMoveClauseMapper.qryReviewFitMoveClauseList(reviewFitMoveClauseReq);

        Map<String, ReviewFitMoveClause> ReviewFitMoveClauseMap =
                Optional.ofNullable(oldReviewFitMoveClauseList)
                        .orElse(new ArrayList<>())
                        .stream()
                        .collect(Collectors.toMap(ReviewFitMoveClause::getClauseId, v -> v, (v1, v2) -> v2));

        List<ReviewFitMoveClause> batchInsert = new ArrayList<>();
        List<ReviewFitMoveClause> batchUpdate = new ArrayList<>();
        for (ReviewClauseMoveInfo reviewClauseMoveInfo : reviewClauseMoveRequest.getClauseMoveInfoList()) {
            String clauseId = reviewClauseMoveInfo.getClauseId();
            ReviewFitMoveClause oldReviewFitMoveClause = ReviewFitMoveClauseMap.get(clauseId);
            if (oldReviewFitMoveClause == null) {
                ReviewFitMoveClause reviewFitMoveClause = new ReviewFitMoveClause();
                reviewFitMoveClause.setAutCode(reviewClauseMoveRequest.getAutCode());
                reviewFitMoveClause.setClauseId(reviewClauseMoveInfo.getClauseId());
                reviewFitMoveClause.setMoveStatus(HospitalConstants.LONG_NUM_1);
                reviewFitMoveClause.setSmallThemeId(reviewClauseMoveInfo.getSmallThemeId());
                reviewFitMoveClause.setThemeId(reviewClauseMoveInfo.getThemeId());
                reviewFitMoveClause.setGroupId(reviewClauseMoveInfo.getGroupId());
                batchInsert.add(reviewFitMoveClause);
            } else {
                oldReviewFitMoveClause.setMoveStatus(HospitalConstants.LONG_NUM_1);
                oldReviewFitMoveClause.setSmallThemeId(reviewClauseMoveInfo.getSmallThemeId());
                oldReviewFitMoveClause.setThemeId(reviewClauseMoveInfo.getThemeId());
                oldReviewFitMoveClause.setGroupId(reviewClauseMoveInfo.getGroupId());
                batchUpdate.add(oldReviewFitMoveClause);
            }
        }
        if (CollectionUtils.isNotEmpty(batchInsert)) {
            reviewFitMoveClauseMapper.moveClauseBatchInsert(batchInsert);
        }
        if (CollectionUtils.isNotEmpty(batchUpdate)) {
            reviewFitMoveClauseMapper.batchUpdateReviewFitMoveClause(batchUpdate);
        }
    }

    private void checkClauseMove(ReviewClauseMoveRequest reviewClauseMoveRequest) {
        // 判断是否已经分配
        // 查询applyNo
        AutSaRelation autSaRelation = autSaRelationService.selectAutSaRelationByAutCode(reviewClauseMoveRequest.getAutCode(), Constants.HospitalConstants.NUM_1);
        String applyNo = autSaRelation.getHospitalApplyNo();

        // 判断是否分配。
        checkIsAllocation(applyNo);

        List<CstCertificationStandards> cstCertificationStandardsList = getCstCertificationStandards(applyNo);

        // 校验挪动后保证每一组至少还有一款
        Map<String, Set<String>> groupIdClauseIdSetMap = cstCertificationStandardsList
                .stream()
                .collect(Collectors.groupingBy(cst -> cst.getDomainId().toString(),
                        Collectors.mapping(cst -> cst.getClauseId().toString(), Collectors.toSet()))
                );

        moveClause(autSaRelation.getAutCode(), groupIdClauseIdSetMap, reviewClauseMoveRequest);

        Map<String, DomainGroupNode> domainGroupNodeMap = queryDomainGroupNodeMap(groupIdClauseIdSetMap.keySet());

        for (Entry<String, Set<String>> entry : groupIdClauseIdSetMap.entrySet()) {
            Set<String> clauseIdSet = entry.getValue();
            if (CollectionUtils.isEmpty(clauseIdSet) || clauseIdSet.size() < 1) {
                DomainGroupNode domainGroupNode = domainGroupNodeMap.getOrDefault(entry.getKey(), new DomainGroupNode());
                throw new ServiceException(String.format("%s可评价款不能少于1款", domainGroupNode.getGroupDetail()));
            }
        }
    }

    private Map<String, DomainGroupNode> queryDomainGroupNodeMap(Set<String> groupIdSet) {
        String groupIds = StringUtils.join(groupIdSet, ",");
       // List<DomainGroupNode> domainGroupNodeList = cstDomainService.selectGroupNodeByNotInIds(groupIds);
        List<DomainGroupNode> domainGroupNodeList = cstDomainMapper.selectGroupNodeBydomainIds(groupIds);
        if (CollectionUtils.isEmpty(domainGroupNodeList)) {
            return new HashMap<>();
        }
        return domainGroupNodeList.stream()
                .collect(Collectors.toMap(DomainGroupNode::getGroupId, v -> v, (v1, v2) -> v2));
    }

    private void moveClause(String autCode, Map<String , Set<String>> groupIdClauseIdSetMap,
                            ReviewClauseMoveRequest reviewClauseMoveRequest) {
        // 增加或者减少款
        moveClause(autCode, groupIdClauseIdSetMap);

        // 减少或者增加当前挪动的款
        List<ReviewClauseMoveInfo> clauseMoveInfoList = reviewClauseMoveRequest.getClauseMoveInfoList();

        // 根据groupId分组
        Map<String, Set<String>> groupIdMoveClauseIdSetMap = clauseMoveInfoList
                .stream()
                .collect(Collectors.groupingBy(ReviewClauseMoveInfo::getGroupId,
                        Collectors.mapping(ReviewClauseMoveInfo::getClauseId, Collectors.toSet())));

        Set<String> allMoveClauseIdSet = clauseMoveInfoList
                .stream()
                .map(ReviewClauseMoveInfo::getClauseId)
                .collect(Collectors.toSet());

        doMoveClause(groupIdClauseIdSetMap, allMoveClauseIdSet, groupIdMoveClauseIdSetMap);
    }

    private List<CstCertificationStandards> getCstCertificationStandards(String applyNo) {
        // 查询当前版本号
        HospitalPlannedDistribution hospitalPlannedDistribution = hospitalPlannedDistributionMapper.selectHospitalPlannedDistributionByApplyNo(applyNo);
        Long versionId = hospitalPlannedDistribution.getVersionId();
        List<CstCertificationStandards> CstCertificationStandardsList = cstCertificationStandardsMapper.selectDomainIdAndClauseIdByVersionId(versionId);

        if (CollectionUtils.isEmpty(CstCertificationStandardsList)) {
            throw new ServiceException(String.format("versionId[%s]查询款项记录为空", versionId), 500);
        }
        return CstCertificationStandardsList;
    }

    private void themeNotApplicableClause(String autCode, List<DomainGroupNode> themeNodeList, List<String> result) {
        // 获取当前自评编码下所有不适用以及挪动的款记录
        List<ReviewFitMoveClause> reviewFitMoveClauses = queryNotApplicableOrMoveClause(autCode);

        if (CollectionUtils.isEmpty(reviewFitMoveClauses)) {
            return;
        }

        // 获取不适用或者挪动的款项id集合
        Set<String> notApplicableOrMoveClauseIdSet = reviewFitMoveClauses
                .stream()
                .map(ReviewFitMoveClause::getClauseId)
                .collect(Collectors.toSet());

        for (DomainGroupNode themeNode : themeNodeList) {
            List<DomainGroupNode> themeAllClauseIdList = new ArrayList<>();

            // 小主题集合
            List<DomainGroupNode> smallThemeNodeList = themeNode.getChildren();
            if (CollectionUtils.isEmpty(smallThemeNodeList)) {
                continue;
            }

            for (DomainGroupNode smallThemeNode : smallThemeNodeList) {
                // 款集合
                List<DomainGroupNode> clauseNodeList = smallThemeNode.getChildren();

                themeAllClauseIdList.addAll(clauseNodeList);
            }

            if (isAllNotApplicable(themeAllClauseIdList, notApplicableOrMoveClauseIdSet)) {
                result.add(themeNode.getGroupId());
            }
        }
    }

    /**
     * 查询不适用和挪动的款项
     *
     * @param autCode 编码
     * @return list
     */
    private List<ReviewFitMoveClause> queryNotApplicableOrMoveClause(String autCode) {
        ReviewFitMoveClause queryParam = new ReviewFitMoveClause();
        queryParam.setAutCode(autCode);
        queryParam.setStatus(HospitalConstants.LONG_NUM_1);
        List<ReviewFitMoveClause> reviewFitMoveClauses = selectReviewFitMoveClauseList(queryParam);

        if (CollectionUtils.isEmpty(reviewFitMoveClauses)) {
            return null;
        }
        // 过滤
        return reviewFitMoveClauses.stream()
                .filter(reviewFitMoveClause -> HospitalConstants.LONG_NUM_1.equals(reviewFitMoveClause.getFitStatus())
                        || HospitalConstants.LONG_NUM_1.equals(reviewFitMoveClause.getMoveStatus()))
                .collect(Collectors.toList());
    }

    private boolean isAllNotApplicable(List<DomainGroupNode> clauseNodeList, Set<String> notApplicableClauseIdSet) {
        if (CollectionUtils.isEmpty(clauseNodeList)) {
            return true;
        }

        return clauseNodeList
                .stream()
                .allMatch(clauseNode -> notApplicableClauseIdSet.contains(clauseNode.getGroupDetail()));
    }


    /**
     * 校验不适用逻辑
     *
     * @param reviewFitMoveClauseReq 入参
     */
    private void checkNotApplicable(ReviewFitMoveClauseReq reviewFitMoveClauseReq) {
        // 取消则不校验
        if (!HospitalConstants.LONG_NUM_1.equals(reviewFitMoveClauseReq.getFitStatus())) {
            return;
        }

        List<String> clauseIdList = reviewFitMoveClauseReq.getClauseIdList();
        // 查询applyNo
        AutSaRelation autSaRelation = autSaRelationService.selectAutSaRelationByAutCode(reviewFitMoveClauseReq.getAutCode(), Constants.HospitalConstants.NUM_1);
        String applyNo = autSaRelation.getHospitalApplyNo();

        // 判断是否分配。
        checkIsAllocation(applyNo);

        // 判断基本款，非基本款是否大于等于3，每一章基本款非基本款必须有一个，以及每组是否至少有一款。
        checkClauseNum(autSaRelation, clauseIdList);
    }

    private void checkClauseNum(AutSaRelation autSaRelation, List<String> clauseIdList) {
        // 查询当前版本所有记录
        List<CstCertificationStandards> cstCertificationStandardsList = getCstCertificationStandards(autSaRelation.getHospitalApplyNo());

        // 查询不适用记录
        ReviewFitMoveClause queryParam = new ReviewFitMoveClause();
        queryParam.setAutCode(autSaRelation.getAutCode());
        queryParam.setFitStatus(HospitalConstants.LONG_NUM_1);
        List<ReviewFitMoveClause> reviewFitMoveClauses = selectReviewFitMoveClauseList(queryParam);

        doCheckClauseNum(cstCertificationStandardsList, reviewFitMoveClauses, clauseIdList);

        // 校验每一组至少有一款适用
        checkGroupClauseNum(cstCertificationStandardsList, reviewFitMoveClauses, clauseIdList);
    }


    private void checkGroupClauseNum(List<CstCertificationStandards> cstCertificationStandardsList,
                                     List<ReviewFitMoveClause> reviewFitMoveClauses,
                                     List<String> clauseIdList) {

        // 获取不适用的款项id集合
        Set<String> notApplicableClauseIdSet = Optional.ofNullable(reviewFitMoveClauses).orElse(new ArrayList<>())
                .stream()
                .map(ReviewFitMoveClause::getClauseId)
                .collect(Collectors.toSet());
        // 把即将不适用的添加到集合
        notApplicableClauseIdSet.addAll(clauseIdList);

        Map<Long, List<CstCertificationStandards>> domainIdMap = cstCertificationStandardsList
                .stream()
                .collect(Collectors.groupingBy(CstCertificationStandards::getDomainId));

        for (Entry<Long, List<CstCertificationStandards>> entry : domainIdMap.entrySet()) {
            List<CstCertificationStandards> cstCertificationStandards = entry.getValue();
            if (doCheckClauseNum(cstCertificationStandards, notApplicableClauseIdSet, HospitalConstants.NUM_1)) {
                throw new ServiceException(String.format("%s组可评价款不能少于%s款",
                        cstCertificationStandards.get(0).getChapter(), HospitalConstants.NUM_1), 500);
            }
        }
    }

    private void doCheckClauseNum(List<CstCertificationStandards> cstCertificationStandardsList,
                                  List<ReviewFitMoveClause> reviewFitMoveClauses,
                                  List<String> clauseIdList) {

        // 获取不适用的款项id集合
        Set<String> notApplicableClauseIdSet = Optional.ofNullable(reviewFitMoveClauses)
                .orElse(new ArrayList<>())
                .stream()
                .map(ReviewFitMoveClause::getClauseId)
                .collect(Collectors.toSet());
        // 把即将不适用的添加到集合
        notApplicableClauseIdSet.addAll(clauseIdList);

        doCheckClauseNum(cstCertificationStandardsList, notApplicableClauseIdSet, "所有分组", HospitalConstants.NUM_3);

        // 校验每一章基本款非基本款不能少于一款。
        checkChapterClauseNum(cstCertificationStandardsList, notApplicableClauseIdSet);
    }

    /**
     * 校验每一章基本款非基本款不能少于一款
     */
    private void checkChapterClauseNum(List<CstCertificationStandards> cstCertificationStandardsList,
                                       Set<String> notApplicableClauseIdSet) {

        // 根据每一章分组。
        Map<Long, List<CstCertificationStandards>> chapterCstListMap = cstCertificationStandardsList.stream()
                .collect(Collectors.groupingBy(CstCertificationStandards::getChapterId));

        for (Entry<Long, List<CstCertificationStandards>> entry : chapterCstListMap.entrySet()) {
            List<CstCertificationStandards> csts = entry.getValue();
            if (CollectionUtils.isEmpty(csts)) {
                continue;
            }

            doCheckClauseNum(csts, notApplicableClauseIdSet, csts.get(0).getChapterNo(), HospitalConstants.NUM_1);
        }
    }

    private void doCheckClauseNum(List<CstCertificationStandards> cstCertificationStandardsList,
                                         Set<String> notApplicableClauseIdSet,
                                         String name,
                                         Integer num) {

        // isStar 1 基本款项 0非基本
        List<CstCertificationStandards> c1 = cstCertificationStandardsList.stream()
                .filter(cstCertificationStandards -> HospitalConstants.STR_NUM_1.equals(cstCertificationStandards.getIsStar()))
                .collect(Collectors.toList());

        if (doCheckClauseNum(c1, notApplicableClauseIdSet, num)) {
            throw new ServiceException(String.format("%s可评价的基本款不能少于%s款", name, num), 500);
        }

        // 非基本款
        List<CstCertificationStandards> c2 = cstCertificationStandardsList.stream()
                .filter(cstCertificationStandards -> HospitalConstants.STR_NUM_0.equals(cstCertificationStandards.getIsStar()))
                .collect(Collectors.toList());

        if (doCheckClauseNum(c2, notApplicableClauseIdSet, num)) {
            throw new ServiceException(String.format("%s可评价的非基本款不能少于%s款", name, num), 500);
        }
    }

    private boolean doCheckClauseNum(List<CstCertificationStandards> cstCertificationStandardsList, Set<String> notApplicableClauseIdSet, Integer num) {
        return cstCertificationStandardsList
                .stream()
                .filter(cstCertificationStandards -> !notApplicableClauseIdSet.contains(cstCertificationStandards.getClauseId().toString()))
                .count() < num;
    }

    private void checkIsAllocation(String applyNo) {
        if (hospitalReviewerMapper.isAllocation(applyNo, HospitalConstants.NUM_1) > 0) {
            throw new ServiceException("当前评审已经分配评审员，不可操作", 500);
        }
    }

    private void batchInsertOrUpdateReviewFitMoveClause(ReviewFitMoveClauseReq reviewFitMoveClauseReq) {
        //查询数据，判断入参数据是新增还是更新
        ReviewFitMoveClauseReq qryReviewFitMoveClauseReq = new ReviewFitMoveClauseReq();
        qryReviewFitMoveClauseReq.setAutCode(reviewFitMoveClauseReq.getAutCode());
        qryReviewFitMoveClauseReq.setClauseIdList(reviewFitMoveClauseReq.getClauseIdList());
        List<ReviewFitMoveClause> qryReviewFitMoveClauseList = reviewFitMoveClauseMapper.qryReviewFitMoveClauseList(qryReviewFitMoveClauseReq);
        Map<String, ReviewFitMoveClause> qryMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(qryReviewFitMoveClauseList)) {
            qryMap = qryReviewFitMoveClauseList.stream().collect(Collectors.toMap(ReviewFitMoveClause::getClauseId, o -> o));
        }
        //如果入参为还原状态，入参的款都落表，插入或更新为适用状态
        //如果入参为不适用状态，同上

        List<String> reqClauseIdList = reviewFitMoveClauseReq.getClauseIdList();
        List<ReviewFitMoveClause> insertClauseIdList = new ArrayList<>();
        List<ReviewFitMoveClause> updateClauseIdList = new ArrayList<>();
        Map<String, ReviewFitMoveClause> finalQryMap = qryMap;

        reqClauseIdList.forEach(reqClauseId -> {
            ReviewFitMoveClause reviewFitMoveClause = new ReviewFitMoveClause();
            if (finalQryMap.containsKey(reqClauseId)) {
                //暂无更新需求逻辑，后面提供共用做准备
                ReviewFitMoveClause mapValue = finalQryMap.get(reqClauseId);
                reviewFitMoveClause.setId(mapValue.getId());
                reviewFitMoveClause.setFitStatus(reviewFitMoveClauseReq.getFitStatus());
                updateClauseIdList.add(reviewFitMoveClause);
            } else {
                reviewFitMoveClause.setAutCode(reviewFitMoveClauseReq.getAutCode());
                reviewFitMoveClause.setClauseId(reqClauseId);
                reviewFitMoveClause.setFitStatus(reviewFitMoveClauseReq.getFitStatus());
                insertClauseIdList.add(reviewFitMoveClause);
            }
        });

        if (CollectionUtils.isNotEmpty(insertClauseIdList)) {
            reviewFitMoveClauseMapper.batchInsertReviewFitMoveClause(insertClauseIdList);
            log.info("自评编码为：{}，更新的不适用款状态为{}，已插入{}条不适用款",reviewFitMoveClauseReq.getAutCode(),reviewFitMoveClauseReq.getFitStatus(),insertClauseIdList.size());
        }
        if (CollectionUtils.isNotEmpty(updateClauseIdList)) {
            reviewFitMoveClauseMapper.batchUpdateReviewFitMoveClause(updateClauseIdList);
            log.info("自评编码为：{}，更新的不适用款状态为{}，已更新{}条不适用款",reviewFitMoveClauseReq.getAutCode(),reviewFitMoveClauseReq.getFitStatus(),updateClauseIdList.size());
        }
    }
}
