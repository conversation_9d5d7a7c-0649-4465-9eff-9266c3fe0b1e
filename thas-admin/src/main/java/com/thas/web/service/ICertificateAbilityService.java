package com.thas.web.service;

import com.thas.web.domain.CertificateAbility;

import java.util.List;

/**
 * 对应证书关联Service接口
 * 
 * <AUTHOR>
 * @date 2022-01-19
 */
public interface ICertificateAbilityService 
{
    /**
     * 查询对应证书关联
     * 
     * @param id 对应证书关联主键
     * @return 对应证书关联
     */
    public CertificateAbility selectCertificateAbilityById(Long id);

    List<CertificateAbility> selectCertificateAbilityByAccountId(String accountId);
    /**
     * 查询对应证书关联列表
     * 
     * @param certificateAbility 对应证书关联
     * @return 对应证书关联集合
     */
    public List<CertificateAbility> selectCertificateAbilityList(CertificateAbility certificateAbility);

    /**
     * 新增对应证书关联
     * 
     * @param certificateAbility 对应证书关联
     * @return 结果
     */
    public int insertCertificateAbility(CertificateAbility certificateAbility);

    /**
     * 修改对应证书关联
     * 
     * @param certificateAbility 对应证书关联
     * @return 结果
     */
    public int updateCertificateAbility(CertificateAbility certificateAbility);

    /**
     * 批量删除对应证书关联
     * 
     * @param ids 需要删除的对应证书关联主键集合
     * @return 结果
     */
    public int deleteCertificateAbilityByIds(Long[] ids);

    /**
     * 删除对应证书关联信息
     * 
     * @param id 对应证书关联主键
     * @return 结果
     */
    public int deleteCertificateAbilityById(Long id);

    void updateStatusByProcessCode(String accountId, int i);
}
