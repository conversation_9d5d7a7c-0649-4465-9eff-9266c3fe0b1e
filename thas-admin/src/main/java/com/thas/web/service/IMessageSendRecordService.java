package com.thas.web.service;

import java.security.GeneralSecurityException;
import java.util.List;
import com.thas.web.domain.MessageSendRecord;
import com.thas.web.domain.dto.MessageSendRecordDTO;

import javax.mail.MessagingException;

/**
 * 消息发送记录Service接口
 *
 * <AUTHOR>
 * @date 2022-01-25
 */
public interface IMessageSendRecordService
{
    /**
     * 查询消息发送记录
     *
     * @param id 消息发送记录主键
     * @return 消息发送记录
     */
    public MessageSendRecord selectMessageSendRecordById(Long id);

    /**
     * 查询消息发送记录列表
     *
     * @param messageSendRecord 消息发送记录
     * @return 消息发送记录集合
     */
    public List<MessageSendRecord> selectMessageSendRecordList(MessageSendRecord messageSendRecord);

    /**
     * 新增消息发送记录
     *
     * @param messageSendRecord 消息发送记录
     * @return 结果
     */
    public int insertMessageSendRecord(MessageSendRecord messageSendRecord);

    /**
     * 修改消息发送记录
     *
     * @param messageSendRecord 消息发送记录
     * @return 结果
     */
    public int updateMessageSendRecord(MessageSendRecord messageSendRecord);

    /**
     * 批量删除消息发送记录
     *
     * @param ids 需要删除的消息发送记录主键集合
     * @return 结果
     */
    public int deleteMessageSendRecordByIds(Long[] ids);

    /**
     * 删除消息发送记录信息
     *
     * @param id 消息发送记录主键
     * @return 结果
     */
    public int deleteMessageSendRecordById(Long id);

    /**
     * 向指定用户发送信息
     * @param messageSendRecordDTO
     * @return
     */
    public boolean sendMessageToUser(MessageSendRecordDTO messageSendRecordDTO) throws GeneralSecurityException, MessagingException;

    /**
     * 邮箱
     *
     * @param msg   内容
     * @param phone 号码
     */
    void sendMsgFromSms(String msg, String phone);

    /**
     * 短信
     *
     * @param msg   内容
     * @param email 邮箱
     */
    void sendMsgFromEmail(String msg, String email, String emailTitle);
}
