package com.thas.web.service;

import com.thas.web.domain.TraQuestionnaireAnswer;
import com.thas.web.domain.vo.TraQuestionnaireAnswerRes;
import com.thas.web.domain.vo.TraQuestionnaireAnswerVO;

import java.util.List;

/**
 * 调查问卷答卷Service接口
 *
 * <AUTHOR>
 * @date 2022-02-14
 */
public interface ITraQuestionnaireAnswerService {
    /**
     * 查询调查问卷答卷
     *
     * @return 调查问卷答卷
     */
    TraQuestionnaireAnswerVO selectTraQuestionnaireAnswerById(String questionnaireId, String answerId);

    /**
     * 查询调查问卷答卷列表
     *
     * @param traQuestionnaireAnswer 调查问卷答卷
     * @return 调查问卷答卷集合
     */
    List<TraQuestionnaireAnswerRes> selectTraQuestionnaireAnswerList(TraQuestionnaireAnswerRes traQuestionnaireAnswer);

    /**
     * 新增调查问卷答卷
     *
     * @param traQuestionnaireAnswer 调查问卷答卷
     * @return 结果
     */
    int insertTraQuestionnaireAnswer(TraQuestionnaireAnswer traQuestionnaireAnswer);

    int updateStatus(Integer status, Long id);

    int delete(Long id);
}
