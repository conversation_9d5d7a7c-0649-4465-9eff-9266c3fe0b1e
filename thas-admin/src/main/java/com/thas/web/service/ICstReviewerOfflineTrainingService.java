package com.thas.web.service;

import com.thas.web.domain.CstReviewerOfflineTraining;
import com.thas.web.domain.vo.OfflineTrainingRegisteredVo;

import java.util.List;


/**
 * 评审员端线下培训管理Service接口
 *
 * <AUTHOR>
 * @date 2022-02-17
 */
public interface ICstReviewerOfflineTrainingService {


    int selectCountByTrainingId(String trainingId);

    /**
     * 查询评审员端线下培训管理
     *
     * @param id 评审员端线下培训管理主键
     * @return 评审员端线下培训管理
     */
    CstReviewerOfflineTraining selectCstReviewerOfflineTrainingById(Long id);

    /**
     * 查询评审员端线下培训管理列表
     *
     * @param cstReviewerOfflineTraining 评审员端线下培训管理(多个trainingId可用字符串’,‘拼接查询)
     * @return 评审员端线下培训管理集合
     */
    List<CstReviewerOfflineTraining> selectCstReviewerOfflineTrainingList(CstReviewerOfflineTraining cstReviewerOfflineTraining);

    /**
     * 新增评审员端线下培训管理
     *
     * @param cstReviewerOfflineTraining 评审员端线下培训管理
     * @return 结果
     */
    int insertCstReviewerOfflineTraining(CstReviewerOfflineTraining cstReviewerOfflineTraining);

    /**
     * 修改评审员端线下培训管理
     *
     * @param cstReviewerOfflineTraining 评审员端线下培训管理
     * @return 结果
     */
    int updateCstReviewerOfflineTraining(CstReviewerOfflineTraining cstReviewerOfflineTraining);

    int updateStatusByAccountId(String accountId, int status);

    int batchInsertOfflineTraining(List<CstReviewerOfflineTraining> cstReviewerOfflineTrainingList);

    List<CstReviewerOfflineTraining> selectCstReviewerOfflineTrainingByAccountId(String accountId);

    /**
     * 删除线下培训管理
     *
     * @param cstReviewerOfflineTraining 评审员端线下培训管理
     * @return 结果
     */
    int deleteCstReviewerOfflineTraining(CstReviewerOfflineTraining cstReviewerOfflineTraining);

    int updateInfoByAccountIdAndTraId(CstReviewerOfflineTraining cstReviewerOfflineTraining);

    /**
     * 查询线下培训管理对应的报名人员信息
     *
     * @param trainingId 线下培训管理id
     * @return 结果
     */
    List<OfflineTrainingRegisteredVo> selectSysUserByTrainingId(String trainingId);
}
