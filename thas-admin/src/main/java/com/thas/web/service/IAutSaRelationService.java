package com.thas.web.service;

import com.thas.common.core.domain.AjaxResult;
import com.thas.web.domain.AutSaRelation;
import com.thas.web.domain.AutSaRelationList;
import com.thas.web.domain.vo.AutSaRelationQueryRes;
import com.thas.web.domain.vo.TraineesReviewRecVO;

import java.util.List;

/**
 * 认证自评关联Service接口
 *
 * <AUTHOR>
 * @date 2022-01-17
 */
public interface IAutSaRelationService {

    /**
     * 根据条件查询认证自评关联信息
     * 非医院角色 需要直接使用自评编码查询相关信息
     * 医院角色: 只需要医院账户信息
     *
     * @param autSaRelation 认证自评关联信息查询条件
     * @return 认证自评关联信息
     */
    AjaxResult queryAutSaRelation(AutSaRelation autSaRelation);

    /**
     * 根据用户id获取医院applyNo
     *
     * @param userId 用户id
     * @return applyNo
     */
    String getHospitalApplyNoByUserId(String userId) ;

    /**
     * 校验医疗机构是否完成评审周期分配和审核
     *
     * @param hosApplyNo 医疗机构applyNo
     */
    void checkHosIsCompCyAlocRev(String hosApplyNo) ;



    /**
     * 新增根据医院id查询医院认证自评关联信息
     *
     * @param hospitalApplyNo 医院编码
     * @param initFlag 初始化标识
     * @return 结果
     */
    AutSaRelation selectAutSaRelationByHospitalApplyNo(String hospitalApplyNo, boolean initFlag);

    /**
     * 查询认证自评关联信息
     *
     * @param autCode 自评编码
     * @return 认证自评关联信息
     */
    AutSaRelation selectAutSaRelationByAutCode(String autCode, Integer status);

    /**
     * 根据条件查询认证自评关联信息
     *
     * @param autSaRelation 认证自评关联
     * @return 认证自评关联集合
     */
    List<AutSaRelation> selectAutSaRelationListByCondition(AutSaRelation autSaRelation);

    /**
     * 获取解析后的周期时间
     *
     * @param applyNo   医疗机构编码
     * @param autCode   自评编码
     * @param curStatus 当前状态
     * @return 周期时间
     */
    List<String> getCycleLists(String applyNo, String autCode, String curStatus) ;

    boolean invalidAutSaAudByCycle(String applyNo, AutSaRelation autSaRelation) ;

    /**
     * 更新认证自评关联
     *
     * @param autSaRelation 认证自评关联
     * @return 结果
     */
    int updateAutSaRelation(AutSaRelation autSaRelation);

    /**
     * 删除认证自评关联信息（根据医院No和有效状态）
     *
     * @param hospitalApplyNo 医院No
     * @return 结果
     */
    int deleteAutSaRelationByHospitalApplyNo(String hospitalApplyNo);

    /**
     * 查询认证自评关联列表
     *
     * @param autSaRelation 认证自评关联信息查询条件
     * @return 认证自评关联信息列表
     */
    List<AutSaRelationList> selectAllAutSaRelationList(AutSaRelation autSaRelation);

    /**
     * 根据医院id，查询认证自评关联列表对应节点状态
     *
     * @param HospitalApplyNo 医院id
     * @return 认证自评关联信息列表
     */
    String selectAllAutSaRelationByHospitalApplyNo(String HospitalApplyNo);

    /**
     * 通过autStatus查询applyNo列表
     * @param autStatusList 入参
     * @return 出参
     */
    List<TraineesReviewRecVO> selectAutSaRelationByAutStatus(List<String> autStatusList);

    /**
     * @param autSaRelation
     * @return
     */
    List<AutSaRelation> selectAutSaRelationListByQueryRes(AutSaRelationQueryRes autSaRelation);

    List<AutSaRelation> selectAutSaRelationByTraineesAssessorId(Long traineesAssessorId);
}
