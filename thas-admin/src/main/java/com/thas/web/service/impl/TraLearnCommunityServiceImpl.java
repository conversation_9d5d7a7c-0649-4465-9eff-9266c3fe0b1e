package com.thas.web.service.impl;

import com.thas.common.constant.Constants;
import com.thas.common.utils.SecurityUtils;
import com.thas.web.domain.TraLearnCommunity;
import com.thas.web.domain.TraLearnCommunityLike;
import com.thas.web.domain.vo.TraLearnCommunityVO;
import com.thas.web.mapper.TraLearnCommunityLikeMapper;
import com.thas.web.mapper.TraLearnCommunityMapper;
import com.thas.web.service.ITraLearnCommunityService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 学习社区文章详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-02-15
 */
@Service
public class TraLearnCommunityServiceImpl implements ITraLearnCommunityService {
    @Autowired
    private TraLearnCommunityMapper traLearnCommunityMapper;

    @Autowired
    private TraLearnCommunityLikeMapper traLearnCommunityLikeMapper;

    @Autowired
    private UploadFileInfoServiceImpl uploadFileInfoService;
    @Autowired
    private CommonServiceImpl commonService;

    /**
     * 查询学习社区文章详情
     *
     * @param id 学习社区文章详情主键
     * @return 学习社区文章详情
     */
    @Override
    public TraLearnCommunityVO selectTraLearnCommunityById(Long id) {
        TraLearnCommunityVO data = traLearnCommunityMapper.selectTraLearnCommunityById(id);
        if (Objects.nonNull(data)) {
            data.setLikeNum(traLearnCommunityLikeMapper.getCountByCommunityId(id));
            data.setReplyNum(traLearnCommunityMapper.getCountByCommunityId(id));
            List<String> likeList = traLearnCommunityLikeMapper.selectTraLearnCommunityLikeList(new TraLearnCommunityLike() {{
                setCommunityId(id);
            }}).stream().map(TraLearnCommunityLike::getCreator).collect(Collectors.toList());
            data.setLikePersons(likeList);
            data.setDoLike(likeList.contains(SecurityUtils.getNickName()) ? Constants.INT_ONE : Constants.INT_ZERO);
            data.setList(traLearnCommunityMapper.selectTraLearnCommunityList(new TraLearnCommunity() {{
                setPid(id);
            }}));
        }
        return data;
    }

    /**
     * 查询学习社区文章详情列表
     *
     * @param traLearnCommunity 学习社区文章详情
     * @return 学习社区文章详情
     */
    @Override
    public List<TraLearnCommunityVO> selectTraLearnCommunityList(TraLearnCommunity traLearnCommunity) {
        traLearnCommunity.setPid(0L);
        List<TraLearnCommunityVO> data = traLearnCommunityMapper.selectTraLearnCommunityList(traLearnCommunity);
        List<Long> likeList = traLearnCommunityLikeMapper.selectTraLearnCommunityLikeList(new TraLearnCommunityLike() {{
            setCreator(SecurityUtils.getNickName());
        }}).stream().map(TraLearnCommunityLike::getCommunityId).collect(Collectors.toList());
        // 列表返回的需要添加点赞数,评论数,当前用户是否点赞
        if (CollectionUtils.isNotEmpty(data)) {
            data.forEach(traLearnCommunityVO -> {
                Long communityId = traLearnCommunityVO.getId();
                traLearnCommunityVO.setLikeNum(traLearnCommunityLikeMapper.getCountByCommunityId(communityId));
                traLearnCommunityVO.setReplyNum(traLearnCommunityMapper.getCountByCommunityId(communityId));
                traLearnCommunityVO.setDoLike(likeList.contains(communityId) ? Constants.INT_ONE : Constants.INT_ZERO);
                if (StringUtils.isNotEmpty(traLearnCommunityVO.getCover())) {
                    traLearnCommunityVO.setCoverUrl(commonService.fileInfoDtoToVo(uploadFileInfoService.getUploadFileInfoByIds(traLearnCommunityVO.getCover())));
                }
                traLearnCommunityVO.setList(traLearnCommunityMapper.selectTraLearnCommunityList(new TraLearnCommunity(){{
                    setPid(communityId);
                }}));
            });
        }
        return data;
    }

    /**
     * 新增学习社区文章详情
     *
     * @param traLearnCommunity 学习社区文章详情
     * @return 结果
     */
    @Override
    public int insertOrUpdateTraLearnCommunity(TraLearnCommunity traLearnCommunity) {
        int id;
        traLearnCommunity.setUpdateBy(SecurityUtils.getNickName());
        // 保存学习社区文章详情并拿到主键id
        if (Objects.isNull(traLearnCommunity.getId())) {
            traLearnCommunity.setCreateBy(SecurityUtils.getNickName());
            traLearnCommunity.setCreateId(SecurityUtils.getUsername());
            id = traLearnCommunityMapper.insertTraLearnCommunity(traLearnCommunity);
        } else {
            id = Math.toIntExact(traLearnCommunity.getId());
            traLearnCommunityMapper.updateTraLearnCommunity(traLearnCommunity);
        }
        return id;
    }

    /**
     * 批量删除学习社区文章详情
     *
     * @param ids 需要删除的学习社区文章详情主键
     * @return 结果
     */
    @Override
    public int deleteTraLearnCommunityByIds(Long[] ids) {
        return traLearnCommunityMapper.deleteTraLearnCommunityByIds(ids);
    }

    @Override
    public int doLike(Long id, Integer type) {
        TraLearnCommunityLike traLearnCommunityLike = new TraLearnCommunityLike();
        traLearnCommunityLike.setCommunityId(id);
        traLearnCommunityLike.setCreator(SecurityUtils.getNickName());
        int result;
        if (Constants.INT_ZERO == type) {
            result = traLearnCommunityLikeMapper.deleteTraLearnCommunityLike(traLearnCommunityLike);
        } else {
            result = traLearnCommunityLikeMapper.insertTraLearnCommunityLike(traLearnCommunityLike);
        }
        return result;
    }
}
