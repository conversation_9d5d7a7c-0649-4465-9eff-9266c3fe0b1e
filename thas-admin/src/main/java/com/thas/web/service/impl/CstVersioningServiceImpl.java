package com.thas.web.service.impl;

import com.thas.common.constant.Constants;
import com.thas.common.core.domain.entity.SysUser;
import com.thas.common.core.redis.RedisCache;
import com.thas.common.enums.AutSaAudRoleEnum;
import com.thas.common.enums.ServiceExceptionEnum;
import com.thas.common.exception.ServiceException;
import com.thas.common.utils.DateUtils;
import com.thas.common.utils.SecurityUtils;
import com.thas.common.utils.StringUtils;
import com.thas.system.mapper.SysUserMapper;
import com.thas.web.domain.AutSaAud;
import com.thas.web.domain.AutSaRelation;
import com.thas.web.domain.CstVersioning;
import com.thas.web.domain.HospitalPlannedDistribution;
import com.thas.web.mapper.*;
import com.thas.web.service.ICstVersioningService;
import com.thas.web.service.IHospitalPlannedDistributionService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 版本管理模板
Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-20
 */
@Transactional(rollbackFor = Exception.class)
@Service
public class CstVersioningServiceImpl implements ICstVersioningService
{
    @Autowired
    private CstVersioningMapper cstVersioningMapper;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private CstCertificationStandardsMapper cstCertificationStandardsMapper;
    @Autowired
    private RedisCache redisCache;

    @Autowired
    private IHospitalPlannedDistributionService iHospitalPlannedDistributionService;

    //cst_versioning aut_sa_aud aut_sa_relation hospital_pre_exam hospital_reviewer hospital_review_cycle hospital_planned_distribution cst_domain
    @Resource
    private AutSaAudMapper autSaAudMapper;
    @Resource
    private AutSaRelationMapper autSaRelationMapper;
    @Resource
    private HospitalPreExamMapper hospitalPreExamMapper;
    @Resource
    private HospitalReviewerMapper hospitalReviewerMapper;
    @Resource
    private HospitalReviewCycleMapper hospitalReviewCycleMapper;
    @Resource
    private CstDomainMapper cstDomainMapper;


    /**
     * 查询版本管理模板
     *
     * @param id 版本管理模板
     *           主键
     * @return 版本管理模板
     */
    @Override
    public CstVersioning selectCstVersioningById(Long id) {
        return cstVersioningMapper.selectCstVersioningById(id);
    }

    @Override
    public Long selectEffCstVersion() {
        return cstVersioningMapper.selectEffCstVersion();
    }

    /**
     * 查询版本管理模板
     * 列表
     *
     * @param cstVersioning 版本管理模板
     * @return 版本管理模板
     */
    @Override
    public List<CstVersioning> selectCstVersioningList(CstVersioning cstVersioning) {
        return cstVersioningMapper.selectCstVersioningList(cstVersioning);
    }

    /**
     * 新增版本管理模板
     *
     * @param cstVersioning 版本管理模板
     * @return 结果
     */
    @Override
    public int insertCstVersioning(CstVersioning cstVersioning) {
        cstVersioning.setCreateTime(DateUtils.getNowDate());
        return cstVersioningMapper.insertCstVersioning(cstVersioning);
    }

    /**
     * 修改版本管理模板
     *
     * @param cstVersioning 版本管理模板
     * @return 结果
     */
    @Override
    public int updateCstVersioning(CstVersioning cstVersioning) {
        cstVersioning.setUpdateTime(DateUtils.getNowDate());
        return cstVersioningMapper.updateCstVersioning(cstVersioning);
    }

    /**
     * 验证密码
     *
     * @param password 密码
     */
    public void verifyPwd(String password) {
        if (StringUtils.isEmpty(password)) {
            throw new ServiceException("密码不能为空！");
        }
        //验证密码是否正确
        Long userId = SecurityUtils.getUserId();
        SysUser sysUser = sysUserMapper.selectUserById(userId);
        if(sysUser.getRoleId() == null){
            throw new ServiceException(ServiceExceptionEnum.SYS_USER_ERROR_1000001);
        }
        //  校验角色 超管 + 普通管理员才可以操作
        if (!AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(sysUser.getRoleKey(), AutSaAudRoleEnum.ADMIN, AutSaAudRoleEnum.COMMON_ADMIN)) {
            throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000003);
        }
        if (!SecurityUtils.matchesPassword(password, sysUser.getPassword())) {
            throw new ServiceException("密码错误！");
        }
    }


    /**
     * 批量删除版本管理模板
     *
     * @param ids 需要删除的版本管理模板主键
     * @return 结果
     */
    @Override
    public int deleteCstVersioningByIds(Long[] ids) {
        return cstVersioningMapper.deleteCstVersioningByIds(ids);
    }

    /**
     * 删除版本管理模板信息
     *
     * @param id 版本管理模板主键
     * @return 结果
     */
    @Override
    public int deleteCstVersioningById(Long id) {
        return cstVersioningMapper.deleteCstVersioningById(id);
    }

    @Override
    public void deleteAndVerifyPwdById(CstVersioning cstVersioning) {
        // 校验删除密码
        verifyPwd(cstVersioning.getPassword());
        /**
         * sql改造
         * create temporary table temp_delete_cst_versioning (select t.aut_code,t.hospital_apply_no,t.aut_cs_id from aut_sa_relation t where t.aut_cs_id = (
         *                     select version_id from cst_versioning where id = #{id} and status != 1) and status = 1);
         *         update aut_sa_aud set status = 0 where aut_code in (select aut_code from temp_delete_cst_versioning) and status = 1;
         *         update aut_sa_relation set status = 0 where aut_code in (select aut_code from temp_delete_cst_versioning) and status = 1;
         *         update hospital_pre_exam set status =2 where apply_no in (select hospital_apply_no from temp_delete_cst_versioning) and status = 1;
         *         update hospital_reviewer set status =2 where apply_no in (select hospital_apply_no from temp_delete_cst_versioning) and status = 1;
         *         update hospital_review_cycle set status =2 where apply_no in (select hospital_apply_no from temp_delete_cst_versioning) and status = 1;
         *         update hospital_planned_distribution set status =2 where apply_no in (select hospital_apply_no from temp_delete_cst_versioning) and status = 1;
         *         update cst_domain set status =1 where version_id = (select DISTINCT aut_cs_id from temp_delete_cst_versioning) and status = 0;
         *         update cst_versioning set del_flag = 1 where id = #{id} and status != 1;
         *         drop table temp_delete_cst_versioning;
         *
         * */
        List<AutSaRelation> autSaRelations = autSaRelationMapper.selectAutSaRelationListByCstVersioningId(cstVersioning.getId());
        List<String> autCodeList = autSaRelations.stream().map(AutSaRelation::getAutCode).collect(Collectors.toList());
        List<String> applyNoList = autSaRelations.stream().map(AutSaRelation::getHospitalApplyNo).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(autCodeList)){
            //aut_sa_aud
            List<AutSaAud> autSaAudList =  autSaRelations.stream().map(o->{
                AutSaAud autSaAud = new AutSaAud();
                autSaAud.setStatus(Constants.INT_ZERO);
                autSaAud.setAutCode(o.getAutCode());
                return autSaAud;
            }).collect(Collectors.toList());
            autSaAudMapper.batchUpdateAutSaAud(autSaAudList);
            //aut_sa_relation
            autSaRelationMapper.updateAutSaRelationByAutCodeList(autCodeList);
            //hospital_pre_exam 改为0
            hospitalPreExamMapper.updateHospitalPreExamByApplyNoList(applyNoList);
            //hospital_reviewer
            hospitalReviewerMapper.updateHospitalReviewerByApplyNoList(applyNoList);
            //hospital_review_cycle
            hospitalReviewCycleMapper.updateHospitalReviewCycleByApplyNoList(applyNoList);
            //hospital_planned_distribution
            iHospitalPlannedDistributionService.updateHospitalPlannedDistributionByApplyNoList(applyNoList);
        }
        //cst_domain
        cstDomainMapper.updateCstDomainByVersionId(cstVersioning.getId().toString());
        //cst_versioning
        cstVersioningMapper.updateCstVersioningById(cstVersioning.getId());
//        // 删除版本时,需要失效自评信息
//        return this.deleteCstVersioningById(cstVersioning.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateStatusStartById(CstVersioning cstVersioning) {
        //根据参数版本id和doMainId=0条件，0为领域未选择条款，查询有数据就返回异常信息结束
        int doMainIdCount = cstCertificationStandardsMapper.selectCountDoMianIdByVersionId(cstVersioning.getVersionId());
        if (doMainIdCount > 0) {
            throw new ServiceException("未分配领域，请在领域管理页面进行分配！") ;
        }
        //验证数据状态和删除标志,有数据就通过
        int versioningCount = cstCertificationStandardsMapper.selectCountVersioningByVersionId(cstVersioning.getVersionId());
        if (versioningCount == 0) {
            throw new ServiceException("版本已启用或已删除，请确认重新选择！") ;
        }

        //验证密码
        verifyPwd(cstVersioning.getPassword());
        //如果是启动
        if (StringUtils.isNotEmpty(cstVersioning.getStatus())) {
            //将其他版本改为未启用
            cstVersioningMapper.updateStatusDisableByOtherId(cstVersioning.getId());
            //将对应版本的标准改为启用
            cstCertificationStandardsMapper.updateStatusStartByVersionId(cstVersioning.getVersionId());
            //将其他版本的标准改为未启用
            cstCertificationStandardsMapper.updateStatusDisableByOtherVersionId(cstVersioning.getVersionId());
        }
        cstVersioning.setUpdateTime(DateUtils.getNowDate());
        return cstVersioningMapper.updateCstVersioning(cstVersioning);
    }

    @Override
    public Long selectVersionId(String applyNo) {
        HospitalPlannedDistribution hospitalPlannedDistribution
                = iHospitalPlannedDistributionService.selectHospitalPlannedDistributionByApplyNo(applyNo);
        Long versionId = null;
        if (Objects.nonNull(hospitalPlannedDistribution)) {
            versionId = hospitalPlannedDistribution.getVersionId();
        }
        if (Objects.isNull(versionId)) {
            return selectEffCstVersion();
        }
        return versionId;
    }
}
