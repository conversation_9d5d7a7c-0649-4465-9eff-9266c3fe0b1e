package com.thas.web.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.thas.common.constant.Constants;
import com.thas.common.exception.ServiceException;
import com.thas.common.utils.DateUtils;
import com.thas.common.utils.PageUtils;
import com.thas.web.domain.CstOfflineTrainingManagement;
import com.thas.web.mapper.CstOfflineTrainingManagementMapper;
import com.thas.web.service.ICstOfflineTrainingManagementService;
import com.thas.web.service.ICstReviewerOfflineTrainingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 线下培训管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-25
 */
@Service
public class CstOfflineTrainingManagementServiceImpl implements ICstOfflineTrainingManagementService {

    @Autowired
    private CstOfflineTrainingManagementMapper cstOfflineTrainingManagementMapper;

    @Autowired
    private ICstReviewerOfflineTrainingService iCstReviewerOfflineTrainingService;

    @Autowired
    ICstOfflineTrainingManagementService managementService;


    /**
     * 查询线下培训管理
     *
     * @param id 线下培训管理主键
     * @return 线下培训管理
     */
    @Override
    public CstOfflineTrainingManagement selectCstOfflineTrainingManagementById(Long id) {
        return cstOfflineTrainingManagementMapper.selectCstOfflineTrainingManagementById(id);
    }

    /**
     * 查询线下培训管理列表
     *
     * @param cstOfflineTrainingManagement 线下培训管理
     * @return 线下培训管理
     */
    @Override
    public List<CstOfflineTrainingManagement> selectCstOfflineTrainingManagementList(CstOfflineTrainingManagement cstOfflineTrainingManagement) {
        // 将已过期的状态更改
        // updateTrainingStatusByNowTime(); // 根据时间修改过期时间方法
        PageUtils.startPage();
        return cstOfflineTrainingManagementMapper.selectCstOfflineTrainingManagementList(cstOfflineTrainingManagement);
    }

    /**
     * 新增线下培训管理
     *
     * @param cstOfflineTrainingManagement 线下培训管理
     * @return 结果
     */
    @Override
    public int insertCstOfflineTrainingManagement(CstOfflineTrainingManagement cstOfflineTrainingManagement) {
        cstOfflineTrainingManagement.setCreateTime(DateUtils.getNowDate());
        return cstOfflineTrainingManagementMapper.insertCstOfflineTrainingManagement(cstOfflineTrainingManagement);
    }

    /**
     * 修改线下培训管理
     *
     * @param cstOfflineTrainingManagement 线下培训管理
     * @return 结果
     */
    @Override
    public int updateCstOfflineTrainingManagement(CstOfflineTrainingManagement cstOfflineTrainingManagement) {
        cstOfflineTrainingManagement.setUpdateTime(DateUtils.getNowDate());
        // 如果是修改当前培训内容为'未发布'需要验证是否有评审员进行学习 或者执行删除操作需要
        if (Constants.HospitalConstants.STR_NUM_2.equals(cstOfflineTrainingManagement.getTrainingStatus())
                || Constants.HospitalConstants.NUM_0.equals(cstOfflineTrainingManagement.getStatus())) {
            int count = iCstReviewerOfflineTrainingService.selectCountByTrainingId(String.valueOf(cstOfflineTrainingManagement.getId()));
            if (count > 0) {
                throw new ServiceException("当前培训已有人学习，无法进行当前操作!");
            }
        }
        return cstOfflineTrainingManagementMapper.updateCstOfflineTrainingManagement(cstOfflineTrainingManagement);
    }

    /**
     * 批量删除线下培训管理
     *
     * @param ids 需要删除的线下培训管理主键
     * @return 结果
     */
    @Override
    public int deleteCstOfflineTrainingManagementByIds(Long[] ids) {
        return cstOfflineTrainingManagementMapper.deleteCstOfflineTrainingManagementByIds(ids);
    }

    /**
     * 删除线下培训管理信息
     *
     * @param id 线下培训管理主键
     * @return 结果
     */
    @Override
    public int deleteCstOfflineTrainingManagementById(Long id) {

        return cstOfflineTrainingManagementMapper.deleteCstOfflineTrainingManagementById(id);
    }

    /**
     * 修改线下培训状态
     *
     * @param id
     * @param status
     * @return
     */
    @Override
    public int updateTrainingStatus(Long id, String status) {
        CstOfflineTrainingManagement cstOfflineTrainingManagement = cstOfflineTrainingManagementMapper.getByManagementId(id);
        cstOfflineTrainingManagement.setTrainingStatus(status);
        cstOfflineTrainingManagement.setId(id);
        return cstOfflineTrainingManagementMapper.updateStatus(cstOfflineTrainingManagement);
    }

    @Override
    public List<CstOfflineTrainingManagement> selectCstOfflineTrainingManagementByAccountId(String accountId) {
        return cstOfflineTrainingManagementMapper.selectCstOfflineTrainingManagementByAccountId(accountId);
    }

    @Override
    public void updateTrainingStatusByNowTime() {
        // 查询已经过期得数据id
        List<Long> ids = cstOfflineTrainingManagementMapper.selectIdsByTime();
        if (CollectionUtil.isNotEmpty(ids)) {
            cstOfflineTrainingManagementMapper.updateTrainingStatusByNowTime(ids);
        }
    }
}
