package com.thas.web.service;


import com.thas.common.core.domain.AjaxResult;
import com.thas.web.domain.CstDomainVO;
import com.thas.web.domain.HospitalDomainGroup;
import com.thas.web.domain.vo.DomainGroupNode;

import java.util.List;

/**
 * 领域对应分组Service接口
 *
 * <AUTHOR>
 * @date 2022-04-22
 */
public interface IHospitalDomainGroupService {


    void insertHospitalDomainGroup(CstDomainVO cstDomainVO);

    void updateHospitalDomainGroup(CstDomainVO cstDomainVO);

    void genDelGroupIds(List<DomainGroupNode> domainGroupNodeList, List<String> delGroupIds);

    List<DomainGroupNode> selectDomainGroup(String domainIds, String versionId);

    void assembleGroupInfo(List<DomainGroupNode> domainGroupNodes);
}
