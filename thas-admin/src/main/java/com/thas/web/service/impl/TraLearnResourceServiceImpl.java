package com.thas.web.service.impl;

import com.alibaba.fastjson.JSON;
import com.thas.common.constant.Constants;
import com.thas.common.exception.ServiceException;
import com.thas.common.utils.SecurityUtils;
import com.thas.common.utils.bean.BeanUtils;
import com.thas.web.domain.FileInfoDTO;
import com.thas.web.domain.TraLearnResource;
import com.thas.web.domain.TraMessage;
import com.thas.web.domain.TraMessageAccount;
import com.thas.web.domain.vo.FileInfoVO;
import com.thas.web.domain.vo.TraLearnResourceVO;
import com.thas.web.mapper.TraAnswerSheetMapper;
import com.thas.web.mapper.TraExamPaperMapper;
import com.thas.web.mapper.TraLearnResourceMapper;
import com.thas.web.mapper.TraMessageAccountMapper;
import com.thas.web.service.CommonService;
import com.thas.web.service.ITraLearnResourceService;
import com.thas.web.service.ITraMessageService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 学习资源Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-20
 */
@Service
public class TraLearnResourceServiceImpl implements ITraLearnResourceService {
    @Autowired
    private TraLearnResourceMapper traLearnResourceMapper;

    @Autowired
    private ITraMessageService traMessageService;

    @Autowired
    private UploadFileInfoServiceImpl uploadFileInfoService;

    @Autowired
    private TraMessageAccountMapper messageAccountMapper;

    @Autowired
    private TraAnswerSheetMapper answerSheetMapper;

    @Autowired
    private TraExamPaperMapper examPaperMapper;

    @Autowired
    private CommonService commonService;

    /**
     * 查询学习资源
     *
     * @param id 学习资源主键
     * @return 学习资源
     */
    @Override
    public TraLearnResourceVO selectTraLearnResourceById(Long id) {
        final String username = SecurityUtils.getUsername();
        TraLearnResource traLearnResource = traLearnResourceMapper.selectTraLearnResourceById(id);
        // 根据学习资源查询留言
        List<TraMessage> traMessageList = traMessageService.selectTraMessageList(new TraMessage() {{
            setLearnResourceId(traLearnResource.getId());
        }});
        TraLearnResourceVO traLearnResourceVO = new TraLearnResourceVO();
        BeanUtils.copyBeanProp(traLearnResourceVO, traLearnResource);
        // 查询当前操作员点赞的留言并返回
        traMessageList.forEach(traMessage -> {
            TraMessageAccount byAccount = messageAccountMapper.getByAccount(new TraMessageAccount() {{
                setMessageId(traMessage.getId());
                setAccount(username);
            }});
            traMessage.setHasLike(Objects.isNull(byAccount) ? Constants.INT_ZERO : Constants.INT_ONE);
        });
        traLearnResourceVO.setTraMessageList(traMessageList);
        // 查询资源和资源封面
        if (StringUtils.isNotEmpty(traLearnResourceVO.getResourceAddress())) {
            traLearnResourceVO.setResourceAddress(JSON.toJSONString(commonService.fileInfoDtoToVo(uploadFileInfoService.getUploadFileInfoByIds(traLearnResourceVO.getResourceAddress()))));
        }
        if (StringUtils.isNotEmpty(traLearnResourceVO.getResourceCoverAddress())) {
            traLearnResourceVO.setResourceCoverAddress(JSON.toJSONString(commonService.fileInfoDtoToVo(uploadFileInfoService.getUploadFileInfoByIds(traLearnResourceVO.getResourceCoverAddress()))));
        }
        // 添加留言回复数
        traLearnResourceVO.setMessageNum((long) traMessageList.size());
        // 添加答题人数
        traLearnResourceVO.setRespondentsNum(answerSheetMapper.selectRespondentsNum(id));
        // 添加当前用户历史最高分
        traLearnResourceVO.setHighestScoreHistory(answerSheetMapper.selectHighestScoreHistory(id, username));
        // 添加试卷id
        traLearnResourceVO.setExamPaperId(examPaperMapper.getIdByLearnSourceId(id));
        // 当前用户是否已答题
        traLearnResourceVO.setHasAnswer(answerSheetMapper.selectCountAnswerCurrent(id, username));
        return traLearnResourceVO;
    }

    /**
     * 查询学习资源列表
     *
     * @param traLearnResource 学习资源
     * @return 学习资源
     */
    @Override
    public List<TraLearnResource> selectTraLearnResourceList(TraLearnResource traLearnResource) {
        List<TraLearnResource> learnResourceList = traLearnResourceMapper.selectTraLearnResourceList(traLearnResource);
        learnResourceList.forEach(lr -> {
            if (StringUtils.isNotEmpty(lr.getResourceAddress())) {
                List<FileInfoDTO> fileInfoDTOList = uploadFileInfoService.getUploadFileInfoByIds(lr.getResourceAddress());
                List<FileInfoVO> fileInfoVOList = commonService.fileInfoDtoToVo(fileInfoDTOList);
                List<String> pathList = fileInfoVOList.stream().map(FileInfoVO::getUrl).collect(Collectors.toList());
                lr.setResourceAddress(pathList.toString());
            }
            if (StringUtils.isNotEmpty(lr.getResourceCoverAddress())) {
                FileInfoDTO fileInfoDTO = uploadFileInfoService.getUploadFileInfoById(Integer.valueOf(lr.getResourceCoverAddress()));
                lr.setResourceCoverAddress(commonService.fileInfoDtoToVo(fileInfoDTO).getUrl());
            }
            // 添加留言回复数
            lr.setMessageNum(traMessageService.selectTraMessageCount(new TraMessage() {{
                setLearnResourceId(lr.getId());
            }}));
        });
        return learnResourceList;
    }

    /**
     * 新增学习资源
     *
     * @param traLearnResource 学习资源
     * @return 结果
     */
    @Override
    public int insertTraLearnResource(TraLearnResource traLearnResource) {

        //相同标题时，提示”资源标题已存在，请使用其他资源标题“
        if(StringUtils.isNotBlank(traLearnResource.getTitle())){
            TraLearnResource qryTraLearnResource = new TraLearnResource();
            qryTraLearnResource.setTitle(traLearnResource.getTitle());
            qryTraLearnResource.setStatus(Constants.INT_ONE);
            List<TraLearnResource> traLearnResources = traLearnResourceMapper.selectTraLearnResourceList(qryTraLearnResource);
            if(traLearnResources.size() > 0){
                throw new ServiceException("资源标题已存在，请使用其他资源标题");
            }
        }
        return traLearnResourceMapper.insertTraLearnResource(traLearnResource);
    }

    /**
     * 修改学习资源
     *
     * @param traLearnResource 学习资源
     * @return 结果
     */
    @Override
    public int updateTraLearnResource(TraLearnResource traLearnResource) {
        return traLearnResourceMapper.updateTraLearnResource(traLearnResource);
    }

    /**
     * 批量删除学习资源
     *
     * @param ids 需要删除的学习资源主键
     * @return 结果
     */
    @Override
    public int deleteTraLearnResourceByIds(Long[] ids) {
        return traLearnResourceMapper.deleteTraLearnResourceByIds(ids);
    }

    /**
     * 删除学习资源信息
     *
     * @param id 学习资源主键
     * @return 结果
     */
    @Override
    public int deleteTraLearnResourceById(Long id) {
        return traLearnResourceMapper.deleteTraLearnResourceById(id);
    }

    @Override
    public int learnNumPlus(Long id) {
        return traLearnResourceMapper.learnNumPlus(id);
    }
}
