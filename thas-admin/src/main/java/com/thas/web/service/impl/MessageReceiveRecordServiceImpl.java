package com.thas.web.service.impl;

import java.util.List;
import com.thas.common.utils.DateUtils;
import com.thas.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.thas.web.mapper.MessageReceiveRecordMapper;
import com.thas.web.domain.MessageReceiveRecord;
import com.thas.web.service.IMessageReceiveRecordService;

/**
 * 消息接收记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-25
 */
@Service
public class MessageReceiveRecordServiceImpl implements IMessageReceiveRecordService
{
    @Autowired
    private MessageReceiveRecordMapper messageReceiveRecordMapper;

    /**
     * 查询消息接收记录
     *
     * @param id 消息接收记录主键
     * @return 消息接收记录
     */
    @Override
    public MessageReceiveRecord selectMessageReceiveRecordById(Long id)
    {
        return messageReceiveRecordMapper.selectMessageReceiveRecordById(id);
    }

    /**
     * 查询消息接收记录列表
     *
     * @param messageReceiveRecord 消息接收记录
     * @return 消息接收记录
     */
    @Override
    public List<MessageReceiveRecord> selectMessageReceiveRecordList(MessageReceiveRecord messageReceiveRecord)
    {
        Long userId = SecurityUtils.getUserId();
        messageReceiveRecord.setUserId(userId);
        return messageReceiveRecordMapper.selectMessageReceiveRecordList(messageReceiveRecord);
    }

    /**
     * 新增消息接收记录
     *
     * @param messageReceiveRecord 消息接收记录
     * @return 结果
     */
    @Override
    public int insertMessageReceiveRecord(MessageReceiveRecord messageReceiveRecord)
    {
        messageReceiveRecord.setCreateTime(DateUtils.getNowDate());
        return messageReceiveRecordMapper.insertMessageReceiveRecord(messageReceiveRecord);
    }

    /**
     * 修改消息接收记录
     *
     * @param messageReceiveRecord 消息接收记录
     * @return 结果
     */
    @Override
    public int updateMessageReceiveRecord(MessageReceiveRecord messageReceiveRecord)
    {
        messageReceiveRecord.setUpdateTime(DateUtils.getNowDate());
        return messageReceiveRecordMapper.updateMessageReceiveRecord(messageReceiveRecord);
    }

    /**
     * 批量删除消息接收记录
     *
     * @param ids 需要删除的消息接收记录主键
     * @return 结果
     */
    @Override
    public int deleteMessageReceiveRecordByIds(Long[] ids)
    {
        return messageReceiveRecordMapper.deleteMessageReceiveRecordByIds(ids);
    }

    /**
     * 删除消息接收记录信息
     *
     * @param id 消息接收记录主键
     * @return 结果
     */
    @Override
    public int deleteMessageReceiveRecordById(Long id)
    {
        return messageReceiveRecordMapper.deleteMessageReceiveRecordById(id);
    }
}
