package com.thas.web.service;


import com.thas.web.domain.TraLearnCommunity;
import com.thas.web.domain.vo.TraLearnCommunityVO;

import java.util.List;

/**
 * 学习社区文章详情Service接口
 *
 * <AUTHOR>
 * @date 2022-02-15
 */
public interface ITraLearnCommunityService {
    /**
     * 查询学习社区文章详情
     *
     * @param id 学习社区文章详情主键
     * @return 学习社区文章详情
     */
    TraLearnCommunityVO selectTraLearnCommunityById(Long id);

    /**
     * 查询学习社区文章详情列表
     *
     * @param traLearnCommunity 学习社区文章详情
     * @return 学习社区文章详情集合
     */
    List<TraLearnCommunityVO> selectTraLearnCommunityList(TraLearnCommunity traLearnCommunity);

    /**
     * 新增学习社区文章详情
     *
     * @param traLearnCommunity 学习社区文章详情
     * @return 结果
     */
    int insertOrUpdateTraLearnCommunity(TraLearnCommunity traLearnCommunity);

    /**
     * 批量删除学习社区文章详情
     *
     * @param ids 需要删除的学习社区文章详情主键集合
     * @return 结果
     */
    int deleteTraLearnCommunityByIds(Long[] ids);

    /**
     * 学习社区文章点赞
     *
     * @param id   文章id
     * @param type 操作类型
     * @return 结果
     */
    int doLike(Long id, Integer type);
}
