package com.thas.web.service.impl;

import cn.hutool.core.util.ZipUtil;
import com.alibaba.fastjson.JSON;
import com.thas.common.core.domain.CstEvaluationCriterionExcel;
import com.thas.common.core.redis.RedisCache;
import com.thas.common.utils.DateUtils;
import com.thas.web.domain.CstCertificationStandards;
import com.thas.web.domain.CstEvaluationCriterion;
import com.thas.web.domain.vo.ChapterVo;
import com.thas.web.mapper.CstEvaluationCriterionMapper;
import com.thas.web.service.ICstCertificationStandardsService;
import com.thas.web.service.ICstEvaluationCriterionService;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.thas.common.constant.Constants.STANDARDS_DETAIL;

/**
 * 评估标准模板Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-21
 */
@Slf4j
@Service
public class CstEvaluationCriterionServiceImpl implements ICstEvaluationCriterionService
{
    @Autowired
    private CstEvaluationCriterionMapper cstEvaluationCriterionMapper;

    @Resource
    private  ICstCertificationStandardsService cstCertificationStandardsService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 查询评估标准模板
     *
     * @param id 评估标准模板主键
     * @return 评估标准模板
     */
    @Override
    public CstEvaluationCriterion selectCstEvaluationCriterionById(Long id)
    {
        return cstEvaluationCriterionMapper.selectCstEvaluationCriterionById(id);
    }

    /**
     * 查询评估标准模板列表
     *
     * @param cstEvaluationCriterion 评估标准模板
     * @return 评估标准模板
     */
    @Override
    public List<CstEvaluationCriterion> selectCstEvaluationCriterionList(CstEvaluationCriterion cstEvaluationCriterion)
    {
        return cstEvaluationCriterionMapper.selectCstEvaluationCriterionList(cstEvaluationCriterion);
    }

    /**
     * 新增评估标准模板
     *
     * @param cstEvaluationCriterion 评估标准模板
     * @return 结果
     */
    @Override
    public int insertCstEvaluationCriterion(CstEvaluationCriterion cstEvaluationCriterion)
    {
        cstEvaluationCriterion.setCreateTime(DateUtils.getNowDate());
        return cstEvaluationCriterionMapper.insertCstEvaluationCriterion(cstEvaluationCriterion);
    }

    /**
     * 修改评估标准模板
     *
     * @param cstEvaluationCriterion 评估标准模板
     * @return 结果
     */
    @Override
    public int updateCstEvaluationCriterion(CstEvaluationCriterion cstEvaluationCriterion)
    {
        cstEvaluationCriterion.setUpdateTime(DateUtils.getNowDate());
        return cstEvaluationCriterionMapper.updateCstEvaluationCriterion(cstEvaluationCriterion);
    }

    /**
     * 批量删除评估标准模板
     *
     * @param ids 需要删除的评估标准模板主键
     * @return 结果
     */
    @Override
    public int deleteCstEvaluationCriterionByIds(Long[] ids)
    {
        return cstEvaluationCriterionMapper.deleteCstEvaluationCriterionByIds(ids);
    }

    /**
     * 删除评估标准模板信息
     *
     * @param id 评估标准模板主键
     * @return 结果
     */
    @Override
    public int deleteCstEvaluationCriterionById(Long id)
    {
        return cstEvaluationCriterionMapper.deleteCstEvaluationCriterionById(id);
    }

    @Override
    @Transactional
    public int updateListByVersionIdAndClauseId(List<CstEvaluationCriterion> cstEvaluationCriteriaList) {
        //先删除，后插入
        if (ObjectUtils.isNotEmpty(cstEvaluationCriteriaList)){
            CstEvaluationCriterion cstEvaluationCriterion = cstEvaluationCriteriaList.get(0);
            Long versionId = cstEvaluationCriterion.getVersionId();
            Long certificationStandardsId = cstEvaluationCriterion.getCertificationStandardsId();
            cstEvaluationCriterionMapper.deleteByVersionIdAndClauseId(versionId,certificationStandardsId);
            int result = cstEvaluationCriterionMapper.insertListByVersionIdAndClauseId(cstEvaluationCriteriaList) > 0 ? 1 : 0;
            //款项详情-评审标准-修改接口，这部分暂不需要存缓存20220301
            //修改缓存的数据
////            List<ChapterVo> chapterVosList = redisCache.getCacheObject(STANDARDS_DETAIL + versionId);
//            List<ChapterVo> chapterVosList = cstCertificationStandardsService.getChapterVoListFromRedis(versionId);
//            if (ObjectUtils.isNotEmpty(chapterVosList)){
//                chapterVosList.forEach(chapterVo -> {
//                    chapterVo.getSectionVoList().forEach(sectionVo -> {
//                        sectionVo.getArticleVoList().forEach(articleVo -> {
//                             articleVo.getClauseVoList().forEach(clauseVo -> {
//                               if (clauseVo.getClauseId().equals(certificationStandardsId)){
//                                   clauseVo.setCstEvaluationCriterionList(cstEvaluationCriteriaList);
//                               }
//                            });
//                        });
//                    });
//                });
//                redisCache.setCacheObject(STANDARDS_DETAIL + versionId,  ZipUtil.gzip(JSON.toJSONBytes(chapterVosList)), 60 , TimeUnit.MINUTES);
//            }
////            redisCache.setCacheObject(STANDARDS_DETAIL+versionId,chapterVosList);
            return result;
        } else {
            return 0;
        }
    }

    /**
     * 查询单条认证标准对应的评价列表信息
     * @param cstEvaluationCriterionExcel 查询条件
     * @return  评价列表信息
     */
    @Override
    public Map<Long, List<CstEvaluationCriterion>> queryCriterionList(CstEvaluationCriterionExcel cstEvaluationCriterionExcel) {
        // 版本号
        String versionId = cstEvaluationCriterionExcel.getVersionId();
        // 认证标准条id
        String articleId = cstEvaluationCriterionExcel.getId();
        // 根据条id获取到对应的款id
        CstCertificationStandards cstCertificationStandards = new CstCertificationStandards();
        cstCertificationStandards.setVersionId(Long.valueOf(versionId));
        cstCertificationStandards.setArticleId(Long.valueOf(articleId));
        cstCertificationStandards.setStatus("1");
        List<CstCertificationStandards> csts = cstCertificationStandardsService.selectCstCertificationStandardsList(cstCertificationStandards);
        log.info("根据版本号：{}，条id：{}，查询到的条款数：{}", versionId, articleId, (null != csts) ? csts.size() : null);
        if (CollectionUtils.isNotEmpty(csts)) {
            List<String> clauseIds = csts.stream().map(k -> String.valueOf(k.getClauseId())).collect(Collectors.toList());
            List<CstEvaluationCriterion> resultList =  cstEvaluationCriterionMapper.selectByVersionIdAndClauseId(versionId, String.join(",", clauseIds));
            return resultList.stream().collect(Collectors.groupingBy(CstEvaluationCriterion::getCertificationStandardsId));
        }
        return new HashMap();
    }

}
