package com.thas.web.service.process.impl;

import com.alibaba.fastjson.JSON;
import com.thas.common.constant.Constants;
import com.thas.common.enums.AutSaAudBusinessCodeEnum;
import com.thas.common.enums.AutSaAudResultEnum;
import com.thas.common.enums.AutSaAudSubmitTypeEnum;
import com.thas.common.enums.ServiceExceptionEnum;
import com.thas.common.enums.StatusProcessEnum;
import com.thas.common.exception.ServiceException;
import com.thas.web.domain.AutSaAud;
import com.thas.web.domain.AutSaAudList;
import com.thas.web.domain.AutSaAudQueryDTO;
import com.thas.web.domain.AutSaAudSaveDTO;
import com.thas.web.domain.vo.AutSaAudDetailVO;
import com.thas.web.dto.DelFileShareRequest;
import com.thas.web.mapper.AutSaAudMapper;
import com.thas.web.mapper.FileShareMapper;
import com.thas.web.service.IAutSaAudBusinessDataService;
import com.thas.web.service.ICstCertificationStandardsService;
import com.thas.web.service.process.BaseProcessService;
import com.thas.web.service.process.CommonProcessService;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 审查员审查流程服务 Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-04-01
 */
@Component("examiner01ProcessService")
@Slf4j
public class Examiner01ProcessServiceImpl implements BaseProcessService {

    @Resource
    private AutSaAudMapper autSaAudMapper;

    @Resource
    private CommonProcessService commonProcessService;

    @Resource
    private IAutSaAudBusinessDataService autSaAudBusinessDataService;

    @Resource
    private ICstCertificationStandardsService cstCertificationStandardsService;

    @Override
    public void process(AutSaAudSaveDTO req) {
        long startTime = System.currentTimeMillis();
        log.info("examiner01ProcessService.process ------ 开始");
        List<AutSaAud> audFirstTrialList = autSaAudMapper.selectAutSaAudListByAutCodeAndTypes(req.getAutCode(), AutSaAudSubmitTypeEnum.FR_CLAUSE.getSubmitType());
        int totalCount = MapUtils.getInteger(cstCertificationStandardsService.selectCountByVersionId(req.getAutSaRelation().getAutCsId()), "clauseCount");

        if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(req.getSubmitType(), AutSaAudSubmitTypeEnum.FR_CLAUSE)) {
            // 初审初查
            commonProcessService.checkClause(req);
            commonProcessService.updateClauseAutSaAud(req.getAutSaAudLists(), audFirstTrialList, totalCount);
        } else if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(req.getSubmitType(), AutSaAudSubmitTypeEnum.FR_CLAUSE_CONFIRM)) {
            // 初审初查确认
            this.processFrClauseConfirm(req, audFirstTrialList, totalCount);
        }
        log.info("examiner01ProcessService.process ------ 结束 耗时：{}", System.currentTimeMillis() - startTime);
    }

    @Autowired
    private FileShareMapper fileShareMapper;

    /**
     * 初审初查确认
     *
     * @param req 流程参数
     */
    private void processFrClauseConfirm(AutSaAudSaveDTO req, List<AutSaAud> audFirstTrialList, int totalCount) {
        // 处理总结提交
        if (audFirstTrialList.size() != totalCount) {
            log.error("请先完成初审初查，在进行初审初查确认");
            throw new ServiceException(ServiceExceptionEnum.AUT_SA_AUD_ERROR_1000007);
        }
        AutSaAud autSaAud = new AutSaAud();
        autSaAud.setSubmitType(req.getSubmitType());
        autSaAud.setAutCode(req.getAutCode());
        autSaAud.setAccountId(req.getAccountId());
        req.setAutSaAudLists(Arrays.asList(autSaAud));
        commonProcessService.processSummary(req);
        String nextStatus;
        List<String> rejectClauseIds = audFirstTrialList.stream().filter(a -> AutSaAudResultEnum.checkIsAutSaAudResultEnum(a.getAutResult(),
                AutSaAudResultEnum.DISAGREE)).map(a -> a.getClauseId()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(rejectClauseIds)) {
            // 根据驳回条款获取对应的驳回信息(初查+复查)
            List<String> clauseTypeList = Arrays.asList(AutSaAudSubmitTypeEnum.SA_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.FR_CLAUSE.getSubmitType());
            List<String> sumTypeList = Arrays.asList(AutSaAudSubmitTypeEnum.SA_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.SA_CLAUSE_CONFIRM.getSubmitType(),
                    AutSaAudSubmitTypeEnum.FR_CLAUSE_CONFIRM.getSubmitType());
            AutSaAud rejectAutSaAud = new AutSaAud();
            rejectAutSaAud.setAutCode(req.getAutCode());
            rejectAutSaAud.setStatus(Constants.HospitalConstants.NUM_1);
            rejectAutSaAud.setClauseId(String.join(",", rejectClauseIds));
            rejectAutSaAud.setSubmitType(String.join(",", clauseTypeList));
            List<AutSaAud> rejectInfo = autSaAudMapper.selectAutSaAudList(rejectAutSaAud);
            // 删除自评总结文件对应的资源库文件信息
//            delFileShareByFileId(req.getAutCode());
            // 失效初审初查+复审复查+自评数据
            commonProcessService.batchInvalidAutSaAudByClauseIds(req.getAutCode(), String.join(",", clauseTypeList), String.join(",", rejectClauseIds));
            // 失效初审复查总结 + 自评总结
            commonProcessService.batchInvalidAutSaAudByClauseIds(req.getAutCode(), String.join(",", sumTypeList), "");
            // 将驳回信息落表保存    初审复查 --- 初审初查     评审复查  --- 评审初查 条款驳回信息
            commonProcessService.saveAutSaAudBusinessData(req.getAutCode(), AutSaAudBusinessCodeEnum.AUT_SA_AUD_REJECT.getCode(), JSON.toJSONString(rejectInfo));
            // 将驳回节点信息落表保存
            commonProcessService.saveAutSaAudBusinessData(req.getAutCode(), AutSaAudBusinessCodeEnum.AUT_SA_AUD_REJECT_STATUS.getCode(), req.getAutSaAudStatusConfig().getCurrentStatus());
            // 节点往下走，翻转到驳回节点
            nextStatus = commonProcessService.getNextStatus(req.getAutSaAudStatusConfig().getNextStatusConfig(), StatusProcessEnum.RJ);
        } else {
            // 清除驳回状态
            autSaAudBusinessDataService.deleteAutSaAudBusinessData(req.getAutCode(), AutSaAudBusinessCodeEnum.AUT_SA_AUD_REJECT_STATUS.getCode());
            autSaAudBusinessDataService.deleteAutSaAudBusinessData(req.getAutCode(), AutSaAudBusinessCodeEnum.AUT_SA_AUD_REJECT.getCode());
            // 节点往下走，不用翻转到驳回节点
            nextStatus = commonProcessService.getNextStatus(req.getAutSaAudStatusConfig().getNextStatusConfig(), StatusProcessEnum.PS);
        }
        log.info("节点翻转到：{}", nextStatus);
        // 更新关联表 aut_sa_relation
        commonProcessService.updateAutSaRelation(nextStatus, req.getAutCode());
    }

    private void delFileShareByFileId(String autCode){
        AutSaAud saSummaryAutSaAud = new AutSaAud();
        saSummaryAutSaAud.setAutCode(autCode);
        saSummaryAutSaAud.setStatus(Constants.HospitalConstants.NUM_1);
        saSummaryAutSaAud.setSubmitType(AutSaAudSubmitTypeEnum.SA_SUMMARY.getSubmitType());
        List<AutSaAud> saSummaryInfos = autSaAudMapper.selectAutSaAudList(saSummaryAutSaAud);
        if(CollectionUtils.isNotEmpty(saSummaryInfos) && StringUtils.isNotBlank(saSummaryInfos.get(0).getFileIds())){
            DelFileShareRequest delFileShareRequest = new DelFileShareRequest();
            delFileShareRequest.setFileIdList(Arrays.stream(saSummaryInfos.get(0).getFileIds().split(","))
                    .map(a -> Long.parseLong(a)).collect(Collectors.toList()));
            // 删除资源库数据
            fileShareMapper.delFileShareByFileIdList(delFileShareRequest);
        }
    }

    @Override
    public List<AutSaAudList> queryList(AutSaAudQueryDTO req) {
        return null;
    }

    @Override
    public AutSaAudDetailVO queryDetail(AutSaAudQueryDTO req) {
        return null;
    }

}
