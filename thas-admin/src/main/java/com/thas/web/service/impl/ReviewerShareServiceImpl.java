package com.thas.web.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.thas.common.constant.Constants;
import com.thas.common.core.domain.entity.SysUser;
import com.thas.common.enums.*;
import com.thas.common.exception.ServiceException;
import com.thas.common.utils.DateUtils;
import com.thas.common.utils.SecurityUtils;
import com.thas.system.domain.vo.UserVo;
import com.thas.system.mapper.SysUserMapper;
import com.thas.system.service.ISysUserService;
import com.thas.web.domain.*;
import com.thas.web.domain.vo.AutSaAudDetailVO;
import com.thas.web.domain.vo.FileInfoVO;
import com.thas.web.dto.HosPlanUserInfoVO;
import com.thas.web.dto.HosReviewPlanVO;
import com.thas.web.dto.ReviewerShareReq;
import com.thas.web.dto.ShareDescDTO;
import com.thas.web.mapper.*;
import com.thas.web.service.IHospitalBaseInfoService;
import com.thas.web.service.IReviewerShareService;
import com.thas.web.service.process.BaseProcessService;
import com.thas.web.service.process.CommonProcessService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class ReviewerShareServiceImpl implements IReviewerShareService {

    @Resource
    private CommonProcessService commonProcessService;
    @Resource
    private IHospitalBaseInfoService hospitalBaseInfoService;
    @Resource
    private AutSaRelationMapper autSaRelationMapper;
    @Resource
    private HospitalReviewerMapper hospitalReviewerMapper;
    @Resource
    private AutSaAudMapper autSaAudMapper;
    @Resource
    private BaseProcessService baseProcessService;
    @Resource
    private SysUserMapper sysUserMapper;
    @Resource
    private ReviewFitMoveClauseMapper reviewFitMoveClauseMapper;
    @Resource
    private ISysUserService sysUserService;

    @Override
    public void submit(ReviewerShareReq req) {
        checkSubmitByRoleAndAutStatus(req);
        //1-1全部填写完提交共享时
        //1-1-1检验：判断填写完自己负责的款才能共享
        cheakIsShare(req);
        //1-3根据入参，更新医院评审表数据为共享状态
        HospitalReviewer hospitalReviewer = new HospitalReviewer();
        hospitalReviewer.setApplyNo(req.getApplyNo());
        hospitalReviewer.setReviewerId(req.getReviewerId());
        hospitalReviewer.setIsShare(req.getIsShare());
        hospitalReviewerMapper.updateHospitalReviewerByApplyNo(hospitalReviewer);
    }

    private void cheakIsShare(ReviewerShareReq req) {
        //获取负责的款
        List<String> assumeClauseIdList = getAssumeClauseId(req);
        //获取提交的款
        AutSaAud autSaAud = new AutSaAud();
        autSaAud.setAutCode(req.getAutCode());
        autSaAud.setSubmitType(AutSaAudSubmitTypeEnum.SR_CLAUSE.getSubmitType());
        autSaAud.setStatus(Constants.INT_ONE);
        List<AutSaAud> submitAutSaAuds = autSaAudMapper.selectAutSaAudList(autSaAud);
        long count = submitAutSaAuds.stream().filter(o -> assumeClauseIdList.contains(o.getClauseId())).count();
        if (assumeClauseIdList.size() != count) {
            log.error("当前评审员负责的款数为{}，提交了的款数为{},不匹配，不能提交共享/提交评审结果", assumeClauseIdList.size(), count);
            throw new ServiceException(ServiceExceptionEnum.REVIEWER_SHARE_1000001);
        }
    }

    @Override
    public List<HospitalReviewerRes> list(ReviewerShareReq reviewerShareReq) {
        List<HospitalReviewerRes> hospitalReviewerResList = new ArrayList<>();
        //查询评审员信息（排除学员和验证评审员）
        List<HospitalReviewer> hospitalReviewers = qryHospitalReviewers(reviewerShareReq);
        //封装中文名
        List<String> reviewerIds = hospitalReviewers.stream().map(HospitalReviewer::getReviewerId).collect(Collectors.toList());
        List<UserVo> userVos = sysUserMapper.selectUserByIds(reviewerIds);
        if (CollectionUtils.isEmpty(userVos) || userVos.size() != reviewerIds.size()) {
            log.error("根据评审员id{}，查询用户信息为空或评审人数不匹配，查询用户信息为：{}", reviewerIds, userVos);
            throw new ServiceException("查询评审员用户信息错误！");
        }
        hospitalReviewers.forEach(hospitalReviewer ->
            userVos.forEach(userVo -> {
                if (ObjectUtil.equal(hospitalReviewer.getReviewerId(), userVo.getUserId().toString())) {
                    HospitalReviewerRes hospitalReviewerRes = BeanUtil.toBean(hospitalReviewer, HospitalReviewerRes.class);
                    hospitalReviewerRes.setReviewerName(userVo.getNickName());
                    hospitalReviewerResList.add(hospitalReviewerRes);
                }
            })
        );
        return hospitalReviewerResList;
    }

    /**
     * 查询评审员信息（排除学员和验证评审员）
     *
     * @param reviewerShareReq
     * @return
     */
    private List<HospitalReviewer> qryHospitalReviewers(ReviewerShareReq reviewerShareReq) {
        if(StringUtils.isBlank(reviewerShareReq.getAutCode())){
            throw new ServiceException("查询医院关联评审员信息,自评编码不能为空！");
        }
        //获取所有评审员信息
        List<HospitalReviewer> hospitalReviewers = hospitalReviewerMapper.selectHospitalReviewerByAutCode(reviewerShareReq.getAutCode());
        if (CollectionUtils.isEmpty(hospitalReviewers)) {
            log.error("根据自评编码{}，查询评审员共享信息为空！",reviewerShareReq.getAutCode());
            throw new ServiceException("查询评审员共享信息为空！");
        }
        //排除学员和验证评审员数据返回
        hospitalReviewers = hospitalReviewers.stream().filter(o -> !(Constants.HospitalConstants.SENIOR_REVIEW.equals(o.getFieldIdList()) ||
                Constants.HospitalConstants.TRAINEES_REVIEW.equals(o.getFieldIdList()))
        ).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(hospitalReviewers)) {
            log.error("查询评审员(排除学员和验证评审员)共享信息为空,查询数据为：{}", hospitalReviewers);
            throw new ServiceException("查询评审员共享信息为空！");
        }
        return hospitalReviewers;
    }

    @Override
    public AutSaAudDetailVO detail(ReviewerShareReq req) {
        //检验是否可共享
        cheakIsShare(req);
        //根据入参自评编码和共享的评审员id调用queryDetail接口返回数据
        AutSaAudQueryDTO autSaAudQueryDTO = new AutSaAudQueryDTO();
        autSaAudQueryDTO.setAutCode(req.getAutCode());
        autSaAudQueryDTO.setAccountId(req.getShareReviewerId());

        return baseProcessService.queryDetail(autSaAudQueryDTO);
    }

    @Override
    public void pass(ReviewerShareReq req) {
        //4-1入参：自评编码，当前登录评审员ID，共享的评审员Id，sr_clause提交类型
        //4-2-1检验：根据入参自评编码，提交类型和提共享修改的用户id（当前登录评审员ID），查询认证自评审核信息是否有修改款，有不能通过（不是他提的共享修改操作，他就可以通过操作）
        AutSaAud autSaAud = new AutSaAud();
        autSaAud.setAutCode(req.getAutCode());
        autSaAud.setSubmitType(AutSaAudSubmitTypeEnum.SR_CLAUSE.getSubmitType());
     //   autSaAud.setProposerIds(req.getReviewerId());
        autSaAud.setAccountId(req.getShareReviewerId());
        autSaAud.setStatus(Constants.INT_ONE);
        autSaAud.setIsShareUpdate(Constants.INT_ONE);
        List<AutSaAud> autSaAuds = autSaAudMapper.selectAutSaAudList(autSaAud);
        if (CollectionUtils.isNotEmpty(autSaAuds)) {
            Set<String> updateClauseIds = autSaAuds.stream().map(AutSaAud::getClauseId).collect(Collectors.toSet());
            log.error("该评审员尚未对您提出的建议修改款进行处理，操作失败,需修改的款为：{}",updateClauseIds);
            throw new ServiceException("该评审员尚未对您提出的建议修改款进行处理，操作失败");
        }
        //4-2-2 根据入参自评编码，共享的评审员Id，查询医院评审关联信息，获取通过人员，判断是否有当前登录用户的ID，如果有不能重复提交通过
        HospitalReviewer hospitalReviewer = new HospitalReviewer();
        hospitalReviewer.setAutCode(req.getAutCode());
        hospitalReviewer.setReviewerId(req.getShareReviewerId());
        hospitalReviewer.setStatus(Constants.INT_ONE);
        List<HospitalReviewer> hospitalReviewers = hospitalReviewerMapper.selectHospitalReviewerList(hospitalReviewer);
        if (CollectionUtils.isEmpty(hospitalReviewers) || hospitalReviewers.size() > 1) {
            throw new ServiceException("查询评审员共享信息错误！");
        }
        String passIds = hospitalReviewers.get(0).getPassIds();
        if (StringUtils.isNotEmpty(passIds) && Arrays.asList(passIds.split(",")).contains(req.getReviewerId())) {
            throw new ServiceException("已提交操作，请勿重复提交");
        }
        //4-3通过时，获取查询的数据，拼接当前登录评审员ID到通过人员字段数据里，更新医院评审数据；
        if (StringUtils.isNotEmpty(passIds)) {
            hospitalReviewers.get(0).setPassIds(passIds + "," + req.getReviewerId());
        } else {
            hospitalReviewers.get(0).setPassIds(req.getReviewerId());
        }
        hospitalReviewers.get(0).setUpdateTime(new Date());
        hospitalReviewerMapper.updateHospitalReviewer(hospitalReviewers.get(0));

        //4-4判断全部都分析通过时，获取转节点方法转节点。
        if (this.checkIsSharePass(req)) {
            log.info("[评审中-030202]评审共享通过,转节点---》》》开始!");
            AutSaAudSaveDTO autSaAudSaveDTO = new AutSaAudSaveDTO();
            autSaAudSaveDTO.setAutCode(req.getAutCode());
            autSaAudSaveDTO.setSubmitType(AutSaAudSubmitTypeEnum.SR_CLAUSE_SHARE.getSubmitType());
            autSaAudSaveDTO.setAccountId(req.getReviewerId());
            autSaAudSaveDTO.setIsSharePass(Constants.STR_NUM_1);
            baseProcessService.process(autSaAudSaveDTO);
            log.info("[评审中-030202]评审共享通过,转节点---》》》结束!");
        }

    }

    /**
     * 检查评审共享是否通过
     *
     * @param req
     * @return
     */
    private boolean checkIsSharePass(ReviewerShareReq req) {
        Boolean isSharePass = false ;
        //4-4-1根据自评编码和状态查询医院评审员关联信息，获取角色为评审员的信息
        //查询评审员信息（排除学员和验证评审员）
        List<HospitalReviewer> hospitalReviewers = qryHospitalReviewers(req);

        //4-4-2然后获取pass_ids数据，判断是否等于当前评审数量-1（不包括自己）
        int size = hospitalReviewers.size() - 1;
        for (HospitalReviewer hospitalReviewer : hospitalReviewers) {
            String passIds = hospitalReviewer.getPassIds();
            //如果有空或者通过人数不相等，证明没有通过
            if (StringUtils.isBlank(passIds) || Arrays.asList(passIds.split(",")).size() != size) {
                isSharePass = false;
                break;
            }
            isSharePass = true;
        }
        //4-4-3如果所有评审员都是，证明都共享通过，翻转节点
        return isSharePass;
    }

    @Override
    public void update(ReviewerShareReq req) {
        this.checkSubmitByRoleAndAutStatus(req);
        //检验是否已提交修改
        //改为：无需判断是否修改
        AutSaAud qryAutSaAud = new AutSaAud();
        qryAutSaAud.setAutCode(req.getAutCode());
        qryAutSaAud.setSubmitType(AutSaAudSubmitTypeEnum.SR_CLAUSE.getSubmitType());
        qryAutSaAud.setClauseId(String.join(",",req.getClauseIds()));
       // qryAutSaAud.setIsShareUpdate(Constants.INT_ONE);
        qryAutSaAud.setStatus(Constants.INT_ONE);
        List<AutSaAud> autSaAuds = autSaAudMapper.selectAutSaAudList(qryAutSaAud);
        if(CollectionUtils.isEmpty(autSaAuds)){
            log.error("查询认证自评审核对象数据为空,查询条件为{}",JSON.toJSONString(qryAutSaAud));
            throw new ServiceException("数据异常，请联系管理员");
        }
        //评审员可看到多个其他评审员提出的修改意见，并可修改评价；
        String shareDesc = packShareDesc(autSaAuds, req);
        List<String> proposerIdList = new ArrayList<>();
        if(StringUtils.isNotBlank(autSaAuds.get(0).getShareDesc())){
            proposerIdList = JSON.parseArray(autSaAuds.get(0).getShareDesc(), ShareDescDTO.class)
                    .stream().map(ShareDescDTO::getUserId).collect(Collectors.toList());
        }
        proposerIdList.add(req.getReviewerId());
        String proposerIds = proposerIdList.stream().distinct().collect(Collectors.joining(","));

        //5-1，入参sr_clause提交类型，是否共享修改状态为1，自评编码，条款id
        //5-2，根据入参，更新认证自评审核数据
        //改为：可多个评审员评一款
        List<AutSaAud> autSaAudLists = new ArrayList<>();
        req.getClauseIds().forEach(clauseId ->{
            AutSaAud autSaAud = new AutSaAud();
            autSaAud.setAutCode(req.getAutCode());
            autSaAud.setSubmitType(AutSaAudSubmitTypeEnum.SR_CLAUSE.getSubmitType());
            autSaAud.setClauseId(clauseId);
            autSaAud.setIsShareUpdate(Constants.INT_ONE);
            autSaAud.setProposerIds(proposerIds);
            autSaAud.setShareDesc(shareDesc);
            autSaAudLists.add(autSaAud);
        });
        autSaAudMapper.batchUpdateAutSaAud(autSaAudLists);
    }

    private String packShareDesc(List<AutSaAud> autSaAuds, ReviewerShareReq req) {
        Map<String, ShareDescDTO> shareDescMap = new LinkedHashMap<>();
        if(StringUtils.isNotBlank(autSaAuds.get(0).getShareDesc())){
            shareDescMap = JSON.parseArray(autSaAuds.get(0).getShareDesc(), ShareDescDTO.class)
                    .stream().collect(Collectors.toMap(ShareDescDTO::getUserId, o -> o));
        }

        //有数据且是当前操作人提交过就更新，反之新增
        if(shareDescMap.containsKey(req.getReviewerId())){
            ShareDescDTO shareDescDTO = shareDescMap.get(req.getReviewerId());
            shareDescDTO.setDesc(req.getShareDesc());
            shareDescDTO.setDateTime(DateUtils.dateTimeNow());
        } else {
            ShareDescDTO shareDescDTO = new ShareDescDTO();
            shareDescDTO.setUserId(req.getReviewerId());
            SysUser sysUser = sysUserMapper.selectUserById(Long.valueOf(req.getReviewerId()));
            if(sysUser == null || StringUtils.isBlank(sysUser.getNickName())){
                throw new ServiceException("查询当前操作人信息为空，请联系管理员");
            }
            shareDescDTO.setUserName(sysUser.getNickName());
            shareDescDTO.setDesc(req.getShareDesc());
            shareDescDTO.setDateTime(DateUtils.dateTimeNow());
            shareDescMap.put(req.getReviewerId(),shareDescDTO);
        }

        List<ShareDescDTO> ShareDescDTOList = shareDescMap.values().stream().sorted(
                Comparator.comparing(ShareDescDTO::getDateTime).reversed()
        ).collect(Collectors.toList());
        return JSON.toJSONString(ShareDescDTOList);
    }

    /**
     * 所有增删改操作的共享接口加角色和节点校验
     *
     * @param req
     */
    @Override
    public void checkSubmitByRoleAndAutStatus(ReviewerShareReq req) {
        if(StringUtils.isBlank(req.getReviewerId())){
            throw new ServiceException("校验评审员角色时，入参评审员Id不能为空");
        }
        if(StringUtils.isBlank(req.getAutCode())){
            throw new ServiceException("校验评审员角色时，入参自评编码不能为空");
        }
        //评审员角色和评审中节点
        String roleKey = sysUserService.getSysUserInfo(req.getReviewerId()).getRoleKey();
        AutSaRelation autSaRelation = qryAutSaRelationByAutCode(req.getAutCode());
        if (!AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(roleKey, AutSaAudRoleEnum.ASSESSOR)){
            throw new ServiceException("当前角色非评审员，操作失败");
        }
        if ( req.getCheckIsAutSaAudStatus() && !AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autSaRelation.getAutStatus(),AutSaAudStatusEnum.SR_CLAUSE_PROCESS)){
            throw new ServiceException("当前节点非评审中，操作失败");
        }
    }

    @Override
    public int submitEvaluate(ReviewerShareReq req) {
        req.setReviewerId(SecurityUtils.getUserId().toString());
        this.checkSubmitByRoleAndAutStatus(req);
        //1-1全部填写完提交共享时
        //1-1-1检验：判断填写完自己负责的款才能共享
        this.cheakIsShare(req);

        //更新
        HospitalReviewer hospitalReviewer = new HospitalReviewer();
        hospitalReviewer.setAutCode(req.getAutCode());
        hospitalReviewer.setReviewerId(req.getReviewerId());
        hospitalReviewer.setStatus(Constants.INT_ONE);
        List<HospitalReviewer> hospitalReviewers = hospitalReviewerMapper.selectHospitalReviewerList(hospitalReviewer);
        if(CollectionUtils.isEmpty(hospitalReviewers) || hospitalReviewers.size()>1){
            log.error("评审员关联医院信息错误，入参：{}，查询数据为{}",JSON.toJSONString(hospitalReviewer),JSON.toJSONString(hospitalReviewers));
            throw new ServiceException("评审员关联医院信息错误，操作失败");
        }
        hospitalReviewer.setId(hospitalReviewers.get(0).getId());
        hospitalReviewer.setIsSubEvaluate(Constants.INT_ONE);
      return hospitalReviewerMapper.updateHospitalReviewer(hospitalReviewer);

    }

    @Override
    public FileInfoVO report(ReviewerShareReq req) {
        //4.1、判断所有评审员是否已提交共享:根据自评编码查询角色为评审员的数据
        List<HospitalReviewer> hospitalReviewers = qryHospitalReviewers(req);
        //且所有人员共享评审状态有为0，证明为不通过
        if(hospitalReviewers.stream().anyMatch(o -> ObjectUtil.equal(o.getIsShare(), Constants.INT_ZERO))){
            //4.2、否则提示:“请等待其他评审员提交共享后再操作”;是则进行如下判断:
            throw new ServiceException("请等待其他评审员提交共享后再操作");
        }

        //4.2.1 判断是否有修改款项，有就提示"尚有建议修改款未外理，请完成后再预览报告”
        //改为：无需判断，先保留代码
        AutSaAud autSaAud = new AutSaAud();
        autSaAud.setAutCode(req.getAutCode());
        autSaAud.setSubmitType(AutSaAudSubmitTypeEnum.SR_CLAUSE.getSubmitType());
        autSaAud.setStatus(Constants.INT_ONE);
        List<AutSaAud> autSaAuds = autSaAudMapper.selectAutSaAudList(autSaAud);
        if(CollectionUtils.isEmpty(autSaAuds)){
            log.error("评审员未评审完，查询自评认证数据条件为{},查询数据为：{}",autSaAud,autSaAuds);
            throw new ServiceException("评审员未评审完,不能操作");
        }
//        //任意一款符合需修改，则提示
//        if(autSaAuds.stream().anyMatch(o-> ObjectUtil.equal(o.getIsShareUpdate(),Constants.INT_ONE))){
//            log.error("尚有建议修改款未外理，请完成后再预览报告,款数据为：{}",autSaAuds);
//            throw new ServiceException("尚有建议修改款未外理，请完成后再预览报告");
//        }

        //4.3、判断是否存在已生成的临时报告:否则生成: 是则进行如下判断:
        //4.4、判断生成报告后是否有新修改内容:否就不生成，直接可预览;是则生成新报告:
        //根据生成报告时间，查询的数据更新时间是否大于报告时间，大于证明有更新修改款数据
        //改为：有报告获取，无报告生成
        FileInfoVO temReviewReportFileInfo = commonProcessService.getTemReviewReportFileInfo(req.getAutCode());
        if(temReviewReportFileInfo == null || autSaAuds.stream().anyMatch(o-> o.getUpdateTime().before(temReviewReportFileInfo.getUpdateTime()))){
             return buildTemReviewReport(req.getAutCode());
        }
        return temReviewReportFileInfo;

    }

    @Override
    public void temPass(ReviewerShareReq req) {
        //当前接口只能评审组长操作
        checkRoleIsReviewerLeader(req);

        checkSubmitByRoleAndAutStatus(req);

        //4.1、判断所有评审员是否已提交共享:根据自评编码查询角色为评审员的数据
        List<HospitalReviewer> hospitalReviewers = qryHospitalReviewers(req);
        //且所有人员共享评审状态有为0，证明为不通过
        //改为评价结果
        if(hospitalReviewers.stream().anyMatch(o -> ObjectUtil.equal(o.getIsSubEvaluate(), Constants.INT_ZERO))){
            //4.2、否则提示:“请等待其他评审员提交共享后再操作”;是则进行如下判断:
            throw new ServiceException("请等待其他评审员提交评价结果后再操作");
        }

        log.info("临时测试直接转节点接口-[评审中-030202]评审共享通过,转节点---》》》开始!");
        AutSaAudSaveDTO autSaAudSaveDTO = new AutSaAudSaveDTO();
        autSaAudSaveDTO.setAutCode(req.getAutCode());
        autSaAudSaveDTO.setSubmitType(AutSaAudSubmitTypeEnum.SR_CLAUSE_SHARE.getSubmitType());
        autSaAudSaveDTO.setAccountId(req.getReviewerId());
        autSaAudSaveDTO.setIsSharePass(Constants.STR_NUM_1);
        baseProcessService.process(autSaAudSaveDTO);
        if(!(ObjectUtil.equal(autSaAudSaveDTO.getTemPassStatus(),Constants.STR_NUM_1))){
            throw new ServiceException("尚有未完成的现场评审或小结，请相关评审员完成后再操作");
        }

        log.info("[临时测试直接转节点接口-评审中-030202]评审共享通过,转节点---》》》结束!");
    }

    @Override
    public void checkRoleIsReviewerLeader(ReviewerShareReq req) {
        List<HospitalReviewer> hospitalReviewers = qryHospitalReviewers(req);
        boolean b = hospitalReviewers.stream().anyMatch(o -> req.getReviewerId().equals(o.getReviewerId()) && ObjectUtil.equal(Constants.INT_ONE, o.getLeaderIs()));
        if(!b){
            throw new ServiceException("当前用户不为评审组长，不能操作！");
        }
    }

    private FileInfoVO buildTemReviewReport(String autCode) {
        log.info("共享评审报告-生成预评审报告---》》》开始");
        AutSaRelation autSaRelation = qryAutSaRelationByAutCode(autCode);
        commonProcessService.temReviewReport(autCode, autSaRelation.getAutCsId());
        FileInfoVO temReviewReportFileInfo = commonProcessService.getTemReviewReportFileInfo(autCode);
        log.info("共享评审报告-生成预评审报告---》》》结束");
        if(temReviewReportFileInfo == null){
            throw new ServiceException(String.format("根据节点:[%s]和业务编码：[%s],获取自评业务数据为空！", autCode, AutSaAudBusinessCodeEnum.TEM_REVIEW_REPORT.getCode()));
        }
        return temReviewReportFileInfo;

    }

    private AutSaRelation qryAutSaRelationByAutCode(String autCode) {
        AutSaRelation autSaRelation = new AutSaRelation();
        autSaRelation.setAutCode(autCode);
        autSaRelation.setStatus(Constants.INT_ONE);
        List<AutSaRelation> autSaRelations = autSaRelationMapper.selectAutSaRelationListByCondition(autSaRelation);
        if(CollectionUtils.isEmpty(autSaRelations) || autSaRelations.size()>1){
            log.error("共享报告-查询认证自评管理信息为空，自评编码为：{}",autCode);
            throw new ServiceException("共享报告-查询认证自评管理信息错误，请联系管理员");
        }
        return autSaRelations.get(0);
    }

    /**
     * 获取当前操作人负责的款id
     */
    private List<String> getAssumeClauseId(ReviewerShareReq reviewerShareReq) {
        // 查询医院分配详情记录
        List<HosReviewPlanVO> hosPlanClauseList = hospitalBaseInfoService.selectHosPlanClauseDetail(reviewerShareReq.getApplyNo());
        if (CollectionUtils.isEmpty(hosPlanClauseList)) {
            log.error("根据医疗机构：{} 查询计划分配详细信息列表为空，提交失败！", reviewerShareReq.getApplyNo());
            throw new ServiceException(ServiceExceptionEnum.REVIEWER_SHARE_1000000);
        }
        HosReviewPlanVO hosReviewPlanVO = hosPlanClauseList.get(0);
        // 获取医疗机构计划分配评审员信息
        List<HosPlanUserInfoVO> reviewerInfoList = commonProcessService.getHosPlanMemberList(hosReviewPlanVO, AutSaAudRoleEnum.ASSESSOR.getRoleKey());
        if (CollectionUtils.isEmpty(reviewerInfoList)) {
            log.error("根据评审员角色刷选对象数据为空，对象数据为：{}", hosReviewPlanVO.toString());
            throw new ServiceException(ServiceExceptionEnum.REVIEWER_SHARE_1000000);
        }
        //获取当前操作人负责的款id
        List<String> clauseIdList = new ArrayList<>();
        reviewerInfoList.forEach(o -> {
            if (ObjectUtil.equal(reviewerShareReq.getReviewerId(), o.getAccountId())) {
                clauseIdList.addAll(o.getCstCertificationStandardVOList().stream().map(o1 -> o1.getClauseId().toString()).collect(Collectors.toList()));
            }
        });
        if (CollectionUtils.isEmpty(clauseIdList)) {
            log.error("当前操作评审员id{},负责的评审的款为空，对象数据为：{}", reviewerShareReq.getReviewerId(), reviewerInfoList.toString());
            throw new ServiceException(ServiceExceptionEnum.REVIEWER_SHARE_1000000);
        }
        //剔除挪动的款
        ReviewFitMoveClause reviewFitMoveClauseReq = new ReviewFitMoveClause();
        reviewFitMoveClauseReq.setAutCode(reviewerShareReq.getAutCode());
        reviewFitMoveClauseReq.setMoveStatus(1L);
        reviewFitMoveClauseReq.setStatus(1L);
        List<ReviewFitMoveClause> reviewFitMoveClauseList = reviewFitMoveClauseMapper.selectReviewFitMoveClauseList(reviewFitMoveClauseReq);
        if(CollectionUtils.isNotEmpty(reviewFitMoveClauseList)){
            List<String> moveClauseList = reviewFitMoveClauseList.stream().map(ReviewFitMoveClause::getClauseId).collect(Collectors.toList());
            clauseIdList.removeAll(moveClauseList);
        }
        return clauseIdList;
    }

}
