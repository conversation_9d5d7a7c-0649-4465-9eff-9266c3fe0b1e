package com.thas.web.service.process.impl;

import com.thas.common.enums.AutSaAudStatusEnum;
import com.thas.common.enums.AutSaAudSubmitTypeEnum;
import com.thas.web.domain.AutSaAudList;
import com.thas.web.domain.AutSaAudQueryDTO;
import com.thas.web.domain.AutSaAudSaveDTO;
import com.thas.web.domain.vo.AutSaAudDetailVO;
import com.thas.web.service.process.BaseProcessService;
import com.thas.web.service.process.CommonProcessService;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 医院事实准确性审查流程服务 Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-04-01
 */
@Component("hospital02ProcessService")
@Slf4j
public class Hospital02ProcessServiceImpl implements BaseProcessService {

    @Resource
    private CommonProcessService commonProcessService;

    @Override
    public void process(AutSaAudSaveDTO req) {
        long startTime = System.currentTimeMillis();
        log.info("hospital02ProcessService.process ------ 开始");
        if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(req.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE)) {
            // 校验款信息
            commonProcessService.checkClause(req);
            if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.WAIT_FAR_CLAUSE)) {
                // 事实准确性待审查
                commonProcessService.processInitialScene(req);
            } else if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.FAR_CLAUSE_PROCESS)) {
                // 事实准确性审查中
                commonProcessService.processClauseScene(req);
            }
        } else if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(req.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_CONFIRM)) {
            // 事实准确性审查确认   直接翻转节点
            commonProcessService.processConfirmSkip(req);
        }
        log.info("hospital02ProcessService.process ------ 结束 耗时：{}", System.currentTimeMillis() - startTime);
    }

    @Override
    public List<AutSaAudList> queryList(AutSaAudQueryDTO req) {
        return null;
    }

    @Override
    public AutSaAudDetailVO queryDetail(AutSaAudQueryDTO req) {
        return null;
    }

}
