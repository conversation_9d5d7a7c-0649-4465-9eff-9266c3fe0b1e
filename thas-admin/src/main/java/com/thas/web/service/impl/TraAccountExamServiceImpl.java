package com.thas.web.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.thas.common.constant.Constants;
import com.thas.common.exception.ServiceException;
import com.thas.web.domain.TraAccountExam;
import com.thas.web.mapper.TraAccountExamMapper;
import com.thas.web.service.IAutRecordService;
import com.thas.web.service.ITraAccountExamService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 评审员学习资源考卷通过情况Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-20
 */
@Service
public class TraAccountExamServiceImpl implements ITraAccountExamService {
    @Autowired
    private TraAccountExamMapper traAccountExamMapper;

    @Autowired
    private IAutRecordService autRecordService;

    /**
     * 查询评审员学习资源考卷通过情况
     *
     * @param id 评审员学习资源考卷通过情况主键
     * @return 评审员学习资源考卷通过情况
     */
    @Override
    public TraAccountExam selectTraAccountExamById(Long id) {
        return traAccountExamMapper.selectTraAccountExamById(id);
    }

    /**
     * 查询评审员学习资源考卷通过情况列表
     *
     * @param traAccountExam 评审员学习资源考卷通过情况
     * @return 评审员学习资源考卷通过情况
     */
    @Override
    public List<TraAccountExam> selectTraAccountExamList(TraAccountExam traAccountExam) {
        return traAccountExamMapper.selectTraAccountExamList(traAccountExam);
    }

    /**
     * 新增评审员学习资源考卷通过情况
     *
     * @param traAccountExam 评审员学习资源考卷通过情况
     * @return 结果
     */
    @Override
    public int insertTraAccountExam(TraAccountExam traAccountExam) {
        return traAccountExamMapper.insertTraAccountExam(traAccountExam);
    }

    /**
     * 修改评审员学习资源考卷通过情况
     *
     * @param traAccountExam 评审员学习资源考卷通过情况
     * @return 结果
     */
    @Override
    public int updateTraAccountExam(TraAccountExam traAccountExam) {
        return traAccountExamMapper.updateTraAccountExam(traAccountExam);
    }

    /**
     * 批量删除评审员学习资源考卷通过情况
     *
     * @param ids 需要删除的评审员学习资源考卷通过情况主键
     * @return 结果
     */
    @Override
    public int deleteTraAccountExamByIds(Long[] ids) {
        return traAccountExamMapper.deleteTraAccountExamByIds(ids);
    }

    /**
     * 删除评审员学习资源考卷通过情况信息
     *
     * @param id 评审员学习资源考卷通过情况主键
     * @return 结果
     */
    @Override
    public int deleteTraAccountExamById(Long id) {
        return traAccountExamMapper.deleteTraAccountExamById(id);
    }

    /**
     * 在线学习通过后提交审核,提交前再次校验数据库
     *
     * @param account 用户账号
     * @param roleId  角色
     * @return 结果
     */
    @Override
    public String pass(String account, Long roleId) {
        /*int failCount = traAccountExamMapper.pass(account);
        // 如果存在未通过或者未考试的考卷,直接返回
        if (failCount > 0) {
            throw new ServiceException("存在未通过或者未考试的考卷,请先点击表中蓝色文字进入学习并考试");
        }
        AutRecord autRecord = new AutRecord();
        autRecord.setAccountId(SecurityUtils.getUserId().toString());
        autRecordService.saveAutRecord(autRecord);*/
        TraAccountExam traAccountExam = new TraAccountExam();
        traAccountExam.setAccount(account);
        List<TraAccountExam> traAccountExamList = traAccountExamMapper.selectTraAccountExamList(traAccountExam);
        if (CollUtil.isEmpty(traAccountExamList)) {
            return "操作成功";
        }
        boolean flag = traAccountExamList.stream()
                .allMatch(tra -> Constants.HospitalConstants.NUM_1.equals(tra.getStatus()));
        if (!flag) {
            throw new ServiceException("存在未通过或者未考试的考卷,请先点击表中蓝色文字进入学习并考试");
        }
        return "操作成功";
    }

    /**
     * 新增或修改用户考试通过情况
     *
     * @param traAccountExam 请求体
     */
    @Override
    public void saveOrUpdate(TraAccountExam traAccountExam) {
        List<TraAccountExam> traAccountExamList = traAccountExamMapper.selectByAccount(new TraAccountExam() {{
            setAccount(traAccountExam.getAccount());
            setExamPaperId(traAccountExam.getExamPaperId());
        }});
        if (CollectionUtils.isEmpty(traAccountExamList)) {
            traAccountExamMapper.insertTraAccountExam(traAccountExam);
        } else if (Constants.INT_ZERO == traAccountExamList.get(0).getStatus()) {
            traAccountExam.setId(traAccountExamList.get(0).getId());
            traAccountExamMapper.updateTraAccountExam(traAccountExam);
        }
    }

}
