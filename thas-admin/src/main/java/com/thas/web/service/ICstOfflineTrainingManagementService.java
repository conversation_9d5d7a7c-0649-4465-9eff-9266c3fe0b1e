package com.thas.web.service;


import com.thas.web.domain.CstOfflineTrainingManagement;

import java.util.List;

/**
 * 线下培训管理Service接口
 * 
 * <AUTHOR>
 * @date 2022-01-25
 */
public interface ICstOfflineTrainingManagementService 
{
    /**
     * 查询线下培训管理
     * 
     * @param id 线下培训管理主键
     * @return 线下培训管理
     */
    public CstOfflineTrainingManagement selectCstOfflineTrainingManagementById(Long id);

    /**
     * 查询线下培训管理列表
     * 
     * @param cstOfflineTrainingManagement 线下培训管理
     * @return 线下培训管理集合
     */
    public List<CstOfflineTrainingManagement> selectCstOfflineTrainingManagementList(CstOfflineTrainingManagement cstOfflineTrainingManagement);

    /**
     * 新增线下培训管理
     * 
     * @param cstOfflineTrainingManagement 线下培训管理
     * @return 结果
     */
    public int insertCstOfflineTrainingManagement(CstOfflineTrainingManagement cstOfflineTrainingManagement);

    /**
     * 修改线下培训管理
     * 
     * @param cstOfflineTrainingManagement 线下培训管理
     * @return 结果
     */
    public int updateCstOfflineTrainingManagement(CstOfflineTrainingManagement cstOfflineTrainingManagement);

    /**
     * 批量删除线下培训管理
     * 
     * @param ids 需要删除的线下培训管理主键集合
     * @return 结果
     */
    public int deleteCstOfflineTrainingManagementByIds(Long[] ids);

    /**
     * 删除线下培训管理信息
     * 
     * @param id 线下培训管理主键
     * @return 结果
     */
    public int deleteCstOfflineTrainingManagementById(Long id);

    int updateTrainingStatus(Long id, String status);

    List<CstOfflineTrainingManagement> selectCstOfflineTrainingManagementByAccountId(String accountId);

    void updateTrainingStatusByNowTime();

}
