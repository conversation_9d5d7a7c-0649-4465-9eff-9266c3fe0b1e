package com.thas.web.service.impl;

import java.util.List;

import com.thas.web.domain.MessageSendRecord;
import com.thas.web.domain.MessageTemplate;
import com.thas.web.domain.dto.MessageSendRecordDTO;
import com.thas.web.mapper.MessageTemplateMapper;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.thas.web.mapper.MessageSendUserMapper;
import com.thas.web.domain.MessageSendUser;
import com.thas.web.service.IMessageSendUserService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 消息发送指定用户关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-25
 */
@Service
public class MessageSendUserServiceImpl implements IMessageSendUserService {
    @Autowired
    private MessageSendUserMapper messageSendUserMapper;


    /**
     * 查询消息发送指定用户关联
     *
     * @param sendMessageId 消息发送指定用户关联主键
     * @return 消息发送指定用户关联
     */
    @Override
    public MessageSendUser selectMessageSendUserBySendMessageId(Long sendMessageId) {
        return messageSendUserMapper.selectMessageSendUserBySendMessageId(sendMessageId);
    }

    /**
     * 查询消息发送指定用户关联列表
     *
     * @param messageSendUser 消息发送指定用户关联
     * @return 消息发送指定用户关联
     */
    @Override
    public List<MessageSendUser> selectMessageSendUserList(MessageSendUser messageSendUser) {
        return messageSendUserMapper.selectMessageSendUserList(messageSendUser);
    }

    /**
     * 新增消息发送指定用户关联
     *
     * @param messageSendUser 消息发送指定用户关联
     * @return 结果
     */
    @Override
    public int insertMessageSendUser(MessageSendUser messageSendUser) {
        return messageSendUserMapper.insertMessageSendUser(messageSendUser);
    }

    /**
     * 修改消息发送指定用户关联
     *
     * @param messageSendUser 消息发送指定用户关联
     * @return 结果
     */
    @Override
    public int updateMessageSendUser(MessageSendUser messageSendUser) {
        return messageSendUserMapper.updateMessageSendUser(messageSendUser);
    }

    /**
     * 批量删除消息发送指定用户关联
     *
     * @param sendMessageIds 需要删除的消息发送指定用户关联主键
     * @return 结果
     */
    @Override
    public int deleteMessageSendUserBySendMessageIds(Long[] sendMessageIds) {
        return messageSendUserMapper.deleteMessageSendUserBySendMessageIds(sendMessageIds);
    }

    /**
     * 删除消息发送指定用户关联信息
     *
     * @param sendMessageId 消息发送指定用户关联主键
     * @return 结果
     */
    @Override
    public int deleteMessageSendUserBySendMessageId(Long sendMessageId) {
        return messageSendUserMapper.deleteMessageSendUserBySendMessageId(sendMessageId);
    }
}
