package com.thas.web.service.impl;

import com.thas.common.utils.DateUtils;
import com.thas.web.domain.ReviewerFieldInfo;
import com.thas.web.dto.ReviewerFieldInfoVO;
import com.thas.web.mapper.ReviewerFieldInfoMapper;
import com.thas.web.service.IReviewerFieldInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 评审员领域关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-19
 */
@Slf4j
@Service
public class ReviewerFieldInfoServiceImpl implements IReviewerFieldInfoService
{
    @Autowired
    private ReviewerFieldInfoMapper reviewerFieldInfoMapper;

    /**
     * 查询评审员领域关联
     *
     * @param id 评审员领域关联主键
     * @return 评审员领域关联
     */
    @Override
    public ReviewerFieldInfo selectReviewerFieldInfoById(Long id)
    {
        return reviewerFieldInfoMapper.selectReviewerFieldInfoById(id);
    }

    @Override
    public List<ReviewerFieldInfoVO> selectReviewerFieldInfoByAccountId(String accountId) {
        return reviewerFieldInfoMapper.selectReviewerFieldInfoByAccountId(accountId);
    }

    /**
     * 查询评审员领域关联列表
     *
     * @param reviewerFieldInfo 评审员领域关联
     * @return 评审员领域关联
     */
    @Override
    public List<ReviewerFieldInfo> selectReviewerFieldInfoList(ReviewerFieldInfo reviewerFieldInfo)
    {
        return reviewerFieldInfoMapper.selectReviewerFieldInfoList(reviewerFieldInfo);
    }

    /**
     * 新增评审员领域关联
     *
     * @param reviewerFieldInfo 评审员领域关联
     * @return 结果
     */
    @Override
    public int insertReviewerFieldInfo(ReviewerFieldInfo reviewerFieldInfo)
    {
        reviewerFieldInfo.setCreateTime(DateUtils.getNowDate());
        return reviewerFieldInfoMapper.insertReviewerFieldInfo(reviewerFieldInfo);
    }

    /**
     * 修改评审员领域关联
     *
     * @param reviewerFieldInfo 评审员领域关联
     * @return 结果
     */
    @Override
    public int updateReviewerFieldInfo(ReviewerFieldInfo reviewerFieldInfo)
    {
        reviewerFieldInfo.setUpdateTime(DateUtils.getNowDate());
        return reviewerFieldInfoMapper.updateReviewerFieldInfo(reviewerFieldInfo);
    }

    /**
     * 批量删除评审员领域关联
     *
     * @param ids 需要删除的评审员领域关联主键
     * @return 结果
     */
    @Override
    public int deleteReviewerFieldInfoByIds(Long[] ids)
    {
        return reviewerFieldInfoMapper.deleteReviewerFieldInfoByIds(ids);
    }

    /**
     * 删除评审员领域关联信息
     *
     * @param id 评审员领域关联主键
     * @return 结果
     */
    @Override
    public int deleteReviewerFieldInfoById(Long id)
    {
        return reviewerFieldInfoMapper.deleteReviewerFieldInfoById(id);
    }

    @Override
    public void updateStatusByAccountId(String accountId, int status) {
        int res = reviewerFieldInfoMapper.updateStatusByAccountId(accountId, status);
        log.info("修改reviewerFieldInfo status:{} accountId:{} res:{}", status, accountId, res);
    }

    @Override
    public void updateStatusByFieldCode(String fieldCode, String status) {
        int res = reviewerFieldInfoMapper.updateStatusByFieldCode(fieldCode, status);
        log.info("修改reviewerFieldInfo status:{} fieldCode:{} res:{}", status, fieldCode, res);
    }
}
