package com.thas.web.service;

import com.thas.web.domain.UploadChunkInfo;
import com.thas.web.domain.UploadResult;

/**
 * 大文件上传Service接口
 *
 * <AUTHOR>
 * @date 2022-03-10
 */
public interface BigFileUploadService {

    /**
     * 校验文件已上传分片信息
     *
     * @param uploadChunkInfo 上传分块信息
     * @return 上传结果
     */
    UploadResult checkChunk(UploadChunkInfo uploadChunkInfo);

    /**
     * 上传分片文件
     *
     * @param uploadChunkInfo 分片文件信息
     * @return 文件信息
     */
    UploadResult chunkUpload(UploadChunkInfo uploadChunkInfo);

    /**
     * 合并文件
     *
     * @param uploadChunkInfo 分片文件信息
     * @return 文件信息
     */
    UploadResult mergeFile(UploadChunkInfo uploadChunkInfo);
}
