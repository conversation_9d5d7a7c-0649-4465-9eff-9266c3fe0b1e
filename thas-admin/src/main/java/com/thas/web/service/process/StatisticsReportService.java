package com.thas.web.service.process;

import com.thas.web.domain.StatisticsReportReq;
import com.thas.web.domain.StatisticsReportRes;

/**
 * 统计报表服务
 *
 * <AUTHOR>
 */
public interface StatisticsReportService {

    /**
     * 利益冲突查询接口
     * @param statisticsReportReq
     * @return
     */
    StatisticsReportRes queryList(StatisticsReportReq statisticsReportReq);

    /**
     * 利益冲突详情接口
     * @param statisticsReportReq
     * @return
     */
    StatisticsReportRes detail(StatisticsReportReq statisticsReportReq);

//    List<StatisticsReportRes> rjAllotQueryList(StatisticsReportReq statisticsReportReq);
//
//    List<StatisticsReportRes> rjAllotDetail(StatisticsReportReq statisticsReportReq);
//
//    List<StatisticsReportRes> acceptQueryList(StatisticsReportReq statisticsReportReq);
//
//    List<StatisticsReportRes> acceptDetail(StatisticsReportReq statisticsReportReq);
}
