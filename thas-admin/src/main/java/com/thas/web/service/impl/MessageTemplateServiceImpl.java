package com.thas.web.service.impl;

import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import com.thas.common.constant.Constants;
import com.thas.common.exception.ServiceException;
import com.thas.common.utils.DateUtils;
import com.thas.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.thas.web.mapper.MessageTemplateMapper;
import com.thas.web.domain.MessageTemplate;
import com.thas.web.service.IMessageTemplateService;

/**
 * 消息模板Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-25
 */
@Service
public class MessageTemplateServiceImpl implements IMessageTemplateService
{
    @Autowired
    private MessageTemplateMapper messageTemplateMapper;

    /**
     * 查询消息模板
     *
     * @param id 消息模板主键
     * @return 消息模板
     */
    @Override
    public MessageTemplate selectMessageTemplateById(Long id)
    {
        return messageTemplateMapper.selectMessageTemplateById(id);
    }

    /**
     * 查询消息模板列表
     *
     * @param messageTemplate 消息模板
     * @return 消息模板
     */
    @Override
    public List<MessageTemplate> selectMessageTemplateList(MessageTemplate messageTemplate)
    {
        return messageTemplateMapper.selectMessageTemplateList(messageTemplate);
    }

    /**
     * 新增消息模板
     *
     * @param messageTemplate 消息模板
     * @return 结果
     */
    @Override
    public int insertMessageTemplate(MessageTemplate messageTemplate)
    {
        //以模板名称为唯一条件，校验是个存在相同名称
        List<MessageTemplate> messageTemplates = selectMessageTemplateListByName(messageTemplate);
        if(messageTemplates.size() > 0){
            throw new ServiceException("模板名称已存在，请使用其他模板名称");
        }

//        String type = messageTemplate.getType();
//        if (Constants.HospitalConstants.STR_NUM_1.equals(type)) {
//            // 如果是启动模板，需要判断当前类型是否有已启动的模板
//            checkTemplateByType(type);
//        }
        messageTemplate.setCreateTime(DateUtils.getNowDate());
        int res;
        try {
            res = messageTemplateMapper.insertMessageTemplate(messageTemplate);
        } catch (Exception e) {
            throw new ServiceException("已经存在当前类型模板！", 500);
        }
        return res;
    }

    /**
     * 修改消息模板
     *
     * @param messageTemplate 消息模板
     * @return 结果
     */
    @Override
    public int updateMessageTemplate(MessageTemplate messageTemplate)
    {
        if(messageTemplate.getId() == null){
            throw new ServiceException("模板Id不能为空");
        }
//        List<MessageTemplate> messageTemplates = selectMessageTemplateListByName(messageTemplate);
//        //如果是自己的模板名称那么就可以修改，返回true
//        boolean b = messageTemplates.stream().allMatch(o -> ObjectUtil.equal(o.getId(),messageTemplate.getId()));
//        if(messageTemplates.size() > 0 && !b ){
//            throw new ServiceException("模板名称已存在，请使用其他模板名称");
//        }
//        if (Constants.HospitalConstants.STR_NUM_1.equals(type)) {
//            // 如果是启动模板，需要判断当前类型是否有已启动的模板
//            checkTemplateByType(type);
//        }
        messageTemplate.setUpdateTime(DateUtils.getNowDate());
        return messageTemplateMapper.updateMessageTemplate(messageTemplate);
    }

    private List<MessageTemplate> selectMessageTemplateListByName(MessageTemplate messageTemplate) {
        String messageTemplateName = messageTemplate.getName();
        if(StringUtils.isBlank(messageTemplateName)){
            throw new ServiceException("模板名称不能为空");
        }
        //以模板名称为唯一条件，校验是个存在相同名称
        MessageTemplate qryMessageTemplate = new MessageTemplate();
        qryMessageTemplate.setName(messageTemplate.getName());
        return messageTemplateMapper.selectMessageTemplateListByName(qryMessageTemplate);
    }

    /**
     * 通过type校验是否已经存在已启动的模板
     *
     * @param type 模板类型
     */
    private void checkTemplateByType(String type) {
        int count = messageTemplateMapper.selectCountByTypeAndStatus(type, 1);
        if (count > 0) {
            throw new ServiceException("当前模板类型已经存在启动模板！", 500);
        }
    }

    /**
     * 批量删除消息模板
     *
     * @param ids 需要删除的消息模板主键
     * @return 结果
     */
    @Override
    public int deleteMessageTemplateByIds(Long[] ids)
    {
        return messageTemplateMapper.deleteMessageTemplateByIds(ids);
    }

    /**
     * 删除消息模板信息
     *
     * @param id 消息模板主键
     * @return 结果
     */
    @Override
    public int deleteMessageTemplateById(Long id)
    {
        return messageTemplateMapper.deleteMessageTemplateById(id);
    }
}
