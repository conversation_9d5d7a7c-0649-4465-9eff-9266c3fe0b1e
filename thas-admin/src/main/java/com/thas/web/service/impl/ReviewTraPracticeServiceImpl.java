package com.thas.web.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.thas.common.exception.ServiceException;
import com.thas.common.utils.DateUtils;
import com.thas.system.domain.vo.UserVo;
import com.thas.system.mapper.SysUserMapper;
import com.thas.web.domain.ReviewTraPractice;
import com.thas.web.dto.SysUserBaseInfo;
import com.thas.web.mapper.ReviewTraPracticeMapper;
import com.thas.web.service.IReviewTraPracticeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 评审学员参与评审记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-05-23
 */
@Service
@Slf4j
public class ReviewTraPracticeServiceImpl implements IReviewTraPracticeService {

    @Autowired
    private ReviewTraPracticeMapper reviewTraPracticeMapper;

    @Autowired
    private SysUserMapper sysUserMapper;


    /**
     * 查询评审学员参与评审记录
     *
     * @param id 评审学员参与评审记录主键
     * @return 评审学员参与评审记录
     */
    @Override
    public ReviewTraPractice selectReviewTraPracticeById(Long id) {
        return reviewTraPracticeMapper.selectReviewTraPracticeById(id);
    }

    /**
     * 查询评审学员参与评审记录列表
     *
     * @param reviewTraPractice 评审学员参与评审记录
     * @return 评审学员参与评审记录
     */
    @Override
    public List<ReviewTraPractice> selectReviewTraPracticeList(ReviewTraPractice reviewTraPractice) {
        List<ReviewTraPractice> reviewTraPracticeList = reviewTraPracticeMapper.selectReviewTraPracticeList(reviewTraPractice);

        // 组装带教评审员基本信息
        getTeaReviewerBaseInfo(reviewTraPracticeList);
        return reviewTraPracticeList;
    }

    private void getTeaReviewerBaseInfo(List<ReviewTraPractice> reviewTraPracticeList) {
        if (CollectionUtil.isEmpty(reviewTraPracticeList)) {
            return;
        }
        List<String> reviewerIds = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(reviewTraPracticeList)) {
            reviewerIds = StrUtil.split(
                    reviewTraPracticeList.stream().map(ReviewTraPractice::getTeaReviewerId).collect(Collectors.joining(",")), ",");
        }
        if (CollectionUtil.isEmpty(reviewerIds)) {
            return;
        }

        List<UserVo> userVoList = sysUserMapper.selectUserByIds(reviewerIds);
        if(CollectionUtil.isEmpty(userVoList) || userVoList.size() != reviewerIds.size()){
            log.error("获取培训教员信息失败，查询为空或用户数据失效数量不匹配，根据reviewerIds：【{}】， 查询数据为【{}】",
                    reviewerIds.toString(), JSON.toJSONString(userVoList));
            throw new ServiceException("获取培训教员信息失败，请联系管理员！");
        }
        Map<String, UserVo> userVoMap = userVoList.stream().collect(Collectors.toMap(o->o.getUserId().toString(), o -> o));

        for (ReviewTraPractice traPractice : reviewTraPracticeList) {
            List<String> teaReviewerIds = StrUtil.split(traPractice.getTeaReviewerId(), ",");
            Map<String, SysUserBaseInfo> teaReviewerMap = new HashMap<>();
            teaReviewerIds.forEach(teaReviewerId->{
                UserVo userVo = userVoMap.get(teaReviewerId);
                SysUserBaseInfo baseInfo = new SysUserBaseInfo();
                baseInfo.setAccountId(teaReviewerId);
                baseInfo.setUserName(userVo.getUserName());
                baseInfo.setName(userVo.getNickName());
                teaReviewerMap.put(teaReviewerId, baseInfo);
            });
            traPractice.setTeaReviewerMap(teaReviewerMap);
        }

    }

    /**
     * 新增评审学员参与评审记录
     *
     * @param reviewTraPractice 评审学员参与评审记录
     * @return 结果
     */
    @Override
    public int insertReviewTraPractice(ReviewTraPractice reviewTraPractice) {
        reviewTraPractice.setCreateTime(DateUtils.getNowDate());
        return reviewTraPracticeMapper.insertReviewTraPractice(reviewTraPractice);
    }


    /**
     * 修改评审学员参与评审记录
     *
     * @param reviewTraPractice 评审学员参与评审记录
     * @return 结果
     */
    @Override
    public int updateReviewTraPractice(ReviewTraPractice reviewTraPractice) {
        reviewTraPractice.setUpdateTime(DateUtils.getNowDate());
        return reviewTraPracticeMapper.updateReviewTraPractice(reviewTraPractice);
    }

    /**
     * 批量删除评审学员参与评审记录
     *
     * @param ids 需要删除的评审学员参与评审记录主键
     * @return 结果
     */
    @Override
    public int deleteReviewTraPracticeByIds(Long[] ids) {
        return reviewTraPracticeMapper.deleteReviewTraPracticeByIds(ids);
    }

    /**
     * 删除评审学员参与评审记录信息
     *
     * @param id 评审学员参与评审记录主键
     * @return 结果
     */
    @Override
    public int deleteReviewTraPracticeById(Long id) {
        return reviewTraPracticeMapper.deleteReviewTraPracticeById(id);
    }
}
