package com.thas.web.service;

import java.util.List;
import com.thas.web.domain.MessageReceiveRecord;

/**
 * 消息接收记录Service接口
 *
 * <AUTHOR>
 * @date 2022-01-25
 */
public interface IMessageReceiveRecordService
{
    /**
     * 查询消息接收记录
     *
     * @param id 消息接收记录主键
     * @return 消息接收记录
     */
    public MessageReceiveRecord selectMessageReceiveRecordById(Long id);

    /**
     * 查询消息接收记录列表
     *
     * @param messageReceiveRecord 消息接收记录
     * @return 消息接收记录集合
     */
    public List<MessageReceiveRecord> selectMessageReceiveRecordList(MessageReceiveRecord messageReceiveRecord);

    /**
     * 新增消息接收记录
     *
     * @param messageReceiveRecord 消息接收记录
     * @return 结果
     */
    public int insertMessageReceiveRecord(MessageReceiveRecord messageReceiveRecord);

    /**
     * 修改消息接收记录
     *
     * @param messageReceiveRecord 消息接收记录
     * @return 结果
     */
    public int updateMessageReceiveRecord(MessageReceiveRecord messageReceiveRecord);

    /**
     * 批量删除消息接收记录
     *
     * @param ids 需要删除的消息接收记录主键集合
     * @return 结果
     */
    public int deleteMessageReceiveRecordByIds(Long[] ids);

    /**
     * 删除消息接收记录信息
     *
     * @param id 消息接收记录主键
     * @return 结果
     */
    public int deleteMessageReceiveRecordById(Long id);
}
