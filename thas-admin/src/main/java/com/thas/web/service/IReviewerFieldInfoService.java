package com.thas.web.service;

import com.thas.web.domain.ReviewerFieldInfo;
import com.thas.web.dto.ReviewerFieldInfoVO;
import com.thas.web.dto.SysUserBaseInfo;

import java.util.List;

/**
 * 评审员领域关联Service接口
 *
 * <AUTHOR>
 * @date 2022-01-19
 */
public interface IReviewerFieldInfoService
{
    /**
     * 查询评审员领域关联
     *
     * @param id 评审员领域关联主键
     * @return 评审员领域关联
     */
    ReviewerFieldInfo selectReviewerFieldInfoById(Long id);


    List<ReviewerFieldInfoVO> selectReviewerFieldInfoByAccountId(String accountId);
    /**
     * 查询评审员领域关联列表
     *
     * @param reviewerFieldInfo 评审员领域关联
     * @return 评审员领域关联集合
     */
    List<ReviewerFieldInfo> selectReviewerFieldInfoList(ReviewerFieldInfo reviewerFieldInfo);

    /**
     * 新增评审员领域关联
     *
     * @param reviewerFieldInfo 评审员领域关联
     * @return 结果
     */
    int insertReviewerFieldInfo(ReviewerFieldInfo reviewerFieldInfo);

    /**
     * 修改评审员领域关联
     *
     * @param reviewerFieldInfo 评审员领域关联
     * @return 结果
     */
    int updateReviewerFieldInfo(ReviewerFieldInfo reviewerFieldInfo);

    /**
     * 批量删除评审员领域关联
     *
     * @param ids 需要删除的评审员领域关联主键集合
     * @return 结果
     */
    int deleteReviewerFieldInfoByIds(Long[] ids);

    /**
     * 删除评审员领域关联信息
     *
     * @param id 评审员领域关联主键
     * @return 结果
     */
    int deleteReviewerFieldInfoById(Long id);

    void updateStatusByAccountId(String accountId, int status);

    void updateStatusByFieldCode(String fieldCode, String status);
}
