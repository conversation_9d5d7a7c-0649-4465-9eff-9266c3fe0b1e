package com.thas.web.service;

import java.util.List;

import com.thas.common.core.domain.AjaxResult;
import com.thas.web.domain.HospitalReviewCycle;
import com.thas.web.domain.vo.ModifyTheReviewCycleVo;

/**
 * 医院评审阶段周期Service接口
 *
 * <AUTHOR>
 * @date 2022-01-27
 */
public interface IHospitalReviewCycleService
{
    /**
     * 查询医院评审阶段周期
     *
     * @param id 医院评审阶段周期主键
     * @return 医院评审阶段周期
     */
    public HospitalReviewCycle selectHospitalReviewCycleById(Long id);

    /**
     * 查询医院评审阶段周期列表
     *
     * @param hospitalReviewCycle 医院评审阶段周期
     * @return 医院评审阶段周期集合
     */
    public List<HospitalReviewCycle> selectHospitalReviewCycleList(HospitalReviewCycle hospitalReviewCycle);

    /**
     * 新增医院评审阶段周期
     *
     * @param hospitalReviewCycle 医院评审阶段周期
     * @return 结果
     */
    public int insertHospitalReviewCycle(HospitalReviewCycle hospitalReviewCycle);

    /**
     * 修改医院评审阶段周期
     *
     * @param hospitalReviewCycle 医院评审阶段周期
     * @return 结果
     */
    public int updateHospitalReviewCycle(HospitalReviewCycle hospitalReviewCycle);

    /**
     * 批量删除医院评审阶段周期
     *
     * @param ids 需要删除的医院评审阶段周期主键集合
     * @return 结果
     */
    public int deleteHospitalReviewCycleByIds(Long[] ids);

    /**
     * 删除医院评审阶段周期信息
     *
     * @param id 医院评审阶段周期主键
     * @return 结果
     */
    public int deleteHospitalReviewCycleById(Long id);

    /**
     * 批量插入
     * @param hospitalReviewCycleList
     * @return
     */
    public int insertHospitalReviewCycleList(List<HospitalReviewCycle> hospitalReviewCycleList);

    List<HospitalReviewCycle> selectHospitalReviewCycleByApplyNo(String applyNo);

    int updateHospitalReviewCycleByApplyNo(HospitalReviewCycle hospitalReviewCycle);

    AjaxResult refuseHospitalCycle(HospitalReviewCycle hospitalReviewCycle);

    HospitalReviewCycle selectByApplyNoAndStageValue(String applyNo, String cycleStage);

    String updateHospitalReviewCycleList(List<HospitalReviewCycle> hospitalReviewCycleList);
}
