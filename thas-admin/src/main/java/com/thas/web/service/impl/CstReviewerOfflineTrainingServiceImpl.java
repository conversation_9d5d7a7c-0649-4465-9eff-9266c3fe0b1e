package com.thas.web.service.impl;

import java.util.List;

import com.thas.common.utils.DateUtils;
import com.thas.web.domain.CstReviewerOfflineTraining;
import com.thas.web.domain.vo.OfflineTrainingRegisteredVo;
import com.thas.web.mapper.CstReviewerOfflineTrainingMapper;
import com.thas.web.service.ICstReviewerOfflineTrainingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 评审员端线下培训管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-02-17
 */
@Service
public class CstReviewerOfflineTrainingServiceImpl implements ICstReviewerOfflineTrainingService {
    @Autowired
    private CstReviewerOfflineTrainingMapper cstReviewerOfflineTrainingMapper;

    @Override
    public int selectCountByTrainingId(String trainingId) {

        return cstReviewerOfflineTrainingMapper.selectCountByTrainingId(trainingId);
    }

    /**
     * 查询评审员端线下培训管理
     *
     * @param id 评审员端线下培训管理主键
     * @return 评审员端线下培训管理
     */
    @Override
    public CstReviewerOfflineTraining selectCstReviewerOfflineTrainingById(Long id) {
        return cstReviewerOfflineTrainingMapper.selectCstReviewerOfflineTrainingById(id);
    }

    /**
     * 查询评审员端线下培训管理列表
     *
     * @param cstReviewerOfflineTraining 评审员端线下培训管理
     * @return 评审员端线下培训管理
     */
    @Override
    public List<CstReviewerOfflineTraining> selectCstReviewerOfflineTrainingList(CstReviewerOfflineTraining cstReviewerOfflineTraining) {
        return cstReviewerOfflineTrainingMapper.selectCstReviewerOfflineTrainingList(cstReviewerOfflineTraining);
    }

    /**
     * 新增评审员端线下培训管理
     *
     * @param cstReviewerOfflineTraining 评审员端线下培训管理
     * @return 结果
     */
    @Override
    public int insertCstReviewerOfflineTraining(CstReviewerOfflineTraining cstReviewerOfflineTraining) {
        cstReviewerOfflineTraining.setCreateTime(DateUtils.getNowDate());
        return cstReviewerOfflineTrainingMapper.insertCstReviewerOfflineTraining(cstReviewerOfflineTraining);
    }

    /**
     * 修改评审员端线下培训管理
     *
     * @param cstReviewerOfflineTraining 评审员端线下培训管理
     * @return 结果
     */
    @Override
    public int updateCstReviewerOfflineTraining(CstReviewerOfflineTraining cstReviewerOfflineTraining) {
        cstReviewerOfflineTraining.setUpdateTime(DateUtils.getNowDate());
        return cstReviewerOfflineTrainingMapper.updateCstReviewerOfflineTraining(cstReviewerOfflineTraining);
    }

    @Override
    public int updateStatusByAccountId(String accountId, int status) {
        return cstReviewerOfflineTrainingMapper.updateStatusByAccountId(accountId, status);
    }

    @Override
    public int batchInsertOfflineTraining(List<CstReviewerOfflineTraining> cstReviewerOfflineTrainingList) {
        return cstReviewerOfflineTrainingMapper.batchInsertOfflineTraining(cstReviewerOfflineTrainingList);
    }

    @Override
    public List<CstReviewerOfflineTraining> selectCstReviewerOfflineTrainingByAccountId(String accountId) {
        return cstReviewerOfflineTrainingMapper.selectCstReviewerOfflineTrainingByAccountId(accountId);
    }

    @Override
    public List<OfflineTrainingRegisteredVo> selectSysUserByTrainingId(String trainingId) {
        return cstReviewerOfflineTrainingMapper.selectSysUserByTrainingId(trainingId);
    }

    @Override
    public int deleteCstReviewerOfflineTraining(CstReviewerOfflineTraining cstReviewerOfflineTraining) {
        return cstReviewerOfflineTrainingMapper.deleteCstReviewerOfflineTraining(cstReviewerOfflineTraining);
    }

    @Override
    public int updateInfoByAccountIdAndTraId(CstReviewerOfflineTraining cstReviewerOfflineTraining) {
        return cstReviewerOfflineTrainingMapper.updateInfoByAccountIdAndTraId(cstReviewerOfflineTraining);
    }
}
