package com.thas.web.service.impl;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import cn.hutool.core.util.StrUtil;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.enums.ServiceExceptionEnum;
import com.thas.common.exception.ServiceException;
import com.thas.common.utils.DateUtils;
import com.thas.system.domain.vo.UserVo;
import com.thas.system.mapper.SysUserMapper;
import com.thas.web.domain.FileInfoDTO;
import com.thas.web.domain.ReviewTraPractice;
import com.thas.web.domain.TraineesPractice;
import com.thas.web.domain.vo.FileInfoVO;
import com.thas.web.domain.vo.ReviewerTraineesRecVO;
import com.thas.web.dto.TraineesPracticeDTO;
import com.thas.web.mapper.TraineesPracticeMapper;
import com.thas.web.service.CommonService;
import com.thas.web.service.ITraineesPracticeService;
import com.thas.web.service.IUploadFileInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 评审学员实践记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-05-31
 */
@Service
public class TraineesPracticeServiceImpl implements ITraineesPracticeService {
    @Autowired
    private TraineesPracticeMapper traineesPracticeMapper;

    @Autowired
    private IUploadFileInfoService uploadFileInfoService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private SysUserMapper sysUserMapper;

    /**
     * 查询评审学员实践记录
     *
     * @param id 评审学员实践记录主键
     * @return 评审学员实践记录
     */
    @Override
    public TraineesPractice selectTraineesPracticeById(Long id) {
        return traineesPracticeMapper.selectTraineesPracticeById(id);
    }

    /**
     * 查询评审学员实践记录列表
     *
     * @param traineesPractice 评审学员实践记录
     * @return 评审学员实践记录
     */
    @Override
    public List<TraineesPractice> selectTraineesPracticeList(TraineesPractice traineesPractice) {
        return traineesPracticeMapper.selectTraineesPracticeList(traineesPractice);
    }

    @Override
    public ReviewerTraineesRecVO getFileInfoAndReviewerById(Long id) {
        TraineesPractice traineesPractice = selectTraineesPracticeById(id);
        if (Objects.isNull(traineesPractice)) {
            return null;
        }
        ReviewerTraineesRecVO reviewerTraineesRecVO = new ReviewerTraineesRecVO();
        // 组装文件详情
        String fileId = traineesPractice.getSignFileId();
        if (StrUtil.isNotEmpty(fileId)) {
            FileInfoDTO fileInfoDTO = uploadFileInfoService.getUploadFileInfoById(Integer.valueOf(fileId));
            FileInfoVO fileInfoVO = commonService.fileInfoDtoToVo(fileInfoDTO);
            reviewerTraineesRecVO.setFileInfo(fileInfoVO);
        }
        return reviewerTraineesRecVO;
    }

    /**
     * 新增评审学员实践记录
     *
     * @param traineesPractice 评审学员实践记录
     * @return 结果
     */
    @Override
    public int insertTraineesPractice(TraineesPractice traineesPractice) {
        traineesPractice.setCreateTime(DateUtils.getNowDate());
        return traineesPracticeMapper.insertTraineesPractice(traineesPractice);
    }

    /**
     * 修改评审学员实践记录
     *
     * @param traineesPractice 评审学员实践记录
     * @return 结果
     */
    @Override
    public int updateTraineesPractice(TraineesPractice traineesPractice) {
        traineesPractice.setUpdateTime(DateUtils.getNowDate());
        return traineesPracticeMapper.updateTraineesPractice(traineesPractice);
    }

    /**
     * 批量删除评审学员实践记录
     *
     * @param ids 需要删除的评审学员实践记录主键
     * @return 结果
     */
    @Override
    public int deleteTraineesPracticeByIds(Long[] ids) {
        return traineesPracticeMapper.deleteTraineesPracticeByIds(ids);
    }

    @Override
    public AjaxResult insertPractice(List<TraineesPracticeDTO> traineesPracticeDTOList) {
        // 添加之前先删除pra_id对应的记录，防止重复添加
        String praId = traineesPracticeDTOList.get(0).getPraId();
        int delRes = traineesPracticeMapper.deleteTraineesPracticeByPraId(praId);
        int i = traineesPracticeMapper.batchInsertTraineesPractice(traineesPracticeDTOList);
        return AjaxResult.success();
    }

    @Override
    public List<TraineesPractice> selectTraineesPracticeByAccountId(String accountId) {
        return traineesPracticeMapper.selectTraineesPracticeByAccountId(accountId);
    }

    /**
     * 删除评审学员实践记录信息
     *
     * @param id 评审学员实践记录主键
     * @return 结果
     */
    @Override
    public int deleteTraineesPracticeById(Long id) {
        return traineesPracticeMapper.deleteTraineesPracticeById(id);
    }
}
