package com.thas.web.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.thas.common.constant.Constants;
import com.thas.common.constant.Constants.HospitalConstants;
import com.thas.common.constant.UserConstants;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.core.domain.entity.ReviewerAcc;
import com.thas.common.core.domain.entity.SysRole;
import com.thas.common.core.domain.entity.SysUser;
import com.thas.common.core.domain.model.LoginUser;
import com.thas.common.core.redis.RedisCache;
import com.thas.common.enums.AutSaAudRoleEnum;
import com.thas.common.enums.AutSaAudStatusEnum;
import com.thas.common.enums.ServiceExceptionEnum;
import com.thas.common.enums.TrainingEvaluateResultEnum;
import com.thas.common.exception.ServiceException;
import com.thas.common.utils.PageUtils;
import com.thas.common.utils.SecurityUtils;
import com.thas.common.utils.StringUtils;
import com.thas.generator.util.NumberGenUtils;
import com.thas.system.domain.SysUserOnline;
import com.thas.system.domain.vo.UserVo;
import com.thas.system.mapper.SysDictDataMapper;
import com.thas.system.mapper.SysRoleMapper;
import com.thas.system.mapper.SysUserMapper;
import com.thas.system.mapper.SysUserRoleMapper;
import com.thas.system.service.ISysRoleService;
import com.thas.system.service.ISysUserOnlineService;
import com.thas.system.service.ISysUserService;
import com.thas.web.domain.AutRecord;
import com.thas.web.domain.CertificateAbility;
import com.thas.web.domain.CstOfflineTrainingManagement;
import com.thas.web.domain.CstReviewerOfflineTraining;
import com.thas.web.domain.FileInfoDTO;
import com.thas.web.domain.HospitalBaseInfo;
import com.thas.web.domain.MessageTemplate;
import com.thas.web.domain.QueryCstOfflineTrainingDTO;
import com.thas.web.domain.ReviewerBaseInfo;
import com.thas.web.domain.ReviewerFieldInfo;
import com.thas.web.domain.TraineesPractice;
import com.thas.web.domain.TrainingEvaluateResult;
import com.thas.web.domain.vo.FileInfoVO;
import com.thas.web.domain.vo.OfflineTrainingRegisteredVo;
import com.thas.web.domain.vo.ReviewInterestVO;
import com.thas.web.domain.vo.TraineesReviewRecVO;
import com.thas.web.dto.AgainRevInfoSubmitRequest;
import com.thas.web.dto.CheckParamOnlyRequest;
import com.thas.web.dto.QueryBaseConditionDTO;
import com.thas.web.dto.QueryReviewerListDTO;
import com.thas.web.dto.RevOfflineTraDTO;
import com.thas.web.dto.ReviewManageDTO;
import com.thas.web.dto.ReviewManageVO;
import com.thas.web.dto.ReviewerBaseInfoDTO;
import com.thas.web.dto.ReviewerBaseInfoDetailVO;
import com.thas.web.dto.ReviewerExportRequest;
import com.thas.web.dto.ReviewerFieldInfoVO;
import com.thas.web.dto.SheetDTO;
import com.thas.web.dto.SysUserBaseInfo;
import com.thas.web.dto.TempRevInfoRequest;
import com.thas.web.dto.UpdFileShareTempDataDto;
import com.thas.web.dto.UpdateReviewerRoleReq;
import com.thas.web.mapper.CertificateAbilityMapper;
import com.thas.web.mapper.HospitalBaseInfoMapper;
import com.thas.web.mapper.HospitalReviewerMapper;
import com.thas.web.mapper.MessageTemplateMapper;
import com.thas.web.mapper.ReviewerBaseInfoMapper;
import com.thas.web.mapper.ReviewerFieldInfoMapper;
import com.thas.web.mapper.TrainingEvaluateResultMapper;
import com.thas.web.service.CommonService;
import com.thas.web.service.IAutRecordService;
import com.thas.web.service.IAutSaRelationService;
import com.thas.web.service.ICertificateAbilityService;
import com.thas.web.service.ICstOfflineTrainingManagementService;
import com.thas.web.service.ICstReviewerOfflineTrainingService;
import com.thas.web.service.IFileShareService;
import com.thas.web.service.IHospitalPreExamService;
import com.thas.web.service.IHospitalReviewerService;
import com.thas.web.service.IMessageSendRecordService;
import com.thas.web.service.IReviewerBaseInfoService;
import com.thas.web.service.IReviewerFieldInfoService;
import com.thas.web.service.IReviewerTmpAccService;
import com.thas.web.service.ITraineesPracticeService;
import com.thas.web.service.IUploadFileInfoService;
import com.thas.web.utils.HutoolExcelUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.thas.common.utils.SecurityUtils.getUsername;


/**
 * 评审员基本信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-20
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class ReviewerBaseInfoServiceImpl implements IReviewerBaseInfoService {

    @Autowired
    private ReviewerBaseInfoMapper reviewerBaseInfoMapper;

    @Autowired
    private CertificateAbilityMapper certificateAbilityMapper;

    @Autowired
    private TrainingEvaluateResultMapper trainingEvaluateResultMapper;

    @Autowired
    private SysDictDataMapper sysDictDataMapper;

    @Autowired
    private IAutSaRelationService autSaRelationService;

    @Autowired
    private IUploadFileInfoService iUploadFileInfoService;

    @Autowired
    private SysRoleMapper sysRoleMapper;

    @Autowired
    private ReviewerFieldInfoMapper reviewerFieldInfoMapper;

    @Autowired
    private ITraineesPracticeService traineesPracticeService;

    @Autowired
    private IReviewerFieldInfoService iReviewerFieldInfoService;

    @Autowired
    private ICertificateAbilityService iCertificateAbilityService;

    @Autowired
    private IHospitalPreExamService iHospitalPreExamService;

    @Autowired
    private IHospitalReviewerService iHospitalReviewerService;

    @Autowired
    private ICstReviewerOfflineTrainingService iCstReviewerOfflineTrainingService;

    @Autowired
    private ICstOfflineTrainingManagementService iCstOfflineTrainingManagementService;

    @Autowired
    private IReviewerTmpAccService reviewerTmpAccService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private ISysUserService iSysUserService;

    @Autowired
    private ISysRoleService iSysRoleService;

    @Autowired
    private IAutRecordService iAutRecordService;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private IFileShareService fileShareService;

    @Autowired
    private HospitalReviewerMapper hospitalReviewerMapper;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private ISysUserOnlineService userOnlineService;

    @Autowired
    private MessageTemplateMapper messageTemplateMapper;

    @Autowired
    private IMessageSendRecordService messageSendRecordService;

    @Autowired
    private HospitalBaseInfoMapper hospitalBaseInfoMapper;

    @Value("${user.resetPassword}")
    private String resetPassword;

    @Value("${user.encryptResetPassword}")
    private String encryptResetPassword;


    @Override
    public AjaxResult reviewerBaseInfoSubmit(ReviewerBaseInfoDTO reviewerBaseInfoDTO, boolean isTmp) {
        String accountId;
        // 获取评审员基本信息
        ReviewerBaseInfo reviewerBaseInfo = reviewerBaseInfoDTO.getReviewerBaseInfo();
        //【医院端】14. 医院申请流程，评审员申请流程，法人性别均去掉“未知”；
        if (reviewerBaseInfo != null && reviewerBaseInfo.getReviewerGender() != null &&
                !(reviewerBaseInfo.getReviewerGender() == Constants.INT_ZERO || reviewerBaseInfo.getReviewerGender() == Constants.INT_ONE)) {
            throw new ServiceException("评审员性别不能为“其他”，请确认后重试！");
        }
        boolean isAdmin = false;
        try {
            isAdmin = iSysRoleService.isAdmin();
        } catch (Exception e) {
            // 如果获取当前用户异常，直接默认为false
            e.printStackTrace();
        }
        if (isTmp) {
            // 如果是临时数据提交，则不需要校验登录角色
            accountId = reviewerBaseInfo.getAccountId();
        } else {
            // 获取当前操作用户角色
            if (isAdmin) {
                // 如果是管理员登录
                log.info("当前为管理员操作");
                if (Objects.isNull(reviewerBaseInfo) || CharSequenceUtil.isEmpty((accountId = reviewerBaseInfo.getAccountId()))) {
                    throw new ServiceException(ServiceExceptionEnum.REVIEWER_ERROR_1000006);
                }
                //查询评审员信息，如果不为空且不为待审核状态，不允许操作,管理员操作防重
                //基本数据认证状态:0待审核 1审核通过 2审核拒绝
                //如果有分组入参，不用校验
                if (CollectionUtil.isEmpty(reviewerBaseInfoDTO.getFields())) {
                    if (reviewerBaseInfoDTO.getReviewerBaseInfo() != null &&
                            StringUtils.isNotEmpty(reviewerBaseInfoDTO.getReviewerBaseInfo().getCertificateNumber()) &&
                            (StringUtils.isNotEmpty(reviewerBaseInfoDTO.getReviewerBaseInfo().getReviewerMobile()) ||
                                    StringUtils.isNotEmpty(reviewerBaseInfoDTO.getReviewerBaseInfo().getReviewerEmail()))) {
                        //当为管理员，且入参信息不为空时，检验唯一性
                        CheckParamOnlyRequest req = new CheckParamOnlyRequest();
                        req.setContactsPhone(reviewerBaseInfoDTO.getReviewerBaseInfo().getReviewerMobile());
                        req.setEmail(reviewerBaseInfoDTO.getReviewerBaseInfo().getReviewerEmail());
                        req.setCertificateNumber(reviewerBaseInfoDTO.getReviewerBaseInfo().getCertificateNumber());
                        this.checkRevBaseInfo(req);
                    }


                    ReviewerBaseInfo qryReviewerBaseInfo = reviewerBaseInfoMapper.selectReviewerBaseInfoByAccountId(accountId);
                    if (ObjectUtil.isNotEmpty(qryReviewerBaseInfo) && Constants.INT_ZERO != qryReviewerBaseInfo.getInfoStatus()) {
                        throw new ServiceException("其他管理员已审核，请刷新页面查看");
                    } else if (CollectionUtil.isNotEmpty(reviewerBaseInfoDTO.getFields()) && StringUtils.isNotEmpty(reviewerBaseInfo.getAccountId())) {
                        //如果已分配的分组与查询的有冲突，报异常
                        List<ReviewerFieldInfoVO> fieldInfoVOList = reviewerFieldInfoMapper.selectReviewerFieldInfoByAccountId(reviewerBaseInfo.getAccountId());
                        boolean flag = fieldInfoVOList.stream().anyMatch(o -> reviewerBaseInfoDTO.getFields().contains(o.getFieldCode()));
                        //不为空，且有冲突
                        if (CollectionUtil.isNotEmpty(fieldInfoVOList) && flag) {
                            throw new ServiceException("其他管理员已分配分组，请刷新页面查看");
                        }
                    }
                }

            } else {
                // 不是管理，获取登录用户
                log.info("当前为评审员用户操作");
                LoginUser loginUser = SecurityUtils.getLoginUser();
                if (Objects.isNull(loginUser) || CharSequenceUtil.isEmpty((accountId = String.valueOf(loginUser.getUserId())))) {
                    throw new ServiceException(ServiceExceptionEnum.REVIEWER_ERROR_1000000);
                }
            }
        }

        // 评审员基本信息为空
        if (Objects.isNull(reviewerBaseInfo)) {
            reviewerBaseInfo = new ReviewerBaseInfo();
        }

        // 如果当前登录角色是管理员登录
        if (isAdmin) {
            reviewerBaseInfo.setAuthPersonId(SecurityUtils.getUserId().toString());
            reviewerBaseInfo.setAuthPersonName(SecurityUtils.getNickName());
            reviewerBaseInfo.setAuthDate(DateUtil.now());
        }

        // 获取对应工作经历
        List<JSONObject> workExperienceList = reviewerBaseInfoDTO.getWorkExperienceList();
        if (CollUtil.isNotEmpty(workExperienceList)) {
            reviewerBaseInfo.setWorkExperience(JSONUtil.toJsonStr(workExperienceList));
        }

        //获取对应证书
        List<JSONObject> certificateList = reviewerBaseInfoDTO.getCertificateList();
        if (CollUtil.isNotEmpty(certificateList)) {
            reviewerBaseInfo.setCertificateList(JSONUtil.toJsonStr(certificateList));
        }

        // 插入表 通过account_id判断是否有记录，如果有则更新操作
        int reBaRes = insertOrUpdateByAccountId(reviewerBaseInfo);
        if (reBaRes <= 0) {
            throw new ServiceException(ServiceExceptionEnum.REVIEWER_ERROR_1000001);
        }

        // 获取对应证书
        List<CertificateAbility> certificateAbilityList = reviewerBaseInfoDTO.getCertificateAbilityList();
        iCertificateAbilityService.updateStatusByProcessCode(accountId, 2);
        if (CollUtil.isNotEmpty(certificateAbilityList)) {
            // 添加证书将之前的证书置为失效
            certificateAbilityList.forEach(certificateAbility -> {
                certificateAbility.setProcessCode(accountId);
                int cerAbiRes = certificateAbilityMapper.insertCertificateAbility(certificateAbility);
                if (cerAbiRes <= 0) {
                    throw new ServiceException(ServiceExceptionEnum.HOSPITAL_ERROR_1000007);
                }
            });
        }

        // 对应的领域列表
        List<String> fields = reviewerBaseInfoDTO.getFields();
        List<ReviewerFieldInfo> reviewerFieldInfoList = new ArrayList<>();
        if (CollUtil.isNotEmpty(fields)) {
            fields.forEach(field -> {
                ReviewerFieldInfo reviewerFieldInfo = new ReviewerFieldInfo();
                reviewerFieldInfo.setAccountId(accountId);
                reviewerFieldInfo.setFieldCode(field);
                reviewerFieldInfoList.add(reviewerFieldInfo);
            });
            // 将之前的记录置为失效
            iReviewerFieldInfoService.updateStatusByAccountId(accountId, 2);
            int batchRes = reviewerFieldInfoMapper.batchInsertReviewerFieldInfo(reviewerFieldInfoList);
            log.info("批量添加:{}, res:{}", JSONUtil.toJsonStr(reviewerFieldInfoList), batchRes);
            if (batchRes <= 0) {
                throw new ServiceException(ServiceExceptionEnum.REVIEWER_ERROR_1000004);
            }
        }
        if (fields != null && fields.size() == 0) {
            // 将之前的记录置为失效
            iReviewerFieldInfoService.updateStatusByAccountId(accountId, 2);
        }

        if (isAdmin) {
            notifyReviewAuthState(reviewerBaseInfo);
        }
        return AjaxResult.success();
    }

    /**
     * 通知评审员审核结果
     *
     * @param reviewerBaseInfo 评审员信息
     */
    private void notifyReviewAuthState(ReviewerBaseInfo reviewerBaseInfo) {
        // 查询基本信息，管理员提交可能没有送基本信息。
        reviewerBaseInfo = reviewerBaseInfoMapper.selectReviewerBaseInfoByAccountId(reviewerBaseInfo.getAccountId());
        Integer rejAuthStatus = 2;

        if (rejAuthStatus.equals(reviewerBaseInfo.getAuthStatus())) {
            notifyReviewRej(reviewerBaseInfo);
        }
    }

    /**
     * 通知拒绝
     *
     * @param reviewerBaseInfo 评审员基本信息
     */
    private void notifyReviewRej(ReviewerBaseInfo reviewerBaseInfo) {
        // 生成验证码
        String code = NumberGenUtils.getVerificationCode();

        String redisKey = Constants.TEMP_QRY_REV_INFO_KEY_PRE + reviewerBaseInfo.getCertificateNumber();

        String content = createRevRejMsg(code);

        // 存入redis
        redisCache.setCacheObject(redisKey, code, Constants.REV_REJ_CODE_TIME, TimeUnit.DAYS);

        if (StringUtils.isNotEmpty(reviewerBaseInfo.getReviewerEmail())) {
            messageSendRecordService.sendMsgFromEmail(content, reviewerBaseInfo.getReviewerEmail(), "评审人员审核结果通知");
        }
        if (StringUtils.isNotEmpty(reviewerBaseInfo.getReviewerMobile())) {
            messageSendRecordService.sendMsgFromSms(content, reviewerBaseInfo.getReviewerMobile());
        }
    }

    /**
     * 创建消息模板
     *
     * @param code code
     * @return content
     */
    private String createRevRejMsg(String code) {
        long id = 5L;
        MessageTemplate messageTemplate = messageTemplateMapper.selectMessageTemplateById(id);
        String content = messageTemplate.getContent();
        return String.format(content, code);
    }

    @Override
    public AjaxResult reviewerBaseInfoTmpSubmit(ReviewerBaseInfoDTO reviewerBaseInfoDTO) {
        ReviewerBaseInfo reviewerBaseInfo = reviewerBaseInfoDTO.getReviewerBaseInfo();
        if (Objects.isNull(reviewerBaseInfo) || StrUtil.isEmpty(reviewerBaseInfo.getCertificateNumber())) {
            throw new ServiceException(ServiceExceptionEnum.REVIEWER_ERROR_1000009);
        }

        // 免登数据提交需要 判断对应证件号在reviewer_tmp_acc表中对应的数据。
        // 如果有数据则直接使用，如果没有则生成一条新数据。
        String accountId = reviewerTmpAccService.genTmpAccountId(reviewerBaseInfo.getCertificateNumber());
        reviewerBaseInfoDTO.getReviewerBaseInfo().setAccountId(accountId);

        // 校验是否已经存在数据
        checkByCertificateNumber(reviewerBaseInfo);

        reviewerBaseInfoSubmit(reviewerBaseInfoDTO, true);
        return AjaxResult.success(accountId);
    }

    private void checkByCertificateNumber(ReviewerBaseInfo reviewerBaseInfo) {
        // 如果当前操作用户是管理员，则不需要校验在途数据
        // 当前接口可能为未登录，获取用户id异常
        boolean isAdmin;
        try {
            isAdmin = iSysRoleService.isAdmin();
        } catch (Exception e) {
            isAdmin = false;
        }
        if (isAdmin) {
            return;
        }

        String certificateNumber = reviewerBaseInfo.getCertificateNumber();
        List<ReviewerBaseInfo> reviewerBaseInfoList
                = reviewerBaseInfoMapper.selectReviewerBaseInfoByCertificateNumber(certificateNumber);
        if (CollUtil.isNotEmpty(reviewerBaseInfoList)) {
            throw new ServiceException("有在途数据，申请失败", 400);
        }
    }

    @Override
    public AjaxResult querySubmitStatus(QueryBaseConditionDTO queryBaseConditionDTO) {
        String certificateNumber = queryBaseConditionDTO.getCommonId();
        if (CharSequenceUtil.isEmpty(certificateNumber)) {
            throw new ServiceException(ServiceExceptionEnum.REVIEWER_ERROR_1000009);
        }
        Map<String, Object> result = MapUtil.newHashMap();
        String tmpAccountId = reviewerTmpAccService.getTmpAccountId(certificateNumber);
        ReviewerBaseInfo reviewerBaseInfo;
        if (CharSequenceUtil.isEmpty(tmpAccountId)) {
            // 如果临时accountId为空，直接通过身份证号码查询。
            ReviewerBaseInfo query = new ReviewerBaseInfo();
            query.setCertificateNumber(certificateNumber);
            // 身份证是唯一主键，直接拿第一个就好。
            List<ReviewerBaseInfo> reviewerBaseInfoList = reviewerBaseInfoMapper.selectReviewerBaseInfoList(query);

            reviewerBaseInfo = CollUtil.isEmpty(reviewerBaseInfoList) ? null : reviewerBaseInfoList.get(0);
        } else {
            reviewerBaseInfo = reviewerBaseInfoMapper.selectReviewerBaseInfoByAccountId(tmpAccountId);
        }
        if (Objects.nonNull(reviewerBaseInfo)) {
            result.put("infoStatus", reviewerBaseInfo.getInfoStatus());
            result.put("authStatus", reviewerBaseInfo.getAuthStatus());
            result.put("createTime", reviewerBaseInfo.getCreateTime());
            result.put("updateTime", reviewerBaseInfo.getUpdateTime());
        }
        return AjaxResult.success(result);
    }

    @Override
    public List<QueryReviewerListDTO> queryReviewerList(QueryReviewerListDTO queryReviewerListDTO) {
        List<QueryReviewerListDTO> queryReviewerListDTOList = reviewerBaseInfoMapper.selectReviewerBaseInfos(queryReviewerListDTO);

        // 查询评审员对应的领域列表
        this.queryReviewerFieldInfo(queryReviewerListDTOList);

        this.queryEvaluateResult(queryReviewerListDTOList);

        return queryReviewerListDTOList;
    }

    private void queryReviewerFieldInfo(List<QueryReviewerListDTO> queryReviewerListDTOList) {
        if (CollUtil.isEmpty(queryReviewerListDTOList)) {
            return;
        }

        List<String> accountIdList = queryReviewerListDTOList.stream()
                .map(QueryReviewerListDTO::getAccountId)
                .collect(Collectors.toList());

        List<ReviewerFieldInfoVO> reviewerFieldInfoVOS =
                reviewerFieldInfoMapper.selectReviewerFieldInfoByAccountIdList(accountIdList);

        if (CollUtil.isEmpty(reviewerFieldInfoVOS)) {
            return;
        }

        Map<String, List<ReviewerFieldInfoVO>> reviewerFieldInfoVOListMap = reviewerFieldInfoVOS.stream()
                .collect(Collectors.groupingBy(ReviewerFieldInfoVO::getAccountId));

        for (QueryReviewerListDTO queryReviewerListDTO : queryReviewerListDTOList) {
            List<ReviewerFieldInfoVO> fieldInfoVOS = reviewerFieldInfoVOListMap.get(queryReviewerListDTO.getAccountId());
            queryReviewerListDTO.setReviewerFieldInfoVOList(fieldInfoVOS);
        }
    }

    private void queryEvaluateResult(List<QueryReviewerListDTO> queryReviewerListDTOList) {
        if (CollUtil.isEmpty(queryReviewerListDTOList)) {
            return;
        }

        // 查询training_evaluate_result表
        List<Long> idList = new ArrayList<>();
        for (QueryReviewerListDTO queryReviewerListDTO : queryReviewerListDTOList) {
            String accountId = queryReviewerListDTO.getAccountId();
            long id;
            try {
                id = Long.parseLong(accountId);
            } catch (NumberFormatException e) {
                continue;
            }
            idList.add(id);
        }

        List<TrainingEvaluateResult> trainingEvaluateResultList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(idList)) {
            trainingEvaluateResultList = trainingEvaluateResultMapper.selectByIdList(idList);
        }

        if (CollUtil.isEmpty(trainingEvaluateResultList)) {
            return;
        }
        Map<Long, List<TrainingEvaluateResult>> trainingEvaluateResultListMap = trainingEvaluateResultList.stream()
                .collect(Collectors.groupingBy(TrainingEvaluateResult::getTraineesAssessorId));
        for (QueryReviewerListDTO reviewerListDTO : queryReviewerListDTOList) {
            long id;
            String accountId = reviewerListDTO.getAccountId();
            try {
                id = Long.parseLong(accountId);
            } catch (Exception e) {
                // 当前异常不予处理
                continue;
            }
            List<TrainingEvaluateResult> evaluateResultList = trainingEvaluateResultListMap.get(id);
            List<QueryReviewerListDTO.EvaluateResult> evaluateResults =
                    BeanUtil.copyToList(evaluateResultList, QueryReviewerListDTO.EvaluateResult.class);
            reviewerListDTO.setEvaluateResultList(evaluateResults);
        }
    }

    @Override
    public List<ReviewerBaseInfo> queryReviewerTmpList(ReviewerBaseInfo reviewerBaseInfo) {
        return reviewerBaseInfoMapper.queryReviewerTmpList(reviewerBaseInfo);
    }

    @Override
    public AjaxResult reviewerAccountDistribute(SysUser sysUser) {
        // 校验用户唯一性参数
        this.checkSysUser(sysUser);

        sysUser.setCreateBy(getUsername());
        sysUser.setPassword(SecurityUtils.encryptPassword(encryptResetPassword));

        reParam(sysUser);
        // 新增用户信息
        int rows = userMapper.insertUser(sysUser);
        // 新增用户岗位关联
        iSysUserService.insertUserPost(sysUser);
        // 新增用户与角色管理
        iSysUserService.insertUserRole(sysUser);
        ReviewerAcc reviewerAcc = sysUser.getReviewerAcc();
        // 将生成的userId更新到对应的reviewer_base_info表中
        reviewerBaseInfoMapper.updateAccountId(String.valueOf(sysUser.getUserId()), reviewerAcc.getAccountId());
        // 将生成的userId更新到对应的certificate_ability表中
        certificateAbilityMapper.updateAccountId(String.valueOf(sysUser.getUserId()), reviewerAcc.getAccountId());
        // 将生成的userId更新到对应的reviewer_field_info表中
        reviewerFieldInfoMapper.updateAccountId(String.valueOf(sysUser.getUserId()), reviewerAcc.getAccountId());
        // 删除临时表reviewer_tmp_acc表中的记录
        reviewerTmpAccService.deleteReviewerTmpAccByCertificateNumber(reviewerAcc.getCertificateNumber());

        // 将评审员申请的临时文件挂到当前userId下。
        UpdFileShareTempDataDto updFileShareTempDataDto = new UpdFileShareTempDataDto();
        updFileShareTempDataDto.setTempCode(reviewerAcc.getAccountId());
        updFileShareTempDataDto.setUserId(sysUser.getUserId());
        updFileShareTempDataDto.setNickName(sysUser.getNickName());
        updFileShareTempDataDto.setOwnerId(String.valueOf(sysUser.getUserId()));
        // 整体修改
        fileShareService.updateByOwnerId(updFileShareTempDataDto);

        String roleKey = this.queryRoleKeyByRoleId(sysUser.getRoleId());
        updFileShareTempDataDto.setTempCode(sysUser.getUserId().toString());
        updFileShareTempDataDto.setRoleKey(roleKey);
        updFileShareTempDataDto.setOldRoleKey(Constants.HospitalConstants.ROLE_TRAINEES_ASSESSOR);
        fileShareService.updateByOwnerIdAndRoleKey(updFileShareTempDataDto);

        // 短信邮件通知用户
        this.notifyReviewAccount(sysUser.getUserId().toString(), sysUser.getUserName(), roleKey);
        return AjaxResult.success();
    }

    /**
     * 短信邮件通知用户
     *
     * @param accountId 用户id
     * @param userName  用户名
     */
    private void notifyReviewAccount(String accountId, String userName, String roleKey) {
        // 获取到当前用户的信息
        ReviewerBaseInfo reviewerBaseInfo = selectReviewerBaseInfoByAccountId(accountId);
        String reviewerEmail = reviewerBaseInfo.getReviewerEmail();
        String reviewerMobile = reviewerBaseInfo.getReviewerMobile();

        // 查询消息模板message_template表
        long id = 3L;
        if (HospitalConstants.ROLE_TRAINEES_ASSESSOR.equals(roleKey)) {
            id = 4L;
        }
        MessageTemplate messageTemplate = messageTemplateMapper.selectMessageTemplateById(id);
        if (Objects.isNull(messageTemplate)) {
            throw new ServiceException(String.format("id[%s]消息模板数据为空", id), 500);
        }

        String content = messageTemplate.getContent();
        String reviewerName = reviewerBaseInfo.getReviewerName();
        if(HospitalConstants.ROLE_TRAINEES_ASSESSOR.equals(roleKey)){
            // 评审学员 替换成 XX先生/女士  评审员性别:0:男 1:女
            Integer reviewerGender = reviewerBaseInfo.getReviewerGender();
            String reviewerGenderStr = reviewerGender  != null &&  reviewerGender.intValue() == Constants.INT_ZERO ? "先生":"女士";
            reviewerName = reviewerName + reviewerGenderStr;
        }
        String msg = String.format(content, reviewerName, userName, resetPassword);
        if (StringUtils.isNotEmpty(reviewerEmail)) {
            messageSendRecordService.sendMsgFromEmail(msg, reviewerEmail, messageTemplate.getName());
        }

        try {
            if (StringUtils.isNotEmpty(reviewerMobile)) {
                messageSendRecordService.sendMsgFromSms(msg, reviewerMobile);
            }
        } catch (Exception e) {
            log.error("创建【{}】评审员账号时，发送手机短信失败，不抛异常正常创建账号，通知的手机号为：{}，内容为：{},异常信息e为:{}",
                    reviewerBaseInfo.getReviewerName(),reviewerMobile,msg,e);
        }

    }

    /**
     * 通过roleId查询roleKey
     *
     * @param roleId 角色id
     * @return roleKey
     */
    private String queryRoleKeyByRoleId(Long roleId) {
        SysRole sysRole = sysRoleMapper.selectRoleById(roleId);
        if (Objects.isNull(sysRole)) {
            throw new ServiceException(String.format("roleId[%s]角色信息为空", roleId), 500);
        }
        return sysRole.getRoleKey();
    }

    /**
     * 校验用户唯一性参数
     *
     * @param user 入参
     */
    private void checkSysUser(SysUser user) {
        if (Objects.isNull(user)) {
            throw new ServiceException("入参不能为空", 400);
        }
        if (UserConstants.NOT_UNIQUE.equals(userService.checkUserNameUnique(user.getUserName()))) {
            throw new ServiceException(String.format("新增用户[%s]失败，账号已存在!", user.getUserName()), 400);
        } else if (StringUtils.isNotEmpty(user.getPhonenumber())
                && UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user))) {
            throw new ServiceException(String.format("新增用户[%s]失败，手机号码已存在!", user.getUserName()), 400);
        } else if (StringUtils.isNotEmpty(user.getEmail())
                && UserConstants.NOT_UNIQUE.equals(userService.checkEmailUnique(user))) {
            throw new ServiceException(String.format("新增用户[%s]失败，邮箱账号已存在!", user.getUserName()), 400);
        }
    }

    private void reParam(SysUser sysUser) {
        ReviewerAcc reviewerAcc = sysUser.getReviewerAcc();
        String reviewerName = reviewerAcc.getReviewerName();
        String reviewerMobile = reviewerAcc.getReviewerMobile();
        String reviewerEmail = reviewerAcc.getReviewerEmail();
        Integer gender = reviewerAcc.getReviewerGender();
        if (CharSequenceUtil.isEmpty(reviewerName) || CharSequenceUtil.isEmpty(reviewerMobile)
                || CharSequenceUtil.isEmpty(reviewerEmail) || Objects.isNull(gender)) {
            throw new ServiceException("分配评审员账号，必填项 reviewerName|reviewerMobile|reviewerEmail|gender 为空！");
        }
        sysUser.setNickName(reviewerName);
        sysUser.setPhonenumber(reviewerMobile);
        sysUser.setEmail(reviewerEmail);
        String sex;
        if (gender == 1) {
            sex = "0";
        } else if (gender == 2) {
            sex = "1";
        } else {
            sex = "2";
        }
        sysUser.setSex(sex);
    }

    @Override
    public ReviewerBaseInfoDTO bingQryReviewerDetail(TempRevInfoRequest tempRevInfoRequest) {
        // 校验验证码是否正确
        checkTempCode(tempRevInfoRequest);
        // 通过身份证号查询填写的信息
        ReviewerBaseInfo reviewerBaseInfo = reviewerBaseInfoMapper.selectByCertificateNumber(tempRevInfoRequest.getCertificateNumber());
        if (Objects.isNull(reviewerBaseInfo)) {
            return new ReviewerBaseInfoDTO();
        }
        return queryReviewerDetail(reviewerBaseInfo.getAccountId());
    }

    /**
     * 临时查询详情，校验code
     *
     * @param request 入参
     */
    private void checkTempCode(TempRevInfoRequest request) {
        String queryRevInfoCode = request.getQueryRevInfoCode();
        // 获取redis保存的临时code
        String certificateNumber = request.getCertificateNumber();
        String redisKey = Constants.TEMP_QRY_REV_INFO_KEY_PRE + certificateNumber;
        String redisTempQueryCode = redisCache.getCacheObject(redisKey);
        if (!queryRevInfoCode.equals(redisTempQueryCode)) {
            throw new ServiceException("校验错误!");
        }
    }

    @Override
    public AjaxResult revAgainSubmit(AgainRevInfoSubmitRequest request) {
        // 校验验证码
        checkTempCode(request.getTempRevInfo());

        // 校验身份证号与accountId是否匹配
        checkCerNumber(request);

        // 保存数据
        reviewerBaseInfoSubmit(request, Boolean.TRUE);

        // 保存成功删除redis验证码
        String redisKey = Constants.TEMP_QRY_REV_INFO_KEY_PRE + request.getTempRevInfo().getCertificateNumber();
        redisCache.deleteObject(redisKey);

        return AjaxResult.success();
    }

    @Override
    public void checkParamOnly(CheckParamOnlyRequest req) {
        if (StringUtils.isBlank(req.getEmail()) && StringUtils.isBlank(req.getContactsPhone())) {
            throw new ServiceException("校验唯一性的数据入参不能为空", 500);
        }

        String role = req.getRole();
        if (HospitalConstants.ROLE_HOSPITAL.equals(role)) {
            checkHosBaseInfo(req);
        }
        if (HospitalConstants.ROLE_ASSESSOR.equals(role)) {
            checkRevBaseInfo(req);
        }
    }

    @Override
    public void checkHosBaseInfo(CheckParamOnlyRequest req) {
        if (StringUtils.isEmpty(req.getPracticeLicenseNo()) && StringUtils.isEmpty(req.getHospitalApplyNo())) {
            throw new ServiceException("医院信息-医疗机构执业许可证登记号或医院编码不能为空", 500);
        }

        if (StringUtils.isNotEmpty(req.getEmail())) {
            List<HospitalBaseInfo> hospitalBaseInfoList = hospitalBaseInfoMapper.qryHosBaseInfoByEmail(req.getEmail());
            hospitalBaseInfoListAnyMatch(hospitalBaseInfoList, req, "医院信息-邮箱已存在，请使用其他邮箱");
        }
        //联系人手机号
        if (StringUtils.isNotEmpty(req.getContactsPhone())) {
            HospitalBaseInfo hospitalBaseInfo = new HospitalBaseInfo();
            hospitalBaseInfo.setContactsPhone(req.getContactsPhone());
            hospitalBaseInfo.setStatus(Constants.INT_ONE);
            List<HospitalBaseInfo> hospitalBaseInfoList = hospitalBaseInfoMapper.selectHospitalBaseInfoList(hospitalBaseInfo);
            hospitalBaseInfoListAnyMatch(hospitalBaseInfoList, req, "医院信息-联系人手机号已存在，请使用其他联系人手机号");
        }

    }

    private void hospitalBaseInfoListAnyMatch(List<HospitalBaseInfo> hospitalBaseInfoList, CheckParamOnlyRequest req, String ms) {
        if (CollectionUtil.isNotEmpty(hospitalBaseInfoList)) {
            boolean flag = hospitalBaseInfoList.stream()
                    .anyMatch(hospitalBaseInfo ->
                            ObjectUtil.equal(hospitalBaseInfo.getPracticeLicenseNo(), req.getPracticeLicenseNo()) ||
                                    ObjectUtil.equal(hospitalBaseInfo.getApplyNo(), req.getHospitalApplyNo()));
            if (!flag) {
                throw new ServiceException(ms, 500);
            }
        }
    }

    private void checkRevBaseInfo(CheckParamOnlyRequest req) {
        if (StringUtils.isNotEmpty(req.getEmail())) {
            List<ReviewerBaseInfo> reviewerBaseInfoList = reviewerBaseInfoMapper.qryReviewerBaseInfoByEmail(req.getEmail());
            //入参的邮箱与查询的是否匹配，且入参的身份证号与查询的是否匹配， 匹配证明是自身的不报错
            //如果是新增，入参的身份证为空，那么有数据证明是其他人员的，直接报错
            reviewerInfoListAnyMatch(reviewerBaseInfoList, req, "邮箱已存在，请使用其他邮箱");
        }
        //联系人手机号
        if (StringUtils.isNotEmpty(req.getContactsPhone())) {
            ReviewerBaseInfo reviewerBaseInfo = new ReviewerBaseInfo();
            reviewerBaseInfo.setReviewerMobile(req.getContactsPhone());
            reviewerBaseInfo.setStatus(Constants.INT_ONE);
            List<ReviewerBaseInfo> reviewerBaseInfoList = reviewerBaseInfoMapper.selectReviewerBaseInfoList(reviewerBaseInfo);
            reviewerInfoListAnyMatch(reviewerBaseInfoList, req, "联系手机号已存在，请使用其他联系手机号");
        }
    }

    private void reviewerInfoListAnyMatch(List<ReviewerBaseInfo> reviewerBaseInfoList, CheckParamOnlyRequest req, String ms) {

        if (CollectionUtil.isNotEmpty(reviewerBaseInfoList)) {
            //入参身份证信息为空时，直接报错
            if (StringUtils.isEmpty(req.getCertificateNumber())) {
                throw new ServiceException(ms, 500);
            }
            //如果匹配的是自身的邮箱/手机不报错
            boolean flag = reviewerBaseInfoList.stream()
                    .anyMatch(reviewerBaseInf ->
                            ObjectUtil.equal(reviewerBaseInf.getCertificateNumber(), req.getCertificateNumber()));
            if (!flag) {
                throw new ServiceException(ms, 500);
            }
        }

    }

    private void checkCerNumber(AgainRevInfoSubmitRequest request) {
        if (Objects.isNull(request.getReviewerBaseInfo())
                || StringUtils.isEmpty(request.getReviewerBaseInfo().getAccountId())) {
            throw new ServiceException("accountId不能为空", 500);
        }
        String accountId = request.getReviewerBaseInfo().getAccountId();
        ReviewerBaseInfo reviewerBaseInfo = reviewerBaseInfoMapper.selectReviewerBaseInfoByAccountId(accountId);
        if (Objects.isNull(reviewerBaseInfo)) {
            throw new ServiceException(String.format("accountId[%s]查询评审员基本信息为空", accountId), 500);
        }
        if (!reviewerBaseInfo.getCertificateNumber().equals(request.getReviewerBaseInfo().getCertificateNumber())) {
            throw new ServiceException(String.
                    format("accountId[%s] certificateNumber[%s]不匹配", accountId, request.getTempRevInfo().getCertificateNumber()));

        }
    }

    @Override
    public ReviewerBaseInfoDTO queryReviewerDetail(String accountId) {
        ReviewerBaseInfoDetailVO reviewerBaseInfoDetailVO = new ReviewerBaseInfoDetailVO();
        // 查询评审员基本信息
        ReviewerBaseInfo reviewerBaseInfo = reviewerBaseInfoMapper.selectReviewerBaseInfoByAccountId(accountId);

        if (Objects.isNull(reviewerBaseInfo)) {
            return null;
        }
        // 通过accountId获取用户名
        String userName = iSysUserService.selectUserNameByAccountId(accountId);
        reviewerBaseInfo.setUserName(userName);

        // 工作经历
        String workExperienceStr = reviewerBaseInfo.getWorkExperience();
        if (StrUtil.isNotEmpty(workExperienceStr)) {
            List<JSONObject> jsonObjects = JSONUtil.toList(JSONUtil.parseArray(workExperienceStr), JSONObject.class);
            reviewerBaseInfoDetailVO.setWorkExperienceList(jsonObjects);
        }
        // 获取对应个人证书
        String certificateStr = reviewerBaseInfo.getCertificateList();
        StringBuilder ids = new StringBuilder();
        if (StrUtil.isNotEmpty(certificateStr)) {
            List<JSONObject> jsonObjects = JSONUtil.toList(JSONUtil.parseArray(certificateStr), JSONObject.class);
            reviewerBaseInfoDetailVO.setCertificateList(jsonObjects);
            jsonObjects.forEach(jsonObject -> {
                String fileId = jsonObject.getStr("certificateId");
                if (StrUtil.isNotEmpty(fileId)) {
                    ids.append(",");
                    ids.append(fileId);
                }
            });
        }

        // 获取国际证书列表
        List<CertificateAbility> certificateAbilityList = iCertificateAbilityService.selectCertificateAbilityByAccountId(accountId);
        certificateAbilityList.forEach(certificateAbility -> {
            if (certificateAbility.getFileId() != null) {
                ids.append(",");
                ids.append(certificateAbility.getFileId());
            }
        });
        reviewerBaseInfoDetailVO.setCertificateAbilityList(certificateAbilityList);

        // 个人头像
        if (Objects.nonNull(reviewerBaseInfo.getHeadPortrait())) {
            ids.append(",");
            ids.append(reviewerBaseInfo.getHeadPortrait());
        }

        reviewerBaseInfoDetailVO.setReviewerBaseInfo(reviewerBaseInfo);
        List<FileInfoDTO> fileInfoDTOList = iUploadFileInfoService.getUploadFileInfoByIds(ids.toString());
        List<FileInfoVO> fileInfoVOList = commonService.fileInfoDtoToVo(fileInfoDTOList);
        Map<String, List<FileInfoVO>> fileDetailMap = fileInfoVOList.stream().collect(Collectors.groupingBy(FileInfoVO::getFileId));
        reviewerBaseInfoDetailVO.setFileDetails(fileDetailMap);

        // 获取评审员领域信息
        List<ReviewerFieldInfoVO> reviewerFieldInfoVOList = iReviewerFieldInfoService.selectReviewerFieldInfoByAccountId(accountId);
        reviewerBaseInfoDetailVO.setReviewerFieldInfoVOList(reviewerFieldInfoVOList);
        // 查询auth_status
        AutRecord autRecord = iAutRecordService.selectAutRecordByAccountId(accountId);
        if (Objects.nonNull(autRecord)) {
            reviewerBaseInfoDetailVO.setAuthStatus(autRecord.getAutStatus());
        }
        return reviewerBaseInfoDetailVO;
    }

    @Override
    public void updateSubmitStatusByAccountId(String accountId) {
        int res = reviewerBaseInfoMapper.updateSubmitStatusByAccountId(accountId);
        log.info("评审员:{} 提交状态修改结果:{}", accountId, res);
        if (res <= 0) {
            throw new ServiceException(ServiceExceptionEnum.REVIEWER_ERROR_1000002);
        }
    }

    @Override
    public int selectCountByAccountId(String accountId) {
        return reviewerBaseInfoMapper.selectCountByAccountId(accountId);
    }

    @Override
    public int insertOrUpdateByAccountId(ReviewerBaseInfo reviewerBaseInfo) {
        String accountId = reviewerBaseInfo.getAccountId();
        if (CharSequenceUtil.isEmpty(accountId)) {
            throw new ServiceException(ServiceExceptionEnum.REVIEWER_ERROR_1000003);
        }
        if (selectCountByAccountId(accountId) > 0) {
            reviewerBaseInfo.setUpdateTime(DateUtil.parse(DateUtil.now(), DatePattern.NORM_DATETIME_PATTERN));
            return reviewerBaseInfoMapper.updateReviewerBaseInfoByAccountId(reviewerBaseInfo);
        } else {
            // 校验当前身份证是否已经存在
            if (CharSequenceUtil.isNotEmpty(reviewerBaseInfo.getCertificateNumber())
                    && reviewerBaseInfoMapper.selectCountByCertificateNumber(reviewerBaseInfo.getCertificateNumber()) > 0) {
                throw new ServiceException(ServiceExceptionEnum.REVIEWER_ERROR_1000013);
            }
            return reviewerBaseInfoMapper.insertReviewerBaseInfo(reviewerBaseInfo);
        }
    }

    @Override
    public List<ReviewManageVO> queryReviewManage(ReviewManageDTO reviewManageDTO) {
        Integer personForm = reviewManageDTO.getPersonForm();
        if (Constants.HospitalConstants.NUM_1.equals(personForm)) {
            // 初审员
            return iHospitalPreExamService.queryReviewManage(reviewManageDTO);
        } else if (Constants.HospitalConstants.NUM_2.equals(personForm)) {
            return iHospitalReviewerService.queryReviewManage(reviewManageDTO);
        } else {
            throw new ServiceException(ServiceExceptionEnum.HOSPITAL_ERROR_1000009);
        }
    }

    @Override
    public AjaxResult reviewOfflineTra(RevOfflineTraDTO revOfflineTraDTO) {
        String trainingId = revOfflineTraDTO.getTrainingId();
        List<Map<String, String>> reviewerIdMapList = revOfflineTraDTO.getReviewerIdMapList();
        List<CstReviewerOfflineTraining> cstReviewerOfflineTrainingList = new ArrayList<>();
        for (Map<String, String> reviewerIdMap : reviewerIdMapList) {
            String signFileId = MapUtil.getStr(reviewerIdMap, "signFileId");
            CstReviewerOfflineTraining cstReviewerOfflineTraining = new CstReviewerOfflineTraining();
            cstReviewerOfflineTraining.setTrainingId(trainingId);
            cstReviewerOfflineTraining.setReviewerId(MapUtil.getStr(reviewerIdMap, "reviewerId"));
            cstReviewerOfflineTraining.setSignFileId(signFileId);
            cstReviewerOfflineTraining.setPartStatus(Constants.HospitalConstants.NUM_0);
            if (CharSequenceUtil.isNotEmpty(signFileId)) {
                cstReviewerOfflineTraining.setPartStatus(Constants.HospitalConstants.NUM_1);
            }
            cstReviewerOfflineTrainingList.add(cstReviewerOfflineTraining);
        }
        for (CstReviewerOfflineTraining cstReviewerOfflineTraining : cstReviewerOfflineTrainingList) {
            int i = iCstReviewerOfflineTrainingService.updateInfoByAccountIdAndTraId(cstReviewerOfflineTraining);
        }
        return AjaxResult.success();
    }

    @Override
    public AjaxResult offlineTrainingSubmit(List<String> idList) {
        // 添加到表
        // 获取当前评审员的登录用户id
        String accountId = String.valueOf(SecurityUtils.getUserId());
        List<CstReviewerOfflineTraining> cstReviewerOfflineTrainingList = new ArrayList<>();
        if (CollUtil.isNotEmpty(idList)) {
            for (String id : idList) {
                CstReviewerOfflineTraining cstReviewerOfflineTraining = new CstReviewerOfflineTraining();
                cstReviewerOfflineTraining.setReviewerId(accountId);
                cstReviewerOfflineTraining.setTrainingId(id);
                cstReviewerOfflineTraining.setApplyStatus(Constants.HospitalConstants.STR_NUM_1);
                cstReviewerOfflineTrainingList.add(cstReviewerOfflineTraining);
            }
            // 将之前的记录置为失效
            iCstReviewerOfflineTrainingService.updateStatusByAccountId(accountId, 0);
            int batchRes = iCstReviewerOfflineTrainingService.batchInsertOfflineTraining(cstReviewerOfflineTrainingList);
            if (batchRes <= 0) {
                throw new ServiceException(ServiceExceptionEnum.REVIEWER_ERROR_1000005);
            }
        }
        return AjaxResult.success();
    }

    @Override
    public AjaxResult offlineTrainingApply(List<CstReviewerOfflineTraining> cstReviewerOfflineTrainingList) {
        // 获取当前评审员的登录用户id
        String accountId = String.valueOf(SecurityUtils.getUserId());
        cstReviewerOfflineTrainingList.forEach(cst -> {
            log.info("/offline/training/apply 评审员线下培训报名入参,线下培训id:{},报名提交状态:{}", cst.getTrainingId(), cst.getApplyStatus());
            String trainingId = cst.getTrainingId();
            String applyStatus = cst.getApplyStatus();
            if (StringUtils.isBlank(accountId)) {
                throw new ServiceException("登录用户id为空，请重新登录重试！");
            }
            if (StringUtils.isBlank(trainingId)) {
                throw new ServiceException("入参校验：线下培训id不能为空！");
            }
            if (StringUtils.isBlank(applyStatus)) {
                throw new ServiceException("入参校验：线下培训报名状态不能为空！");
            }
            CstReviewerOfflineTraining cstReviewerOfflineTraining = new CstReviewerOfflineTraining();
            cstReviewerOfflineTraining.setReviewerId(accountId);
            cstReviewerOfflineTraining.setTrainingId(trainingId);
            //查询是否有数据，没数据新增，有数据更新
//        List<CstReviewerOfflineTraining> cstReviewerOfflineTrainings = iCstReviewerOfflineTrainingService.selectCstReviewerOfflineTrainingList(cstReviewerOfflineTraining);

            //报名状态:未报名"0",已报名"1"
            if ("1".equals(applyStatus)) {
                cstReviewerOfflineTraining.setApplyStatus(applyStatus);
                //报名状态为报名，新增
                int insert = iCstReviewerOfflineTrainingService.insertCstReviewerOfflineTraining(cstReviewerOfflineTraining);
                if (insert <= 0) {
                    throw new ServiceException(ServiceExceptionEnum.REVIEWER_ERROR_1000005);
                }
            } else if ("0".equals(applyStatus)) {
                //报名状态为取消报名，删除
                iCstReviewerOfflineTrainingService.deleteCstReviewerOfflineTraining(cstReviewerOfflineTraining);
            } else {
                log.info("报名状态为：{}", applyStatus);
                throw new ServiceException("不支持当前报名状态码，入参只能传0报名或1未报名！");
            }
        });
        return AjaxResult.success();
    }

    @Override
    public List<OfflineTrainingRegisteredVo> offlineTrainingRegistered(String trainingId) {
        if (StringUtils.isBlank(trainingId)) {
            throw new ServiceException("线下培训id不能为空！");
        }
        //1.根据线下培训id，查询对应报名培训人员用户信息，封装用户信息返回
        List<OfflineTrainingRegisteredVo> offlineTrainingRegisteredVoList
                = iCstReviewerOfflineTrainingService.selectSysUserByTrainingId(trainingId);

        if (CollectionUtil.isEmpty(offlineTrainingRegisteredVoList)) {
            return offlineTrainingRegisteredVoList;
        }
        // 组装查询对应的附件
        FileInfoDTO fileInfoDTO = new FileInfoDTO();
        FileInfoVO fileInfoVO;
        for (OfflineTrainingRegisteredVo offlineTrainingRegisteredVo : offlineTrainingRegisteredVoList) {
            if (Constants.HospitalConstants.NUM_1.equals(offlineTrainingRegisteredVo.getPartStatus())) {
                fileInfoDTO.setId(Long.valueOf(offlineTrainingRegisteredVo.getSignFileId()));
                fileInfoDTO.setPlatform(offlineTrainingRegisteredVo.getPlatform());
                fileInfoDTO.setPath(offlineTrainingRegisteredVo.getUrl());
                fileInfoVO = commonService.fileInfoDtoToVo(fileInfoDTO);
                offlineTrainingRegisteredVo.setUrl(fileInfoVO.getUrl());
            }
        }
        return offlineTrainingRegisteredVoList;
    }

    @Override
    public int updateReviewerBaseInfoByUser(SysUser user) {
        ReviewerBaseInfo reviewerBaseInfo = new ReviewerBaseInfo();
        reviewerBaseInfo.setAccountId(String.valueOf(user.getUserId()));
        reviewerBaseInfo.setReviewerName(user.getNickName());
        reviewerBaseInfo.setReviewerMobile(user.getPhonenumber());
        reviewerBaseInfo.setReviewerEmail(user.getEmail());
        if (CharSequenceUtil.isNotEmpty(user.getSex())) {
            reviewerBaseInfo.setReviewerGender(Integer.parseInt(user.getSex()) + 1);
        }
        return reviewerBaseInfoMapper.updateReviewerBaseInfoByAccountId(reviewerBaseInfo);
    }

    @Override
    public List<CstOfflineTrainingManagement> queryCstOfflineTrainingList(QueryCstOfflineTrainingDTO queryCstOfflineTrainingDTO) {
        String accountId = queryCstOfflineTrainingDTO.getAccountId();
        String filterIs = queryCstOfflineTrainingDTO.getFilterIs();
        // 将已过期的状态更改
        if (CharSequenceUtil.isEmpty(filterIs)) {
            // 如果是否过滤字段为空查询对应的勾选的线下培训数据
            // iCstOfflineTrainingManagementService.updateTrainingStatusByNowTime();
            if (CharSequenceUtil.isEmpty(accountId)) {
                throw new ServiceException(ServiceExceptionEnum.TRAINING_ERROR_1000001);
            }
            PageUtils.startPage();
            return iCstOfflineTrainingManagementService.selectCstOfflineTrainingManagementByAccountId(accountId);
        }
        // 查询线下培训的分页所有数据列表
        queryCstOfflineTrainingDTO.setStatus(1);
        queryCstOfflineTrainingDTO.setTrainingStatus("1");
        queryCstOfflineTrainingDTO.setTrainingTime(DateUtil.parse(DateUtil.now(), DatePattern.NORM_DATE_PATTERN));

        // 获取当前角色
        if (iSysRoleService.isInspector()) {
            // 如果是审查员
            queryCstOfflineTrainingDTO.setTrainingType(Constants.HospitalConstants.ROLE_INSPECTOR);
        } else if (iSysRoleService.isAssessor()) {
            // 如果是评审员
            queryCstOfflineTrainingDTO.setTrainingType(Constants.HospitalConstants.ROLE_ASSESSOR);
        }

        List<CstOfflineTrainingManagement> cstOfflineTrainingManagementList
                = iCstOfflineTrainingManagementService.selectCstOfflineTrainingManagementList(queryCstOfflineTrainingDTO);
        // 查询出评审员已选择的线下培训id
        filterStudy(cstOfflineTrainingManagementList, accountId);
        return cstOfflineTrainingManagementList;
    }

    @Override
    public boolean hasAuthReviewInfo(String accountId) {
        int count = reviewerBaseInfoMapper.selectCountByAccountId(accountId);
        return count > 0;
    }

    @Override
    public void traineesToReviewer(QueryCstOfflineTrainingDTO queryCstOfflineTrainingDTO) {
        String userId = queryCstOfflineTrainingDTO.getAccountId();
        String roleKey = sysUserMapper.selectRoleKeyByUserId(Long.valueOf(userId));
        // 判断当前角色是否为评审学员
        if (!Constants.HospitalConstants.ROLE_TRAINEES_ASSESSOR.equals(roleKey)) {
            throw new ServiceException("当前提交用户角色不是评审学员，不能提交！");
        }

        // 判断当前学员是否有豁免申请，如果有则不需要实践记录。
        ReviewerBaseInfo reviewerBaseInfo = reviewerBaseInfoMapper.selectReviewerBaseInfoByAccountId(userId);
        if (Constants.HospitalConstants.NUM_1.equals(reviewerBaseInfo.getSkipFlag())) {
            int i = sysUserRoleMapper.updateUserRoleInfo(AutSaAudRoleEnum.ASSESSOR.getCode(), userId);
            if (i > 0) {
                log.info("评审学员id为{}，转为评审员成功！", userId);
            }
            return;
        }

        // 需要判断管理员是否已经提交签到表
        List<TraineesPractice> traineesPracticeList = traineesPracticeService.selectTraineesPracticeByAccountId(userId);

        if (CollUtil.isEmpty(traineesPracticeList)) {
            throw new ServiceException("当前评审学员未参加实践培训！");
        }
        boolean flag = traineesPracticeList.stream()
                .anyMatch(traineesPractice -> CharSequenceUtil.isEmpty(traineesPractice.getSignFileId()));
        if (flag) {
            throw new ServiceException("当前评审学员存在在途评审！");
        }
        // 都通过转为评审员，根据用户id，更新用户为评审员
        int i = sysUserRoleMapper.updateUserRoleInfo(AutSaAudRoleEnum.ASSESSOR.getCode(), userId);
        if (i > 0) {
            log.info("评审学员id为{}，转为评审员成功！", userId);
        }
        // 将学员对应的附件修改为评审员
        UpdFileShareTempDataDto updFileShareTempDataDto = new UpdFileShareTempDataDto();
        updFileShareTempDataDto.setTempCode(userId);
        updFileShareTempDataDto.setRoleKey(Constants.HospitalConstants.ROLE_ASSESSOR);
        updFileShareTempDataDto.setOldRoleKey(Constants.HospitalConstants.ROLE_TRAINEES_ASSESSOR);
        fileShareService.updateByOwnerIdAndRoleKey(updFileShareTempDataDto);
    }

    @Override
    public List<TraineesReviewRecVO> traineesReviewList() {
        // 1.通过aut_status从表aut_sa_relation查询出满足节点条件的applyNo列表
        // 2.通过applyNo在表hospital_reviewer查出学员列表

        List<String> autStatusList = Arrays.asList(AutSaAudStatusEnum.FAR_CLAUSE_M_SUMMARY.getStatus());
        List<TraineesReviewRecVO> traineesReviewRecVOList = autSaRelationService.selectAutSaRelationByAutStatus(autStatusList);
        for (TraineesReviewRecVO traineesReviewRecVO : traineesReviewRecVOList) {
            // 组装开始时间和结束时间
            String time = traineesReviewRecVO.getStartTime();
            String[] times = time.split(",");
            if (times.length == 2) {
                traineesReviewRecVO.setStartTime(times[0]);
                traineesReviewRecVO.setEndTime(times[1]);
            }
            // 查询出医疗机构对应的学员列表
            List<SysUserBaseInfo> traBaseInfoList = selectTraBaseInfoListByApplyNo(traineesReviewRecVO.getApplyNo());
            traineesReviewRecVO.setTraBaseInfoList(traBaseInfoList);
        }
        return traineesReviewRecVOList;
    }

    private List<SysUserBaseInfo> selectTraBaseInfoListByApplyNo(String applyNo) {
        return reviewerBaseInfoMapper.selectTraBaseInfoListByApplyNo(applyNo);
    }

    @Override
    public List<ReviewInterestVO> interestList() {
        // 获取当前登录用户角色
        SysRole sysRole = commonService.selectSysRole();
        log.info("当前登录角色key:{}", sysRole.getRoleKey());
        if (!Constants.HospitalConstants.ROLE_ASSESSOR.equals(sysRole.getRoleKey())) {
            throw new ServiceException("当前用户非评审员用户");
        }
        // 获取当前登录用户userId
        Long userId = SecurityUtils.getUserId();
        // 查询出该评审员下所有的医疗机构
        List<ReviewInterestVO> reviewInterestVOList = iHospitalReviewerService.selectReviewInterestVOByAccountId(String.valueOf(userId));
        if (CollUtil.isEmpty(reviewInterestVOList)) {
            return new ArrayList<>();
        }
        commonService.reviewInterestVOFilePathToUrl(reviewInterestVOList);
        return reviewInterestVOList;
    }

    @Override
    public ReviewerBaseInfo selectReviewerBaseInfoByAccountId(String accountId) {
        return reviewerBaseInfoMapper.selectReviewerBaseInfoByAccountId(accountId);
    }

    private void filterStudy(List<CstOfflineTrainingManagement> cstOfflineTrainingManagementList, String accountId) {
        // 查询出评审员已选择的线下培训id
        //已报名有数据，未报名没数据,线下培训报名状态:未报名"0",已报名"1"
        List<CstReviewerOfflineTraining> cstReviewerOfflineTrainingList
                = iCstReviewerOfflineTrainingService.selectCstReviewerOfflineTrainingByAccountId(accountId);
        // 组装accountId下报名线下培训的
        Set<String> applyIds = cstReviewerOfflineTrainingList.stream()
                .map(CstReviewerOfflineTraining::getTrainingId)
                .collect(Collectors.toSet());

        // 组装accountId下报名实际参与的
        Set<String> partIds = cstReviewerOfflineTrainingList.stream()
                .filter(cstReviewerOfflineTraining ->
                        Constants.HospitalConstants.NUM_1.equals(cstReviewerOfflineTraining.getPartStatus()))
                .map(CstReviewerOfflineTraining::getTrainingId)
                .collect(Collectors.toSet());

        // 判断是否已培训
        cstOfflineTrainingManagementList.forEach(cstOfflineTrainingManagement -> {
            String id = String.valueOf(cstOfflineTrainingManagement.getId());

            // 默认未报名未参与
            cstOfflineTrainingManagement.setApplyStatus("0");
            cstOfflineTrainingManagement.setParticipatesTraining(1);
            if (applyIds.contains(id)) {
                //有数据，封装已报名状态
                cstOfflineTrainingManagement.setApplyStatus("1");
            }
            if (partIds.contains(id)) {
                cstOfflineTrainingManagement.setParticipatesTraining(0);
            }
        });
    }

    @Override
    public void reviewerListExport(ReviewerExportRequest request) {
        // 获取当前HttpResponse
        ServletRequestAttributes servletRequestAttributes =
                (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        assert servletRequestAttributes != null;
        HttpServletResponse response = servletRequestAttributes.getResponse();

        // 获取exec第一行标题。
        Map<String, String> fieldAndAliaMap = this.getFieldAndAliaMap();
        // 获取对应数据
        List<Map> reviewerBaseInfoListMap = this.getReviewerBaseInfoListMap(request.getAccountIdList(), fieldAndAliaMap);
        // 设置一个单元页
        SheetDTO sheet = new SheetDTO();
        sheet.setSheetName(request.getSheetName());
        sheet.setFieldAndAlias(fieldAndAliaMap);
        sheet.setCollection(reviewerBaseInfoListMap);
        List<SheetDTO> sheetList = Collections.singletonList(sheet);

        // 导出exec
        HutoolExcelUtils.exportExcel(response, sheetList, request.getFileName());
    }

    @Override
    public void updateReviewerRole(UpdateReviewerRoleReq req) {
        Integer updateRoleId;
        //判断当前角色是评审员还是验证评审员
        if (String.valueOf(AutSaAudRoleEnum.ASSESSOR.getCode()).equals(req.getRoleId())) {
            updateRoleId = AutSaAudRoleEnum.SENIOR_ASSESSOR.getCode();
        } else if (String.valueOf(AutSaAudRoleEnum.SENIOR_ASSESSOR.getCode()).equals(req.getRoleId())) {
            updateRoleId = AutSaAudRoleEnum.ASSESSOR.getCode();
            // 验证评审员转评审员时，将分组之前的记录置为失效
            iReviewerFieldInfoService.updateStatusByAccountId(req.getReviewerId(), 2);
        } else {
            throw new ServiceException("当前用户角色不支持操作，仅支持评审员或验证评审员转换，角色id为:" + req.getRoleId());
        }
        //判断当前用户是否在途
        //评审员在途的数量
        if (hospitalReviewerMapper.selectStatusByReviewerId(Long.valueOf(req.getReviewerId())) > 0) {
            throw new ServiceException("当前用户有在途任务，操作失败！");
        }
        //更新当前用户角色
        int updateUserRoleInfo = sysUserRoleMapper.updateUserRoleInfo(updateRoleId, req.getReviewerId());
        if (updateUserRoleInfo <= 0) {
            log.error("更新评审员或验证评审员角色转换失败-变更的角色id为{},评审员id为{}！", updateRoleId, req.getReviewerId());
            throw new ServiceException("转换角色失败，请联系管理员！");
        }

        // 评审员验证评审员互转，将对应资源库附件角色一并修改。
        this.updateFileShareRoleKey(req);

        //用户角色转换成功，如果用户在登录中，强制退出(清除转换角色显示菜单等前端缓存问题)
        this.forceLogout(req.getUserName());

    }

    public void forceLogout(String userName) {
        log.info("强制退出操作开始,用户名为：{}", userName);
        //TableDataInfo list = sysUserOnlineController.list(null, userName);
        List<SysUserOnline> sysUserOnlines = sysUserOnlineList(userName);
        if (CollectionUtil.isNotEmpty(sysUserOnlines)) {
            log.info("强制退出操作-用户登录中,用户名为：{}", userName);
            List<String> tokenIds = sysUserOnlines.stream().filter(o -> StringUtils.isNotEmpty(o.getTokenId())).
                    map(o -> Constants.LOGIN_TOKEN_KEY + o.getTokenId()).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(tokenIds)) {
                redisCache.deleteObject(tokenIds);
                log.info("强制退出操作，用户强制退出成功！用户名为：{}", userName);
            }
        }
    }

    private List<SysUserOnline> sysUserOnlineList(String userName) {
        Collection<String> keys = redisCache.keys(Constants.LOGIN_TOKEN_KEY + "*");
        List<SysUserOnline> userOnlineList = new ArrayList<>();
        for (String key : keys) {
            LoginUser user = redisCache.getCacheObject(key);
            if (StringUtils.isNotEmpty(userName) && StringUtils.isNotNull(user.getUser())) {
                if (StringUtils.equals(userName, user.getUsername())) {
                    userOnlineList.add(userOnlineService.selectOnlineByUserName(userName, user));
                }
            }

        }
        return userOnlineList;
    }

    /**
     * 修改对应资源库roleKey
     *
     * @param req 入参
     */
    private void updateFileShareRoleKey(UpdateReviewerRoleReq req) {
        String roleKey;
        String oldRoleKey;
        if (String.valueOf(AutSaAudRoleEnum.ASSESSOR.getCode()).equals(req.getRoleId())) {
            roleKey = AutSaAudRoleEnum.SENIOR_ASSESSOR.getRoleKey();
            oldRoleKey = AutSaAudRoleEnum.ASSESSOR.getRoleKey();
        } else {
            roleKey = AutSaAudRoleEnum.ASSESSOR.getRoleKey();
            oldRoleKey = AutSaAudRoleEnum.SENIOR_ASSESSOR.getRoleKey();
        }
        UpdFileShareTempDataDto updFileShareTempDataDto = new UpdFileShareTempDataDto();
        updFileShareTempDataDto.setTempCode(req.getReviewerId());
        updFileShareTempDataDto.setRoleKey(roleKey);
        updFileShareTempDataDto.setOldRoleKey(oldRoleKey);
        fileShareService.updateByOwnerIdAndRoleKey(updFileShareTempDataDto);
    }

    /**
     * 获取第一行标题
     *
     * @return Map
     */
    private Map<String, String> getFieldAndAliaMap() {
        Map<String, String> fieldAndAliaMap = new LinkedHashMap<>();
        fieldAndAliaMap.put("reviewerName", "姓名");
        fieldAndAliaMap.put("reviewerGender", "性别");
        fieldAndAliaMap.put("certificateType", "证件类型");
        fieldAndAliaMap.put("certificateNumber", "证件号码");
        fieldAndAliaMap.put("reviewerBirthday", "出生年月");
        fieldAndAliaMap.put("reviewerMobile", "联系电话");
        fieldAndAliaMap.put("reviewerEmail", "联系邮箱");
        fieldAndAliaMap.put("liveAddress", "居住地址");
        fieldAndAliaMap.put("company", "工作单位");
        fieldAndAliaMap.put("companyPost", "现任职务");
        fieldAndAliaMap.put("majorField", "专业领域");
        fieldAndAliaMap.put("englishWrit", "英语写作水平");
        fieldAndAliaMap.put("englishOral", "英语口语水平");
        fieldAndAliaMap.put("workExperience", "工作简历");
        fieldAndAliaMap.put("learnIs", "是否参加过的国际认证/医院等级评审评审员培训");
        fieldAndAliaMap.put("learn", "参加过的国际认证/医院等级评审评审员培训");
        fieldAndAliaMap.put("hosRevIs", "是否是医院评审评价评审员");
        fieldAndAliaMap.put("hosRevDetail", "医院评审评价评审员具体为");
        fieldAndAliaMap.put("reviewHospitalIs", "是否评审过医院");
        fieldAndAliaMap.put("reviewHospitalYear", "评审医院年限");
        fieldAndAliaMap.put("reviewHospitalNum", "评审医院数量");
        fieldAndAliaMap.put("reviewHospitalCoach", "是否对医院评审辅导");
        fieldAndAliaMap.put("coachHospitalNum", "辅导医院数量");
        fieldAndAliaMap.put("trainReviewerIs", "是否培训过评审员");
        fieldAndAliaMap.put("trainNum", "培训过的场次");
        fieldAndAliaMap.put("trainOrganization", "具体是哪个组织或单位举办的评审员培训项目");
        fieldAndAliaMap.put("otherExperience", "其他相关经历及社会兼职");
        fieldAndAliaMap.put("trainWarning", "对培训、工作安排及生活照顾需要中心特别注意的事项");
        fieldAndAliaMap.put("theoryCourseStartTime", "理论培训评估开始时间");
        fieldAndAliaMap.put("theoryCourseEndTime", "理论培训评估结束时间");
        fieldAndAliaMap.put("theoryCourseFormat", "理论培训评估课程形式");
        fieldAndAliaMap.put("theoryTrainerName", "理论培训评估培训教员姓名");
        fieldAndAliaMap.put("theoryConclusion", "理论培训评估结论");
        fieldAndAliaMap.put("sceneCourseStartTime", "现场评审带教培训评估开始时间");
        fieldAndAliaMap.put("sceneCourseEndTime", "现场评审带教培训评估结束时间");
        fieldAndAliaMap.put("sceneCourseFormat", "现场评审带教培训评估课程形式");
        fieldAndAliaMap.put("sceneTrainerName", "现场评审带教培训评估教员姓名");
        fieldAndAliaMap.put("sceneConclusion", "现场评审带教培训评估结论");
        return fieldAndAliaMap;
    }

    /**
     * 获取基本数据
     *
     * @param accountIdList accountIdList
     * @return list
     */
    private List<Map> getReviewerBaseInfoListMap(List<String> accountIdList, Map<String, String> fieldAndAliaMap) {
        // 查询评审员基本信息
        List<ReviewerBaseInfo> reviewerBaseInfoList =
                reviewerBaseInfoMapper.selectReviewerBaseInfoByAccountIdList(accountIdList, null);
        // 查询评审评估结果
        List<TrainingEvaluateResult> trainingEvaluateResultList
                = trainingEvaluateResultMapper.selectTrainingEvaluateResultByTraineesAssessorIdList(accountIdList);
        String jsonStr = JSON.toJSONString(reviewerBaseInfoList);
        List<Map> reviewerBaseInfoListMap = JSON.parseArray(jsonStr, Map.class);
        this.initKey(reviewerBaseInfoListMap, fieldAndAliaMap);
        // 处理评审评估
        this.revEva(reviewerBaseInfoListMap, trainingEvaluateResultList);
        // 映射处理
        this.reviewerBaseInfoMap(reviewerBaseInfoListMap);
        return reviewerBaseInfoListMap;
    }

    private void initKey(List<Map> reviewerBaseInfoListMap, Map<String, String> fieldAndAliaMap) {
        for (Map map : reviewerBaseInfoListMap) {
            for (String key : fieldAndAliaMap.keySet()) {
                if (!map.containsKey(key)) {
                    map.put(key, null);
                }
            }
        }
    }

    /**
     * 处理评审评估
     */
    private void revEva(List<Map> reviewerBaseInfoListMap,
                        List<TrainingEvaluateResult> trainingEvaluateResultList) {
        if (CollUtil.isEmpty(trainingEvaluateResultList)) {
            return;
        }
        // 使用学员id分组
        Map<Long, List<TrainingEvaluateResult>> trainingEvaluateResultMap = trainingEvaluateResultList.stream()
                .collect(Collectors.groupingBy(TrainingEvaluateResult::getTraineesAssessorId));
        for (Map reviewerBaseInfo : reviewerBaseInfoListMap) {
            this.revEva(reviewerBaseInfo, trainingEvaluateResultMap);
        }
    }

    private void revEva(Map reviewerBaseInfo, Map<Long, List<TrainingEvaluateResult>> trainingEvaluateResultMap) {
        String accountId = MapUtil.getStr(reviewerBaseInfo, "accountId");
        Long aLong;
        try {
            aLong = Long.valueOf(accountId);
        } catch (Exception e) {
            return;
        }
        List<TrainingEvaluateResult> trainingEvaluateResultList =
                trainingEvaluateResultMap.get(aLong);
        if (CollUtil.isEmpty(trainingEvaluateResultList)) {
            return;
        }
        // 理论
        final Long Num1 = 1L;
        TrainingEvaluateResult t1 = trainingEvaluateResultList.stream()
                .filter(trainingEvaluateResult -> Num1.equals(trainingEvaluateResult.getReviewResultType()))
                .findFirst()
                .orElse(null);
        this.revEva(reviewerBaseInfo, t1, "theory");
        // 现场
        final Long Num2 = 2L;
        TrainingEvaluateResult t2 = trainingEvaluateResultList.stream()
                .filter(trainingEvaluateResult -> Num2.equals(trainingEvaluateResult.getReviewResultType()))
                .findFirst()
                .orElse(null);
        this.revEva(reviewerBaseInfo, t2, "scene");
    }


    private void revEva(Map reviewerBaseInfo,
                        TrainingEvaluateResult trainingEvaluateResult,
                        String fieldPre) {
        if (Objects.isNull(trainingEvaluateResult)) {
            return;
        }

        reviewerBaseInfo.put(fieldPre + "CourseStartTime",
                DateUtil.format(trainingEvaluateResult.getCourseStartTime(), "yyyy-MM-dd"));
        reviewerBaseInfo.put(fieldPre + "CourseEndTime",
                DateUtil.format(trainingEvaluateResult.getCourseEndTime(), "yyyy-MM-dd"));
        reviewerBaseInfo.put(fieldPre + "CourseFormat",
                Objects.requireNonNull(TrainingEvaluateResultEnum.courseFormatEnum.getConclusionEnumByCode(trainingEvaluateResult.getCourseFormat())).getDesc());

        String trainerName = this.getTrainerName(trainingEvaluateResult);
        reviewerBaseInfo.put(fieldPre + "TrainerName", trainerName);

        if (trainingEvaluateResult.getConclusion() == 0) {
            reviewerBaseInfo.put(fieldPre + "Conclusion", trainingEvaluateResult.getRemark());
        } else {
            reviewerBaseInfo.put(fieldPre + "Conclusion",
                    Objects.requireNonNull(TrainingEvaluateResultEnum.ConclusionEnum.getConclusionEnumByCode(Math.toIntExact(trainingEvaluateResult.getConclusion()))).getDesc());
        }
    }

    private String getTrainerName(TrainingEvaluateResult trainingEvaluateResult) {
        String trainerId = trainingEvaluateResult.getTrainerId();
        StringBuilder trainerNameSb = new StringBuilder();
        if (StringUtils.isNotEmpty(trainerId)) {
            List<String> accountIdList = Arrays.asList(trainerId.split(","));
            List<UserVo> userVoList = sysUserMapper.selectUserByIds(accountIdList);
            if (CollUtil.isNotEmpty(userVoList)) {
                for (UserVo userVo : userVoList) {
                    trainerNameSb.append(userVo.getNickName())
                            .append(" ");
                }
            }
        }
        if (StringUtils.isNotEmpty(trainingEvaluateResult.getTrainerName())) {
            trainerNameSb.append(trainingEvaluateResult.getTrainerName().replace(",", " "));
        }
        return trainerNameSb.toString();
    }


    /**
     * 映射处理
     */
    private void reviewerBaseInfoMap(List<Map> reviewerBaseInfoListMap) {
        // 获取字段对应字典类型
        String dictFilePath = "execl/reviewer/ReviewerDict.json";
        Map<String, String> dictTypeMap = commonService.readerJsonFile(dictFilePath, Map.class);

        String yesOrNoFilePath = "execl/reviewer/yesOrNo.json";
        Map<String, String> yesOrNoFileMap = commonService.readerJsonFile(yesOrNoFilePath, Map.class);

        Map<String, String> tempCache = new HashMap<>();
        for (Map map : reviewerBaseInfoListMap) {
            for (Map.Entry<String, String> entry : dictTypeMap.entrySet()) {
                String key = entry.getKey();
                String dictType = entry.getValue();
                String dictValue = MapUtil.getStr(map, key);
                String cacheKey = dictType + "-" + dictValue;
                String value;
                if (!tempCache.containsKey(cacheKey)) {
                    value = this.queryDictValue(dictType, dictValue);
                    tempCache.put(cacheKey, value);
                } else {
                    value = tempCache.get(cacheKey);
                }
                map.put(key, value);
            }
            // 查询学习经历
            this.queryLearn(map);
            // 处理工作经历
            this.workExperience(map);
            // 是否处理
            this.isNotPro(map, yesOrNoFileMap);
            // 处理是否是医院评审员
            this.proHosRev(map);
            // 处理专业领域
            this.promajorField(map, tempCache);
        }
    }

    private void promajorField(Map map, Map<String, String> tempCache) {
        String majorField = MapUtil.getStr(map, "majorField");
        if (StringUtils.isEmpty(majorField)) {
            return;
        }
        String[] ss = majorField.split(",");
        StringBuilder sb = new StringBuilder();
        for (String s : ss) {
            if ("5".equals(s)) {
                continue;
            }
            String k = majorField + "-" + s;
            String value;
            if (!tempCache.containsKey(k)) {
                value = this.queryDictValue("reviewer_major", s);
                tempCache.put(k, value);
            } else {
                value = tempCache.get(k);
            }
            sb.append(value).append(" ");
        }

        sb.append(this.preMajorDirection(map));
        map.put("majorField", sb.toString());
    }

    private String preMajorDirection(Map map) {
        String majorDirection = MapUtil.getStr(map, "majorDirection");
        if (StringUtils.isEmpty(majorDirection)) {
            return "";
        }
        Map m = JSON.parseObject(majorDirection, Map.class);
        StringBuilder sb = new StringBuilder();
        for (Object value : m.values()) {
            sb.append(value).append(" ");
        }
        return sb.toString();
    }

    private void proHosRev(Map map) {
        String hosRevDetailStr = MapUtil.getStr(map, "hosRevDetail");
        if (StringUtils.isEmpty(hosRevDetailStr)) {
            return;
        }
        List<Map> hosRevDetailList = JSON.parseArray(hosRevDetailStr, Map.class);
        StringBuilder sb = new StringBuilder();
        for (Map m : hosRevDetailList) {
            sb.append(MapUtil.getStr(m, "hosRevOther")).append("  ");
        }
        map.put("hosRevDetail", sb.toString());
    }

    private void isNotPro(Map map, Map<String, String> yesOrNoFileMap) {
        String[] keys = {"hosRevIs", "reviewHospitalIs", "reviewHospitalCoach", "trainReviewerIs"};
        for (String key : keys) {
            map.put(key, yesOrNoFileMap.get(key + "-" + map.get(key)));
        }
    }

    private void workExperience(Map map) {
        String workExperience = MapUtil.getStr(map, "workExperience");
        if (CharSequenceUtil.isEmpty(workExperience)) {
            return;
        }
        List<com.alibaba.fastjson.JSONObject> jsonObjectList =
                JSON.parseArray(workExperience, com.alibaba.fastjson.JSONObject.class);
        StringBuilder sb = new StringBuilder();
        for (com.alibaba.fastjson.JSONObject jsonObject : jsonObjectList) {
            sb.append(jsonObject.get("startTime"))
                    .append("-")
                    .append(jsonObject.get("endTime"))
                    .append("   ")
                    .append(jsonObject.get("company"))
                    .append("-")
                    .append(jsonObject.get("companyPost"))
                    .append("   ");
        }
        map.put("workExperience", sb.toString());
    }

    private void queryLearn(Map map) {
        map.put("learnIs", "否");
        String accountId = MapUtil.getStr(map, "accountId");
        List<CertificateAbility> certificateAbilityList =
                certificateAbilityMapper.selectCertificateAbilityByProcessCode(accountId);
        if (CollUtil.isEmpty(certificateAbilityList)) {
            return;
        }
        StringBuilder leanSb = new StringBuilder();
        for (CertificateAbility certificateAbility : certificateAbilityList) {
            String abilityName = certificateAbility.getAbilityName();
            if (CharSequenceUtil.isEmpty(abilityName)) {
                continue;
            }
            leanSb.append(abilityName).append("\n");
        }
        String learn = leanSb.toString();
        map.put("learnIs", StringUtils.isEmpty(learn) ? "否" : "是");
        map.put("learn", learn);
    }

    private String queryDictValue(String dictType, String dictValue) {
        return sysDictDataMapper.selectDictLabel(dictType, dictValue);
    }
}
