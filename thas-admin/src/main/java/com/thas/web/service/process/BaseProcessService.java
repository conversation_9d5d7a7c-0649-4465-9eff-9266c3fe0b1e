package com.thas.web.service.process;

import com.thas.web.domain.AutSaAudList;
import com.thas.web.domain.AutSaAudQueryDTO;
import com.thas.web.domain.AutSaAudSaveDTO;
import com.thas.web.domain.vo.AutSaAudDetailVO;
import java.util.List;

/**
 * 评审流程服务
 *
 * <AUTHOR>
 * @date 2022-04-01
 */
public interface BaseProcessService {

    /**
     * 流程处理
     *
     * @param req 流程参数
     */
    void process(AutSaAudSaveDTO req);

    /**
     * 查询列表
     *
     * @param req 流程参数
     * @return 列表数据
     */
    List<AutSaAudList> queryList(AutSaAudQueryDTO req);

    /**
     * 查询详情
     *
     * @param req 流程参数
     * @return 详情数据
     */
    AutSaAudDetailVO queryDetail(AutSaAudQueryDTO req);

}
