package com.thas.web.service;

import com.thas.web.domain.ReviewTraPractice;
import com.thas.web.domain.vo.ReviewerTraineesRecVO;

import java.util.List;


/**
 * 评审学员参与评审记录Service接口
 *
 * <AUTHOR>
 * @date 2022-05-23
 */
public interface IReviewTraPracticeService {
    /**
     * 查询评审学员参与评审记录
     *
     * @param id 评审学员参与评审记录主键
     * @return 评审学员参与评审记录
     */
    ReviewTraPractice selectReviewTraPracticeById(Long id);

    /**
     * 查询评审学员参与评审记录列表
     *
     * @param reviewerTraineesRec 评审学员参与评审记录
     * @return 评审学员参与评审记录集合
     */
    List<ReviewTraPractice> selectReviewTraPracticeList(ReviewTraPractice reviewerTraineesRec);

    /**
     * 新增评审学员参与评审记录
     *
     * @param reviewerTraineesRec 评审学员参与评审记录
     * @return 结果
     */
    int insertReviewTraPractice(ReviewTraPractice reviewerTraineesRec);

    /**
     * 修改评审学员参与评审记录
     *
     * @param reviewerTraineesRec 评审学员参与评审记录
     * @return 结果
     */
    int updateReviewTraPractice(ReviewTraPractice reviewerTraineesRec);

    /**
     * 批量删除评审学员参与评审记录
     *
     * @param ids 需要删除的评审学员参与评审记录主键集合
     * @return 结果
     */
    int deleteReviewTraPracticeByIds(Long[] ids);

    /**
     * 删除评审学员参与评审记录信息
     *
     * @param id 评审学员参与评审记录主键
     * @return 结果
     */
    int deleteReviewTraPracticeById(Long id);

}
