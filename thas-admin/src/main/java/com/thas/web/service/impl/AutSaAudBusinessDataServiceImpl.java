package com.thas.web.service.impl;

import com.thas.web.domain.AutSaAudBusinessData;
import com.thas.web.mapper.AutSaAudBusinessDataMapper;
import com.thas.web.service.IAutSaAudBusinessDataService;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 自评审核业务数据 Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-17
 */
@Component
@Slf4j
public class AutSaAudBusinessDataServiceImpl implements IAutSaAudBusinessDataService {

    @Resource
    private AutSaAudBusinessDataMapper autSaAudBusinessDataMapper;

    /**
     * 查询自评审核业务数据
     *
     * @param autCodes      自评编码
     * @param businessCode 环节
     * @return 自评审核业务数据
     */
    @Override
    public List<AutSaAudBusinessData> selectAutSaAudBusinessData(String autCodes, String businessCode) {
        return autSaAudBusinessDataMapper.selectAutSaAudBusinessData(autCodes, businessCode);
    }

    /**
     * 保存自评审核业务数据
     *
     * @param autSaAudBusinessData 自评审核业务数据
     * @return 结果
     */
    @Override
    public int saveAutSaAudBusinessData(AutSaAudBusinessData autSaAudBusinessData) {
        return autSaAudBusinessDataMapper.saveAutSaAudBusinessData(autSaAudBusinessData);
    }

    /**
     * 更新自评审核业务数据
     *
     * @param autSaAudBusinessData 自评审核业务数据
     * @return 结果
     */
    @Override
    public int updateAutSaAudBusinessData(AutSaAudBusinessData autSaAudBusinessData) {
        return autSaAudBusinessDataMapper.updateAutSaAudBusinessData(autSaAudBusinessData);
    }

    /**
     * 删除自评审核业务数据
     *
     * @param autCode      自评编码
     * @param businessCode 环节
     * @return 自评审核业务数据
     * @return 结果
     */
    @Override
    public int deleteAutSaAudBusinessData(String autCode, String businessCode) {
        return autSaAudBusinessDataMapper.deleteAutSaAudBusinessData(autCode, businessCode);
    }
}
