package com.thas.web.service.impl;

import com.thas.common.utils.DateUtils;
import com.thas.web.domain.AsaStatusConfig;
import com.thas.web.mapper.AsaStatusConfigMapper;
import com.thas.web.service.IAsaStatusConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 节点配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-08-17
 */
@Service
public class AsaStatusConfigServiceImpl implements IAsaStatusConfigService
{
    @Autowired
    private AsaStatusConfigMapper asaStatusConfigMapper;

    /**
     * 查询节点配置
     * 
     * @param id 节点配置主键
     * @return 节点配置
     */
    @Override
    public AsaStatusConfig selectAsaStatusConfigById(Long id)
    {
        return asaStatusConfigMapper.selectAsaStatusConfigById(id);
    }

    /**
     * 查询节点配置列表
     * 
     * @param asaStatusConfig 节点配置
     * @return 节点配置
     */
    @Override
    public List<AsaStatusConfig> selectAsaStatusConfigList(AsaStatusConfig asaStatusConfig)
    {
        return asaStatusConfigMapper.selectAsaStatusConfigList(asaStatusConfig);
    }

    /**
     * 新增节点配置
     * 
     * @param asaStatusConfig 节点配置
     * @return 结果
     */
    @Override
    public int insertAsaStatusConfig(AsaStatusConfig asaStatusConfig)
    {
        asaStatusConfig.setCreateTime(DateUtils.getNowDate());
        return asaStatusConfigMapper.insertAsaStatusConfig(asaStatusConfig);
    }

    /**
     * 修改节点配置
     * 
     * @param asaStatusConfig 节点配置
     * @return 结果
     */
    @Override
    public int updateAsaStatusConfig(AsaStatusConfig asaStatusConfig)
    {
        asaStatusConfig.setUpdateTime(DateUtils.getNowDate());
        return asaStatusConfigMapper.updateAsaStatusConfig(asaStatusConfig);
    }

    /**
     * 批量删除节点配置
     * 
     * @param ids 需要删除的节点配置主键
     * @return 结果
     */
    @Override
    public int deleteAsaStatusConfigByIds(Long[] ids)
    {
        return asaStatusConfigMapper.deleteAsaStatusConfigByIds(ids);
    }

    /**
     * 删除节点配置信息
     * 
     * @param id 节点配置主键
     * @return 结果
     */
    @Override
    public int deleteAsaStatusConfigById(Long id)
    {
        return asaStatusConfigMapper.deleteAsaStatusConfigById(id);
    }
}
