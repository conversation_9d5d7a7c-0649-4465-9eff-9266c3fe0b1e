package com.thas.web.service;

import com.thas.web.domain.TraAccountExam;

import java.util.List;

/**
 * 评审员学习资源考卷通过情况Service接口
 *
 * <AUTHOR>
 * @date 2022-01-20
 */
public interface ITraAccountExamService {
    /**
     * 查询评审员学习资源考卷通过情况
     *
     * @param id 评审员学习资源考卷通过情况主键
     * @return 评审员学习资源考卷通过情况
     */
    TraAccountExam selectTraAccountExamById(Long id);

    /**
     * 查询评审员学习资源考卷通过情况列表
     *
     * @param traAccountExam 评审员学习资源考卷通过情况
     * @return 评审员学习资源考卷通过情况集合
     */
    List<TraAccountExam> selectTraAccountExamList(TraAccountExam traAccountExam);

    /**
     * 新增评审员学习资源考卷通过情况
     *
     * @param traAccountExam 评审员学习资源考卷通过情况
     * @return 结果
     */
    int insertTraAccountExam(TraAccountExam traAccountExam);

    /**
     * 修改评审员学习资源考卷通过情况
     *
     * @param traAccountExam 评审员学习资源考卷通过情况
     * @return 结果
     */
    int updateTraAccountExam(TraAccountExam traAccountExam);

    /**
     * 批量删除评审员学习资源考卷通过情况
     *
     * @param ids 需要删除的评审员学习资源考卷通过情况主键集合
     * @return 结果
     */
    int deleteTraAccountExamByIds(Long[] ids);

    /**
     * 删除评审员学习资源考卷通过情况信息
     *
     * @param id 评审员学习资源考卷通过情况主键
     * @return 结果
     */
    int deleteTraAccountExamById(Long id);

    String pass(String account, Long roleId);

    void saveOrUpdate(TraAccountExam traAccountExam);
}
