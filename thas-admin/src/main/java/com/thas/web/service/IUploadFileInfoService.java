package com.thas.web.service;

import com.thas.web.domain.FileInfoDTO;
import java.util.List;
import javax.validation.Valid;

/**
 * 上传文件信息服务
 *
 * <AUTHOR>
 * @date 2022-01-17
 */
public interface IUploadFileInfoService {

    /**
     * 查询列表
     *
     * @param fileInfoDTO 查询条件
     * @return 文件信息
     */
    List<FileInfoDTO> selectUploadFileInfoList(FileInfoDTO fileInfoDTO);

    /**
     * 根据文件ids查询文件信息
     *
     * @param ids 文件id列表（逗号拼接）
     * @return 文件信息
     */
    List<FileInfoDTO> getUploadFileInfoByIds(String ids);

    /**
     * 根据文件名获取文件id值
     *
     * @param origin 文件名
     * @return 文件信息
     */
    List<FileInfoDTO> getUploadFileInfoByOrigin(String origin);

    /**
     * 根据文件id查询信息
     *
     * @param fileId 文件id
     * @return 文件信息
     */
    FileInfoDTO getUploadFileInfoById(Integer fileId);

    /**
     * 保存文件信息，返回生成的iileId
     *
     * @param fileInfoDTO 文件信息
     * @return 生成的fileId
     */
    FileInfoDTO saveUploadFileInfo(@Valid FileInfoDTO fileInfoDTO);

}
