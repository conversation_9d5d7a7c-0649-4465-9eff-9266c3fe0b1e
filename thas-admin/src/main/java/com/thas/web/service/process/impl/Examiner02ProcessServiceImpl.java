package com.thas.web.service.process.impl;

import cn.hutool.core.util.ObjectUtil;
import com.thas.common.constant.Constants;
import com.thas.common.constant.Constants.HospitalConstants;
import com.thas.common.enums.*;
import com.thas.common.exception.ServiceException;
import com.thas.web.domain.*;
import com.thas.web.domain.vo.AutSaAudDetailVO;
import com.thas.web.domain.vo.FileInfoVO;
import com.thas.web.mapper.AutSaAudMapper;
import com.thas.web.service.CommonService;
import com.thas.web.service.IAutSaAudBusinessDataService;
import com.thas.web.service.IUploadFileInfoService;
import com.thas.web.service.process.BaseProcessService;
import com.thas.web.service.process.CommonProcessService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.TreeSet;
import java.util.stream.Collectors;

/**
 * 审查员审查评审报告流程服务 Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-04-01
 */
@Component("examiner02ProcessService")
@Slf4j
public class Examiner02ProcessServiceImpl implements BaseProcessService {

    @Resource
    private CommonProcessService commonProcessService;

    @Resource
    private AutSaAudMapper autSaAudMapper;

    @Autowired
    private CommonService commonService;

    @Autowired
    private IUploadFileInfoService uploadFileInfoService;

    @Autowired
    private IAutSaAudBusinessDataService autSaAudBusinessDataService;

    @Override
    public void process(AutSaAudSaveDTO req) {
        long startTime = System.currentTimeMillis();
        log.info("examiner02ProcessService.process ------ 开始");
        if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.WAIT_FR_REPORT_R_CLAUSE, AutSaAudStatusEnum.FR_REPORT_R_CLAUSE_PROCESS)) {
            // 评审报告待审查 + 评审报告审查中
            if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(req.getSubmitType(), AutSaAudSubmitTypeEnum.FR_REPORT_R_CLAUSE)) {
                commonProcessService.checkClause(req);
                if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.WAIT_FR_REPORT_R_CLAUSE)) {
                    commonProcessService.processInitialScene(req);
                } else {
                    commonProcessService.processClauseScene(req);
                }
            } else {
                commonProcessService.processConfirmSkip(req);
            }
        } else if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.FR_REPORT_R_CLAUSE_SUMMARY)) {
            // 评审报告审查总结
            AutSaAud autSaAud = new AutSaAud();
            autSaAud.setSubmitType(req.getSubmitType());
            autSaAud.setAutCode(req.getAutCode());
            autSaAud.setAccountId(req.getAccountId());
            req.setAutSaAudLists(Arrays.asList(autSaAud));
            commonProcessService.processSummary(req);
            List<AutSaAud> frReportRClauses = autSaAudMapper.selectAutSaAudListByAutCodeAndTypes(req.getAutCode(), AutSaAudSubmitTypeEnum.FR_REPORT_R_CLAUSE.getSubmitType());
            // 有驳回数据，节点翻转到评审员第一次待修改条款流程服务      没有驳回数据，节点直接跳转到事实准确性待审查
            StatusProcessEnum statusProcessEnum = (CollectionUtils.isNotEmpty(frReportRClauses) &&
                    frReportRClauses.stream().anyMatch(a -> AutSaAudResultEnum.checkIsAutSaAudResultEnum(a.getAutResult(), AutSaAudResultEnum.DISAGREE))) ? StatusProcessEnum.RJ : StatusProcessEnum.PS;
            String nextStatus = commonProcessService.getNextStatus(req.getAutSaAudStatusConfig().getNextStatusConfig(), statusProcessEnum);
            log.info("节点翻转到：{}", nextStatus);
            // 更新关联表 aut_sa_relation
            commonProcessService.updateAutSaRelation(nextStatus, req.getAutCode());
            //如果流程通过，删除驳回修改评审报告业务数据
            if (StatusProcessEnum.PS.equals(statusProcessEnum)) {
                commonProcessService.delAutSaAudBusinessData(req.getAutSaRelation().getAutCode(), AutSaAudBusinessCodeEnum.SR_CLAUSE_M_DATA.getCode());
            }
            //评审报告形式审查驳回后，邮件通知，只通知评审组长、和对应条款的评审员，抄送审查组长、所有的管理员；
            commonProcessService.frReportEmailToUsers(frReportRClauses, req);
        } else if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.SR_REPORT_M)) {
            // 修改评审报告
            if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(req.getSubmitType(), AutSaAudSubmitTypeEnum.SR_REPORT_M)) {
                commonProcessService.checkClause(req);
                //查询入参款是否有数据，有数据把之前的修改数据置换
                //如果没数据插入，不用置换
                this.packAutDescAndAutResult(req);

                commonProcessService.processClauseScene(req);
            } else {
                commonProcessService.processConfirmSkip(req);
                if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(req.getSubmitType(), AutSaAudSubmitTypeEnum.SR_REPORT_M_CONFIRM)) {
                    //重新评分，生成业务数据
                    commonProcessService.generateReport(req.getAutSaRelation().getAutCode(), req.getAutSaRelation().getAutCsId(),
                            String.join(",", Arrays.asList(AutSaAudSubmitTypeEnum.SR_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.SR_CLAUSE_M.getSubmitType(),
                                    AutSaAudSubmitTypeEnum.FAR_CLAUSE_M.getSubmitType(), AutSaAudSubmitTypeEnum.TR_CLAUSE_M.getSubmitType(),
                                    AutSaAudSubmitTypeEnum.SR_REPORT_M.getSubmitType(), AutSaAudSubmitTypeEnum.SR_REPORT_M_SUMMARY.getSubmitType())),
                            AutSaAudBusinessCodeEnum.AUT_SA_AUD_REPORT.getCode(), Lists.newArrayList());
                }
            }
        } else if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.SR_REPORT_M_MEET)) {
            AutSaAudBusinessData autSaAudBusinessData = req.getAutSaAudBusinessData();
            if (autSaAudBusinessData == null || StringUtils.isBlank(autSaAudBusinessData.getBusinessCode()) || StringUtils.isBlank(autSaAudBusinessData.getData())) {
                throw new ServiceException("【审查组长上传会议纪要】节点，AutSaAudBusinessData入参不能为空");
            }
            //保存上会纪要文件信息
            autSaAudBusinessData.setAutCode(req.getAutCode());
            autSaAudBusinessDataService.saveAutSaAudBusinessData(autSaAudBusinessData);

            String nextStatus = commonProcessService.getNextStatus(req.getAutSaAudStatusConfig().getNextStatusConfig(), StatusProcessEnum.PS);
            log.info("节点翻转到：{}", nextStatus);
            // 更新关联表 aut_sa_relation
            commonProcessService.updateAutSaRelation(nextStatus, req.getAutCode());


        }
        log.info("examiner02ProcessService.process ------ 结束 耗时：{}", System.currentTimeMillis() - startTime);
    }

    private void packAutDescAndAutResult(AutSaAudSaveDTO req) {
        List<AutSaAud> autSaAudLists = req.getAutSaAudLists();
        if (CollectionUtils.isNotEmpty(autSaAudLists)) {
            List<String> clauseIds = autSaAudLists.stream().filter(o -> StringUtils.isNotBlank(o.getClauseId())).map(AutSaAud::getClauseId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(clauseIds)) {
                AutSaAud autSaAud = new AutSaAud();
                autSaAud.setStatus(Constants.INT_ONE);
                autSaAud.setAutCode(req.getAutCode());
                autSaAud.setSubmitType(AutSaAudSubmitTypeEnum.SR_REPORT_M.getSubmitType());
                autSaAud.setClauseId(StringUtils.join(clauseIds, ","));
                List<AutSaAud> autSaAudList = autSaAudMapper.selectAutSaAudList(autSaAud);
                //获取最新的驳回款进行置换
                autSaAudList = autSaAudList.stream().sorted(Comparator.comparing(AutSaAud::getCreateTime).reversed()).
                        collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(AutSaAud::getClauseId))), ArrayList::new));
                //查询出来的描述置换到’修改前的描述字段‘
                if (CollectionUtils.isNotEmpty(autSaAudList)) {
                    List<AutSaAud> finalAutSaAudList = autSaAudList;
                    req.getAutSaAudLists().forEach(reqAutSaAud -> {
                        finalAutSaAudList.forEach(qryAutSaAud -> {
                            if (ObjectUtil.equal(reqAutSaAud.getClauseId(), qryAutSaAud.getClauseId())) {
                                reqAutSaAud.setBeforeAutDesc(qryAutSaAud.getAutDesc());
                                reqAutSaAud.setBeforeAutResult(qryAutSaAud.getAutResult());
                            }
                        });
                    });
                }
            }
        }
    }

    @Override
    public List<AutSaAudList> queryList(AutSaAudQueryDTO req) {
        List<AutSaAudList> autSaAudLists = new ArrayList<>();
        AutSaAudList autSaAudList = new AutSaAudList();

        // 公共反参
        commonProcessService.getGroupProgressList(autSaAudList, req);

        // 查询 AutSaAud
        queryAutSaAud(req, autSaAudList);

        autSaAudLists.add(autSaAudList);

        //排序：按完成时间倒序排序
        return commonProcessService.listSorted(autSaAudLists);
    }

    private void queryAutSaAud(AutSaAudQueryDTO req, AutSaAudList autSaAudList) {
        AutSaAud autSaAud = new AutSaAud();
        autSaAud.setAutCode(req.getAutSaRelation().getAutCode());
        autSaAud.setSubmitType("tr_clause,tr_summary,sr_clause,tr_clause_m,tr_clause_m_skip,far_summary,far_clause_m_submit");
        autSaAud.setStatus(HospitalConstants.NUM_1);
        List<AutSaAud> autSaAudLists = autSaAudMapper.selectAutSaAudList(autSaAud);

        if (CollectionUtils.isEmpty(autSaAudLists)) {
            return;
        }

        autSaAudList.setAutSaAud(autSaAudLists);

        assFileInfoMap(autSaAudList);
    }

    private void assFileInfoMap(AutSaAudList autSaAudList) {
        List<AutSaAud> autSaAudLists = autSaAudList.getAutSaAud();
        if (CollectionUtils.isEmpty(autSaAudLists)) {
            return;
        }

        Optional<AutSaAud> autSaAudOpt = autSaAudLists.stream()
                .filter(autSaAud -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(autSaAud.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_SUMMARY))
                .filter(autSaAud -> StringUtils.isNotBlank(autSaAud.getFileIds()))
                .findAny();

        if (!autSaAudOpt.isPresent()) {
            return;
        }

        String fileIds = autSaAudOpt.get().getFileIds();
        List<FileInfoDTO> fileInfoDTOList = uploadFileInfoService.getUploadFileInfoByIds(fileIds);
        List<FileInfoVO> fileInfoVOList = commonService.fileInfoDtoToVo(fileInfoDTOList);
        Map<String, List<FileInfoVO>> fileDetailMap = fileInfoVOList.stream().map(o -> {
            o.setFileType(AutSaAudSubmitTypeEnum.FAR_SUMMARY.getSubmitType());
            return o;
        }).collect(Collectors.groupingBy(FileInfoVO::getFileId));

        if (MapUtils.isEmpty(autSaAudList.getFileDetailMap())) {
            autSaAudList.setFileDetailMap(fileDetailMap);
        } else {
            autSaAudList.getFileDetailMap().putAll(fileDetailMap);
        }
    }

    @Override
    public AutSaAudDetailVO queryDetail(AutSaAudQueryDTO req) {
        return null;
    }

}
