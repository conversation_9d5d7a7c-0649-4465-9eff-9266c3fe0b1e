package com.thas.web.service;

import com.thas.common.enums.ImportFileTypeEnum;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;

/**
 * 认证记录Service接口
 *
 * <AUTHOR>
 * @date 2022-01-17
 */
public interface ImportService {

    /**
     * 处理导入的文件
     *
     * @param in                 输入流
     * @param importFileTypeEnum 导入的文件对应的枚举
     * @param title              标题
     * @return
     * @throws Exception
     */
    void importExcel(InputStream in, ImportFileTypeEnum importFileTypeEnum, String title);

    /**
     * 测试-认证导入-优化内容
     * */
    void testImportExcel(InputStream in, ImportFileTypeEnum importFileTypeEnum, String title, HttpServletResponse response);

}
