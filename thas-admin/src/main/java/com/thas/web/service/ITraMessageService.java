package com.thas.web.service;

import com.thas.web.domain.TraMessage;

import java.util.List;

/**
 * 留言管理Service接口
 *
 * <AUTHOR>
 * @date 2022-01-20
 */
public interface ITraMessageService {

    /**
     * 查询留言管理列表
     *
     * @param traMessage 留言管理
     * @return 留言管理集合
     */
    List<TraMessage> selectTraMessageList(TraMessage traMessage);

    /**
     * 新增留言管理
     *
     * @param traMessage 留言管理
     * @return 结果
     */
    int insertTraMessage(TraMessage traMessage);

    /**
     * 批量删除留言管理
     *
     * @param ids 需要删除的留言管理主键集合
     * @return 结果
     */
    int deleteTraMessageByIds(Long[] ids);

    int doLike(Long messageId, String type);

    Long selectTraMessageCount(TraMessage traMessage);
}
