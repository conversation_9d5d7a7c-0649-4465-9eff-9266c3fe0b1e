package com.thas.web.service;

import com.thas.web.domain.CstDomain;
import com.thas.web.domain.CstDomainVO;
import com.thas.web.domain.vo.DomainGroupNode;
import com.thas.web.dto.UnDomainListVO;

import java.util.List;
import java.util.Map;


/**
 * 领域Service接口
 *
 * <AUTHOR>
 * @date 2022-01-21
 */
public interface ICstDomainService {
    /**
     * 查询领域
     *
     * @param id        领域主键
     * @param versionId
     * @return 领域
     */
    Map selectCstDomainById(Long id, String versionId);

    /**
     * 查询领域列表
     *
     * @param cstDomain 领域
     * @return 领域集合
     */
    List<CstDomain> selectCstDomainList(CstDomain cstDomain);

    /**
     * 新增领域
     *
     * @param cstDomainVO 领域
     * @return 结果
     */
    Integer insertCstDomain(CstDomainVO cstDomainVO);

    /**
     * 修改领域
     *
     * @param cstDomainVO 领域
     * @return 结果
     */
    int updateCstDomain(CstDomainVO cstDomainVO);

    /**
     * 删除领域信息
     *
     * @param id 领域主键
     * @return 结果
     */
    int deleteCstDomainById(Long id);

    List<DomainGroupNode> selectGroupNodeByNotInIds(String domainIds);

    /**
     * 通过有效版本号查询领域对应的分组信息
     * @param versionId 有效版本号
     * @return 对应分组信息
     */
    List<DomainGroupNode> selectCstDomainGroup(String domainIds, String versionId);

    int saveGroupIdListById(List<DomainGroupNode> insertList, Long id);
}
