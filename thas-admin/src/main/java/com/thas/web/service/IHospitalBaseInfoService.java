package com.thas.web.service;

import com.thas.common.core.domain.AjaxResult;
import com.thas.common.core.domain.entity.SysUser;
import com.thas.system.domain.vo.SendEmailCodeVo;
import com.thas.web.domain.AutRecord;
import com.thas.web.domain.HospitalBaseInfo;
import com.thas.web.domain.vo.ModifyTheReviewCycleVo;
import com.thas.web.dto.AgainHosInfoSubmitRequest;
import com.thas.web.dto.CstCertificationStandardVO;
import com.thas.web.dto.HosPlanDetailDTO;
import com.thas.web.dto.HosPlanDetailVO;
import com.thas.web.dto.HosPlanSubmitDTO;
import com.thas.web.dto.HosReviewPlanVO;
import com.thas.web.dto.HospitalBaseInfoDTO;
import com.thas.web.dto.QryHosStatusRequest;
import com.thas.web.dto.QueryBaseConditionDTO;
import com.thas.web.dto.QueryHosReviewPlanDTO;
import com.thas.web.dto.QueryHospitalListDTO;
import com.thas.web.dto.QueryUnassignedHosDTO;
import com.thas.web.dto.TempHosInfoRequest;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Map;

/**
 * 医疗机构详情Service接口
 *
 * <AUTHOR>
 * @date 2022-01-19
 */
public interface IHospitalBaseInfoService {


    /**
     * 医疗机构提交信息
     *
     * @param hospitalBaseInfoDTO 信息数据
     * @return 提交结果
     */
    AjaxResult hospitalBaseInfoSubmit(HospitalBaseInfoDTO hospitalBaseInfoDTO);


    /**
     * 查询医疗结构数据列表
     *
     * @param queryHospitalListDTO 查询入参
     * @return 查询结果
     */
    List<QueryHospitalListDTO> queryHospitalList(QueryHospitalListDTO queryHospitalListDTO);

    /**
     * 通过认证编号查询对应的认证详情
     * @param queryBaseConditionDTO 认证编号
     * @return 查询结果
     */
    HospitalBaseInfoDTO queryHospitalDetail(QueryBaseConditionDTO queryBaseConditionDTO);

    int updateAuthStatusByApplyNo(AutRecord autRecord);

    int updateHospitalBaseInfo(HospitalBaseInfo hospitalBaseInfo);

    /**
     * 通知医院客户，账号生成
     *
     * @param applyNo  申请号
     * @param userName 用户名
     * @param password 初始密码
     */
    void notifyHosAccount(String applyNo, String userName, String password);

    List<HosReviewPlanVO> queryReviewPlanList(QueryHosReviewPlanDTO queryHosReviewPlanDTO);

    List<CstCertificationStandardVO> queryHosPlanClauseDetail(HosPlanDetailDTO hosPlanDetailDTO);

    HosPlanDetailVO queryHosPlanDetail(HosPlanDetailDTO hosPlanDetailDTO);

    /**
     * 根据医疗机构认证编码失效对应评审分配计划
     *
     * @param applyNo 医疗机构认证编码
     */
    void invalidHosPlan(String applyNo);

    List<HosReviewPlanVO> selectHosPlanClauseDetail(String applyNo);
    /**
     * 通过登录用户id查询对应医疗机构applyNo认证编码
     */
    String selectApplyNoByUserId();

    List<QueryUnassignedHosDTO> queryUnassignedHos(QueryBaseConditionDTO queryBaseConditionDTO);

    AjaxResult hosPlanSubmit(HosPlanSubmitDTO hosPlanSubmitDTO);

    /**
     * 查询医疗机构详情
     *
     * @param queryBaseConditionDTO 医疗机构详情认证编号
     * @return 医疗机构详情
     */
    HospitalBaseInfo selectHospitalByApplyNo(QueryBaseConditionDTO queryBaseConditionDTO);

    /**
     * 查询医院申请前，当年已申请的数量
     *
     * @param applyNO 医院编码
     * @return 医院申请前，当年已申请的数量
     */
    Map<String, Object> selectCurrentYearApplyCountByApplyNO(String applyNO);

    /**
     * 免登查询医疗机构详情
     *
     * @param request 入参
     * @return HospitalBaseInfoDTO
     */
    HospitalBaseInfoDTO bingTangHospitalDetail(TempHosInfoRequest request);

    /**
     * 医疗机构基本数据列表查询-导出
     * @param queryHospitalListDTO 搜索查询条件
     * @return
     */
    void hospitalListExport(QueryHospitalListDTO queryHospitalListDTO, HttpServletResponse response) throws UnsupportedEncodingException;

    /**
     * 查询医疗机构状态
     *
     * @param request 入参
     * @return Map
     */
    Map<String, Object> qryHosStatus(QryHosStatusRequest request);

    /**
     * 医疗机构再次提交
     *
     * @param request 入参
     * @return AjaxResult
     */
    AjaxResult hosAgainSubmit(AgainHosInfoSubmitRequest request);


    /**
     * 查询当前登录医院用户信息
     *
     * @return HospitalBaseInfo
     */
    HospitalBaseInfo queryCurHosBaseInfo();

    /**
     * 查询已经分配账号且审核通过医院信息
     *
     * @return HospitalBaseInfo
     */
    List<HospitalBaseInfo> qryPassHosInfo();

    void sendEmailCode(SendEmailCodeVo sendEmailCodeVo);

    Boolean reviewCycleJudge(HosPlanDetailVO hosPlanDetailVO, String applyNo, ModifyTheReviewCycleVo modifyTheReviewCycleVo);

    void checkUserWork(SysUser user);
}
