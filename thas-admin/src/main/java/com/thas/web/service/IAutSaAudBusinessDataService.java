package com.thas.web.service;

import com.thas.web.domain.AutSaAudBusinessData;
import java.util.List;

/**
 * 自评审核业务数据 Service接口
 *
 * <AUTHOR>
 * @date 2022-01-17
 */
public interface IAutSaAudBusinessDataService {

    /**
     * 查询自评审核业务数据
     *
     * @param autCodes      自评编码
     * @param businessCode 环节
     * @return 自评审核业务数据
     */
    List<AutSaAudBusinessData> selectAutSaAudBusinessData(String autCodes, String businessCode);

    /**
     * 保存自评审核业务数据
     *
     * @param autSaAudBusinessData 自评审核业务数据
     * @return 结果
     */
    int saveAutSaAudBusinessData(AutSaAudBusinessData autSaAudBusinessData);

    /**
     * 更新自评审核业务数据
     *
     * @param autSaAudBusinessData 自评审核业务数据
     * @return 结果
     */
    int updateAutSaAudBusinessData(AutSaAudBusinessData autSaAudBusinessData);

    /**
     * 删除自评审核业务数据
     *
     * @param autCode      自评编码
     * @param businessCode 环节
     * @return 自评审核业务数据
     * @return 结果
     */
    int deleteAutSaAudBusinessData(String autCode, String businessCode);

}
