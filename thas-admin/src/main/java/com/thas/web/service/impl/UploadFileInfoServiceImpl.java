package com.thas.web.service.impl;

import com.alibaba.fastjson.JSON;
import com.thas.common.exception.ServiceException;
import com.thas.common.validate.ValidGroup;
import com.thas.web.domain.FileInfoDTO;
import com.thas.web.mapper.UploadFileInfoMapper;
import com.thas.web.service.IUploadFileInfoService;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 文件服务
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@Component
@Transactional(rollbackFor = Exception.class)
public class UploadFileInfoServiceImpl implements IUploadFileInfoService {

    @Resource
    private UploadFileInfoMapper uploadFileInfoMapper;

    /**
     * 查询列表
     *
     * @param fileInfoDTO 查询条件
     * @return 文件信息
     */
    @Override
    public List<FileInfoDTO> selectUploadFileInfoList(FileInfoDTO fileInfoDTO) {
        log.info("查询文件id列表：{} 对应的文件信息", JSON.toJSONString(fileInfoDTO));
        return uploadFileInfoMapper.selectUploadFileInfoList(fileInfoDTO);
    }

    /**
     * 根据文件ids查询文件信息
     *
     * @param ids 文件id列表（逗号拼接）
     * @return 文件信息
     */
    @Override
    public List<FileInfoDTO> getUploadFileInfoByIds(String ids) {
        log.info("查询文件id列表：{} 对应的文件信息", ids);
        return uploadFileInfoMapper.getUploadFileInfoByIds(ids);
    }

    /**
     * 根据文件名获取文件id值
     *
     * @param origin 文件名
     * @return 文件信息
     */
    @Override
    public List<FileInfoDTO> getUploadFileInfoByOrigin(String origin) {
        log.info("查询文件名：{} 对应的文件信息", origin);
        return uploadFileInfoMapper.getUploadFileInfoByOrigin(origin);
    }

    /**
     * 根据文件id查询信息
     *
     * @param fileId 文件id
     * @return 文件信息
     */
    @Override
    public FileInfoDTO getUploadFileInfoById(Integer fileId) {
        log.info("查询文件id：{} 对应的文件信息", fileId);
        return uploadFileInfoMapper.getUploadFileInfoById(fileId);
    }

    /**
     * 保存文件信息，返回生成的iileId
     *
     * @param fileInfoDTO 文件信息
     * @return 生成的fileId
     */
    @Override
    @Validated(ValidGroup.Group1.class)
    public FileInfoDTO saveUploadFileInfo(@Valid FileInfoDTO fileInfoDTO) {
        log.info("落表保存的文件信息：{}", JSON.toJSONString(fileInfoDTO));
        int count = uploadFileInfoMapper.insertUploadFileInfo(fileInfoDTO);
        if (count != 1) {
            log.info("落表保存的文件信息：{} 异常", JSON.toJSONString(fileInfoDTO));
            throw new ServiceException("保存文件信息异常");
        }
        return fileInfoDTO;
    }

}
