package com.thas.web.service.process;

import com.thas.common.enums.StatusProcessEnum;
import com.thas.web.domain.AutSaAud;
import com.thas.web.domain.AutSaAudList;
import com.thas.web.domain.AutSaAudQueryDTO;
import com.thas.web.domain.AutSaAudReport;
import com.thas.web.domain.AutSaAudSaveDTO;
import com.thas.web.domain.vo.AutSaAudDetailVO;
import com.thas.web.domain.vo.FileInfoVO;
import com.thas.web.dto.HosPlanUserInfoVO;
import com.thas.web.dto.HosReviewPlanVO;
import java.lang.reflect.Method;
import java.util.List;

/**
 * 通用流程服务
 *
 * <AUTHOR>
 * @date 2022-04-01
 */
public interface CommonProcessService {

    /**
     * 校验提交流程周期时间
     *
     * @param applyNo   医疗机构编码
     * @param autCode   自评编码
     * @param curStatus 当前节点状态
     */
    void checkSubmitCycle(String applyNo, String autCode, String curStatus);

    /**
     * 校验查询流程周期时间
     *
     * @param applyNo   医疗机构编码
     * @param autCode   自评编码
     * @param curStatus 当前节点状态
     */
    void checkQueryCycle(String applyNo, String autCode, String curStatus);

    /**
     * 校验提交权限--(校验用户是否已分配到医疗机构名下)
     *
     * @param req 流程参数
     */
    void checkSubmitPermission(AutSaAudSaveDTO req);

    /**
     * 校验提交的款id
     *
     * @param req 流程参数
     */
    void checkClause(AutSaAudSaveDTO req);

    /**
     * 处理初始场景
     *
     * @param req 流程参数
     */
    void processInitialScene(AutSaAudSaveDTO req);

    /**
     * 处理款提交场景
     *
     * @param req 流程参数
     */
    void processClauseScene(AutSaAudSaveDTO req);

    /**
     * 处理条或者款提交结果
     *
     * @param req        流程参数
     * @param totalCount 条/款上限
     * @return 结果
     */
    List<AutSaAud> processClauseOrArticle(AutSaAudSaveDTO req, int totalCount);

    /**
     * 更新条款信息
     *
     * @param autSaAudLists    当前提交的数据
     * @param hisAutSaAudLists 已提交数据
     * @param totalCount       条款总数
     * @return
     */
    List<AutSaAud> updateClauseAutSaAud(List<AutSaAud> autSaAudLists, List<AutSaAud> hisAutSaAudLists, int totalCount);

    /**
     * 处理总结提交场景（总结提交无节点复杂翻转逻辑） --数据做校验
     *
     * @param req 流程参数
     */
    void processSummaryScene(AutSaAudSaveDTO req);

    /**
     * 处理确认提交场景（总结提交无节点复杂翻转逻辑）--数据不做校验
     *
     * @param req 流程参数
     */
    void processConfirmScene(AutSaAudSaveDTO req);

    /**
     * 处理确认并跳过节点操作（总结提交无节点复杂翻转逻辑）--数据不做校验
     *
     * @param req 流程参数
     */
    void processConfirmSkip(AutSaAudSaveDTO req);

    /**
     * 处理总结提交
     *
     * @param req 流程参数
     */
    void processSummary(AutSaAudSaveDTO req);

    /**
     * 处理款提交和驳回场景(复查驳回到初查)
     *
     * @param req 流程参数
     */
    void processClauseAndRJ04Scene(AutSaAudSaveDTO req);

    /**
     * 处理待修改场景(评审组长查看待修改条款)
     *
     * @param req 流程参数
     */
    void processWaitModifyScene(AutSaAudSaveDTO req);

    /**
     * 处理修改场景(评审员修改条款)
     *
     * @param req 流程参数
     */
    void processModifyScene(AutSaAudSaveDTO req);

    /**
     * 批量插入自评审核信息
     *
     * @param autSaAudLists 认证自评审核
     */
    void batchInsertAutSaAud(List<AutSaAud> autSaAudLists);

    /**
     * 批量插入自评审核信息
     *
     * @param autSaAudLists 认证自评审核
     */
    void batchUpdateAutSaAud(List<AutSaAud> autSaAudLists);

    /**
     * 更新自评审核关联信息
     *
     * @param updateStatus 节点
     * @param autCode      自评编码
     */
    void updateAutSaRelation(String updateStatus, String autCode);

    /**
     * 更新自评审核关联信息
     *
     * @param updateStatus 节点
     * @param applyNo      医疗机构编码
     */
    void updateAutSaRelationByApplyNo(String updateStatus, String applyNo);

    /**
     * 批量失效自评审核信息
     *
     * @param autCode   自评编码
     * @param types     类型
     * @param clauseIds 款id
     * @return
     */
    void batchInvalidAutSaAudByClauseIds(String autCode, String types, String clauseIds);

    /**
     * 根据下一节点标识获取下一节点
     *
     * @param nextStatusConfig  下一节点配置
     * @param statusProcessEnum 节点流程枚举值
     * @return 下一节点
     */
    String getNextStatus(String nextStatusConfig, StatusProcessEnum statusProcessEnum);

    /**
     * 保存自评审核业务数据
     *
     * @param autCode      自评编码
     * @param businessCode 环节
     * @param data         业务数据
     */
    void saveAutSaAudBusinessData(String autCode, String businessCode, String data);

    /**
     * 删除自评审核业务数据
     *
     * @param autCode      自评编码
     * @param businessCode 环节
     */
    void delAutSaAudBusinessData(String autCode, String businessCode);

    /**
     * 校验页面展示类型
     *
     * @param req 流程参数
     */
    void checkPageType(AutSaAudQueryDTO req);

    /**
     * 解析医疗机构计划分配详情获取组员信息
     *
     * @param hosReviewPlanVO 分配信息
     * @param roleKey         角色权限字符串
     * @return 分配详情信息
     */
    List<HosPlanUserInfoVO> getHosPlanMemberList(HosReviewPlanVO hosReviewPlanVO, String roleKey);

    /**
     * 校验是否是组长
     *
     * @param hosPlanUserInfoVO 用户分配的信息
     * @return 是否为组长
     */
    boolean checkIsLeader(HosPlanUserInfoVO hosPlanUserInfoVO);

    /**
     * 获取小组管理信息
     *
     * @param autSaAudList 审核信息
     * @param req          流程数据
     */
    void getGroupProgressList(AutSaAudList autSaAudList, AutSaAudQueryDTO req);

    /**
     * 获取该用户的分配信息
     *
     * @param accountId  账户
     * @param memberList 组员分配列表
     * @return 当前用户的分配信息
     */
    HosPlanUserInfoVO getHosPlanUserInfoVOFromList(String accountId, List<HosPlanUserInfoVO> memberList);

    /**
     * 获取该用户的分配信息
     *
     * @param accountId       账户
     * @param hosReviewPlanVO 分配信息
     * @param roleKey         角色权限字符串
     * @return 当前用户的分配信息
     */
    HosPlanUserInfoVO getHosPlanUserInfoVOFromList(String accountId, HosReviewPlanVO hosReviewPlanVO, String roleKey);

    /**
     * 组织查询详情反参
     *
     * @param req 流程参数
     * @return
     */
    AutSaAudDetailVO tissueDetailResult(AutSaAudQueryDTO req);

    /**
     * 组织查询详情反参
     *
     * @param autSaAuds 审核数据
     * @param req       流程参数
     * @return 详情反参
     */
    AutSaAudDetailVO tissueDetailResult(List<AutSaAud> autSaAuds, AutSaAudQueryDTO req);

    /**
     * 根据类和方法名得到方法
     *
     * @param clazz      class对象
     * @param methodName 方法名
     * @return 方法对象
     */
    Method getMethodByClassAndName(Class clazz, String methodName);

    /**
     * 生成自评报告
     *
     * @param autCode      自评编码
     * @param versionId    版本号
     * @param submitTypes  提交类型
     * @param businessCode 业务编码
     * @return 自评报告
     */
    AutSaAudReport generateReport(String autCode, String versionId, String submitTypes, String businessCode, List<AutSaAud> autSaAuds);

    /**
     * 获取查询用提交类型
     *
     * @param req         流程参数
     * @param isQueryList 是否查询列表
     * @return 结果
     */
    String getQuerySubmitType(AutSaAudQueryDTO req, boolean isQueryList);

    void generatePdf(String autCode, String versionId);

    /**
     * 处理节点直接翻转场景
     *
     * @param req 流程参数
     */
    void processNodeFilpScene(AutSaAudSaveDTO req);

    void frReportEmailToUsers(List<AutSaAud> frReportRClauses, AutSaAudSaveDTO req);

    void temReviewReport(String autCode, String versionId);

    FileInfoVO getTemReviewReportFileInfo(String autCode);

    List<AutSaAudList> listSorted(List<AutSaAudList> resultList);

    AutSaAudList queryTraineesAssessorAutSaAudList(AutSaAudQueryDTO req, List<HosPlanUserInfoVO> memberList);

    double getRate(String isDivided, String divisor);
}

