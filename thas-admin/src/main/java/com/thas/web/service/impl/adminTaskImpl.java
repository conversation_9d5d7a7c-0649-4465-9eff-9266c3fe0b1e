package com.thas.web.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.thas.common.constant.Constants;
import com.thas.common.core.domain.entity.SysUser;
import com.thas.common.enums.AutSaAudCycleEnum;
import com.thas.common.enums.AutSaAudRoleEnum;
import com.thas.common.exception.ServiceException;
import com.thas.common.utils.StringUtils;
import com.thas.system.domain.vo.UserVo;
import com.thas.system.mapper.SysUserMapper;
import com.thas.web.domain.AutSaRelation;
import com.thas.web.domain.MessageTemplate;
import com.thas.web.domain.dto.ApplyAndCycleListDTO;
import com.thas.web.domain.dto.MessageSendRecordDTO;
import com.thas.web.dto.HospitalReviewCycleDTO;
import com.thas.web.dto.ReviewerExpNoticeVo;
import com.thas.web.dto.SysUserBaseInfo;
import com.thas.web.mapper.*;
import com.thas.web.service.AdminTaskService;
import com.thas.web.service.IAutSaRelationService;
import com.thas.web.service.IHospitalBaseInfoService;
import com.thas.web.service.IMessageSendRecordService;
import com.thas.web.service.process.CommonProcessService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.mail.MessagingException;
import java.security.GeneralSecurityException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@Service
public class adminTaskImpl implements AdminTaskService {

    //医疗机构分配计划详情表，{pre_dis_complete，'初审员是否分配完成:1完成 2未完成'}，{`senior_review_dis_complete`，'资深评审员是否分配完成:1完成 2未完成'}
    private static final String PRE_FIELD = "pre_dis_complete";
    private static final String SENIOR_FIELD = "senior_review_dis_complete";


    @Resource
    private HospitalReviewCycleMapper hospitalReviewCycleMapper;

    @Resource
    private IMessageSendRecordService MessageSendRecordService;

    @Autowired
    private HospitalBaseInfoMapper hospitalBaseInfoMapper;

    @Resource
    private AutSaAudMapper autSaAudMapper;

    @Autowired
    private IHospitalBaseInfoService hospitalBaseInfoService;

    @Autowired
    private CommonProcessService commonProcessService;

    @Autowired
    private IAutSaRelationService autSaRelationService;

    @Resource
    private AutSaRelationMapper autSaRelationMapper;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private MessageTemplateMapper messageTemplateMapper;

    @Override
    public Boolean noticeTask(String date) {
        if (StringUtils.isBlank(date)) {
            throw new ServiceException("设置超时发送信息时间不能为空！");
        }
        long dateL = Long.parseLong(date);
        if (dateL <= 0) {
            throw new ServiceException("设置超时发送信息时间不能小于1天！");
        }
        //1.查询医院分配详情表，获取未完成审查员和验证评审员分配的医院；
        //未完成的审查员
        String stageValue = AutSaAudCycleEnum.INITIAL_REVIEW_CYCLE.getCycleStageValue();
        String field = PRE_FIELD;
        List<HospitalReviewCycleDTO> pres = hospitalReviewCycleMapper.getHospitalReviewCycleByStageValueAndStatus(stageValue, field);

        //未完成的验证评审员
        stageValue = AutSaAudCycleEnum.VALIDATION_REVIEWER_REVIEW_CYCLE.getCycleStageValue();
        field = SENIOR_FIELD;
        List<HospitalReviewCycleDTO> seniors = hospitalReviewCycleMapper.getHospitalReviewCycleByStageValueAndStatus(stageValue, field);
        if (CollUtil.isEmpty(pres) && CollUtil.isEmpty(seniors)) {
            log.info("定时任务-当前所有医院未有未分配完成的人员！");
            return true;
        }
        //2.根据医院信息，查询对应的医院周期是否超过15天，超过就发送信息；
        //获取超过通知时间未分配审查员的医院和管理员用户信息
        List<HospitalReviewCycleDTO> sendPres = getPerInfo(pres, dateL);

        //验证评审员
        List<HospitalReviewCycleDTO> sendSeniors = getPerInfo(seniors, dateL);

        //要通知未分配的医院名信息
        StringBuffer sb = new StringBuffer("温馨提示：\n");
        int length = sb.length();
        //被通知人的信息
        Set<SysUserBaseInfo> SysUserBaseInfoSet = new HashSet<>();
        //2.获取超级管理员和普通管理员的电话，邮件等信息
        if (CollUtil.isNotEmpty(sendPres)) {
            sendPres.forEach(send -> {
                SysUserBaseInfoSet.add(send.getSysUserBaseInfo());
                sb.append("【" + send.getHospitalName() + "】的验证评审即将于【" + send.getCycleStartTime() + "】开始,\n");
            });
            sb.append("请您尽快完成审查员分配。");
        }
        if (CollUtil.isNotEmpty(sendSeniors)) {
            if (sb.length() > length) {
                sb.append("\n");
            }
            sendSeniors.forEach(send -> {
                SysUserBaseInfoSet.add(send.getSysUserBaseInfo());
                sb.append("【" + send.getHospitalName() + "】的验证评审即将于【" + send.getCycleStartTime() + "】开始,\n");
            });
            sb.append("请您尽快完成验证评审员分配。");
        }
        //3.然后通知管理员和超级管理员,对应医院未分配的信息；
        if (CollUtil.isNotEmpty(SysUserBaseInfoSet)) {
            MessageSendRecordDTO messageSendRecordDTO = new MessageSendRecordDTO();
            List<UserVo> userVoList = new ArrayList<>();
            SysUserBaseInfoSet.forEach(o -> {
                UserVo userVo = new UserVo();
                userVo.setUserId(Long.valueOf(o.getAccountId()));
                userVo.setUserName(o.getUserName());
                userVo.setNickName(o.getName());
                userVo.setEmail(o.getEmail());
                userVo.setPhonenumber(o.getPhoneNumber());
                userVoList.add(userVo);
            });
            messageSendRecordDTO.setContent(sb.toString());
            //发送方式（0系统发送 1邮件发送 2手机短信发送）
            messageSendRecordDTO.setSendType("0,1,2");
            messageSendRecordDTO.setUserVoList(userVoList);
            messageSendRecordDTO.setTaskStatus("1");
            try {
                long time1 = System.currentTimeMillis();
                MessageSendRecordService.sendMessageToUser(messageSendRecordDTO);
                log.info("定时任务-发送短信与邮箱耗时：" + (System.currentTimeMillis() - time1) + "ms");
            } catch (GeneralSecurityException e) {
                log.error("定时任务-发送短信链接客户端异常:" + e.getMessage());
                //throw new RuntimeException("复审驳回-发送短信与邮箱异常："+e.getMessage());
            } catch (MessagingException e) {
                log.error("定时任务-发送短信异常:" + e.getMessage());
                //throw new RuntimeException("复审驳回-发送短信与邮箱异常："+e.getMessage());
            }
        }
        return true;
    }

    /**
     * 根据评审周期失效已过期的评审数据
     */
    @Override
    public void invalidAutSaAudByCycle() {
        // 查询出当前系统内，医院和对应周期信息
        List<ApplyAndCycleListDTO> applyAndCycleList = hospitalBaseInfoMapper.getApplyNoAndCycleList();
        log.info("定时任务 invalidAutSaAudByCycle --查询到的失效数据信息：{}", JSON.toJSONString(applyAndCycleList));
        if (CollectionUtils.isNotEmpty(applyAndCycleList)) {
            for (ApplyAndCycleListDTO applyAndCycleListDTO : applyAndCycleList) {
                log.info("失效医疗机构applyNo：{},当前数据：{}，---开始", applyAndCycleListDTO.getApplyNo(), JSON.toJSONString(applyAndCycleListDTO));
                hospitalBaseInfoService.invalidHosPlan(applyAndCycleListDTO.getApplyNo());
                // 失效自评信息
                AutSaRelation updateAutSaRelation = new AutSaRelation();
                updateAutSaRelation.setHospitalApplyNo(applyAndCycleListDTO.getApplyNo());
                updateAutSaRelation.setStatus(Constants.HospitalConstants.NUM_0);
                autSaRelationService.updateAutSaRelation(updateAutSaRelation);
                // 失效自评信息
                String autCode = applyAndCycleListDTO.getAutCode();
                if (StringUtils.isNotBlank(autCode)) {
                    autSaAudMapper.invalidAutSaAud(autCode);
                }
                log.info("失效医疗机构applyNo：{} 完成", applyAndCycleListDTO.getApplyNo());
            }
        }
        log.info("定时任务 invalidAutSaAudByCycle --结束");
    }

    @Override
    public void reviewerExpNotice(String date) {
        //结束日期到期前3天每天短信和邮箱通知一次；
        //1.查询评审周期时间
        //2.获取周期值最后一天，判断结束时间是否大于等于当前时间0-2天以内，发送通知
        List<ReviewerExpNoticeVo> reviewerExpNoticeVos = autSaRelationMapper.qryReviewerExpNotice(date);
        if (CollectionUtils.isEmpty(reviewerExpNoticeVos)) {
            log.info("没有评审到期通知的医院！");
            return;
        }
        //查询普通管理员的信息，获取短信和邮箱编码
        SysUser sysUser = new SysUser();
        sysUser.setRoleId(Long.valueOf(AutSaAudRoleEnum.COMMON_ADMIN.getCode()));
        List<SysUser> userList = sysUserMapper.selectUserList(sysUser);
        if (CollectionUtils.isEmpty(userList)) {
            log.info("普通管理员为空，没有对应通知人员！");
            return;
        }
        //管理员您好，XXX医院的XXX节点离计划的结束时间还有X天，如需延长结束时间，请在“评审计划分配-评审计划列表”进行修改；如无需修改，请忽略。
        StringBuilder stringBuilder = new StringBuilder();
        reviewerExpNoticeVos.forEach(reviewerExpNoticeVo ->{
            stringBuilder.append(String.format("[%s]的[%s]节点离计划的结束时间不足%s天，",reviewerExpNoticeVo.getHospitalName(),
                    AutSaAudCycleEnum.getDesc(reviewerExpNoticeVo.getCycleStage()),(Integer.parseInt(reviewerExpNoticeVo.getDay())+ 1) ));
        });

        MessageTemplate qryMessageTemplate = new MessageTemplate();
        qryMessageTemplate.setName("评审临期通知模板");
        List<MessageTemplate> messageTemplates = messageTemplateMapper.selectMessageTemplateList(qryMessageTemplate);
        if(CollectionUtils.isEmpty(messageTemplates) || messageTemplates.size()>1 || StringUtils.isBlank(messageTemplates.get(0).getContent())){
            log.error("获取【评审临期通知模板】模板错误。");
            return;
        }
        qryMessageTemplate = messageTemplates.get(0);
        String content = String.format(qryMessageTemplate.getContent(), stringBuilder.toString());

        //3.发送短信和电话通知
            MessageSendRecordDTO messageSendRecordDTO = new MessageSendRecordDTO();
            messageSendRecordDTO.setContent(content);
            //发送方式（0系统发送 1邮件发送 2手机短信发送）
            messageSendRecordDTO.setSendType("0,1,2");
            messageSendRecordDTO.setUserVoList(BeanUtil.copyToList(userList, UserVo.class));
            messageSendRecordDTO.setTaskStatus("1");
            try {
                long time1 = System.currentTimeMillis();
                MessageSendRecordService.sendMessageToUser(messageSendRecordDTO);
                log.info("定时任务-发送短信与邮箱耗时：" + (System.currentTimeMillis() - time1) + "ms");
            } catch (GeneralSecurityException e) {
                log.error("定时任务-发送短信链接客户端异常:" + e.getMessage());
                //throw new RuntimeException("复审驳回-发送短信与邮箱异常："+e.getMessage());
            } catch (MessagingException e) {
                log.error("定时任务-发送短信异常:" + e.getMessage());
                //throw new RuntimeException("复审驳回-发送短信与邮箱异常："+e.getMessage());
            }


    }

    private List<HospitalReviewCycleDTO> getPerInfo(List<HospitalReviewCycleDTO> pres, long dateL) {
        List<HospitalReviewCycleDTO> sendPres = new ArrayList<>();
        if (CollUtil.isNotEmpty(pres)) {
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String currentTime = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            pres.forEach(pre -> {
                try {
                    //判断两个时间距离
                    long day = formatter.parse(pre.getCycleStartTime() + " 00:00:00").getTime() - formatter.parse(currentTime + " 00:00:00").getTime();
                    long chaday = day / 86400000;
                    //0<分配的时间-当前时间<15天(入参设置的超时发送时间)，发送短信
                    if (chaday <= dateL && chaday > 0) {
                        sendPres.add(pre);
                    }
                } catch (ParseException e) {
                    log.error("审查员计算时间差失败，当前时间为{}，分配时间为{},异常信息为{}", currentTime, pre, e.getMessage());
                    throw new ServiceException("操作失败,请联系后台管理员！");
                }
            });
        }
        return sendPres;
    }

}
