package com.thas.web.service;


import com.thas.web.domain.AsaStatusConfig;

import java.util.List;

/**
 * 节点配置Service接口
 * 
 * <AUTHOR>
 * @date 2023-08-17
 */
public interface IAsaStatusConfigService 
{
    /**
     * 查询节点配置
     * 
     * @param id 节点配置主键
     * @return 节点配置
     */
    public AsaStatusConfig selectAsaStatusConfigById(Long id);

    /**
     * 查询节点配置列表
     * 
     * @param asaStatusConfig 节点配置
     * @return 节点配置集合
     */
    public List<AsaStatusConfig> selectAsaStatusConfigList(AsaStatusConfig asaStatusConfig);

    /**
     * 新增节点配置
     * 
     * @param asaStatusConfig 节点配置
     * @return 结果
     */
    public int insertAsaStatusConfig(AsaStatusConfig asaStatusConfig);

    /**
     * 修改节点配置
     * 
     * @param asaStatusConfig 节点配置
     * @return 结果
     */
    public int updateAsaStatusConfig(AsaStatusConfig asaStatusConfig);

    /**
     * 批量删除节点配置
     * 
     * @param ids 需要删除的节点配置主键集合
     * @return 结果
     */
    public int deleteAsaStatusConfigByIds(Long[] ids);

    /**
     * 删除节点配置信息
     * 
     * @param id 节点配置主键
     * @return 结果
     */
    public int deleteAsaStatusConfigById(Long id);
}
