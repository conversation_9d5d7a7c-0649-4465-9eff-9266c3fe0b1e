package com.thas.web.service;

import com.thas.common.core.domain.AjaxResult;
import com.thas.web.domain.TraineesPractice;
import com.thas.web.domain.vo.ReviewerTraineesRecVO;
import com.thas.web.dto.TraineesPracticeDTO;

import java.util.List;

/**
 * 评审学员实践记录Service接口
 *
 * <AUTHOR>
 * @date 2022-05-31
 */
public interface ITraineesPracticeService {
    /**
     * 查询评审学员实践记录
     *
     * @param id 评审学员实践记录主键
     * @return 评审学员实践记录
     */
    TraineesPractice selectTraineesPracticeById(Long id);

    /**
     * 查询评审学员实践记录列表
     *
     * @param traineesPractice 评审学员实践记录
     * @return 评审学员实践记录集合
     */
    List<TraineesPractice> selectTraineesPracticeList(TraineesPractice traineesPractice);


    ReviewerTraineesRecVO getFileInfoAndReviewerById(Long id);

    /**
     * 新增评审学员实践记录
     *
     * @param traineesPractice 评审学员实践记录
     * @return 结果
     */
    int insertTraineesPractice(TraineesPractice traineesPractice);

    /**
     * 修改评审学员实践记录
     *
     * @param traineesPractice 评审学员实践记录
     * @return 结果
     */
    int updateTraineesPractice(TraineesPractice traineesPractice);

    /**
     * 批量删除评审学员实践记录
     *
     * @param ids 需要删除的评审学员实践记录主键集合
     * @return 结果
     */
    int deleteTraineesPracticeByIds(Long[] ids);

    /**
     * 删除评审学员实践记录信息
     *
     * @param id 评审学员实践记录主键
     * @return 结果
     */
    int deleteTraineesPracticeById(Long id);

    AjaxResult insertPractice(List<TraineesPracticeDTO> traineesPracticeDTOList);

    List<TraineesPractice> selectTraineesPracticeByAccountId(String accountId);
}
