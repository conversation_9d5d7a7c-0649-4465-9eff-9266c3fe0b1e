package com.thas.web.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.thas.common.core.domain.entity.SysUser;
import com.thas.common.enums.*;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.thas.common.constant.Constants;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.exception.ServiceException;
import com.thas.common.utils.DateUtils;
import com.thas.common.utils.SecurityUtils;
import com.thas.common.utils.StringUtils;
import com.thas.web.domain.AutSaRelation;
import com.thas.web.domain.HospitalPlannedDistribution;
import com.thas.web.domain.HospitalReviewer;
import com.thas.web.domain.vo.ModifyTheReviewCycleVo;
import com.thas.web.dto.HosPlanDetailVO;
import com.thas.web.mapper.HospitalPreExamMapper;
import com.thas.web.mapper.HospitalReviewerMapper;
import com.thas.web.service.IAutSaRelationService;
import com.thas.web.service.ICstVersioningService;
import com.thas.web.service.IHospitalPlannedDistributionService;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.thas.web.mapper.HospitalReviewCycleMapper;
import com.thas.web.domain.HospitalReviewCycle;
import com.thas.web.service.IHospitalReviewCycleService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 医院评审阶段周期Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-27
 */
@Service
@Slf4j
public class HospitalReviewCycleServiceImpl implements IHospitalReviewCycleService
{
    @Autowired
    private HospitalReviewCycleMapper hospitalReviewCycleMapper;

    @Autowired
    private IHospitalPlannedDistributionService iHospitalPlannedDistributionService;

    @Autowired
    private ICstVersioningService iCstVersioningService;

    @Autowired
    private HospitalPreExamMapper hospitalPreExamMapper;
    @Autowired
    private HospitalReviewerMapper hospitalReviewerMapper;
    @Autowired
    private HospitalBaseInfoServiceImpl hospitalBaseInfoServiceImpl;
    @Autowired
    private IAutSaRelationService iAutSaRelationService;
    @Autowired
    private AutSaRelationServiceImpl autSaRelationServiceImpl;
    /**
     * 查询医院评审阶段周期
     *
     * @param id 医院评审阶段周期主键
     * @return 医院评审阶段周期
     */
    @Override
    public HospitalReviewCycle selectHospitalReviewCycleById(Long id)
    {
        return hospitalReviewCycleMapper.selectHospitalReviewCycleById(id);
    }

    /**
     * 查询医院评审阶段周期列表
     *
     * @param hospitalReviewCycle 医院评审阶段周期
     * @return 医院评审阶段周期
     */
    @Override
    public List<HospitalReviewCycle> selectHospitalReviewCycleList(HospitalReviewCycle hospitalReviewCycle)
    {
        return hospitalReviewCycleMapper.selectHospitalReviewCycleList(hospitalReviewCycle);
    }

    /**
     * 新增医院评审阶段周期
     *
     * @param hospitalReviewCycle 医院评审阶段周期
     * @return 结果
     */
    @Override
    public int insertHospitalReviewCycle(HospitalReviewCycle hospitalReviewCycle)
    {
        //插入数据，需要添加对应医院的自评编码

        hospitalReviewCycle.setCreateTime(DateUtils.getNowDate());
        return hospitalReviewCycleMapper.insertHospitalReviewCycle(hospitalReviewCycle);
    }

    /**
     * 修改医院评审阶段周期
     *
     * @param hospitalReviewCycle 医院评审阶段周期
     * @return 结果
     */
    @Override
    public int updateHospitalReviewCycle(HospitalReviewCycle hospitalReviewCycle)
    {
        hospitalReviewCycle.setUpdateTime(DateUtils.getNowDate());
        return hospitalReviewCycleMapper.updateHospitalReviewCycle(hospitalReviewCycle);
    }

    /**
     * 批量删除医院评审阶段周期
     *
     * @param ids 需要删除的医院评审阶段周期主键
     * @return 结果
     */
    @Override
    public int deleteHospitalReviewCycleByIds(Long[] ids)
    {
        return hospitalReviewCycleMapper.deleteHospitalReviewCycleByIds(ids);
    }

    /**
     * 删除医院评审阶段周期信息
     *
     * @param id 医院评审阶段周期主键
     * @return 结果
     */
    @Override
    public int deleteHospitalReviewCycleById(Long id)
    {
        return hospitalReviewCycleMapper.deleteHospitalReviewCycleById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertHospitalReviewCycleList(List<HospitalReviewCycle> hospitalReviewCycleList) {
        List<String> applyNoList = new ArrayList<>();
        hospitalReviewCycleList.forEach(hospitalReviewCycle -> {
            if (StringUtils.isEmpty(hospitalReviewCycle.getCycle())){
                throw new ServiceException("周期不能为空！");
            }
            applyNoList.add(hospitalReviewCycle.getApplyNo());
            hospitalReviewCycle.setCreator(SecurityUtils.getUsername());
            hospitalReviewCycle.setCreateTime(DateUtils.getNowDate());
        });
        hospitalReviewCycleMapper.deleteByApplyNoList(applyNoList);

        String applyNo = hospitalReviewCycleList.get(0).getApplyNo();

        //接口只能在刚分配评审周期和医院拒绝后再修改周期时触发，
        //又因医院通过或拒绝操作会生成认证自评关联信息aut_sa_relation，
        // 既没有节点数据或当前节点为最初节点时，允许操作
        AutSaRelation autSaRelation = new AutSaRelation();
        autSaRelation.setHospitalApplyNo(applyNo);
        autSaRelation.setStatus(Constants.INT_ONE);
        List<AutSaRelation> autSaRelations = iAutSaRelationService.selectAutSaRelationListByCondition(autSaRelation);
        if(CollectionUtils.isNotEmpty(autSaRelations) && !(AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(
                autSaRelations.get(0).getAutStatus(),AutSaAudStatusEnum.WAIT_SA_CLAUSE))){
            throw new ServiceException("评审计划安排的状态已发生变更，请刷新页面重试");
        }

        //管理员操作时，判断是否’0-待审核‘或’1-通过‘，如果是，再触发接口报错，防重提交
        SysUser sysUser = SecurityUtils.getSysUser();
        HospitalPlannedDistribution hDb = iHospitalPlannedDistributionService.selectHospitalPlannedDistributionByApplyNo(applyNo);
        if(hDb != null && AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(sysUser.getRoleKey(),AutSaAudRoleEnum.ADMIN,AutSaAudRoleEnum.COMMON_ADMIN)){
            if (ObjectUtil.equal(Constants.INT_ZERO,hDb.getCycleStatus())){
                throw new ServiceException("待医疗机构确认评审周期安排，暂不能修改！");
            } else if (ObjectUtil.equal(Constants.INT_ONE,hDb.getCycleStatus())){
                throw new ServiceException("评审安排与流程已由医院审核通过，请刷新页面查看");
            }

        }


        // 更新评审计划列表中的操作人
        Long versionId = iCstVersioningService.selectVersionId(applyNo);
        HospitalPlannedDistribution hospitalPlannedDistribution = new HospitalPlannedDistribution();
        hospitalPlannedDistribution.setApplyNo(applyNo);
        hospitalPlannedDistribution.setVersionId(versionId);
        hospitalPlannedDistribution.setCycleStatus(Constants.HospitalConstants.NUM_0);
        hospitalPlannedDistribution.setHosStatus(Constants.HospitalConstants.NUM_0);
        hospitalPlannedDistribution.setSeniorReviewDisComplete(Constants.HospitalConstants.NUM_2);
        iHospitalPlannedDistributionService.insertOrUpdateHospitalPlannedDistribution(hospitalPlannedDistribution);

        // 分配周期需要清理验证评审员。
        HospitalReviewer hospitalReviewer = new HospitalReviewer();
        hospitalReviewer.setApplyNo(applyNo);
        hospitalReviewer.setFieldIdList(Constants.HospitalConstants.SENIOR_REVIEW);
        hospitalReviewer.setStatus(Constants.HospitalConstants.NUM_2);
        hospitalReviewerMapper.updateHosRecByAplNAndFil(hospitalReviewer);

        //医院拒绝-评审周期修改时，如果AutSaRelation相关医院有数据删除，避免医院端历史记录产生多余记录
        iAutSaRelationService.deleteAutSaRelationByHospitalApplyNo(applyNo);

        return hospitalReviewCycleMapper.insertHospitalReviewCycleList(hospitalReviewCycleList);
    }

    @Override
    public List<HospitalReviewCycle> selectHospitalReviewCycleByApplyNo(String applyNo) {
        return hospitalReviewCycleMapper.selectHospitalReviewCycleByApplyNo(applyNo);
    }

    @Override
    public int updateHospitalReviewCycleByApplyNo(HospitalReviewCycle hospitalReviewCycle) {
        return hospitalReviewCycleMapper.updateHospitalReviewCycleByApplyNo(hospitalReviewCycle);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult refuseHospitalCycle(HospitalReviewCycle hospitalReviewCycle) {
        // 如果周期审核通过，仅需要更新医疗机构分配计划详情表 医疗结构对周期安排审核状态字段  hospital_planned_distribution.cycle_status=1 并且失效验证评审员
        // 如果周期审核拒绝，需要更新医疗机构分配计划详情表 医疗结构对周期安排审核状态字段  hospital_planned_distribution.cycle_status=2,hosStatus=2
        Integer status = hospitalReviewCycle.getStatus();
        String applyNo = hospitalReviewCycle.getApplyNo();
        if(!StringUtils.equalsAny(status.toString(), Constants.HospitalConstants.STR_NUM_1,  Constants.HospitalConstants.STR_NUM_2)){
            log.error("refuseHospitalCycle--applyNo：{} status:{} 不满足要求", applyNo, status);
            throw new ServiceException(ServiceExceptionEnum.COMMON_ERROR_1000000);
        }
        HospitalPlannedDistribution hospitalPlannedDistribution = new HospitalPlannedDistribution();
        hospitalPlannedDistribution.setApplyNo(applyNo);
        hospitalPlannedDistribution.setCycleStatus(status);
        hospitalPlannedDistribution.setHosStatus(status);
        hospitalPlannedDistribution.setSeniorReviewDisComplete(Constants.HospitalConstants.NUM_2);

        iHospitalPlannedDistributionService.updateHospitalPlannedDistributionByApplyNo(hospitalPlannedDistribution);

        AutSaRelation autSaRelation = new AutSaRelation();
        autSaRelation.setHospitalApplyNo(applyNo);
        autSaRelation.setStatus(Constants.INT_ONE);
        List<AutSaRelation> autSaRelations = iAutSaRelationService.selectAutSaRelationListByCondition(autSaRelation);
        if(CollectionUtils.isNotEmpty(autSaRelations)){
            throw new ServiceException("其他人员已处理，请刷新页面查看");
        }
        autSaRelationServiceImpl.initAutSaRelation(applyNo);
        return AjaxResult.success();
    }

    @Override
    public HospitalReviewCycle selectByApplyNoAndStageValue(String applyNo, String cycleStage) {
        return hospitalReviewCycleMapper.selectByApplyNoAndStageValue(applyNo, cycleStage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updateHospitalReviewCycleList(List<HospitalReviewCycle> hospitalReviewCycleList) {
        List<String> applyNoList = new ArrayList<>();
//        //中心形式审查
//        StringBuffer cycleValue2 = new StringBuffer();
//        //现场评审-审报告并提交
//        StringBuffer cycleValue4And9 = new StringBuffer();

        List<String> applyNos = hospitalReviewCycleList.stream().map(HospitalReviewCycle::getApplyNo).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(applyNos)){
            throw new ServiceException("入参applyNo不能为空");
        }
        if(applyNos.size()>1){
            throw new ServiceException("入参applyNo不同，仅支持同一家医院批量修改");
        }

        hospitalReviewCycleList.forEach(hospitalReviewCycle -> {
            if (StringUtils.isEmpty(hospitalReviewCycle.getCycle())){
                throw new ServiceException("周期不能为空！");
            }
            applyNoList.add(hospitalReviewCycle.getApplyNo());
            hospitalReviewCycle.setCreator(SecurityUtils.getUsername());
            hospitalReviewCycle.setCreateTime(DateUtils.getNowDate());
//            //中心形式审查-阶段名“2”
//            if(AutSaAudCycleEnum.INITIAL_REVIEW_CYCLE.getCycleStageValue().equals(hospitalReviewCycle.getStageValue())){
//                cycleValue2.append(hospitalReviewCycle.getCycle());
//            }
//            //现场评审-阶段名“4” 到 审报告并提交-阶段名“9”
//            if(AutSaAudCycleEnum.REVIEW_CYCLE.getCycleStageValue().equals(hospitalReviewCycle.getStageValue())){
//                String startTime4 = hospitalReviewCycle.getCycle().substring(0, hospitalReviewCycle.getCycle().indexOf(","));
//                cycleValue4And9.append(startTime4);
//            }
//            if(AutSaAudCycleEnum.VALIDATION_REVIEWER_REVIEW_CYCLE.getCycleStageValue().equals(hospitalReviewCycle.getStageValue())){
//                String endTime9 = hospitalReviewCycle.getCycle().substring(hospitalReviewCycle.getCycle().indexOf(",") + 1);
//                cycleValue4And9.append(","+endTime9);
//            }
        });
        List<String> AutCodes = hospitalReviewCycleList.stream().filter(o->
                StringUtils.isNotEmpty(o.getAutCode())).map(HospitalReviewCycle::getAutCode).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(AutCodes)){
            AutSaRelation autSaRelation = iAutSaRelationService.selectAutSaRelationByHospitalApplyNo(applyNos.get(0), false);
            if(autSaRelation == null || StringUtils.isBlank(autSaRelation.getAutCode())){
                log.error("根据applyNo{}，查询AutSaRelation为空",applyNos.get(0));
                throw new ServiceException("获取数据错误，请联系管理员");
            }
            hospitalReviewCycleList.forEach(o-> o.setAutCode(autSaRelation.getAutCode()));
        }

//        //获取当前医院已分配的初审员与评审员id 初审员(审查员)表：hospital_pre_exam 评审员：hospital_reviewer
//        String applyNo = hospitalReviewCycleList.get(0).getApplyNo();
//        List<String> preExamIds = hospitalPreExamMapper.selectHospitalPreExamIdsByApplyNo(applyNo);
//        List<String> reviewerIds = hospitalReviewerMapper.selectHospitalReviewerReviewerIdsByApplyNoAndRoleKey(applyNo, Constants.HospitalConstants.ROLE_ASSESSOR);
//        ModifyTheReviewCycleVo preExam = new ModifyTheReviewCycleVo();
//        ModifyTheReviewCycleVo reviewer = new ModifyTheReviewCycleVo();
//        ModifyTheReviewCycleVo seniorAssessor = new ModifyTheReviewCycleVo();
//        //检验修改的周期时间与已分配的人员周期是否有冲突，获取冲突人员的用户id
//        //审查员
//        if(CollectionUtil.isNotEmpty(preExamIds)){
//            preExam.setPreExamIds(preExamIds);
//            preExam.setCycleValue(cycleValue2.toString());
//            hospitalBaseInfoServiceImpl.reviewCycleJudge(new HosPlanDetailVO(), applyNo,preExam);
//        }
//        //评审员
//        if(CollectionUtil.isNotEmpty(reviewerIds)){
//            reviewer.setPreExamIds(reviewerIds);
//            reviewer.setCycleValue(cycleValue4And9.toString());
//            hospitalBaseInfoServiceImpl.reviewCycleJudge(new HosPlanDetailVO(),applyNo,reviewer);
//        }
//        //冲突，封装返回提醒信息，删除冲突人员的分配数据，没冲突不用删除 初审员表：hospital_pre_exam 评审员：hospital_reviewer
//        //医院分配计划表，修改冲突人员的分配状态为为分配 hospital_planned_distribution
//        List<String> preExamConflictingPeople = preExam.getConflictingPeople();
//        if(CollectionUtil.isNotEmpty(preExamConflictingPeople)){
//            //删除初审员表和修改医院分配计划表对应分配状态
//            int i = hospitalPreExamMapper.deleteHospitalPreExamByPreExamIdsAndApplyNo(preExamConflictingPeople, applyNo);
//            if(i>0){ log.info("修改周期有冲突-删除对应的医疗结构认证信息与【初审员】信息表成功！"); }
//            HospitalPlannedDistribution hospitalPlannedDistribution = new HospitalPlannedDistribution();
//            hospitalPlannedDistribution.setApplyNo(applyNo);
//            hospitalPlannedDistribution.setPreDisComplete(Constants.HospitalConstants.NUM_2);
//            int i1 = iHospitalPlannedDistributionService.updateHospitalPlannedDistributionByApplyNo(hospitalPlannedDistribution);
//            if(i1>0){ log.info("修改周期有冲突-修改医院分配计划表对应【初审员】分配状态为未分配成功！"); }
//        }
//
//        List<String> reviewerConflictingPeople = reviewer.getConflictingPeople();
//        if(CollectionUtil.isNotEmpty(reviewerConflictingPeople)){
//            //删除评审员表和修改医院分配计划表对应分配状态
//            int i = hospitalReviewerMapper.deleteHospitalReviewerByReviewerIdsAndApplyNo(reviewerConflictingPeople, applyNo);
//            if(i>0){ log.info("修改周期有冲突-删除对应的医疗结构认证信息与【评审员】信息表成功！"); }
//            HospitalPlannedDistribution hospitalPlannedDistribution = new HospitalPlannedDistribution();
//            hospitalPlannedDistribution.setApplyNo(applyNo);
//            hospitalPlannedDistribution.setPreDisComplete(Constants.HospitalConstants.NUM_2);
//            int i1 = iHospitalPlannedDistributionService.updateHospitalPlannedDistributionByApplyNo(hospitalPlannedDistribution);
//            if(i1>0){ log.info("修改周期有冲突-修改医院分配计划表对应【评审员】分配状态为未分配成功！"); }
//        }
//        //删除验证评审员分配数据，重新分配，封装提醒信息 hospital_reviewer
//        List<String> seniorAssessorIds = hospitalReviewerMapper.selectHospitalReviewerReviewerIdsByApplyNoAndRoleKey(applyNo, Constants.HospitalConstants.ROLE_SENIOR_ASSESSOR);
//        if(CollectionUtil.isNotEmpty(seniorAssessorIds)){
//            log.info("当前已分配验证评审员id{}已重置!",seniorAssessorIds.toString());
//            int i = hospitalReviewerMapper.deleteHospitalReviewerByReviewerIdsAndApplyNo(seniorAssessorIds, applyNo);
//            if(i>0){ log.info("修改周期有冲突-删除对应的医疗结构认证信息与【评审员】信息表成功！"); }
//            seniorAssessor.setConflictingMessage("当前已分配验证评审员已重置，请重新分配！\n");
//
//            //hospital_planned_distribution 验证评审员改为未分配
//            HospitalPlannedDistribution hospitalPlannedDistribution = new HospitalPlannedDistribution();
//            hospitalPlannedDistribution.setApplyNo(applyNo);
//            hospitalPlannedDistribution.setSeniorReviewDisComplete(Constants.INT_TWO);
//            iHospitalPlannedDistributionService.updateHospitalPlannedDistributionByApplyNo(hospitalPlannedDistribution);
//        }

        //改：1. 评审流程中各个节点，不受评审计划里的结束时间限制；固不做周期校验，可修改评审周期

        //根据入参，修改周期表数据
        int i = hospitalReviewCycleMapper.deleteByApplyNoList(applyNoList);
        if(i>0){ log.info("修改周期-删除对应的医院评审阶段周期表成功！"); }

        int i1 = hospitalReviewCycleMapper.insertHospitalReviewCycleList(hospitalReviewCycleList);
        if(i1>0){ log.info("修改周期-新增对应的医院评审阶段周期表成功！"); }

//        String conflictingMessage = preExam.getConflictingMessage() + reviewer.getConflictingMessage() + seniorAssessor.getConflictingMessage();
        return "";
    }
}
