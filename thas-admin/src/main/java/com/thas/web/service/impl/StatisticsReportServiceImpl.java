package com.thas.web.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.thas.common.constant.Constants;
import com.thas.common.enums.AutSaAudRoleEnum;
import com.thas.common.enums.AutSaAudStatusEnum;
import com.thas.common.enums.StatisticsReportTypeEnum;
import com.thas.common.enums.TraQuestionnaireFeedBackTypeEnum;
import com.thas.common.exception.ServiceException;
import com.thas.common.utils.StringUtils;
import com.thas.system.service.ISysRoleService;
import com.thas.web.domain.AsaStatusConfigVo;
import com.thas.web.domain.StatisticsReportReq;
import com.thas.web.domain.StatisticsReportRes;
import com.thas.web.domain.StatisticsReportVo;
import com.thas.web.mapper.AutSaRelationMapper;
import com.thas.web.mapper.TraQuestionnaireFeedBackRecordMapper;
import com.thas.web.service.process.StatisticsReportService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class StatisticsReportServiceImpl implements StatisticsReportService {

    @Autowired
    private ISysRoleService sysRoleService;

    @Resource
    private AutSaRelationMapper autSaRelationMapper;

    @Resource
    private TraQuestionnaireFeedBackRecordMapper traQuestionnaireFeedBackRecordMapper;


    @Override
    public StatisticsReportRes queryList(StatisticsReportReq statisticsReportReq) {
        //校验角色是否是管理员
        if (!sysRoleService.isAdmin()) {
            log.debug("当前仅支持管理员查看报表！");
            return null;
        }
        String statisticsReportType = statisticsReportReq.getStatisticsReportType();
        StatisticsReportRes statisticsReportRes = new StatisticsReportRes();
        //冲突报表(只输出有冲突的医院数据)
        if (StringUtils.isBlank(statisticsReportType) || StatisticsReportTypeEnum.checkEnum(statisticsReportType, StatisticsReportTypeEnum.INTEREST)) {
            statisticsReportRes.setInterestResList(this.packInterestResList(statisticsReportReq, Constants.STR_NUM_0));
        }
        //医院拒绝分配报表
        if (StringUtils.isBlank(statisticsReportType) || StatisticsReportTypeEnum.checkEnum(statisticsReportType, StatisticsReportTypeEnum.RJ_ALLOT)) {
            //当医院拒绝分配时，医院评审关联表对应数据更新为失效且审核状态为拒绝；既查询条件:审核状态为拒绝(0待审核 1计划通过 2被拒绝)
            statisticsReportRes.setRjAllotReviewerResList(this.packRjAllotReviewerResList(statisticsReportReq, Constants.STR_NUM_0));
        }
        //受评医院报表
        if (StringUtils.isBlank(statisticsReportType) || StatisticsReportTypeEnum.checkEnum(statisticsReportType, StatisticsReportTypeEnum.ACCEPT)) {
            statisticsReportRes.setAcceptResList(this.packAcceptResList(statisticsReportReq, Constants.STR_NUM_0));
        }
        //各评审员工作量报表
        if (StringUtils.isBlank(statisticsReportType) || StatisticsReportTypeEnum.checkEnum(statisticsReportType, StatisticsReportTypeEnum.REVIEWER_WORK)) {
            statisticsReportRes.setReviewersWorkResList(this.packReviewersWorkResList());
        }
        //培训教员工作量报表
        if (StringUtils.isBlank(statisticsReportType) || StatisticsReportTypeEnum.checkEnum(statisticsReportType, StatisticsReportTypeEnum.TRAIN_TEACHER_WORK)) {
            statisticsReportRes.setTrainTeacherResList(this.packTrainTeacherResList());
        }
        return statisticsReportRes;
    }

    private List<StatisticsReportRes.ReviewerWorkRes> packTrainTeacherResList() {
        List<StatisticsReportRes.ReviewerWorkRes> ReviewerWorkResList = new ArrayList<>();
        List<StatisticsReportVo> statisticsReportVoList = traQuestionnaireFeedBackRecordMapper.selectTrainTeacherStatisticsReport();
        //1.评审员为培训教员，获取所有评审员角色的用户ID
        //2.根据用户ID和记录表的反馈问卷类型，查询记录表，统计次数
        if (CollectionUtils.isNotEmpty(statisticsReportVoList)) {
            statisticsReportVoList.stream().collect(Collectors.groupingBy(StatisticsReportVo::getUserId)).forEach((userId, list) -> {
                StatisticsReportRes.ReviewerWorkRes trainTeacherRes = new StatisticsReportRes.ReviewerWorkRes();
                trainTeacherRes.setReviewerName(list.get(0).getReviewerName());
                long count = list.stream().filter(o -> ObjectUtil.equal(TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_7.getCode(), o.getFeedBackType())).count();
                trainTeacherRes.setReviewerNum(String.valueOf(count));
                ReviewerWorkResList.add(trainTeacherRes);
            });
        }
        return ReviewerWorkResList;
    }

    private List<StatisticsReportRes.ReviewerWorkRes> packReviewersWorkResList() {
        List<StatisticsReportRes.ReviewerWorkRes> ReviewerWorkResList = new ArrayList<>();
        List<StatisticsReportVo> statisticsReportVoList = autSaRelationMapper.selectReviewerWorkStatisticsReport();
        List<AsaStatusConfigVo> asaStatusConfigVos = autSaRelationMapper.selectAsaStatusConfigVo();
        if (CollectionUtil.isEmpty(asaStatusConfigVos)) {
            throw new ServiceException("查询节点配置为空！");
        }
        //获取待评审员评审之前的节点
        Integer waitSrClauseId = asaStatusConfigVos.stream().filter(o -> AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(
                o.getCurrentStatus(), AutSaAudStatusEnum.WAIT_SR_CLAUSE)).collect(Collectors.toList()).get(0).getId();
        List<String> waitSrClauseBeforeStatus = asaStatusConfigVos.stream().filter(o -> o.getId() < waitSrClauseId).map(AsaStatusConfigVo::getCurrentStatus).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(waitSrClauseBeforeStatus)) {
            throw new ServiceException("查询节点配置错误！");
        }
        //获取待验证评审员评审包括自身之后的节点
        Integer waitTrClauseId = asaStatusConfigVos.stream().filter(o -> AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(
                o.getCurrentStatus(), AutSaAudStatusEnum.WAIT_TR_CLAUSE)).collect(Collectors.toList()).get(0).getId();
        List<String> waitTrClauseAfterStatus = asaStatusConfigVos.stream().filter(o -> o.getId() >= waitTrClauseId).map(AsaStatusConfigVo::getCurrentStatus).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(waitTrClauseAfterStatus)) {
            throw new ServiceException("查询节点配置错误！");
        }

        //查询出来，需根据用户角色筛选对应有自评编码的数据，且状态有效(1有效，2无效)，且各角色节点判断
        if (CollectionUtils.isNotEmpty(statisticsReportVoList)) {
            Map<String, List<StatisticsReportVo>> listMap = statisticsReportVoList.stream().collect(Collectors.groupingBy(StatisticsReportVo::getReviewerName));
            listMap.forEach((reviewerName, list) -> {
                StatisticsReportRes.ReviewerWorkRes reviewerWorkRes = new StatisticsReportRes.ReviewerWorkRes();
                reviewerWorkRes.setReviewerName(reviewerName);
                //学员排除次数
                //评审员角色时，等于待评审之前的节点，不统计数量
                //验证评审员角色时，等于待评审包括自身之后的节点，统计数量
                long count = list.stream().filter(o -> StringUtils.isNotEmpty(o.getAutStatus()) && ObjectUtil.equal(o.getHrStatus(), Constants.STR_NUM_1) &&
                        !ObjectUtil.equal(o.getFieldIdList(), Constants.HospitalConstants.TRAINEES_REVIEW) &&
                        ((AutSaAudRoleEnum.ASSESSOR.getCode().toString().equals(o.getRoleId()) && !waitSrClauseBeforeStatus.contains(o.getAutStatus())) ||
                                (AutSaAudRoleEnum.SENIOR_ASSESSOR.getCode().toString().equals(o.getRoleId()) && waitTrClauseAfterStatus.contains(o.getAutStatus())))
                ).count();
                reviewerWorkRes.setReviewerNum(String.valueOf(count));
                ReviewerWorkResList.add(reviewerWorkRes);
            });
        }
        return ReviewerWorkResList;
    }

    /**
     * 受评医院报表数据封装
     *
     * @param req  入参（详情接口-自评编码必填）
     * @param flag 0-查询接口 1-详情接口
     * @return
     */
    private List<StatisticsReportRes.AcceptRes> packAcceptResList(StatisticsReportReq req, String flag) {
        List<StatisticsReportRes.AcceptRes> acceptResList = new ArrayList<>();
        List<StatisticsReportVo> statisticsReportVoList = autSaRelationMapper.selectAcceptStatisticsReport(new StatisticsReportVo(req.getAcceptDetailType()));
        if (CollectionUtils.isNotEmpty(statisticsReportVoList)) {
            StatisticsReportRes.AcceptRes acceptRes = new StatisticsReportRes.AcceptRes();
            if (flag.equals(Constants.STR_NUM_0)) {
                acceptRes.setAcceptTotalNum(String.valueOf(statisticsReportVoList.size()));
                long acceptedNum = statisticsReportVoList.stream().filter(o ->
                        AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(o.getAutStatus(), AutSaAudStatusEnum.REVIEW_PROCESS_END)).count();
                acceptRes.setAcceptedNum(String.valueOf(acceptedNum));
                acceptRes.setAcceptingNum(String.valueOf((statisticsReportVoList.size() - acceptedNum)));
                acceptResList.add(acceptRes);
            } else {
                acceptResList.addAll(BeanUtil.copyToList(statisticsReportVoList, StatisticsReportRes.AcceptRes.class));

            }

        }

        return acceptResList;
    }

    /**
     * 医院拒绝分配报表
     *
     * @param req  入参（详情接口-自评编码必填）
     * @param flag 0-查询接口 1-详情接口
     * @return
     */
    private List<StatisticsReportRes.RjAllotReviewerRes> packRjAllotReviewerResList(StatisticsReportReq req, String flag) {
        List<StatisticsReportRes.RjAllotReviewerRes> rjAllotReviewerResList = new ArrayList<>();
        List<StatisticsReportVo> statisticsReportVoList = autSaRelationMapper.selectAutSaRelationToStatisticsReport(
                new StatisticsReportVo(null, Constants.STR_NUM_2, null, req.getAutCode()));
        if (CollectionUtils.isNotEmpty(statisticsReportVoList)) {
            statisticsReportVoList.stream().collect(Collectors.groupingBy(StatisticsReportVo::getAutCode)).forEach(((k, v) -> {
                StatisticsReportRes.RjAllotReviewerRes rjAllotReviewerRes = new StatisticsReportRes.RjAllotReviewerRes();
//                if (flag.equals(Constants.STR_NUM_0)) {
//                    rjAllotReviewerRes.setHospitalName(v.get(0).getHospitalName());
//                    rjAllotReviewerRes.setRjAllotReviewerNum(String.valueOf(v.size()));
//                } else {
//                    rjAllotReviewerRes.setReviewerName(v.stream().map(StatisticsReportVo::getReviewerName).collect(Collectors.toList()));
//                }
                rjAllotReviewerRes.setHospitalName(v.get(0).getHospitalName());
                rjAllotReviewerRes.setRjAllotReviewerNum(String.valueOf(v.size()));
                List<StatisticsReportRes.RjAllotReviewerDetailRes> rjAllotReviewerDetailResList = v.stream().map(o -> {
                    StatisticsReportRes.RjAllotReviewerDetailRes rjAllotReviewerDetailRes = new StatisticsReportRes.RjAllotReviewerDetailRes();
                    rjAllotReviewerDetailRes.setReviewerName(o.getReviewerName());
                    return rjAllotReviewerDetailRes;
                }).collect(Collectors.toList());
                rjAllotReviewerRes.setRjAllotReviewerDetailResList(rjAllotReviewerDetailResList);
                rjAllotReviewerResList.add(rjAllotReviewerRes);
            }));
        }
        return rjAllotReviewerResList;
    }

    /**
     * @param statisticsReportReq 入参（详情接口-自评编码必填）
     * @param flag                0-查询接口 1-详情接口
     * @return
     */
    private List<StatisticsReportRes.InterestRes> packInterestResList(StatisticsReportReq statisticsReportReq, String flag) {
        List<StatisticsReportRes.InterestRes> interestResList = new ArrayList<>();
        //查询认证自评信息,条件：有效且有冲突
        List<StatisticsReportVo> statisticsReportVoList = autSaRelationMapper.selectAutSaRelationToStatisticsReport(
                new StatisticsReportVo(Constants.STR_NUM_1, null, Constants.STR_NUM_1, statisticsReportReq.getAutCode()));
        if (CollectionUtils.isNotEmpty(statisticsReportVoList)) {
            statisticsReportVoList.stream()
                    .collect(Collectors.groupingBy(StatisticsReportVo::getHospitalName)).forEach((k, v) -> {
                StatisticsReportRes.InterestRes interestRes = new StatisticsReportRes.InterestRes();
//                if (flag.equals(Constants.STR_NUM_0)) {
//                    interestRes.setHospitalName(k);
//                    interestRes.setInterestReviewerNum(String.valueOf(v.size()));
//                    interestRes.setAutCode(v.get(0).getAutCode());
//                    interestResList.add(interestRes);
//                } else {
//                    interestResList.addAll(BeanUtil.copyToList(v, StatisticsReportRes.InterestRes.class));
//                }
                interestRes.setHospitalName(k);
                interestRes.setInterestReviewerNum(String.valueOf(v.size()));
                interestRes.setAutCode(v.get(0).getAutCode());
                interestRes.setInterestDetailResList(BeanUtil.copyToList(v, StatisticsReportRes.InterestDetailRes.class));
                interestResList.add(interestRes);

            });
        }
        return interestResList;
    }

    @Override
    public StatisticsReportRes detail(StatisticsReportReq statisticsReportReq) {
        //校验角色是否是管理员
        if (!sysRoleService.isAdmin()) {
            log.debug("当前仅支持管理员查看报表！");
            return null;
        }
        if (StringUtils.isBlank(statisticsReportReq.getStatisticsReportType())) {
            throw new ServiceException("详情-入参统计报表类型不能为空！");
        }
        String statisticsReportType = statisticsReportReq.getStatisticsReportType();
        StatisticsReportRes statisticsReportRes = new StatisticsReportRes();
        //冲突报表(只输出有冲突的医院数据)
        if (StringUtils.isBlank(statisticsReportType) || StatisticsReportTypeEnum.checkEnum(statisticsReportType, StatisticsReportTypeEnum.INTEREST)) {
            if (StringUtils.isBlank(statisticsReportReq.getAutCode())) {
                throw new ServiceException("冲突报表详情-入参自评编码不能为空！");
            }
            statisticsReportRes.setInterestResList(this.packInterestResList(statisticsReportReq, Constants.STR_NUM_1));
        }
        //医院拒绝分配报表
        if (StringUtils.isBlank(statisticsReportType) || StatisticsReportTypeEnum.checkEnum(statisticsReportType, StatisticsReportTypeEnum.RJ_ALLOT)) {
            if (StringUtils.isBlank(statisticsReportReq.getAutCode())) {
                throw new ServiceException("医院拒绝分配报表详情-入参自评编码不能为空！");
            }
            statisticsReportRes.setRjAllotReviewerResList(this.packRjAllotReviewerResList(statisticsReportReq, Constants.STR_NUM_1));
        }
        //受评医院报表
        if (StringUtils.isBlank(statisticsReportType) || StatisticsReportTypeEnum.checkEnum(statisticsReportType, StatisticsReportTypeEnum.ACCEPT)) {
            if (StringUtils.isBlank(statisticsReportReq.getAcceptDetailType())) {
                throw new ServiceException("受评医院报表详情-入参受评医院明细类型不能为空！");
            }
            statisticsReportRes.setAcceptResList(this.packAcceptResList(statisticsReportReq, Constants.STR_NUM_1));
        }

        return statisticsReportRes;
    }
}
