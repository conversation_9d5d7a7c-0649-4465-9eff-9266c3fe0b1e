package com.thas.web.service;

import com.thas.web.domain.CstCertificationStandards;
import com.thas.web.domain.dto.CstCertificationStandardsDetailQueryDTO;
import com.thas.web.domain.dto.SelectStandardsByClauseIdsDTO;
import com.thas.web.domain.vo.ChapterVo;
import com.thas.web.domain.vo.SelectByVersionIdVo;
import com.thas.web.dto.CstCertificationStandardVO;
import java.util.List;
import java.util.Map;


/**
 * 认证标准模板Service接口
 *
 * <AUTHOR>
 * @date 2022-01-21
 */
public interface ICstCertificationStandardsService {
    /**
     * 查询认证标准模板
     *
     * @param id 认证标准模板主键
     * @return 认证标准模板
     */
    public CstCertificationStandards selectCstCertificationStandardsById(Long id);

    /**
     * 查询认证标准模板列表
     *
     * @param cstCertificationStandards 认证标准模板
     * @return 认证标准模板集合
     */
    public List<CstCertificationStandards> selectCstCertificationStandardsList(CstCertificationStandards cstCertificationStandards);

    /**
     * 新增认证标准模板
     *
     * @param cstCertificationStandards 认证标准模板
     * @return 结果
     */
    public int insertCstCertificationStandards(CstCertificationStandards cstCertificationStandards);

    /**
     * 修改认证标准模板
     *
     * @param cstCertificationStandards 认证标准模板
     * @return 结果
     */
    public int updateCstCertificationStandards(CstCertificationStandards cstCertificationStandards);

    /**
     * 批量删除认证标准模板
     *
     * @param ids 需要删除的认证标准模板主键集合
     * @return 结果
     */
    public int deleteCstCertificationStandardsByIds(Long[] ids);

    /**
     * 删除认证标准模板信息
     *
     * @param id 认证标准模板主键
     * @return 结果
     */
    public int deleteCstCertificationStandardsById(Long id);

    /**
     * 查询最新版本的标准
     *
     * @param versionId
     * @return
     */
    public SelectByVersionIdVo selectByVersionId(Long versionId);

    /**
     * 查询最新版本的标准-款详情
     *
     * @param versionId
     * @param articleId
     * @return
     */
    public List<ChapterVo> selectDetailByVersionIdAndClauseId(Long versionId, Long articleId);

    /**
     * 通过版本id,款id查找详情
     *
     * @param cstCertificationStandardsDetailQueryDTO
     * @return
     */
    public CstCertificationStandards selectDetailByVersionIdAndClauseId(CstCertificationStandardsDetailQueryDTO cstCertificationStandardsDetailQueryDTO);

    /**
     * 通过版本id和款id进行修改
     *
     * @param cstCertificationStandards
     * @return
     */
    public int updateByClauseIdAndVersionId(CstCertificationStandards cstCertificationStandards);

    /**
     * 通过版本id和款id列表进行修改
     *
     * @param paramMap
     * @return
     */
    public int updateByClauseIdAndVersionIds(Map paramMap);

    /**
     * 通过版本id和领域id进行修改
     *
     * @param cstCertificationStandards
     * @return
     */
    public int updateByDomainIdAndVersionId(CstCertificationStandards cstCertificationStandards);

    /**
     * 查询版本对应的总款数
     *
     * @param versionId
     * @return
     */
    Map<String, Integer> selectCountByVersionId(String versionId);

    /**
     * 通过指定的款id获取标准认证
     *
     * @param selectStandardsByClauseIdsDTO
     * @return
     */
    public List<CstCertificationStandards> selectByClauseIdsAndVersionId(SelectStandardsByClauseIdsDTO selectStandardsByClauseIdsDTO);

    List<CstCertificationStandardVO> selectCstCertificationStandardVOByIds(String clauses);

    List<CstCertificationStandardVO> selectCstCertificationStandardVONoInIds(String ids, Long versionId);

    List<CstCertificationStandardVO> selectCstCertificationStandardsByDomainId(String domainIds, Long versionId);

    List<CstCertificationStandardVO> selectCstCertificationStandardsNotInDomainId(String domainIds, Long versionId);

    Integer selectCstCertificationStandardsCountByVersionId(Long versionId);

    Integer selectCstCertificationStandardsCountByDomainIds(String fieldIdList, Long versionId);

    /**
     * 根据版本号查询所有的款id
     *
     * @param versionId
     * @return
     */
    List<CstCertificationStandards> selectAllClauseIdByVersionId(String versionId);

    List<String> selectDomainIdListByVersionId(Long versionId);

    /**
     * 初始化版本信息
     */
    void initStandardsVersions();

    /**
     * 根据版本号获取缓存的章节信息信息
     *
     * @param versionId
     * @return
     */
    List<ChapterVo> getChapterVoListFromRedis(Long versionId);

    /**
     * 获取版本号对应的认证标准模板数据
     *
     * @param versionId
     * @return
     */
    List<ChapterVo> selectCstCertificationStandardsByVersionId(String versionId);

    /**
     * 获取款id对应的款数
     *
     * @param clauseIds
     * @param autCsId 版本号
     * @return
     */
    List<String> selectClauseNosByClauseIds(List<String> clauseIds, String autCsId);
}
