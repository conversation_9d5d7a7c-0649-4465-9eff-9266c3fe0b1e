package com.thas.web.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.thas.common.constant.Constants;
import com.thas.common.enums.AutSaAudCycleEnum;
import com.thas.common.enums.AutSaAudStatusEnum;
import com.thas.common.enums.ServiceExceptionEnum;
import com.thas.common.exception.ServiceException;
import com.thas.common.utils.DateUtils;
import com.thas.common.utils.SecurityUtils;
import com.thas.common.utils.StringUtils;
import com.thas.system.domain.SysUserHospital;
import com.thas.system.mapper.SysUserHospitalMapper;
import com.thas.web.domain.*;
import com.thas.web.domain.vo.*;
import com.thas.web.dto.DelFileShareRequest;
import com.thas.web.mapper.FileShareMapper;
import com.thas.web.mapper.HospitalPlannedDistributionMapper;
import com.thas.web.mapper.HospitalReviewerMapper;
import com.thas.web.service.*;

import java.util.ArrayList;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * 医疗机构分配计划详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-27
 */
@Slf4j
@Service
public class HospitalPlannedDistributionServiceImpl implements IHospitalPlannedDistributionService
{
    @Autowired
    private HospitalPlannedDistributionMapper hospitalPlannedDistributionMapper;

    @Autowired
    private HospitalReviewerMapper hospitalReviewerMapper;

    @Autowired
    private SysUserHospitalMapper sysUserHospitalMapper;

    @Autowired
    private FileShareMapper fileShareMapper;

    @Resource
    private IAutSaRelationService autSaRelationService;

    @Autowired
    private IUploadFileInfoService iUploadFileInfoService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private IHospitalReviewCycleService hospitalReviewCycleService;

    /**
     * 查询医疗机构分配计划详情
     *
     * @param id 医疗机构分配计划详情主键
     * @return 医疗机构分配计划详情
     */
    @Override
    public HospitalPlannedDistribution selectHospitalPlannedDistributionById(Long id)
    {
        return hospitalPlannedDistributionMapper.selectHospitalPlannedDistributionById(id);
    }

    @Override
    public HospitalPlannedDistribution selectHospitalPlannedDistributionByApplyNo(String applyNo) {
        return hospitalPlannedDistributionMapper.selectHospitalPlannedDistributionByApplyNo(applyNo);
    }

    /**
     * 查询医疗机构分配计划详情列表
     *
     * @param hospitalPlannedDistribution 医疗机构分配计划详情
     * @return 医疗机构分配计划详情
     */
    @Override
    public List<HospitalPlannedDistribution> selectHospitalPlannedDistributionList(HospitalPlannedDistribution hospitalPlannedDistribution)
    {
        return hospitalPlannedDistributionMapper.selectHospitalPlannedDistributionList(hospitalPlannedDistribution);
    }

    /**
     * 新增医疗机构分配计划详情
     *
     * @param hospitalPlannedDistribution 医疗机构分配计划详情
     * @return 结果
     */
    @Override
    public int insertHospitalPlannedDistribution(HospitalPlannedDistribution hospitalPlannedDistribution)
    {
        hospitalPlannedDistribution.setCreateTime(DateUtils.getNowDate());
        return hospitalPlannedDistributionMapper.insertHospitalPlannedDistribution(hospitalPlannedDistribution);
    }

    /**
     * 修改医疗机构分配计划详情
     *
     * @param hospitalPlannedDistribution 医疗机构分配计划详情
     * @return 结果
     */
    @Override
    public int updateHospitalPlannedDistribution(HospitalPlannedDistribution hospitalPlannedDistribution) {
        hospitalPlannedDistribution.setUpdateTime(DateUtils.getNowDate());
        return hospitalPlannedDistributionMapper.updateHospitalPlannedDistribution(hospitalPlannedDistribution);
    }

    /**
     * 批量删除医疗机构分配计划详情
     *
     * @param ids 需要删除的医疗机构分配计划详情主键
     * @return 结果
     */
    @Override
    public int deleteHospitalPlannedDistributionByIds(Long[] ids)
    {
        return hospitalPlannedDistributionMapper.deleteHospitalPlannedDistributionByIds(ids);
    }

    /**
     * 删除医疗机构分配计划详情信息
     *
     * @param id 医疗机构分配计划详情主键
     * @return 结果
     */
    @Override
    public int deleteHospitalPlannedDistributionById(Long id)
    {
        return hospitalPlannedDistributionMapper.deleteHospitalPlannedDistributionById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int submitAuditResult(HospitalPlannedDistribution hospitalPlannedDistribution) {
        boolean seniorFlag = true;
        boolean reviewerFlag = true;
        boolean traineesFlag = true;
        String applyNo = hospitalPlannedDistribution.getApplyNo();
        if(StringUtils.equals("senior-assessor", hospitalPlannedDistribution.getType())){
            // 验证评审员审核
            int senior = hospitalPlannedDistribution.getSeniorFlag();
            seniorFlag = Constants.HospitalConstants.NUM_1.equals(senior);
            if(!seniorFlag){
                // 资深评审员分配数据失效
                hospitalReviewerMapper.updateReviewerStatusByApplyNoAndStatus(applyNo,Constants.HospitalConstants.SENIOR_REVIEW);
            }else{
                // 评审员和资深评审员都通过则修改状态
                hospitalReviewerMapper.updateReviewerStatusByApplyNo(applyNo,Constants.HospitalConstants.SENIOR_REVIEW);
            }
            // 资深评审员是否分配完成状态
            hospitalPlannedDistribution.setSeniorReviewDisComplete(seniorFlag ? 1 : 2);
        }else{
            //评审员审核
            int review = hospitalPlannedDistribution.getReviewFlag();
            reviewerFlag = Constants.HospitalConstants.NUM_1.equals(review);
            //通过 true
            traineesFlag = Constants.HospitalConstants.NUM_1.equals(hospitalPlannedDistribution.getTraineesFlag());
            //仅学员不通过，hospital_reviewer失效学员的分配数据,hospital_planned_distribution只更新文件信息
            if(reviewerFlag && !traineesFlag){
                // hospital_reviewer失效学员的分配数据
                hospitalReviewerMapper.updateReviewerStatusByApplyNoAndStatus(applyNo,"");
            } else if(!reviewerFlag){
                // 将被拒绝的评审员的利益冲突表文件删除。
                this.delInterestFileShare(applyNo);

                // 评审员分配数据失效
                hospitalReviewerMapper.updateReviewerStatusByApplyNoAndStatus(applyNo,"");
                // 评审组长改为组员，待分配为组长
                HospitalReviewer hospitalReviewer = new HospitalReviewer();
                hospitalReviewer.setApplyNo(applyNo);
                hospitalReviewer.setLeaderIs(Constants.INT_TWO);
                hospitalReviewerMapper.updateHospitalReviewerByApplyNo(hospitalReviewer);

            }else{
                // 评审员和资深评审员都通过则修改状态
                hospitalReviewerMapper.updateReviewerStatusByApplyNo(applyNo,"");
            }
            HospitalPlannedDistribution hospitalPlannedDis = selectHospitalPlannedDistributionById(hospitalPlannedDistribution.getId());
            //将PDF文件组装存入
            String newRevFileId = hospitalPlannedDistribution.getRevFileId();
            String oldRevFileId = hospitalPlannedDis.getRevFileId();
            if (CharSequenceUtil.isNotEmpty(oldRevFileId)) {
                newRevFileId = oldRevFileId + "," + newRevFileId;
            }
            // 设置文件id
            hospitalPlannedDistribution.setRevFileId(newRevFileId);
            // 评审员是否分配完成状态
            hospitalPlannedDistribution.setReviewDisComplete(reviewerFlag ? 1 : 2);
        }
        // 整个审核状态
        hospitalPlannedDistribution.setHosStatus(seniorFlag && reviewerFlag && traineesFlag ? 1 : 2);
        hospitalPlannedDistribution.setConfirmTime(DateUtils.getNowDate());
        hospitalPlannedDistribution.setUpdateTime(DateUtils.getNowDate());
        // 获取操作人员
        hospitalPlannedDistribution.setUpdater(SecurityUtils.getUserId().toString());
        int res = hospitalPlannedDistributionMapper.updateHospitalPlannedDistribution(hospitalPlannedDistribution);
        log.info("更新评审计划表:{} 结果:{}", JSONUtil.toJsonStr(hospitalPlannedDistribution), res);
        return res;
    }

    /**
     * 删除被医院拒绝的评审员的利益冲突文件资源。
     *
     * @param applyNo 申请号
     */
    private void delInterestFileShare(String applyNo) {
        List<String> interestFileIdList = hospitalReviewerMapper.selectRejInterestFileId(applyNo);
        if (CollectionUtils.isEmpty(interestFileIdList)) {
            return;
        }
        List<Long> interestFileIds = interestFileIdList.stream()
                .filter(Objects::nonNull)
                .map(Long::valueOf)
                .collect(Collectors.toList());

        DelFileShareRequest delFileShareRequest = new DelFileShareRequest();
        delFileShareRequest.setFileIdList(interestFileIds);
        fileShareMapper.delFileShareByFileIdList(delFileShareRequest);
    }

    @Override
    public void insertOrUpdateHospitalPlannedDistribution(HospitalPlannedDistribution hospitalPlannedDistribution) {
        Long userId = SecurityUtils.getUserId();
        HospitalPlannedDistribution hDb = selectHospitalPlannedDistributionByApplyNo(hospitalPlannedDistribution.getApplyNo());
        if (Objects.nonNull(hDb)) {
            hospitalPlannedDistribution.setId(hDb.getId());
            hospitalPlannedDistribution.setUpdater(String.valueOf(userId));
            updateHospitalPlannedDistribution(hospitalPlannedDistribution);
        } else {
            hospitalPlannedDistribution.setCreator(String.valueOf(userId));
            hospitalPlannedDistribution.setUpdater(String.valueOf(userId));
            insertHospitalPlannedDistribution(hospitalPlannedDistribution);
        }
    }

    @Override
    public int updateHospitalPlannedDistributionByApplyNo(HospitalPlannedDistribution hospitalPlannedDistribution) {
       return hospitalPlannedDistributionMapper.updateHospitalPlannedDistributionByApplyNo(hospitalPlannedDistribution);
    }


    /**
     * 根据类型查询评审计划信息
     *
     * @param type 类型
     * @return 评审计划信息
     */
    @Override
    public HospitalPlannedDistributionVo selectPlannedDistributionInfoByType(String type) {
        // 查询用户医院关系
        Long userId = SecurityUtils.getUserId();
        SysUserHospital sysUserHospital = sysUserHospitalMapper.selectSysUserHospitalByUserId(userId);
        log.info("selectPlannedDistributionInfoByType————userId:{}, sysUserHospital:{}", userId, sysUserHospital);
        if (ObjectUtils.isEmpty(sysUserHospital)) {
            log.error("selectPlannedDistributionInfoByType————查询用户userId:{} 医院关系为空，该账号未分配医疗机构", userId);
            throw new ServiceException(ServiceExceptionEnum.PLAN_ERROR_1000000);
        }

        AutSaRelation autSaRelation = autSaRelationService.selectAutSaRelationByHospitalApplyNo(sysUserHospital.getHospitalApplyNo(), false);
        if(ObjectUtil.isNotEmpty(autSaRelation) && AutSaAudStatusEnum.CONFIRM_REVIEWER.getStatus().equals(autSaRelation.getAutStatus())){
            //确认评审员节点检验
            throw new ServiceException(ServiceExceptionEnum.PLAN_ERROR_1000010);
        }

        boolean invalidAutSaAudByCycleFlag = autSaRelationService.invalidAutSaAudByCycle(sysUserHospital.getHospitalApplyNo(),autSaRelation);
        if(invalidAutSaAudByCycleFlag){
            log.info("invalidAutSaAudByCycle---时间超时，直接失效数据，请联系管理员进行评审计划分配");
            throw new ServiceException(ServiceExceptionEnum.PLAN_ERROR_1000001);
        }
        // 查询审核信息
        HospitalPlannedDistributionVo hospitalPlannedDistributionVo = hospitalPlannedDistributionMapper.selectPlannedDistributionInfoByApplyNo(sysUserHospital.getHospitalApplyNo());
        if(null == hospitalPlannedDistributionVo){
            log.error("医疗结构:{} 未分配评审计划，请联系管理员进行评审计划分配", sysUserHospital.getHospitalApplyNo());
            throw new ServiceException(ServiceExceptionEnum.PLAN_ERROR_1000001);
        }
        log.info("selectPlannedDistributionInfoByType————applyNo:{}, hospitalPlannedDistributionVo:{}", sysUserHospital.getHospitalApplyNo(), hospitalPlannedDistributionVo);
        List<HospitalReviewCycleVo> hospitalReviewCycleVoList = hospitalPlannedDistributionVo.getHospitalReviewCycleVoList();
        if(CollectionUtils.isEmpty(hospitalReviewCycleVoList) || hospitalReviewCycleVoList.stream().anyMatch(a -> StringUtils.isBlank(a.getCycle()))){
            log.error("selectPlannedDistributionInfoByType————评审周期计划审核为空，请联系管理员进行评审计划分配");
            throw new ServiceException(ServiceExceptionEnum.PLAN_ERROR_1000001);
        }


        if(StringUtils.equals(type, "hospital")){
            //医院端评审周期计划审核
            hospitalReviewCycleVoList = hospitalReviewCycleVoList.stream().filter(a -> StringUtils.equalsAny(a.getStageValue(), AutSaAudCycleEnum.HOSPITAL_SELF_ASSESSMENT.getCycleStageValue(), AutSaAudCycleEnum.SELF_ASSESSMENT_FINAL_CONFIRM.getCycleStageValue(), AutSaAudCycleEnum.REVIEW_CYCLE.getCycleStageValue(), AutSaAudCycleEnum.FACTUAL_ACCURACY_REVIEW_CYCLE.getCycleStageValue())).collect(Collectors.toList());
            hospitalPlannedDistributionVo.setHospitalReviewCycleVoList(hospitalReviewCycleVoList);
            hospitalPlannedDistributionVo.setHospitalReviewerVoList(null);
        }else if(StringUtils.equals(type, "reviewer")){
            //医院端评审员计划审核
            List<HospitalReviewerVo> hospitalReviewerVoList = hospitalPlannedDistributionVo.getHospitalReviewerVoList();
            if(StringUtils.equals(hospitalPlannedDistributionVo.getCycleStatus().toString(), Constants.HospitalConstants.STR_NUM_0)){
                log.error("selectPlannedDistributionInfoByType————查询医院端评审员计划审核时，医疗结构对周期安排审核状态为待审核");
                throw new ServiceException(ServiceExceptionEnum.PLAN_ERROR_1000003);
            }

//            if(ObjectUtil.isNull(hospitalPlannedDistributionVo.getPreDisComplete()) || !StringUtils.equals(hospitalPlannedDistributionVo.getPreDisComplete().toString(), Constants.HospitalConstants.STR_NUM_1)){
//                log.error("selectPlannedDistributionInfoByType————查询医院端评审员计划审核时，管理员未完成对审查员分配");
//                throw new ServiceException(ServiceExceptionEnum.PLAN_ERROR_1000004);
//            }
            if(CollectionUtils.isEmpty(hospitalReviewerVoList) || hospitalReviewerVoList.stream().anyMatch(a -> StringUtils.isBlank(a.getReviewerId()))){
                log.error("selectPlannedDistributionInfoByType————查询医院端评审员计划审核时，评审员计划分配为空 ，请联系管理员进行评审计划分配");
                throw new ServiceException(ServiceExceptionEnum.PLAN_ERROR_1000005);
            }
            Integer reviewDisComplete = hospitalPlannedDistributionVo.getReviewDisComplete();
            if(reviewDisComplete != 1){
                log.error("selectPlannedDistributionInfoByType————查询医院端评审员计划审核时，评审员未完成分配 ，请联系管理员进行评审计划分配");
                throw new ServiceException(ServiceExceptionEnum.PLAN_ERROR_1000005);
            }

            hospitalPlannedDistributionVo.setHospitalReviewCycleVoList(null);
            hospitalReviewerVoList.forEach(hospitalReviewerVo -> {
                if (StringUtils.isNotEmpty(hospitalReviewerVo.getFieldIdList())) {
                    //医生的领域数量
                    String[] split = hospitalReviewerVo.getFieldIdList().split(",");
                    hospitalReviewerVo.setFieldCount(split.length);
                }
            });

            //获取头像文件ID
            List<Integer> headPortrait = hospitalReviewerVoList.stream().
                    filter(o-> o.getReviewerBaseInfoVo() != null && o.getReviewerBaseInfoVo().getHeadPortrait() != null)
                    .map(o->o.getReviewerBaseInfoVo().getHeadPortrait()).collect(Collectors.toList());
            String fileIds = StringUtils.join(headPortrait, ",");
            //获取的头像文件
            if (StringUtils.isNotBlank(fileIds)) {
                log.info("获取的头像文件--开始");
                List<FileInfoDTO> fileInfoDTOList = iUploadFileInfoService.getUploadFileInfoByIds(fileIds);
                if(CollectionUtil.isNotEmpty(fileInfoDTOList)){
                    log.info("获取的头像文件--有数据");
                    List<FileInfoVO> fileInfoVOList = commonService.fileInfoDtoToVo(fileInfoDTOList);
                    Map<String, List<FileInfoVO>> fileDetailMap = fileInfoVOList.stream().collect(Collectors.groupingBy(FileInfoVO::getFileId));
                    hospitalPlannedDistributionVo.setFileDetailMap(fileDetailMap);
                }
                log.info("获取的头像文件--revFileId数据有：{}",hospitalPlannedDistributionVo.getFileDetailMap());
            }
            //组装现场评审时间前二个月时间点
            List<HospitalReviewCycle> hospitalReviewCycleList = hospitalReviewCycleService.selectHospitalReviewCycleByApplyNo(sysUserHospital.getHospitalApplyNo());
            HospitalReviewCycle siteReviewCycle = hospitalReviewCycleList.stream()
                    .filter(hospitalReviewCycle -> "4".equals(hospitalReviewCycle.getStageValue()))
                    .findFirst().orElse(new HospitalReviewCycle());
            if (CharSequenceUtil.isEmpty(siteReviewCycle.getCycle())) {
                throw new ServiceException(ServiceExceptionEnum.ERROR_REVIEW_CYCLE_1000000);
            }
            String[] cycleDates = siteReviewCycle.getCycle().split(",");
            DateTime beforeCycleDate = DateUtil.offsetMonth(DateUtil.parseDate(cycleDates[0]), -2);
            String beforeCycleDateStr = DateUtil.format(beforeCycleDate, DatePattern.CHINESE_DATE_PATTERN);
            hospitalPlannedDistributionVo.setBeforeCycleDateStr(beforeCycleDateStr);


        }else{
            log.error("selectPlannedDistributionInfoByType————userId:{}, type:{},未配置查询结果", userId, type);
            throw new ServiceException(ServiceExceptionEnum.PLAN_ERROR_1000002);
        }
        if (ObjectUtil.isNotNull(autSaRelation)) {
            hospitalPlannedDistributionVo.setAutCode(autSaRelation.getAutCode());
        }
        return hospitalPlannedDistributionVo;
    }

    @Override
    public int updateHospitalPlannedDistributionByApplyNoList(List<String> applyNoList) {
        return hospitalPlannedDistributionMapper.updateHospitalPlannedDistributionByApplyNoList(applyNoList) ;
    }
}
