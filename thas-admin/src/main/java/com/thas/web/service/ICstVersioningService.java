package com.thas.web.service;

import com.thas.web.domain.CstVersioning;

import java.util.List;


/**
 * 版本管理模板
Service接口
 *
 * <AUTHOR>
 * @date 2022-01-20
 */
public interface ICstVersioningService
{
    /**
     * 查询版本管理模板
     *
     * @param id 版本管理模板主键
     * @return 版本管理模板
     */
    public CstVersioning selectCstVersioningById(Long id);

    Long selectEffCstVersion();
    /**
     * 查询版本管理模板列表
     *
     * @param cstVersioning 版本管理模板
     * @return 版本管理模板集合
     */
    public List<CstVersioning> selectCstVersioningList(CstVersioning cstVersioning);

    /**
     * 新增版本管理模板
     *
     * @param cstVersioning 版本管理模板
     * @return 结果
     */
    public int insertCstVersioning(CstVersioning cstVersioning);

    /**
     * 修改版本管理模板
     *
     * @param cstVersioning 版本管理模板
     * @return 结果
     */
    public int updateCstVersioning(CstVersioning cstVersioning);

    /**
     * 批量删除版本管理模板
     *
     * @param ids 需要删除的版本管理模板主键集合
     * @return 结果
     */
    public int deleteCstVersioningByIds(Long[] ids);

    /**
     * 删除版本管理模板信息
     *
     * @param id 版本管理模板主键
     * @return 结果
     */
    public int deleteCstVersioningById(Long id);

    /**
     * 验证密码再删除
     * @param cstVersioning
     * @return
     */
    public void deleteAndVerifyPwdById(CstVersioning cstVersioning);

    /**
     * 通过id修改状态为启用
     * @param cstVersioning
     * @return
     */
    public int updateStatusStartById(CstVersioning cstVersioning);

    Long selectVersionId(String applyNo);

}
