package com.thas.web.service.impl;

import cn.hutool.core.bean.BeanUtil;

import java.util.Arrays;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.thas.common.constant.Constants;
import com.thas.common.core.domain.entity.SysUser;
import com.thas.common.enums.*;
import com.thas.common.exception.ServiceException;
import com.thas.common.utils.DateUtils;
import com.thas.common.utils.StringUtils;
import com.thas.common.utils.bean.BeanUtils;
import com.thas.system.domain.vo.RoleAndUserVo;
import com.thas.system.domain.vo.TrainerValidDataQueryVo;
import com.thas.system.domain.vo.UserVo;
import com.thas.system.mapper.SysUserMapper;
import com.thas.system.mapper.SysUserRoleMapper;
import com.thas.system.service.ISysUserService;
import com.thas.web.domain.*;
import com.thas.web.domain.dto.TraQuestionnaireDTO;
import com.thas.web.domain.dto.TrainingEvaluateResultReq;
import com.thas.web.domain.vo.*;
import com.thas.web.dto.UpdFileShareTempDataDto;
import com.thas.web.mapper.TraQuestionnaireFeedBackRecordMapper;
import com.thas.web.mapper.TraQuestionnaireMapper;
import com.thas.web.mapper.TrainingEvaluateResultMapper;
import com.thas.web.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class ITrainingEvaluateResultImpl implements ITrainingEvaluateResultService {

    @Autowired
    private ISysUserService userService;
    @Autowired
    private TrainingEvaluateResultMapper trainingEvaluateResultMapper;
    @Autowired
    private ITraAnswerSheetService traAnswerSheetService;
    @Autowired
    private ICstOfflineTrainingManagementService cstOfflineTrainingManagementService;
    @Autowired
    private ICstReviewerOfflineTrainingService iCstReviewerOfflineTrainingService;
    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;
    @Autowired
    private IHospitalReviewerService hospitalReviewerService;
    @Autowired
    private IAutSaRelationService autSaRelationService;
    @Autowired
    private IUploadFileInfoService iUploadFileInfoService;
    @Autowired
    private TraQuestionnaireFeedBackServiceImpl traQuestionnaireFeedBackServiceImpl;
    @Autowired
    private TraQuestionnaireFeedBackRecordMapper traQuestionnaireFeedBackRecordMapper;
    @Autowired
    private TraQuestionnaireMapper traQuestionnaireMapper;
    @Autowired
    private ReviewerBaseInfoServiceImpl reviewerBaseInfoService;
    @Autowired
    private SysUserMapper sysUserMapper;


    @Override
    public List<TrainerValidDataQueryVo> trainerValidDataQuery(String roleName) {
        List<TrainerValidDataQueryVo> trainerValidDataQueryVos = new ArrayList<>();
        List<RoleAndUserVo> roleAndUserVos = userService.selectUserListGroupByRole(roleName);
        trainerValidDataQueryVos = roleAndUserVos.stream().map(roleAndUserVo -> {
            TrainerValidDataQueryVo trainerValidDataQueryVo = new TrainerValidDataQueryVo();
            trainerValidDataQueryVo.setRoleId(roleAndUserVo.getRoleId());
            trainerValidDataQueryVo.setRoleName(roleAndUserVo.getRoleName());
            trainerValidDataQueryVo.setSysUserList(BeanUtil.copyToList(roleAndUserVo.getSysUserList(), TrainerValidDataQueryVo.UserInfo.class));
            return trainerValidDataQueryVo;
        }).collect(Collectors.toList());
        return trainerValidDataQueryVos;
    }

    /**
     * 查询培训评估结果
     *
     * @param resultId 培训评估结果主键
     * @return 培训评估结果
     */
    @Override
    public TrainingEvaluateResult selectTrainingEvaluateResultByResultId(Long resultId) {
        return trainingEvaluateResultMapper.selectTrainingEvaluateResultByResultId(resultId);
    }

    /**
     * 查询培训评估结果列表
     *
     * @param trainingEvaluateResult 培训评估结果
     * @return 培训评估结果
     */
    @Override
    public List<TrainingEvaluateResult> selectTrainingEvaluateResultList(TrainingEvaluateResult trainingEvaluateResult) {
        return trainingEvaluateResultMapper.selectTrainingEvaluateResultList(trainingEvaluateResult);
    }

    /**
     * 新增培训评估结果
     *
     * @param trainingEvaluateResult 培训评估结果
     * @return 结果
     */
    @Override
    public int insertTrainingEvaluateResult(TrainingEvaluateResult trainingEvaluateResult) {
        trainingEvaluateResult.setCreateTime(DateUtils.getNowDate());
        return trainingEvaluateResultMapper.insertTrainingEvaluateResult(trainingEvaluateResult);
    }

    /**
     * 修改培训评估结果
     *
     * @param trainingEvaluateResult 培训评估结果
     * @return 结果
     */
    @Override
    public int updateTrainingEvaluateResult(TrainingEvaluateResult trainingEvaluateResult) {
        return trainingEvaluateResultMapper.updateTrainingEvaluateResult(trainingEvaluateResult);
    }

    /**
     * 批量删除培训评估结果
     *
     * @param resultIds 需要删除的培训评估结果主键
     * @return 结果
     */
    @Override
    public int deleteTrainingEvaluateResultByResultIds(Long[] resultIds) {
        return trainingEvaluateResultMapper.deleteTrainingEvaluateResultByResultIds(resultIds);
    }

    /**
     * 删除培训评估结果信息
     *
     * @param resultId 培训评估结果主键
     * @return 结果
     */
    @Override
    public int deleteTrainingEvaluateResultByResultId(Long resultId) {
        return trainingEvaluateResultMapper.deleteTrainingEvaluateResultByResultId(resultId);
    }

    @Override
    public TrainingEvaluateResultReq theoryQuery(TrainingEvaluateResultRes res) {
        //1-1 根据入参角色名称，判断是否为评审学员，是给操作，不是抛异常
        if (!res.getRoleName().equals(AutSaAudRoleEnum.TRAINEES_ASSESSOR.getDesc())) {
            throw new ServiceException("当前操作的角色不是评审学员，请确认是否是评审学员后在重新操作！");
        }

//        //TODO: 理论培训-在线学习、考试流程取消（代码不删，后续恢复）
//        //1-2-1-1根据评审员学员id和账号状态查询用户表，获取用户名称，根据名称和是否及格状态获取对应的线上考卷答卷数据，
//        SysUser user = userService.selectUserById(res.getTraineesAssessorId());
//        if (ObjectUtil.isNull(user) || user.getStatus().equals(UserConstants.USER_DISABLE)) {
//            log.error("查询用户信息有误--{}", ObjectUtil.isNull(user) ? "用户信息为空，用户Id为：" + res.getTraineesAssessorId() : "用户账号已封禁：状态为" + user.getStatus());
//            throw new ServiceException("查询用户信息有误，请联系管理员！");
//        }
//        //校验：理论培训
//        traQuestionnaireFeedBackServiceImpl.checkTheoryTraining(user);

        //返参数据
        TrainingEvaluateResultReq trainingEvaluateResultReq = new TrainingEvaluateResultReq();

        //1-2根据评审学员Id、是否有效和评审结果类型，查询学员培训评估结果表
        TrainingEvaluateResult trainingEvaluateResult = new TrainingEvaluateResult();
        BeanUtils.copyProperties(res, trainingEvaluateResult);
        trainingEvaluateResult.setValidFlag((long) Constants.INT_ONE);
        List<TrainingEvaluateResult> trainingEvaluateResultList = this.selectTrainingEvaluateResultList(trainingEvaluateResult);

        //1-2-1 如果有数据
        if (CollectionUtils.isEmpty(trainingEvaluateResultList)) {
            //如果没数据，不回显
            return null;
        }
        if (trainingEvaluateResultList.size() > 1) {
            log.error("根据评审学员Id、是否有效和评审结果类型--{}，查询学员培训评估结果表有多条数据，理应为1条数据;", JSONUtil.toJsonStr(res));
            throw new ServiceException("查询培训评估结果有误，请联系管理员！");
        }
        BeanUtils.copyProperties(trainingEvaluateResultList.get(0), trainingEvaluateResultReq);

//        //1-2-1-2如果有数据，判断获取提交时间是否大于学员培训评估结果表的修改时间且考卷状态为及格以上，
//        //createId 为用户名称user_name
//        TraAnswerSheet traAnswerSheet = new TraAnswerSheet();
//        traAnswerSheet.setCreateId(user.getUserName());
//        traAnswerSheet.setStatus(Constants.INT_ONE);
//        traAnswerSheet.setCreateTime(trainingEvaluateResultReq.getModifyTime());
//        List<TraAnswerSheetVO> list = traAnswerSheetService.selectTraAnswerSheetList(traAnswerSheet);
//        //如果大于且及格有数据，证明有新的理论培训通过了培训，结果表的数据不展示（需线上线下都通过逻辑才不展示）
//        if (CollectionUtils.isNotEmpty(list)) {
//            return null;
//        }
//
//        //1-2-1-3根据状态为已发布，是否有效和举办时间是否大于结果表的修改时间，查询线下培训管理列表
//        CstOfflineTrainingManagement trainingManagement = new CstOfflineTrainingManagement();
//        trainingManagement.setTrainingStatus(Constants.STR_NUM_1);
//        trainingManagement.setStatus(Constants.INT_ONE);
//        trainingManagement.setTrainingTime(trainingEvaluateResultReq.getModifyTime());
//        trainingManagement.setFilterIs(Constants.STR_NUM_1);
//        List<CstOfflineTrainingManagement> managementList = cstOfflineTrainingManagementService.selectCstOfflineTrainingManagementList(trainingManagement);
//
//        //1-2-1-4如果有数据，根据线下培训id，评审员id(评审学员Id),参与状态和是否有效条件，查询培训对应的报名情况是否有数据，有的话，结果表信息不展示
//        if (CollectionUtils.isNotEmpty(managementList)) {
//            List<Long> collect = managementList.stream().map(CstOfflineTrainingManagement::getId).collect(Collectors.toList());
//            CstReviewerOfflineTraining training = new CstReviewerOfflineTraining();
//            training.setTrainingId(StringUtils.join(collect, ","));
//            training.setReviewerId(res.getTraineesAssessorId().toString());
//            training.setPartStatus(Constants.INT_ONE);
//            training.setStatus(Constants.INT_ONE);
//            List<CstReviewerOfflineTraining> trainingList = iCstReviewerOfflineTrainingService.selectCstReviewerOfflineTrainingList(training);
//            if (CollectionUtils.isNotEmpty(trainingList)) {
//                return null;
//            }
//        }
        //1-2-1-5反之，回显数据
        //封装文件信息；
        packFileInfoList(trainingEvaluateResultList, trainingEvaluateResultReq);
        return trainingEvaluateResultReq;
    }

    private void packFileInfoList(List<TrainingEvaluateResult> trainingEvaluateResultList, TrainingEvaluateResultReq trainingEvaluateResultReq) {
        String fileId = trainingEvaluateResultList.get(0).getFileId();
        if (StringUtils.isNotEmpty(fileId)) {
            List<FileInfoDTO> fileInfoDTOList = iUploadFileInfoService.getUploadFileInfoByIds(fileId);
            if (CollectionUtils.isEmpty(fileInfoDTOList)) {
                log.error("查询文件信息为空,文件Id为：{}", fileId);
                throw new ServiceException("查询文件信息为空，请联系管理员");
            }
            List<FileInfoVO> fileInfoList = new ArrayList<>();
            fileInfoDTOList.forEach(fileInfoDTO -> {
                FileInfoVO fileInfoVO = new FileInfoVO();
                fileInfoVO.setFileId(fileInfoDTO.getId().toString());
                fileInfoVO.setFileName(fileInfoDTO.getOrigin());
                fileInfoVO.setUrl(fileInfoDTO.getPath());
                fileInfoList.add(fileInfoVO);

            });
            trainingEvaluateResultReq.setFileInfoList(fileInfoList);
        }
    }

    @Override
    public void theorySubmit(TrainingEvaluateResultSubmitRes res) {
        //如果结论状态为转为评审员，判断学员是否在途，有在途不给转
        String conclusion = res.getConclusion().toString();

        //其他备注(结论状态为其他时，不能为空)
        if (TrainingEvaluateResultEnum.ConclusionEnum.OTHER_0.getStrCode().equals(conclusion) && StringUtils.isBlank(res.getOtherRemark())) {
            throw new ServiceException("结论为其他时，描述不能为空");
        }

        if (TrainingEvaluateResultEnum.ConclusionEnum.THEORY_EXEMPT_10.getStrCode().equals(conclusion) ||
                TrainingEvaluateResultEnum.ConclusionEnum.SITE_FIT_CONSIDER_20.getStrCode().equals(conclusion) ||
                TrainingEvaluateResultEnum.ConclusionEnum.SITE_FIT_NO_CONSIDER_21.getStrCode().equals(conclusion)
        ) {
            List<AutSaRelation> autSaRelation = autSaRelationService.selectAutSaRelationByTraineesAssessorId(res.getTraineesAssessorId());
            //筛选在途数量
            long count = autSaRelation.stream().filter(o -> ObjectUtil.equal(o.getEvaluateFlag(), Constants.INT_ZERO)).count();
            if (count > 0) {
                //学员在途
                throw new ServiceException("当前评审学员有在途任务，不能转为评审员！");
            }
        }

        //2-1.查询结果表是否有数据，有数据就更新为失效，再插入一条为新数据，没数据直接插入
        TrainingEvaluateResult trainingEvaluateResult = queryTrainingEvaluateResultList(res);
        if (trainingEvaluateResult != null) {
            //校验是否学员或教员有未填写完的反馈表
            checkIsFillFeedBack(res, trainingEvaluateResult);

            //判断结论是否转为评审员，是抛异常，证明已触发此接口，防重
            String qryConclusion = String.valueOf(trainingEvaluateResult.getConclusion());
            if (!TrainingEvaluateResultEnum.ConclusionEnum.OTHER_0.getStrCode().equals(qryConclusion) &&
                    TrainingEvaluateResultEnum.ConclusionEnum.BECOME_REVIEWER.getStrCode().contains(qryConclusion)) {
                throw new ServiceException("其他管理员已评估，请刷新页面查看");
            }

            TrainingEvaluateResult updateTraining = new TrainingEvaluateResult();
            updateTraining.setResultId(trainingEvaluateResult.getResultId());
            updateTraining.setValidFlag((long) Constants.INT_ZERO);
            //更新为失效
            int i = this.updateTrainingEvaluateResult(updateTraining);
            if (i <= 0) {
                log.error("培训评估结果表更新失败，数据为：{}", updateTraining.toString());
                throw new ServiceException("提交失败，请联系管理员！");
            }
        }
        //插入
        TrainingEvaluateResult insertTraining = new TrainingEvaluateResult();
        BeanUtils.copyProperties(res, insertTraining);
        int i = this.insertTrainingEvaluateResult(insertTraining);
        if (i <= 0) {
            log.error("培训评估结果表新增失败，数据为：{}", insertTraining.toString());
            throw new ServiceException("提交失败，请联系管理员！");
        }

        //是否更新用户角色,默认不更新
        Boolean UpRoleFlag = false;
        //2-2.判断结论状态是否更新用户角色，需要更新
        if (!conclusion.equals(TrainingEvaluateResultEnum.ConclusionEnum.OTHER_0.getStrCode()) &&
                TrainingEvaluateResultEnum.ConclusionEnum.BECOME_REVIEWER.getStrCode().contains(conclusion)) {
            int updateUserRoleInfo = sysUserRoleMapper.updateUserRoleInfo(AutSaAudRoleEnum.ASSESSOR.getCode(), res.getTraineesAssessorId().toString());
            if (updateUserRoleInfo <= 0) {
                log.error("培训评估结果提交-评审学员id为{}，转为评审员失败！", res.getTraineesAssessorId());
                throw new ServiceException("提交失败，请联系管理员！");
            }

            // 将学员对应的附件修改为评审员
            UpdFileShareTempDataDto updFileShareTempDataDto = new UpdFileShareTempDataDto();
            updFileShareTempDataDto.setTempCode(res.getTraineesAssessorId().toString());
            updFileShareTempDataDto.setRoleKey(Constants.HospitalConstants.ROLE_ASSESSOR);
            updFileShareTempDataDto.setOldRoleKey(Constants.HospitalConstants.ROLE_TRAINEES_ASSESSOR);
            fileShareService.updateByOwnerIdAndRoleKey(updFileShareTempDataDto);
            UpRoleFlag = true;
        }
        //反馈问卷填写统计记录表同步生成数据(理论培训、实践培训和培训教员反馈表)
        //理论评估下发的反馈表改为问卷列表-手动下发
        if (ObjectUtil.equal(TrainingEvaluateResultEnum.ReviewResultTypeEnum.REVIEW_RESULT_TYPE_2.getCode(), Math.toIntExact(res.getReviewResultType()))) {
            this.insertBatchTraQuestionnaireFeedBackRecord(res);
        }

        //用户角色转换成功，如果用户在登录中，强制退出(清除转换角色显示菜单等前端缓存问题)
        if (UpRoleFlag) {
            SysUser sysUser = sysUserMapper.selectUserById(res.getTraineesAssessorId());
            if (sysUser == null) {
                log.error("获取学员用户信息为空，用户id为：{}", res.getTraineesAssessorId());
                throw new ServiceException("获取当前学员用户信息为空！");
            }
            reviewerBaseInfoService.forceLogout(sysUser.getUserName());
        }

    }

    /**
     * 校验是否学员或教员有未填写完的反馈表
     *
     * @param res
     * @param trainingEvaluateResult
     */
    private void checkIsFillFeedBack(TrainingEvaluateResultSubmitRes res, TrainingEvaluateResult trainingEvaluateResult) {

        //如果有在途未填写的提示文案：
        //学员的，提示“${评审学员1}、${评审学员2}存在未填写的学员理论培训反馈表，需填写完成后再下发”；
        //教员的，提示“${教员1}、${教员2}存在未填写的教员反馈表，需填写完成后再下发”。
        if (ObjectUtil.equal(TrainingEvaluateResultEnum.ReviewResultTypeEnum.REVIEW_RESULT_TYPE_1.getCode(), Math.toIntExact(res.getReviewResultType()))) {
            //根据评审结果id，反馈问卷类型，未填写状态，查询是否有记录，有记录报异常
            TraQuestionnaireFeedBackRecord qryTraQuestionnaireFeedBackRecord = new TraQuestionnaireFeedBackRecord();
            qryTraQuestionnaireFeedBackRecord.setResultId(trainingEvaluateResult.getResultId());
            qryTraQuestionnaireFeedBackRecord.setFillStatus(Constants.STR_NUM_0);
            qryTraQuestionnaireFeedBackRecord.setStatus(Long.valueOf(Constants.STR_NUM_1));
            List<TraQuestionnaireFeedBackRecord> traQuestionnaireFeedBackRecordList =
                    traQuestionnaireFeedBackRecordMapper.selectTraQuestionnaireFeedBackRecordList(qryTraQuestionnaireFeedBackRecord);
            if (CollectionUtils.isNotEmpty(traQuestionnaireFeedBackRecordList)) {
                StringBuffer er = new StringBuffer();
                Map<String, List<TraQuestionnaireFeedBackRecord>> groupTypeMap =
                        traQuestionnaireFeedBackRecordList.stream().collect(Collectors.groupingBy(o -> o.getFeedBackType().toString()));
                groupTypeMap.forEach((feedBackType, v) -> {
                    //学员
                    if (TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_5.getCode().equals(feedBackType)) {
                        packFeedBackTypeErInfo(v, er, feedBackType);
                    }
                    //教员
                    if (TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_7.getCode().equals(feedBackType)) {
                        packFeedBackTypeErInfo(v, er, feedBackType);
                    }
                });
                if (StringUtils.isNotEmpty(er.toString())) {
                    throw new ServiceException(String.format("%s需填写完成后再评估！", er.toString()));
                }
            }

        }

    }

    private StringBuffer packFeedBackTypeErInfo(List<TraQuestionnaireFeedBackRecord> v, StringBuffer er, String feedBackType) {
        List<String> needId = v.stream().map(TraQuestionnaireFeedBackRecord::getNeedId).collect(Collectors.toList());
        List<UserVo> userVos = sysUserMapper.selectUserByIds(needId);
        String userNames = userVos.stream().map(UserVo::getNickName).collect(Collectors.joining("、"));
        //如果用户账号失效时，忽略
        if (StringUtils.isNotEmpty(userNames)) {
            er.append(userNames);
            if (TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_5.getCode().equals(feedBackType)) {
                er.append("存在未填写的学员理论培训反馈表；");
            } else {
                er.append("存在未填写的教员反馈表；");
            }
        }
        return er;
    }

    private void insertBatchTraQuestionnaireFeedBackRecord(TrainingEvaluateResultSubmitRes res) {
        log.info("insertBatchTraQuestionnaireFeedBackRecord-评估结果-生成反馈文件记录表数据-开始，入参：[{}]", res.toString());
        //新的有效评估结果数据
        TrainingEvaluateResult trainingEvaluateResult = queryTrainingEvaluateResultList(res);
        if (trainingEvaluateResult == null) {
            throw new ServiceException("反馈问卷填写统计记录表同步生成数据-查询培训评估结果有误，请联系管理员！");
        }

        List<TraQuestionnaireVO> traQuestionnaireVOS = new ArrayList<>();

        List<TraQuestionnaireFeedBackRecord> traQuestionnaireFeedBackRecordList = new ArrayList<>();
        //为理论培训评估结果时，生成对应反馈表记录数据，附件3：对理论培训的反馈，附件9：培训教员反馈表（多个教员对应生成多份）
        if (ObjectUtil.equal(TrainingEvaluateResultEnum.ReviewResultTypeEnum.REVIEW_RESULT_TYPE_1.getCode(), Math.toIntExact(res.getReviewResultType()))) {
            //附件3：对理论培训的反馈
            //获取问卷类型对应问卷id（1对多）
            List<Integer> typeList = new ArrayList<>();
            typeList.add(TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_5.getIntCode());
            typeList.add(TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_7.getIntCode());
            traQuestionnaireVOS = queryTraQuestionnaireList(typeList);

            traQuestionnaireVOS.stream().filter(o -> ObjectUtil.equal(TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_5.getIntCode(), o.getType())).forEach(traQuestionnaireVO -> {
                TraQuestionnaireFeedBackRecord traQuestionnaireFeedBackRecord = new TraQuestionnaireFeedBackRecord();
                traQuestionnaireFeedBackRecord.setResultId(trainingEvaluateResult.getResultId());
                traQuestionnaireFeedBackRecord.setNeedId(String.valueOf(res.getTraineesAssessorId()));
                traQuestionnaireFeedBackRecord.setFeedBackType(Long.valueOf(TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_5.getCode()));
                traQuestionnaireFeedBackRecord.setQuestionnaireId(traQuestionnaireVO.getId());
                traQuestionnaireFeedBackRecordList.add(traQuestionnaireFeedBackRecord);
            });

        } else if (ObjectUtil.equal(TrainingEvaluateResultEnum.ReviewResultTypeEnum.REVIEW_RESULT_TYPE_2.getCode(), Math.toIntExact(res.getReviewResultType()))) {
            //为带教培训评估结果时，生成对应反馈表记录数据，附件3：对实践培训的反馈
            //获取问卷类型对应问卷id（1对多）
            List<Integer> typeList = new ArrayList<>();
            typeList.add(TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_8.getIntCode());
            typeList.add(TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_7.getIntCode());
            traQuestionnaireVOS = queryTraQuestionnaireList(typeList);
            //根据需要填写用户Id,问卷类型，是否已填写，是否有效，查询反馈问卷记录，如果有记录（现逻辑现场评审完发放肯定有记录），更新，没记录报错
            TraQuestionnaireFeedBackRecord qryTraQuestionnaireFeedBackRecord = new TraQuestionnaireFeedBackRecord();
            qryTraQuestionnaireFeedBackRecord.setNeedId(String.valueOf(res.getTraineesAssessorId()));
            qryTraQuestionnaireFeedBackRecord.setFeedBackType(Long.valueOf(TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_8.getCode()));
            qryTraQuestionnaireFeedBackRecord.setFillStatus(Constants.STR_NUM_0);
            qryTraQuestionnaireFeedBackRecord.setStatus(1L);
            List<TraQuestionnaireFeedBackRecord> qryRecordList = traQuestionnaireFeedBackRecordMapper.selectTraQuestionnaireFeedBackRecordList(qryTraQuestionnaireFeedBackRecord);
            if (CollectionUtils.isEmpty(qryRecordList)) {
                throw new ServiceException("学员未进行/未完成再带教培训，不能更改评估结果，请稍后再试");
            }
//            List<TraQuestionnaireFeedBackRecord> updateList = new ArrayList<>();
            traQuestionnaireVOS.stream().filter(o -> ObjectUtil.equal(TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_8.getIntCode(), o.getType())).forEach(traQuestionnaireVO -> {
                qryRecordList.forEach(o -> {
                    o.setResultId(trainingEvaluateResult.getResultId());
                    o.setQuestionnaireId(traQuestionnaireVO.getId());
                });
//                TraQuestionnaireFeedBackRecord traQuestionnaireFeedBackRecord = new TraQuestionnaireFeedBackRecord();
//                traQuestionnaireFeedBackRecord.setResultId(trainingEvaluateResult.getResultId());
//                traQuestionnaireFeedBackRecord.setNeedId(String.valueOf(res.getTraineesAssessorId()));
//                traQuestionnaireFeedBackRecord.setFeedBackType(Long.valueOf(TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_8.getCode()));
//                traQuestionnaireFeedBackRecord.setQuestionnaireId(traQuestionnaireVO.getId());
//                updateList.add(traQuestionnaireFeedBackRecord);
            });
            //更新
            traQuestionnaireFeedBackRecordMapper.updateTraQuestionnaireFeedBackRecordList(qryRecordList);

        } else {
            throw new ServiceException("评审结果类型错误，目前只支持1-理论培训评估结果 2-现场评审带教培训评估结果！");
        }
        //附件9：培训教员反馈表（多个教员对应生成多份）
        traQuestionnaireVOS.stream().filter(o -> ObjectUtil.equal(TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_7.getIntCode(), o.getType())).forEach(traQuestionnaireVO -> {
            List<String> trainerIdList = Arrays.asList(res.getTrainerId().split(","));
            for (String trainerId : trainerIdList) {
                TraQuestionnaireFeedBackRecord traQuestionnaireFeedBackRecord = new TraQuestionnaireFeedBackRecord();
                traQuestionnaireFeedBackRecord.setResultId(trainingEvaluateResult.getResultId());
                traQuestionnaireFeedBackRecord.setNeedId(trainerId);
                traQuestionnaireFeedBackRecord.setFeedBackType(Long.valueOf(TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_7.getCode()));
                traQuestionnaireFeedBackRecord.setQuestionnaireId(traQuestionnaireVO.getId());
                traQuestionnaireFeedBackRecordList.add(traQuestionnaireFeedBackRecord);
            }
        });
        if (CollectionUtils.isNotEmpty(traQuestionnaireFeedBackRecordList)) {
            traQuestionnaireFeedBackRecordMapper.insertBatchTraQuestionnaireFeedBackRecord(traQuestionnaireFeedBackRecordList, Constants.INT_ZERO);
        }
        log.info("insertBatchTraQuestionnaireFeedBackRecord-评估结果-生成反馈文件记录表数据-结束，落表数据：[{}]", JSON.toJSONString(traQuestionnaireFeedBackRecordList));
    }

    public List<TraQuestionnaireVO> queryTraQuestionnaireList(List<Integer> typeList) {
        TraQuestionnaireDTO traQuestionnaireDTO = new TraQuestionnaireDTO();
        traQuestionnaireDTO.setTypeList(typeList);
        traQuestionnaireDTO.setStatus(Constants.INT_ONE);
        return traQuestionnaireMapper.selectTraQuestionnaireList(traQuestionnaireDTO);
    }

    private TrainingEvaluateResult queryTrainingEvaluateResultList(TrainingEvaluateResultSubmitRes res) {
        TrainingEvaluateResult trainingQuery = new TrainingEvaluateResult();
        trainingQuery.setTraineesAssessorId(res.getTraineesAssessorId());
        trainingQuery.setValidFlag((long) Constants.INT_ONE);
        trainingQuery.setReviewResultType(res.getReviewResultType());
        List<TrainingEvaluateResult> trainingEvaluateResultList = this.selectTrainingEvaluateResultList(trainingQuery);

        if (CollectionUtils.isNotEmpty(trainingEvaluateResultList) && trainingEvaluateResultList.size() > 1) {
            log.error("根据评审学员Id、是否有效和评审结果类型--{}，查询学员培训评估结果表有多条数据，理应为1条数据;", JSONUtil.toJsonStr(res));
            throw new ServiceException("查询培训评估结果有误，请联系管理员！");
        }
        if (CollectionUtils.isEmpty(trainingEvaluateResultList)) {
            return null;
        }
        return trainingEvaluateResultList.get(0);
    }

    @Autowired
    private IFileShareService fileShareService;

    @Override
    public TrainingEvaluateResultReq siteReviewQuery(TrainingEvaluateResultRes res) {
        //现场评审：hospital_reviewer，aut_sa_relation
        //3-1根据入参评审员id查询，查询hospital_reviewer医院与评审员信息表，获取医院编号
        HospitalReviewer hospitalReviewer = new HospitalReviewer();
        hospitalReviewer.setReviewerId(res.getTraineesAssessorId().toString());
        List<HospitalReviewer> list = hospitalReviewerService.selectHospitalReviewerList(hospitalReviewer);
        if (CollectionUtils.isEmpty(list)) {
            log.error("查询关联医院与评审员信息为空，不允许查询操作,评审员Id为{}", res.getTraineesAssessorId());
            throw new ServiceException("没有完成现场评审，不允许查询操作");
        }

        //3-2根据医院编号、自评认证状态为最后完成节点和是否有效字段，查询aut_sa_relation认证自评关联表，如果有数据，证明完成了至少一次现场评审，这边可以查询操作
        List<String> applyNoList = list.stream().map(HospitalReviewer::getApplyNo).collect(Collectors.toList());
        AutSaRelationQueryRes autSaRelation = new AutSaRelationQueryRes();
        autSaRelation.setHospitalApplyNoList(applyNoList);
        autSaRelation.setStatus(Constants.INT_ONE);
        autSaRelation.setEvaluateFlag(Constants.INT_ONE);
        List<AutSaRelation> autSaRelations = autSaRelationService.selectAutSaRelationListByQueryRes(autSaRelation);
        if (CollectionUtils.isEmpty(autSaRelations)) {
            log.error("查询关联医院与评审员信息为空，不允许查询操作，查询条件为：{}", autSaRelation.toString());
            throw new ServiceException("没有完成现场评审，不允许查询操作");
        }

        //3-4根据区分现场评审还是理论培训字段和评审员Id来 查询结果表，有数据就回显
        TrainingEvaluateResult trainingEvaluateResult = new TrainingEvaluateResult();
        BeanUtils.copyProperties(res, trainingEvaluateResult);
        trainingEvaluateResult.setValidFlag((long) Constants.INT_ONE);
        List<TrainingEvaluateResult> trainingEvaluateResultList = this.selectTrainingEvaluateResultList(trainingEvaluateResult);
        TrainingEvaluateResultReq trainingEvaluateResultReq = new TrainingEvaluateResultReq();
        if (CollectionUtils.isEmpty(trainingEvaluateResultList)) {
            return null;
        }
        BeanUtils.copyProperties(trainingEvaluateResultList.get(0), trainingEvaluateResultReq);

        //封装文件信息
        this.packFileInfoList(trainingEvaluateResultList, trainingEvaluateResultReq);
        return trainingEvaluateResultReq;
    }

    /**
     * @param conclusion 结果状态，可以用’,‘拼接查询
     * @return
     */
    @Override
    public List<TrainingEvaluateResult> selectTrainingEvaluateResultListByConclusion(String conclusion) {
        if (StringUtils.isBlank(conclusion)) {
            throw new ServiceException("conclusion入参不能为空！");
        }
        return trainingEvaluateResultMapper.selectTrainingEvaluateResultListByConclusion(conclusion);
    }

}
