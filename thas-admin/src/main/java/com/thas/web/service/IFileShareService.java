package com.thas.web.service;

import com.thas.common.core.page.TableDataInfo;
import com.thas.web.domain.FileShare;
import com.thas.web.dto.DelFileShareRequest;
import com.thas.web.dto.FileShareCreateRequest;
import com.thas.web.dto.QryFileShareRequest;
import com.thas.web.dto.UpdFileShareTempDataDto;

import java.util.List;


/**
 * 管理员文件分享关联Service接口
 *
 * <AUTHOR>
 * @date 2022-10-27
 */
public interface IFileShareService {
    /**
     * 查询管理员文件分享关联
     *
     * @param roleKey 管理员文件分享关联主键
     * @return 管理员文件分享关联
     */
    FileShare selectFileShareByRoleKey(String roleKey);

    /**
     * 查询管理员文件分享关联列表
     *
     * @param fileShare 管理员文件分享关联
     * @return 管理员文件分享关联集合
     */
    List<FileShare> selectFileShareList(FileShare fileShare);

    /**
     * 通过文件id查询
     *
     * @param fileIdList 文件id集合
     * @return FileShare
     */
    List<FileShare> selectFileShareList(List<Long> fileIdList);

    /**
     * 新增管理员文件分享关联
     *
     * @param fileShare 管理员文件分享关联
     * @return 结果
     */
    int insertFileShare(FileShare fileShare);

    /**
     * 批量保存
     *
     * @param fileShareList 管理员文件分享关联
     * @return 保存结果
     */
    int batchInsertFileShare(List<FileShare> fileShareList);

    /**
     * 修改管理员文件分享关联
     *
     * @param fileShare 管理员文件分享关联
     * @return 结果
     */
    int updateFileShare(FileShare fileShare);

    /**
     * 批量删除管理员文件分享关联
     *
     * @param roleKeys 需要删除的管理员文件分享关联主键集合
     * @return 结果
     */
    int deleteFileShareByRoleKeys(String[] roleKeys);

    /**
     * 删除管理员文件分享关联信息
     *
     * @param roleKey 管理员文件分享关联主键
     * @return 结果
     */
    int deleteFileShareByRoleKey(String roleKey);

    /**
     * 文件共享提交-免登
     *
     * @param request 入参
     */
    void fileShareBingCreate(FileShareCreateRequest request);

    /**
     * 文件共享提交
     *
     * @param request 入参
     */
    void fileShareCreate(FileShareCreateRequest request);

    /**
     * 查询文件
     *
     * @param request 入参
     * @return QryFileShareResponse
     */
    TableDataInfo qryFileShare(QryFileShareRequest request);

    /**
     * 给免登阶段上传的文件赋值具体的userId
     *
     * @param request 入参
     */
    void updateByOwnerId(UpdFileShareTempDataDto request);

    /**
     * 通过所属人id和roleyKey修改角色
     */
    void updateByOwnerIdAndRoleKey(UpdFileShareTempDataDto request);

    /**
     * 通过文件id删除对应文件共享资源
     *
     * @param request 请求入参
     */
    void delFileShareByFileId(DelFileShareRequest request);
}
