package com.thas.web.service.process.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.thas.common.constant.Constants;
import com.thas.common.core.text.Convert;
import com.thas.common.enums.*;
import com.thas.common.exception.ServiceException;
import com.thas.common.utils.SecurityUtils;
import com.thas.system.domain.SysUserHospital;
import com.thas.system.service.ISysUserHospitalService;
import com.thas.system.service.ISysUserService;
import com.thas.web.domain.*;
import com.thas.web.domain.vo.AutSaAudDetailVO;
import com.thas.web.domain.vo.HospitalReviewerVo;
import com.thas.web.dto.HosPlanUserInfoVO;
import com.thas.web.dto.HosReviewPlanVO;
import com.thas.web.dto.QueryBaseConditionDTO;
import com.thas.web.mapper.*;
import com.thas.web.service.IAutSaRelationService;
import com.thas.web.service.ICstCertificationStandardsService;
import com.thas.web.service.IHospitalBaseInfoService;
import com.thas.web.service.IHospitalPreExamService;
import com.thas.web.service.IHospitalReviewerService;
import com.thas.web.service.process.BaseProcessService;
import com.thas.web.service.process.CommonProcessService;
import com.thas.web.utils.SpringContextUtil;

import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 审查员审查流程服务 Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-04-01
 */
@Component("baseProcessService")
@Slf4j
public class BaseProcessServiceImpl implements BaseProcessService {

    @Resource
    private AutSaAudMapper autSaAudMapper;

    @Resource
    private IAutSaRelationService autSaRelationService;

    @Resource
    private IHospitalBaseInfoService hospitalBaseInfoService;

    @Resource
    private CommonProcessService commonProcessService;

    @Resource
    private ISysUserService sysUserService;

    @Resource
    private IHospitalPreExamService hospitalPreExamService;

    @Resource
    private IHospitalReviewerService hospitalReviewerService;

    @Resource
    private ICstCertificationStandardsService cstCertificationStandardsService;

    @Resource
    private ISysUserHospitalService sysUserHospitalService;

    @Resource
    private CommonProcessMapper commonProcessMapper;

    @Resource
    private AutSaAudBusinessDataMapper autSaAudBusinessDataMapper;
    @Resource
    private HospitalPlannedDistributionMapper hospitalPlannedDistributionMapper;

    @Resource
    private HospitalReviewerMapper hospitalReviewerMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void process(AutSaAudSaveDTO req) {
        List<AutSaAud> autSaAudLists = req.getAutSaAudLists();
        if (CollectionUtils.isNotEmpty(autSaAudLists)) {
            autSaAudLists.stream().forEach(a -> {
                a.setSubmitType(req.getSubmitType());
                a.setAutCode(req.getAutCode());
                a.setAccountId(req.getAccountId());
            });
        }
        // 根据自评编码获取自评关联信息
        AutSaRelation autSaRelation = autSaRelationService.selectAutSaRelationByAutCode(req.getAutCode(), Constants.HospitalConstants.NUM_1);
        // 校验当前节点周期时间
        //改：评审流程中各个节点，不受评审计划里的结束时间限制；
        commonProcessService.checkSubmitCycle(autSaRelation.getHospitalApplyNo(), autSaRelation.getAutCode(), autSaRelation.getAutStatus());
        req.setAutSaRelation(autSaRelation);
        // 获取当前节点对应服务配置信息
        AutSaAudStatusConfig autSaAudStatusConfig = commonProcessMapper.selectAutSaAudStatusConfig(autSaRelation.getAutStatus());
        if (ObjectUtil.isNull(autSaAudStatusConfig) || StringUtils.isBlank(autSaAudStatusConfig.getServiceName())) {
            log.error("当前节点:{} 未获取到对应配置服务信息", autSaRelation.getAutStatus());
            throw new ServiceException(ServiceExceptionEnum.AUT_SA_AUD_STATUS_CONFIG_ERROR_1000000);
        }
        req.setAutSaAudStatusConfig(autSaAudStatusConfig);
        // 校验入参提交类型
        if (StringUtils.isBlank(autSaAudStatusConfig.getSubmitType()) || !Arrays.asList(Convert.toStrArray(autSaAudStatusConfig.getSubmitType())).contains(req.getSubmitType())) {
            log.error("当前节点:{} 配置的提交类型:{} 不包含入参的提交类型：{}", autSaRelation.getAutStatus(), autSaAudStatusConfig.getSubmitType(), req.getSubmitType());
            //throw new ServiceException(ServiceExceptionEnum.AUT_SA_AUD_ERROR_1000003);
            throw new ServiceException("流程节点已翻转，请刷新页面查看");
        }
        // 校验用户有提交权限（对应有分配信息给到当前用户）
        commonProcessService.checkSubmitPermission(req);

        //所有节点提交操作，封装医院名称（用于生成报告文件名）
        req.setHospitalName(this.packHospitalName(autSaRelation));

        // 校验方法不为空，调用校验方法
        this.invokeMethod(autSaAudStatusConfig.getCheckMethod(), req);
        if (StringUtils.isNotBlank(autSaAudStatusConfig.getProcessMethod())) {
            // 流程处理方法不为空，调用流程处理方法
            this.invokeMethod(autSaAudStatusConfig.getProcessMethod(), req);
        } else {
            // 流程处理服务为空，调用流程服务处理
            BaseProcessService baseProcessService = (BaseProcessService) SpringContextUtil.getBean(autSaAudStatusConfig.getServiceName());
            if (baseProcessService == null) {
                log.error("节点：{} 配置的流程处理服务：{} 为空，请确认项目内有配置对应流程处理服务", autSaRelation.getAutStatus(), autSaAudStatusConfig.getServiceName());
                throw new ServiceException(ServiceExceptionEnum.AUT_SA_AUD_STATUS_CONFIG_ERROR_1000000);
            }
            baseProcessService.process(req);
        }
    }

    private String packHospitalName(AutSaRelation autSaRelation) {
        QueryBaseConditionDTO queryBaseConditionDTO = new QueryBaseConditionDTO();
        queryBaseConditionDTO.setCommonId(autSaRelation.getHospitalApplyNo());
        HospitalBaseInfo hospitalBaseInfo = hospitalBaseInfoService.selectHospitalByApplyNo(queryBaseConditionDTO);
        if (ObjectUtil.isEmpty(hospitalBaseInfo)) {
            log.error("根据医院编码{}，查询有效的医院基本信息为空", autSaRelation.getHospitalApplyNo());
            throw new ServiceException("获取医院基本信息错误，请联系管理员");
        }
        return hospitalBaseInfo.getHospitalName();
    }

    @Override
    public List<AutSaAudList> queryList(AutSaAudQueryDTO req) {
        // 校验角色,封装角色，用户Id入参等
        commonProcessService.checkPageType(req);

        if(AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(req.getRoleKey(),AutSaAudRoleEnum.TRAINEES_ASSESSOR)){
            //如果学员未分配医院或流程不在现场评审节点，展示为空白
            List<HospitalReviewerVo> hospitalReviewerVoList =  hospitalReviewerService.selectHospitalReviewerByTraineesAssessorReviewerId(req.getAccountId());
            if(CollectionUtils.isEmpty(hospitalReviewerVoList)){
                log.info("学员未分配医院或流程不在现场评审节点，展示为空白,accountId{}",req.getAccountId());
                return new ArrayList<>() ;
            }
            req.setAutCode(hospitalReviewerVoList.get(0).getAutCode());
            List<String> applyNos = new ArrayList<>();
            applyNos.add(hospitalReviewerVoList.get(0).getApplyNo());
            return this.queryAutSaAudListByApplyNos(applyNos, req);
        }

        // 根据条件获取对应的医疗机构列表
        List<String> applyNos = this.getApplyNosByCondition(req);
        // 查询对应审核信息
        return this.queryAutSaAudListByApplyNos(applyNos, req);
    }

    /**
     * 根据条件获取账户对应的医疗机构列表
     *
     * @param req 流程参数
     * @return 医疗机构列表
     */
    private List<String> getApplyNosByCondition(AutSaAudQueryDTO req) {
        List<HospitalBaseInfo> applyNos = Lists.newArrayList();
        if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(req.getRoleKey(), AutSaAudRoleEnum.INSPECTOR)) {
            // 审查员 + 审查员组长
            applyNos = hospitalPreExamService.selectApplyNosByPreExamId(req.getAccountId());
        } else if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(req.getRoleKey(), AutSaAudRoleEnum.SENIOR_ASSESSOR)) {
            // 资深评审员
            applyNos = hospitalReviewerService.selectApplyNosByReviewerId(req.getAccountId(), Constants.HospitalConstants.SENIOR_REVIEW);
        } else if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(req.getRoleKey(), AutSaAudRoleEnum.ASSESSOR)) {
            // 评审员 + 评审员组长
            applyNos = hospitalReviewerService.selectApplyNosByReviewerId(req.getAccountId(), null);
        } else {
            // 其他角色
            log.info("其他角色：{} 未配置列表查询权限");
        }
        if (CollectionUtils.isEmpty(applyNos)) {
            log.info("账户；{}没有查询到关联的医疗机 构相关信息", req.getAccountId());
            return Lists.newArrayList();
        }

        // 进行条件筛选区分  医院名称  + 状态
        if (StringUtils.isNotBlank(req.getDistributeHospitalName())) {
            // 通过医院名称筛选医院机构编码
            applyNos = applyNos.stream().filter(a -> StringUtils.isNotBlank(a.getHospitalName()) && a.getHospitalName().contains(req.getDistributeHospitalName())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(applyNos)) {
            log.info("通过医院名称 :{} 筛选医院机构编码:{}，结果为空", req.getDistributeHospitalName(), applyNos);
            return Lists.newArrayList();
        }
        List<String> applyNoList = applyNos.stream().map(HospitalBaseInfo::getApplyNo).collect(Collectors.toList());
        if (StringUtils.isNotBlank(req.getAutCode())) {
            // 入参自评编码不为空，直接查询该值 即可
            AutSaRelation autSaRelation = autSaRelationService.selectAutSaRelationByAutCode(req.getAutCode(), Constants.HospitalConstants.NUM_1);
            // 校验医院计划周期时间
            commonProcessService.checkQueryCycle(autSaRelation.getHospitalApplyNo(), autSaRelation.getAutCode(), autSaRelation.getAutStatus());
            if (!applyNoList.contains(autSaRelation.getHospitalApplyNo())) {
                // 入参的自评编码不与用户关联
                log.info("通过入参的自评编码:{} 获取到的医疗机构编码：{} 不在筛选出的医院机构编码中，返回结果为空", req.getAutCode(), autSaRelation.getHospitalApplyNo());
                return Lists.newArrayList();
            }
            applyNoList = Arrays.asList(autSaRelation.getHospitalApplyNo());
        }
        return applyNoList;
    }

    /**
     * 获取评审员/审核员 对应某一个医院机构的审核信息
     *
     * @param applyNos 医院机构编码列表
     * @param req      流程参数
     * @return 审核信息
     */
    private List<AutSaAudList> queryAutSaAudListByApplyNos(List<String> applyNos, AutSaAudQueryDTO req) {
        List<AutSaAudList> resultList = Lists.newArrayList();
        for (String applyNo : applyNos) {
            AutSaRelation autSaRelation = autSaRelationService.selectAutSaRelationByHospitalApplyNo(applyNo, true);
            log.info("根据医疗机构编码：{} 查询医院认证自评关联信息：{}", applyNo, autSaRelation);
            // 校验医院计划周期时间
            try {
//                commonProcessService.checkQueryCycle(autSaRelation.getHospitalApplyNo(), autSaRelation.getAutCode(), autSaRelation.getAutStatus());
                if (autSaRelationService.invalidAutSaAudByCycle(applyNo, autSaRelation)) {
                    log.info("applyNo:{} 校验计划周期时间过期，不展示该条数据信息", autSaRelation.getHospitalApplyNo());
                    continue;
                }
            } catch (ServiceException e) {
                log.info("applyNo:{} 校验计划周期时间异常 e:{}", autSaRelation.getHospitalApplyNo(), e.getMessage());
                continue;
            }
            // 根据入参状态筛选结果
            if (StringUtils.isNotBlank(req.getDistributeStatus()) && StringUtils.isNotBlank(autSaRelation.getAutStatus()) && !Arrays.asList(req.getDistributeStatus().split(",")).contains(autSaRelation.getAutStatus())) {
                log.info("查询页面：{} 信息时，当前记录状态：{} 和入参状态：{} 不匹配，不展示该条数据信息", req.getPageType(), autSaRelation.getAutStatus(), req.getDistributeStatus());
                continue;
            }

            if (StringUtils.isNotBlank(req.getShowStatus()) && !Arrays.asList(Convert.toStrArray(req.getShowStatus())).contains(autSaRelation.getAutStatus())) {
                // 页面需要校验对应节点
                log.info("页面:{} 展示的节点：{},当前节点:{}", req.getPageType(), req.getShowStatus(), autSaRelation.getAutStatus());
                continue;
            }
            //  返回时间 + 纯数量 + 驳回ids +节点 + 组长名称 + 组员信息 + 报告     审查初查+审查复查+评审初查+审查复查+评审报告审查页+事实准确性待审查+待修改评审列表页+验证评审员页面
            // 查询医院分配详情记录
            List<HosReviewPlanVO> hosPlanClauseList = hospitalBaseInfoService.selectHosPlanClauseDetail(autSaRelation.getHospitalApplyNo());
            log.info("根据医疗机构：{}查询计划分配详细信息列表：{}", autSaRelation.getHospitalApplyNo(), hosPlanClauseList);
            if (CollectionUtils.isEmpty(hosPlanClauseList)) {
                log.info("根据医疗机构：{} 查询计划分配详细信息列表为空，不展示该条数据信息", applyNo);
                continue;
            }
            HosReviewPlanVO hosReviewPlanVO = hosPlanClauseList.get(0);
            // 获取医疗机构计划分配成员信息
            List<HosPlanUserInfoVO> memberList = commonProcessService.getHosPlanMemberList(hosReviewPlanVO, req.getRoleKey());

            //评审学员角色时，只返回条款id列表
            if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(req.getRoleKey(), AutSaAudRoleEnum.TRAINEES_ASSESSOR)) {
                req.setAutSaRelation(autSaRelation);
                AutSaAudList autSaAudList = commonProcessService.queryTraineesAssessorAutSaAudList(req,memberList);
                //获取分组信息，当前业务逻辑是一组对应一个评审员，如果出现多组对应一个评审员暂不支持分组查看
                List<HospitalReviewerVo> domainReviewerList = hospitalReviewerMapper.selectHospitalReviewerAndCstDomainByAutCode(req.getAutSaRelation().getAutCode());
                autSaAudList.setDomainReviewerList(domainReviewerList);
                autSaAudList.setHospitalName(packHospitalName(autSaRelation));
                resultList.add(autSaAudList);
                return resultList;
            } else {
                HosPlanUserInfoVO hosPlanUserInfoVO = commonProcessService.getHosPlanUserInfoVOFromList(req.getAccountId(), memberList);
                boolean isLeader = commonProcessService.checkIsLeader(hosPlanUserInfoVO);
                if (AutSaAudPageTypeEnum.checkIsAutSaAudPageTypeEnum(req.getPageType(), AutSaAudPageTypeEnum.FR_V_CLAUSE_PAGE, AutSaAudPageTypeEnum.SR_V_CLAUSE_PAGE, AutSaAudPageTypeEnum.FR_REPORT_R_CLAUSE_PAGE) && !isLeader) {
                    log.info("查询页面：{} 信息时，用户：{}不是组长，不展示该条数据信息", req.getPageType(), req.getAccountId());
                    continue;
                }
            }
            // 获取对应服务bean
            BaseProcessService baseProcessService = this.getBeanByStatus(autSaRelation.getAutStatus());
            // 获取该节点对应列表数据
            req.setMemberList(memberList);
            req.setAutSaRelation(autSaRelation);
            // 查询对应节点数据
            //验证评审报告文件封装涉及，R01,E02,V01对应映射方法
            List<AutSaAudList> queryLists = baseProcessService.queryList(req);
            AutSaAudList autSaAudList = new AutSaAudList();
            if (CollectionUtils.isEmpty(queryLists)) {
                commonProcessService.getGroupProgressList(autSaAudList, req);
            } else {
                autSaAudList = queryLists.get(0);
            }
            //通用数据
            autSaAudList.setAutCode(autSaRelation.getAutCode());
            autSaAudList.setAutCsId(autSaRelation.getAutCsId());
            autSaAudList.setStatus(autSaRelation.getAutStatus());
            //  医院名称
            autSaAudList.setHospitalName(hosReviewPlanVO.getHospitalName());
            // 设置计划周期相关数据
            this.setAudCycleInfo(autSaAudList, hosReviewPlanVO.getHospitalReviewCycleList());
            autSaAudList.setApplyNo(applyNo);
            resultList.add(autSaAudList);
        }
            //排序：按完成时间倒序排序
        return commonProcessService.listSorted(resultList);
    }

    /**
     * 设置计划周期相关数据
     *
     * @param autSaAudList
     * @param hospitalReviewCycleList
     */
    private void setAudCycleInfo(AutSaAudList autSaAudList, List<HospitalReviewCycle> hospitalReviewCycleList) {
        // 医院审查阶段周期
        hospitalReviewCycleList.forEach(a -> {
            if (AutSaAudCycleEnum.INITIAL_REVIEW_CYCLE.getCycleStageValue().equals(a.getStageValue())) {
                // 初审周期
                autSaAudList.setAudCycle(a.getCycle());
            } else if (AutSaAudCycleEnum.REVIEW_CYCLE.getCycleStageValue().equals(a.getStageValue())) {
                // 评审周期
                autSaAudList.setAudSecondCycle(a.getCycle());
            }
        });
    }

    @Override
    public AutSaAudDetailVO queryDetail(AutSaAudQueryDTO req) {
        // 获取用户角色权限字符串
        String roleKey = sysUserService.getSysUserInfo(req.getAccountId()).getRoleKey();
        if (!AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(roleKey, AutSaAudRoleEnum.HOSPITAL, AutSaAudRoleEnum.ASSESSOR, AutSaAudRoleEnum.INSPECTOR, AutSaAudRoleEnum.SENIOR_ASSESSOR)) {
            // 入参角色错误
            log.info("入参角色：{}错误", roleKey);
            throw new ServiceException(ServiceExceptionEnum.AUT_SA_RELATION_ERROR_1000014);
        }
        // 查询当前自评编码对应的自评关联关系
        AutSaRelation autSaRelation = autSaRelationService.selectAutSaRelationByAutCode(req.getAutCode(), null);
        req.setAutSaRelation(autSaRelation);
        if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(roleKey, AutSaAudRoleEnum.HOSPITAL)) {
            // 校验医院端用户
            this.checkUserHospital(req.getAccountId(), autSaRelation.getHospitalApplyNo());
            if (autSaRelation.getStatus().intValue() == 0) {
                // 该自评编码过期了 ，并且入参角色是医院   ，查的是医院端的历史审核信息
                List<AutSaAud> autSaAuds = autSaAudMapper.selectLatestSaAudListByAutCode(req.getAutCode());
                req.setHospitalName(SecurityUtils.getNickName());
                req.setRoleKey(roleKey);
                // 获取详情
                return commonProcessService.tissueDetailResult(autSaAuds, req);
            }
            //当前节点在010101-待自评且’医院已拒绝当前评审安排与流程‘时，不能看详情
            if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autSaRelation.getAutStatus(), AutSaAudStatusEnum.WAIT_SA_CLAUSE)) {
                HospitalPlannedDistribution hospitalPlannedDistribution = hospitalPlannedDistributionMapper.selectHospitalPlannedDistributionByApplyNo(autSaRelation.getHospitalApplyNo());
                if (hospitalPlannedDistribution == null || ObjectUtil.equal(Constants.INT_TWO, hospitalPlannedDistribution.getCycleStatus())) {
                    throw new ServiceException("医院已拒绝当前评审安排与流程,暂不支持查看");
                }
            }
        }
        // 校验医院计划周期时间
        commonProcessService.checkQueryCycle(autSaRelation.getHospitalApplyNo(), autSaRelation.getAutCode(), autSaRelation.getAutStatus());
        // 查询医院分配详情记录
        List<HosReviewPlanVO> hosPlanClauseList = hospitalBaseInfoService.selectHosPlanClauseDetail(autSaRelation.getHospitalApplyNo());
        if (CollectionUtils.isEmpty(hosPlanClauseList)) {
            log.info("医院账户:{} 没有完成医疗机构分配计划流程", autSaRelation.getAccountId());
            throw new ServiceException(ServiceExceptionEnum.PLAN_ERROR_1000001);
        }
        if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(roleKey, AutSaAudRoleEnum.ASSESSOR, AutSaAudRoleEnum.INSPECTOR, AutSaAudRoleEnum.SENIOR_ASSESSOR)) {
            // 获取并校验该用户的分配信息
            commonProcessService.getHosPlanUserInfoVOFromList(req.getAccountId(), hosPlanClauseList.get(0), roleKey);
        }

        if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(roleKey, AutSaAudRoleEnum.HOSPITAL) &&
                AutSaAudPageTypeEnum.checkIsAutSaAudPageTypeEnum(req.getPageType(), AutSaAudPageTypeEnum.FAR_CLAUSE_PAGE) &&
                !AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autSaRelation.getAutStatus(), AutSaAudStatusEnum.WAIT_FAR_CLAUSE,
                        AutSaAudStatusEnum.FAR_CLAUSE_PROCESS, AutSaAudStatusEnum.FAR_SUMMARY)) {
            log.error("医院端：{}查询事实准确性审查数据时，节点：{} 不为事实准确性审查相关节点", req.getAccountId(), autSaRelation.getAutStatus());
            throw new ServiceException(ServiceExceptionEnum.AUT_SA_RELATION_ERROR_1000018);
        }

        // 获取对应服务bean
        BaseProcessService baseProcessService = this.getBeanByStatus(autSaRelation.getAutStatus());
        req.setHospitalName(hosPlanClauseList.get(0).getHospitalName());
        req.setRoleKey(roleKey);
        // 查询对应节点数据
        AutSaAudDetailVO autSaAudDetailVO = baseProcessService.queryDetail(req);
        if (ObjectUtil.isNull(autSaAudDetailVO)) {
            autSaAudDetailVO = commonProcessService.tissueDetailResult(req);
        }

        if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autSaRelation.getAutStatus(), AutSaAudStatusEnum.SR_REPORT_M_SUMMARY)) {
            List<AutSaAudBusinessData> autSaAudBusinessData = autSaAudBusinessDataMapper.selectAutSaAudBusinessData(autSaRelation.getAutCode(), AutSaAudBusinessCodeEnum.AUT_SA_AUD_REPORT.getCode());
            if (CollectionUtils.isEmpty(autSaAudBusinessData) || StringUtils.isEmpty(autSaAudBusinessData.get(0).getData())) {
                throw new ServiceException("获取业务数据为空，入参为：" + autSaRelation.getAutCode() + "BusinessCode:" + AutSaAudBusinessCodeEnum.AUT_SA_AUD_REPORT.getCode());
            }
            AutSaAudReport autSaAudReport = JSON.parseObject(autSaAudBusinessData.get(0).getData(), AutSaAudReport.class);
            autSaAudDetailVO.setAutResult(autSaAudReport.getAutSaAudResult().toString());
        }
        //过滤不适用款
        if (MapUtil.isNotEmpty(autSaAudDetailVO.getAutSaAudListMap())) {
            autSaAudDetailVO.getAutSaAudListMap().forEach((submitType, autSaAudList) ->
                    autSaAudList.removeAll(autSaAudList.stream().filter(o -> AutSaAudResultEnum.checkIsAutSaAudResultEnum(o.getAutResult(), AutSaAudResultEnum.ADMIN_NOT_APPLICABLE)).
                            collect(Collectors.toList()))
            );
        }
        return autSaAudDetailVO;
    }

    /**
     * 校验医院端用户
     *
     * @param accountId 账户
     * @param applyNo   医疗机构编码
     */
    private void checkUserHospital(String accountId, String applyNo) {
        // 查询入参医院账户id对应的医院信息
        SysUserHospital sysUserHospital = sysUserHospitalService.selectSysUserHospitalByUserId(Long.valueOf(accountId));
        if (null == sysUserHospital) {
            // 自评编码对应的医院机构id不是入参的医院机构id
            log.error("账户：{} 未关联医疗机构", accountId);
            throw new ServiceException(ServiceExceptionEnum.PLAN_ERROR_1000000);
        }
        if (!applyNo.equals(sysUserHospital.getHospitalApplyNo())) {
            // 自评编码对应的医院机构id不是入参的医院机构id
            log.error("入参账户id：{} 对应的医疗机构id：{} 和自评编码获取的医疗机构：{},不匹配", accountId, sysUserHospital.getHospitalApplyNo(), applyNo);
            throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000002);
        }
    }

    /**
     * 调用配置的对应处理方法
     *
     * @param methodName 方法名
     * @param req        流程参数
     */
    private void invokeMethod(String methodName, AutSaAudSaveDTO req) {
        try {
            if (StringUtils.isBlank(methodName)) {
                return;
            }
            String[] methods = Convert.toStrArray(methodName);
            for (String methodStr : methods) {
                Method method = commonProcessService.getMethodByClassAndName(commonProcessService.getClass(), methodStr);
                if (method == null) {
                    log.error("节点：{} 配置的方法：{} 为空，请确认项目处理类内有对应的方法", req.getAutSaRelation().getAutStatus(), methodStr);
                    throw new ServiceException(ServiceExceptionEnum.AUT_SA_AUD_STATUS_CONFIG_ERROR_1000000);
                }
                method.invoke(commonProcessService, req);
            }
        } catch (Exception e) {
            log.error("调用配置的对应处理方法：{} 异常e:{}", methodName, e.getCause().getMessage());
            throw new ServiceException(e.getCause().getMessage());
        }
    }

    /**
     * 根据状态获取对应服务bean
     *
     * @param autStatus 节点状态
     * @return 对应服务bean
     */
    private BaseProcessService getBeanByStatus(String autStatus) {
        AutSaAudStatusConfig autSaAudStatusConfig = commonProcessMapper.selectAutSaAudStatusConfig(autStatus);
        if (Objects.isNull(autSaAudStatusConfig) || StringUtils.isBlank(autSaAudStatusConfig.getServiceName())) {
            log.error("当前节点:{} 未配置对应服务信息", autStatus);
            throw new ServiceException(ServiceExceptionEnum.AUT_SA_AUD_STATUS_CONFIG_ERROR_1000000);
        }
        // 流程处理服务不为空，调用流程处理服务
        BaseProcessService baseProcessService = (BaseProcessService) SpringContextUtil.getBean(autSaAudStatusConfig.getServiceName());
        if (baseProcessService == null) {
            log.error("节点：{} 配置的流程处理服务：{} 为空，请确认项目内有配置对应流程处理服务", autStatus, autSaAudStatusConfig.getServiceName());
            throw new ServiceException(ServiceExceptionEnum.AUT_SA_AUD_STATUS_CONFIG_ERROR_1000000);
        }
        return baseProcessService;
    }

}
