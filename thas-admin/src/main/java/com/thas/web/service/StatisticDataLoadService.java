package com.thas.web.service;

import com.thas.common.exception.ServiceException;
import com.thas.common.utils.StringUtils;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2022/10/28
 */
public interface StatisticDataLoadService {

    /**
     * 加载数据
     *
     * @param param 入参
     * @return 出参
     */
    Map<String, Object> load(Map<String, Object> param);

    /**
     * 校验入参
     *
     * @param param 入参
     * @param keys keys
     */
    default void checkParam(Map<String, Object> param, String ...keys) {
        if (Objects.isNull(keys) || keys.length == 0) {
            return;
        }
        if (Objects.isNull(param)) {
            throw new ServiceException("入参不能为空", 400);
        }
        StringBuilder defKey = new StringBuilder();
        for (String key : keys) {
            if (StringUtils.isEmpty(key)) {
                continue;
            }
            if (!param.containsKey(key)) {
                defKey.append(key);
                defKey.append(",");
            }
        }
        if (defKey.length() > 0) {
            defKey.deleteCharAt(defKey.length() - 1);
            throw new ServiceException(String.format("入参[%s]不能为空", defKey), 400);
        }
    }
}
