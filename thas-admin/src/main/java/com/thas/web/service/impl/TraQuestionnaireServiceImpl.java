package com.thas.web.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.thas.common.constant.Constants;
import com.thas.common.core.domain.entity.SysUser;
import com.thas.common.core.domain.model.LoginUser;
import com.thas.common.enums.AutSaAudRoleEnum;
import com.thas.common.enums.TraQuestionnaireFeedBackTypeEnum;
import com.thas.common.exception.ServiceException;
import com.thas.common.utils.DateUtils;
import com.thas.common.utils.PageUtils;
import com.thas.common.utils.SecurityUtils;
import com.thas.common.utils.StringUtils;
import com.thas.system.domain.vo.UserVo;
import com.thas.system.mapper.SysUserHospitalMapper;
import com.thas.system.mapper.SysUserMapper;
import com.thas.system.service.ISysRoleService;
import com.thas.web.domain.*;
import com.thas.web.domain.dto.TraQuestionnaireDTO;
import com.thas.web.domain.vo.AnswerCountVO;
import com.thas.web.domain.vo.TraQuestionnaireVO;
import com.thas.web.mapper.*;
import com.thas.web.service.ITraQuestionnaireFeedBackService;
import com.thas.web.service.ITraQuestionnaireService;
import com.thas.web.service.ITrainingEvaluateResultService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 调查问卷Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-02-14
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TraQuestionnaireServiceImpl implements ITraQuestionnaireService {

    @Autowired
    private TraQuestionnaireMapper traQuestionnaireMapper;

    @Autowired
    private TraQuestionnaireDetailsMapper questionnaireDetailsMapper;

    @Autowired
    private TraQuestionnaireAnswerMapper questionnaireAnswerMapper;

    @Autowired
    private TraQuestionnaireAnswerDetailsMapper questionnaireAnswerDetailsMapper;

    @Autowired
    private ISysRoleService sysRoleService;

    @Autowired
    private SysUserHospitalMapper sysUserHospitalMapper;

    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private HospitalReviewerMapper hospitalReviewerMapper;
    @Autowired
    private ITraQuestionnaireFeedBackService traQuestionnaireFeedBackService;
    @Autowired
    private ITrainingEvaluateResultService iTrainingEvaluateResultService;
    @Autowired
    private TrainingEvaluateResultMapper trainingEvaluateResultMapper;
    @Autowired
    private TraQuestionnaireFeedBackRecordMapper traQuestionnaireFeedBackRecordMapper;

    /**
     * 查询调查问卷
     *
     * @param id 调查问卷主键
     * @return 调查问卷
     */
    @Override
    public TraQuestionnaireVO selectTraQuestionnaireById(Long id) {
        TraQuestionnaireVO data = traQuestionnaireMapper.selectTraQuestionnaireById(id);

        // 将问卷题目查询并添加到返回结果
        if (Objects.nonNull(data)) {
            data.setData(questionnaireDetailsMapper.selectByQuestionnaireId(id));
        }
        // 将问卷答题统计添加到返回结果
        if (CollectionUtils.isNotEmpty(data.getData())) {
            data.getData().forEach(traQuestionnaireDetails -> {
                List<AnswerCountVO> mapList = questionnaireAnswerDetailsMapper.getAnswerCountMap(traQuestionnaireDetails.getId());
                HashMap<String, Integer> answerCountMap = new HashMap<>();
                final Integer[] answerCount = {0};
                mapList.forEach(map -> {
                    if (Constants.INT_TWO == traQuestionnaireDetails.getType()) {
                        String[] options = map.getOptions().replace("[", "").replace("]", "")
                                .replace("\"", "").split(Constants.COMMA);
                        for (String option : options) {
                            answerCountMap.put(option, answerCountMap.getOrDefault(option, 0) + map.getValue());
                            answerCount[0] += map.getValue();
                        }
                    } else {
                        answerCountMap.put(map.getOptions(), map.getValue());
                        answerCount[0] += map.getValue();
                    }
                });
                traQuestionnaireDetails.setAnswerCountMap(answerCountMap);
                traQuestionnaireDetails.setAnswerCount(answerCount[0]);
            });
        }
        return data;
    }

    /**
     * 查询调查问卷列表
     *
     * @param traQuestionnaireDTO 调查问卷
     * @return 调查问卷
     */
    @Override
    public List<TraQuestionnaireVO> selectTraQuestionnaireList(TraQuestionnaireDTO traQuestionnaireDTO) {
        // 根据登录角色判断，如果不是管理员，那么就只能展示发布的问卷
        boolean isAdmin = sysRoleService.isAdmin();
        if (!isAdmin) {
            traQuestionnaireDTO.setStatus(Constants.HospitalConstants.NUM_1);
            //获取当前登录用户角色对应的反馈问卷，查询条件，问卷类别（类型）
            List<Integer> typeList = packTraQuestionnaireFeedBackType();
            traQuestionnaireDTO.setTypeList(typeList);
        }
        PageUtils.startPage();
        List<TraQuestionnaireVO> traQuestionnaireVOS = traQuestionnaireMapper.selectTraQuestionnaireList(traQuestionnaireDTO);
        //封装是否有反馈表状态，当前用户是否有此类型的反馈表
        traQuestionnaireVOS.forEach(o -> {
            if (o.getType() != null) {
                List<TraQuestionnaireFeedBack> questionnaireFeedBacks = traQuestionnaireFeedBackService.details(Long.valueOf(o.getType()), o.getId());
                if (CollectionUtils.isNotEmpty(questionnaireFeedBacks)) {
                    o.setFeedBackFlag(Constants.STR_NUM_1);
                    //统计反馈问卷类型的答卷数量
                    o.setAnswerNum(questionnaireFeedBacks.size());
                }
            }
        });
        return traQuestionnaireVOS;
    }

    private List<Integer> packTraQuestionnaireFeedBackType() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = loginUser.getUser();
        List<Integer> typeList = Lists.newArrayList();
        typeList.add(TraQuestionnaireFeedBackTypeEnum.QUESTIONNAIRE_TYPE_1.getIntCode());
        typeList.add(TraQuestionnaireFeedBackTypeEnum.QUESTIONNAIRE_TYPE_2.getIntCode());
        typeList.add(TraQuestionnaireFeedBackTypeEnum.QUESTIONNAIRE_TYPE_3.getIntCode());
        if (AutSaAudRoleEnum.HOSPITAL.getRoleKey().equals(user.getRoleKey())) {
            //医院
            typeList.add(TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_6.getIntCode());
        } else if (AutSaAudRoleEnum.TRAINEES_ASSESSOR.getRoleKey().equals(user.getRoleKey())) {
            //评审学员
            typeList.add(TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_5.getIntCode());
            typeList.add(TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_8.getIntCode());
        } else if (AutSaAudRoleEnum.ASSESSOR.getRoleKey().equals(user.getRoleKey())) {

            //如果创建账号时就为评审员，不用填学员的反馈表，在列表展示控制，判断评估结果表是否有数据，有数据证明是学员角色转为评审员,有数据需要展示
            TrainingEvaluateResult trainingEvaluateResult = new TrainingEvaluateResult();
            trainingEvaluateResult.setTraineesAssessorId(user.getUserId());
            trainingEvaluateResult.setValidFlag((long) Constants.INT_ONE);
            List<TrainingEvaluateResult> trainingEvaluateResultList = iTrainingEvaluateResultService.selectTrainingEvaluateResultList(trainingEvaluateResult);
            if (CollectionUtils.isNotEmpty(trainingEvaluateResultList)) {
                //防止评审学员转为评审员时未填写反馈表
                typeList.add(TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_5.getIntCode());
                typeList.add(TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_8.getIntCode());
            }
            //审批员
            typeList.add(TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_7.getIntCode());
            typeList.add(TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_9.getIntCode());
        }
        return typeList;
    }

    /**
     * 新增调查问卷
     *
     * @param traQuestionnaire 调查问卷
     * @return 结果
     */
    @Override
    public void insertOrUpdateTraQuestionnaire(TraQuestionnaire traQuestionnaire) {
        //问卷名称不能相同
        TraQuestionnaireDTO traQuestionnaireDTO = new TraQuestionnaireDTO();
        traQuestionnaireDTO.setTitle(traQuestionnaire.getTitle());
        List<TraQuestionnaireVO> traQuestionnaireVOS = traQuestionnaireMapper.selectTraQuestionnaireList(traQuestionnaireDTO);

        traQuestionnaire.setUpdateBy(SecurityUtils.getNickName());
        // 保存调查问卷并拿到主键id
        if (Objects.isNull(traQuestionnaire.getId())) {
            //新增不能有问卷名称相同的
            if (CollectionUtils.isNotEmpty(traQuestionnaireVOS)) {
                throw new ServiceException("问卷名称:" + traQuestionnaire.getTitle() + "已存在，请使用其他问卷名称！");
            }
            traQuestionnaire.setCreateBy(SecurityUtils.getNickName());
            traQuestionnaire.setCreateId(SecurityUtils.getUsername());
            traQuestionnaireMapper.insertTraQuestionnaire(traQuestionnaire);
        } else {
            //修改时，如果有相同的问卷名，查询的问卷名对应id是否和入参的问卷id一致，一致证明是自身的这条修改数据，可修改
            if (CollectionUtils.isNotEmpty(traQuestionnaireVOS) && !traQuestionnaireVOS.get(0).getId().equals(traQuestionnaire.getId())) {
                throw new ServiceException("问卷名称:" + traQuestionnaire.getTitle() + "已存在，请使用其他问卷名称！");
            }

            //校验如果有下发问卷，不能查看，从而不能编辑问卷
            TraQuestionnaireFeedBackRecord traQuestionnaireFeedBackRecord = new TraQuestionnaireFeedBackRecord();
            traQuestionnaireFeedBackRecord.setQuestionnaireId(traQuestionnaire.getId());
            traQuestionnaireFeedBackRecord.setStatus(Long.valueOf(Constants.STR_NUM_1));
            List<TraQuestionnaireFeedBackRecord> traQuestionnaireFeedBackRecordList =
                    traQuestionnaireFeedBackRecordMapper.selectTraQuestionnaireFeedBackRecordList(traQuestionnaireFeedBackRecord);
            if (CollectionUtils.isNotEmpty(traQuestionnaireFeedBackRecordList)) {
                throw new ServiceException("该问卷已下发，不能编辑");
            }

            traQuestionnaireMapper.updateTraQuestionnaire(traQuestionnaire);
        }
        long id = traQuestionnaire.getId();
        // 保存调查问卷题目
//        if(CollectionUtils.isNotEmpty(traQuestionnaire.getData())){
//            //调查问卷Id+问卷题目为唯一条件；
//            List<TraQuestionnaireDetails> traQuestionnaireDetailsList = questionnaireDetailsMapper.selectByQuestionnaireId(id);
//            if(CollectionUtils.isEmpty(traQuestionnaireDetailsList)){
//                //新增
//                traQuestionnaire.getData().forEach(traQuestionnaireDetails -> {
//                    traQuestionnaireDetails.setQuestionnaireId(id);
//                    questionnaireDetailsMapper.insertTraQuestionnaireDetails(traQuestionnaireDetails);
//                    log.info("新增的问卷详情模板，数据为：{}", JSON.toJSONString(traQuestionnaireDetails));
//                });
//            } else {
//                //问卷题目匹配上的更新，不匹配新增
//                //<题目，对象>
//                Map<String, TraQuestionnaireDetails> traQuestionnaireDetailsMap =
//                        traQuestionnaireDetailsList.stream().collect(Collectors.toMap(TraQuestionnaireDetails::getQuestion, o -> o));
//                traQuestionnaire.getData().forEach(traQuestionnaireDetails -> {
//                    if(traQuestionnaireDetailsMap.containsKey(traQuestionnaireDetails.getQuestion())){
//                        //更新
//                        Long qryId = traQuestionnaireDetailsMap.get(traQuestionnaireDetails.getQuestion()).getId();
//                        traQuestionnaireDetails.setId(qryId);
//                        questionnaireDetailsMapper.updateTraQuestionnaireDetails(traQuestionnaireDetails);
//                        log.info("更新的问卷详情模板，数据为：{}", JSON.toJSONString(traQuestionnaireDetails));
//                    } else {
//                        //新增
//                        traQuestionnaireDetails.setQuestionnaireId(id);
//                        questionnaireDetailsMapper.insertTraQuestionnaireDetails(traQuestionnaireDetails);
//                        log.info("新增的问卷详情模板，数据为：{}", JSON.toJSONString(traQuestionnaireDetails));
//                    }
//                });
//
//            }
//        }

        if (CollectionUtils.isNotEmpty(traQuestionnaire.getData())) {
            questionnaireDetailsMapper.deleteByQuestionnaireId(id);
            traQuestionnaire.getData().forEach(traQuestionnaireDetails -> {
                traQuestionnaireDetails.setQuestionnaireId(id);
                questionnaireDetailsMapper.insertTraQuestionnaireDetails(traQuestionnaireDetails);
            });
        }
    }

    /**
     * 批量删除调查问卷
     *
     * @param ids 需要删除的调查问卷主键
     * @return 结果
     */
    @Override
    public int deleteTraQuestionnaireByIds(Long[] ids) {
        //1.删除操作，下发后有人填写了反馈表，不展示【删除】，未填写反馈表的要同步删除
        List<TraQuestionnaireFeedBackRecord> traQuestionnaireFeedBackRecordList = new ArrayList<>();
        Arrays.asList(ids).forEach(id -> {
            traQuestionnaireFeedBackRecordList.addAll(qryTraQuestionnaireFeedBackRecord(id));
        });
        if (CollectionUtils.isNotEmpty(traQuestionnaireFeedBackRecordList)) {
            Long[] recordIds = traQuestionnaireFeedBackRecordList.stream().map(TraQuestionnaireFeedBackRecord::getRecordId).toArray(Long[]::new);
            int i = traQuestionnaireFeedBackRecordMapper.deleteTraQuestionnaireFeedBackRecordByRecordIds(recordIds);
            log.info("需要删除的反馈表记录Ids[{}],数量为{}；已删除数量：{}", recordIds, recordIds.length, i);
        }

        return traQuestionnaireMapper.deleteTraQuestionnaireByIds(ids);
    }

    @Override
    public int updateStatus(Integer status, Long id) {
        //入参为发布状态且有相同问卷类型已发布时校验
        if (status == Constants.INT_ONE) {
            TraQuestionnaireVO traQuestionnaireVO = traQuestionnaireMapper.selectTraQuestionnaireById(id);
            if (traQuestionnaireVO != null) {
                Integer type = traQuestionnaireVO.getType();
                TraQuestionnaireDTO traQuestionnaireDTO = new TraQuestionnaireDTO();
                traQuestionnaireDTO.setType(type);
                traQuestionnaireDTO.setStatus(Constants.INT_ONE);
                List<TraQuestionnaireVO> traQuestionnaireVOS = traQuestionnaireMapper.selectTraQuestionnaireList(traQuestionnaireDTO);
                if (CollectionUtils.isNotEmpty(traQuestionnaireVOS)) {
                    String titles = traQuestionnaireVOS.stream().map(TraQuestionnaireVO::getTitle).collect(Collectors.joining(","));
                    throw new ServiceException(String.format("请停止同类型的%s后再发布", titles));
                }
            }
        }
        //status 0:停止, 1:发布 ; id 问卷id
        List<TraQuestionnaireFeedBackRecord> traQuestionnaireFeedBackRecordList = qryTraQuestionnaireFeedBackRecord(id);

        if (CollectionUtils.isNotEmpty(traQuestionnaireFeedBackRecordList)) {
            //2-1.停止操作，失效对应未填写反馈记录表;
            //2-2.发布（恢复）操作，对应未填写反馈记录表改为生效
            int statusFlag = ObjectUtil.equal(status, Constants.INT_ZERO) ? Constants.INT_ZERO : (ObjectUtil.equal(status, Constants.INT_ONE) ? Constants.INT_ONE : 3);
            if (statusFlag == 3) {
                throw new ServiceException("入参status错误！");
            }
            //筛选未填写，封装失效或恢复数据
            traQuestionnaireFeedBackRecordList.forEach(o ->
                    {
                        o.setStatus((long) statusFlag);
                        o.setCreateTime(null);
                        o.setUpdateTime(DateUtils.getNowDate());
                    }
            );
            int i = traQuestionnaireFeedBackRecordMapper.updateTraQuestionnaireFeedBackRecordList(traQuestionnaireFeedBackRecordList);
            log.info("需要更新的是否失效状态为：{}，反馈表记录数据为[{}],数量为{}；更新成功数量为：{}",
                    statusFlag, traQuestionnaireFeedBackRecordList, traQuestionnaireFeedBackRecordList.size(), i);
        }

        return traQuestionnaireMapper.updateStatus(status, id);
    }

    /**
     * 查询问卷ID且未填写的反馈问卷记录表数据
     *
     * @param id 问卷Id
     */
    private List<TraQuestionnaireFeedBackRecord> qryTraQuestionnaireFeedBackRecord(Long id) {
        TraQuestionnaireFeedBackRecord qryTraQuestionnaireFeedBackRecord = new TraQuestionnaireFeedBackRecord();
        qryTraQuestionnaireFeedBackRecord.setQuestionnaireId(id);
        qryTraQuestionnaireFeedBackRecord.setFillStatus(Constants.STR_NUM_0);
        return traQuestionnaireFeedBackRecordMapper.selectTraQuestionnaireFeedBackRecordList(qryTraQuestionnaireFeedBackRecord);

    }

    @Override
    public List<SendInfoRes> sendInfo(Long id) {
        //是否为管理员操作
        if (!sysRoleService.isAdmin()) {
            throw new ServiceException("当前登录用户非管理员无权限操作");
        }

        List<SendInfoRes> sendInfoResList = new ArrayList<>();

        //1-1.展示已有学员理论培训评估结果的评审员信息
        TrainingEvaluateResult qryTrainingEvaluateResult = new TrainingEvaluateResult();
        qryTrainingEvaluateResult.setValidFlag((long) Constants.INT_ONE);
        qryTrainingEvaluateResult.setReviewResultType((long) Constants.INT_ONE);
        List<TrainingEvaluateResult> trainingEvaluateResultList =
                trainingEvaluateResultMapper.selectTrainingEvaluateResultList(qryTrainingEvaluateResult);

        if (CollectionUtils.isNotEmpty(trainingEvaluateResultList)) {
            //查询反馈表对应下发筛选条件数据
            //1-2.过滤角色为评审员或验证评审员已下发过反馈表的人员
            //1-3.过滤有反馈表但未填写的评审学员
            //1-4.会有相同类型问卷，所以对应问卷Id查询唯一
            List<String> traineesAssessorIds = trainingEvaluateResultList.stream().map(o -> String.valueOf(o.getTraineesAssessorId())).collect(Collectors.toList());
            List<String> sendConditionTraineesAssessorIds = traQuestionnaireFeedBackRecordMapper.selectTraQuestionnaireFeedBackRecordIdsBySendCondition(traineesAssessorIds, id);
            traineesAssessorIds.removeAll(sendConditionTraineesAssessorIds);
            //排除过滤的人员后，如果为空不需要展示
            if (CollectionUtils.isNotEmpty(traineesAssessorIds)) {
                //获取对应需要展示的信息
                trainingEvaluateResultList = trainingEvaluateResultList.stream().filter(o -> traineesAssessorIds.contains(o.getTraineesAssessorId().toString())).collect(Collectors.toList());

                //2-1.查询评估结果时，获取培训教员展示
                //<学员Id,教员Ids>
                Map<String, List<String>> map = new HashMap<>();
                trainingEvaluateResultList.forEach(o -> {
                            String trainerId = o.getTrainerId();
                            List<String> trainerIds = new ArrayList<>();
                            if (StringUtils.isNotEmpty(trainerId)) {
                                trainerIds = Arrays.asList(trainerId.split(","));
                            }
                            map.put(o.getTraineesAssessorId().toString(), trainerIds);
                        }
                );
                List<UserVo> userVos = sysUserMapper.selectUserByIds(new ArrayList<>(map.keySet()));
                if (userVos.size() != map.size()) {
                    log.info("停用账号不下发的数量有，查询的用户数量【{}】，下发的用户数量【{}】，下发的用户Ids【{}】", userVos.size(), map.size(), map.keySet());
                }
                if (CollectionUtils.isNotEmpty(userVos)) {

                    Map<String, String> userMap = userVos.stream().collect(Collectors.toMap(o -> o.getUserId().toString(), UserVo::getNickName));
                    //<评审员Id,评估结果Id>
                    Map<String, Long> terMap = trainingEvaluateResultList.stream().collect(Collectors.toMap(o -> o.getTraineesAssessorId().toString(), TrainingEvaluateResult::getResultId));
                    map.forEach((traineesAssessorId, trainerIds) -> {
                        //账号失效无数据时不封装
                        if (StringUtils.isNotEmpty(userMap.get(traineesAssessorId))) {
                            SendInfoRes sendInfoRes = new SendInfoRes();
                            sendInfoRes.setResultId(terMap.get(traineesAssessorId));
                            sendInfoRes.setTraineesAssessorId(traineesAssessorId);
                            sendInfoRes.setTraineesAssessorName(userMap.get(traineesAssessorId));
                            sendInfoRes.setQuestionnaireId(id);

                            List<SendInfoRes.TrainerInfo> trainerInfoList = new ArrayList<>();
                            trainerIds.forEach(trainerId -> {
                                if (StringUtils.isNotEmpty(userMap.get(trainerId))) {
                                    SendInfoRes.TrainerInfo trainerInfo = new SendInfoRes.TrainerInfo();
                                    trainerInfo.setTrainerId(trainerId);
                                    trainerInfo.setTrainerName(userMap.get(trainerId));
                                    trainerInfoList.add(trainerInfo);
                                }
                            });
                            sendInfoRes.setTrainerInfoList(trainerInfoList);
                            sendInfoResList.add(sendInfoRes);
                        }
                    });
                }
            }
        }
        return sendInfoResList;

    }

    @Override
    public String sendSubmit(List<SendInfoRes> sendInfoResList) {
        //是否为管理员操作
        if (!sysRoleService.isAdmin()) {
            throw new ServiceException("当前登录用户非管理员无权限操作");
        }
        //校验：
        //教员是否已填写对应学员的教员反馈表
        //根据入参评估结果Id,教员Id，教员反馈表类型，是否填写状态为未填写， 查询反馈问卷统计表
        List<Long> resultIds = sendInfoResList.stream().map(SendInfoRes::getResultId).collect(Collectors.toList());
        List<String> needIdList = new ArrayList<>();
        sendInfoResList.forEach(o -> {
            List<String> trainerIds = o.getTrainerInfoList().stream().map(SendInfoRes.TrainerInfo::getTrainerId).collect(Collectors.toList());
            needIdList.addAll(trainerIds);
        });
        //未填写的教员名称
        List<String> unFillTrainerNameList = traQuestionnaireFeedBackRecordMapper.
                selectTraQuestionnaireFeedBackRecordListByTrainerCondition(needIdList, resultIds);
        //如未填写，提示“${教员1}、${教员2}存在未填写的教员反馈表，需填写完成后再下发”
        if (CollectionUtils.isNotEmpty(unFillTrainerNameList)) {
            return String.format("[%s]存在未填写的教员反馈表，需填写完成后再下发", String.join("、", unFillTrainerNameList));
        }
        //查询问卷Id
        TraQuestionnaireDTO qryTraQuestionnaireDTO = new TraQuestionnaireDTO();
        qryTraQuestionnaireDTO.setTypeList(Arrays.asList(TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_7.getIntCode()));
        List<TraQuestionnaireVO> traQuestionnaireVOS = traQuestionnaireMapper.selectTraQuestionnaireList(qryTraQuestionnaireDTO);
        //获取已发布的培训教员问卷
        List<Long> feedBackType7Ids = traQuestionnaireVOS.stream().filter(o -> ObjectUtil.equal(o.getStatus(), Constants.INT_ONE)).map(TraQuestionnaireVO::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(feedBackType7Ids)) {
            log.error("查询培训教员反馈问卷数据异常，查询的数据为{}，发布的问卷Id为空", traQuestionnaireVOS);
            throw new ServiceException("查询培训教员反馈问卷数据异常，请联系管理员！");
        }

        //提交成功，生成下发反馈记录表数据
        List<TraQuestionnaireFeedBackRecord> traQuestionnaireFeedBackRecordList = new ArrayList<>();

        List<TrainingEvaluateResult> trainingEvaluateResultList = new ArrayList<>();
        sendInfoResList.forEach(o -> {
            //学员,一个学员只有一份
            TraQuestionnaireFeedBackRecord traQuestionnaireFeedBackRecord = new TraQuestionnaireFeedBackRecord();
            traQuestionnaireFeedBackRecord.setResultId(o.getResultId());
            traQuestionnaireFeedBackRecord.setNeedId(o.getTraineesAssessorId());
            traQuestionnaireFeedBackRecord.setFeedBackType(Long.valueOf(TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_5.getCode()));
            traQuestionnaireFeedBackRecord.setQuestionnaireId(sendInfoResList.get(0).getQuestionnaireId());
            traQuestionnaireFeedBackRecordList.add(traQuestionnaireFeedBackRecord);
            //教员，一个教员可下发多份（如果有多个教员反馈表类型问卷下发多份）
            o.getTrainerInfoList().forEach(trainerInfo -> {
                feedBackType7Ids.forEach(id -> {
                    TraQuestionnaireFeedBackRecord trainerTQFBR = new TraQuestionnaireFeedBackRecord();
                    trainerTQFBR.setResultId(o.getResultId());
                    trainerTQFBR.setNeedId(trainerInfo.getTrainerId());
                    trainerTQFBR.setFeedBackType(Long.valueOf(TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_7.getCode()));
                    trainerTQFBR.setQuestionnaireId(id);
                    traQuestionnaireFeedBackRecordList.add(trainerTQFBR);
                });
            });
            //同步更新评估表
            TrainingEvaluateResult trainingEvaluateResult = new TrainingEvaluateResult();
            trainingEvaluateResult.setResultId(o.getResultId());
            String trainerIds = o.getTrainerInfoList().stream().map(SendInfoRes.TrainerInfo::getTrainerId).collect(Collectors.joining(","));
            trainingEvaluateResult.setTrainerId(trainerIds);
            trainingEvaluateResultList.add(trainingEvaluateResult);

        });
        traQuestionnaireFeedBackRecordMapper.insertBatchTraQuestionnaireFeedBackRecord(traQuestionnaireFeedBackRecordList, Constants.INT_ZERO);
        //同步反馈表里的培训教员到评估表
        trainingEvaluateResultMapper.batchUpdateTrainingEvaluateResult(trainingEvaluateResultList);
        return null;
    }

    @Override
    public List<SendDetailRes> sendDetail(TraQuestionnaireFeedBackRecord req) {
        //是否为管理员操作
        if (!sysRoleService.isAdmin()) {
            throw new ServiceException("当前登录用户非管理员无权限操作");
        }
        if (req.getFeedBackType() == null) {
            throw new ServiceException("入参feedBackType反馈问卷类型不能为空");
        }
        if (req.getQuestionnaireId() == null) {
            throw new ServiceException("入参questionnaireId问卷Id不能为空");
        }
        List<SendDetailRes> qryList = new ArrayList<>();
        req.setStatus(1l);
        if (TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_6.getCode().equals(req.getFeedBackType().toString())) {
            qryList = traQuestionnaireFeedBackRecordMapper.selectTraQuestionnaireFeedBackRecordAndHospitalInfoList(req);
        } else {
            qryList = traQuestionnaireFeedBackRecordMapper.selectTraQuestionnaireFeedBackRecordAndUserInfoList(req);
        }
        return qryList;
    }

}
