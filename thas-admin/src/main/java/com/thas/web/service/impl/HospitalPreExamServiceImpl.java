package com.thas.web.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSONObject;
import com.thas.common.constant.Constants;
import com.thas.common.utils.DateUtils;
import com.thas.common.utils.SecurityUtils;
import com.thas.web.domain.HospitalBaseInfo;
import com.thas.web.domain.HospitalPreExam;
import com.thas.web.dto.HosPlanUserInfoVO;
import com.thas.web.dto.ReviewManageDTO;
import com.thas.web.dto.ReviewManageVO;
import com.thas.web.mapper.HospitalPreExamMapper;
import com.thas.web.service.IHospitalPreExamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 医疗机构认证信息对应初审员Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-25
 */
@Service
public class HospitalPreExamServiceImpl implements IHospitalPreExamService {

    @Autowired
    private HospitalPreExamMapper hospitalPreExamMapper;

    @Override
    public List<HosPlanUserInfoVO> selectHosPlanUserInfoByApplyNo(String applyNo) {
        List<HospitalPreExam> hospitalPreExamList = hospitalPreExamMapper.selectHospitalPreExamByApplyNo(applyNo);
        if (CollectionUtil.isEmpty(hospitalPreExamList)) {
            return new ArrayList<>();
        }
        List<HosPlanUserInfoVO> hosPlanUserInfoVOList = hospitalPreExamMapper.selectHosPlanUserInfoByAccountId(hospitalPreExamList);
        // 防止sql一致导致走缓存生成的对象一致，使用序列化生成新的对象
        List<HosPlanUserInfoVO> hosPlanUserInfoVOs = JSONObject.parseArray(JSONObject.toJSONString(hosPlanUserInfoVOList), HosPlanUserInfoVO.class);

        // 将是组长的评审员id放入一个集合
        List<String> leaderAccountIdList = hospitalPreExamList.stream()
                .filter(hospitalPreExam -> Constants.HospitalConstants.NUM_1.equals(hospitalPreExam.getLeaderIs()))
                .map(HospitalPreExam::getPreExamId).collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(leaderAccountIdList)) {
            hosPlanUserInfoVOs.forEach(hosPlanUserInfoVO -> {
                if (leaderAccountIdList.contains(hosPlanUserInfoVO.getAccountId())) {
                    hosPlanUserInfoVO.setLeaderIs(1);
                } else {
                    hosPlanUserInfoVO.setLeaderIs(2);
                }
            });
        }
        return hosPlanUserInfoVOs;
    }

    /**
     * 查询医疗机构认证信息对应初审员
     *
     * @param id 医疗机构认证信息对应初审员主键
     * @return 医疗机构认证信息对应初审员
     */
    @Override
    public HospitalPreExam selectHospitalPreExamById(Long id) {
        return hospitalPreExamMapper.selectHospitalPreExamById(id);
    }

    /**
     * 查询医疗机构认证信息对应初审员列表
     *
     * @param hospitalPreExam 医疗机构认证信息对应初审员
     * @return 医疗机构认证信息对应初审员
     */
    @Override
    public List<HospitalPreExam> selectHospitalPreExamList(HospitalPreExam hospitalPreExam) {
        return hospitalPreExamMapper.selectHospitalPreExamList(hospitalPreExam);
    }

    /**
     * 新增医疗机构认证信息对应初审员
     *
     * @param hospitalPreExam 医疗机构认证信息对应初审员
     * @return 结果
     */
    @Override
    public int insertHospitalPreExam(HospitalPreExam hospitalPreExam) {
        hospitalPreExam.setCreateTime(DateUtils.getNowDate());
        return hospitalPreExamMapper.insertHospitalPreExam(hospitalPreExam);
    }

    /**
     * 修改医疗机构认证信息对应初审员
     *
     * @param hospitalPreExam 医疗机构认证信息对应初审员
     * @return 结果
     */
    @Override
    public int updateHospitalPreExam(HospitalPreExam hospitalPreExam) {
        hospitalPreExam.setUpdateTime(DateUtils.getNowDate());
        return hospitalPreExamMapper.updateHospitalPreExam(hospitalPreExam);
    }

    /**
     * 批量删除医疗机构认证信息对应初审员
     *
     * @param ids 需要删除的医疗机构认证信息对应初审员主键
     * @return 结果
     */
    @Override
    public int deleteHospitalPreExamByIds(Long[] ids) {
        return hospitalPreExamMapper.deleteHospitalPreExamByIds(ids);
    }

    /**
     * 删除医疗机构认证信息对应初审员信息
     *
     * @param id 医疗机构认证信息对应初审员主键
     * @return 结果
     */
    @Override
    public int deleteHospitalPreExamById(Long id) {
        return hospitalPreExamMapper.deleteHospitalPreExamById(id);
    }

    @Override
    public HospitalPreExam selectHospitalPreExamByApplyNoAndAccountId(String applyNo, String accountId) {
        HospitalPreExam hospitalPreExam = new HospitalPreExam();
        hospitalPreExam.setApplyNo(applyNo);
        hospitalPreExam.setPreExamId(accountId);
        return hospitalPreExamMapper.selectHospitalPreExamByApplyNoAndAccountId(hospitalPreExam);
    }

    @Override
    public HospitalPreExam selectHosPreExamByLeader(String applyNo, Integer leaderIs) {
        return hospitalPreExamMapper.selectHosPreExamByLeader(applyNo, leaderIs);
    }

    @Override
    public void insertOrUpdateHospitalPreExam(HospitalPreExam hospitalPreExam) {
        // 获取当前登录用户
        Long userId = SecurityUtils.getUserId();
        HospitalPreExam query = new HospitalPreExam();
        query.setApplyNo(hospitalPreExam.getApplyNo());
        query.setPreExamId(hospitalPreExam.getPreExamId());
        query.setStatus(Constants.HospitalConstants.NUM_1);
        List<HospitalPreExam> hospitalPreExamList = selectHospitalPreExamList(query);
        if (CollUtil.isNotEmpty(hospitalPreExamList)) {
            String clauseList = hospitalPreExam.getClauseList();
            String groupIdList = hospitalPreExam.getGroupIdList();
            Integer leaderIs = hospitalPreExam.getLeaderIs();
            hospitalPreExam = hospitalPreExamList.get(0);
            if (CharSequenceUtil.isNotEmpty(clauseList)) {
                String newClauseList = hospitalPreExam.getClauseList() + "," + clauseList;
                hospitalPreExam.setClauseList(newClauseList);
            }
            if (CharSequenceUtil.isNotEmpty(groupIdList)) {
                String newGroupIdList = hospitalPreExam.getGroupIdList() + "," + groupIdList;
                hospitalPreExam.setGroupIdList(newGroupIdList);
            }
            hospitalPreExam.setLeaderIs(leaderIs);
            hospitalPreExam.setUpdater(String.valueOf(userId));
            updateHospitalPreExam(hospitalPreExam);
        } else {
            hospitalPreExam.setCreator(String.valueOf(userId));
            hospitalPreExam.setUpdater(String.valueOf(userId));
            insertHospitalPreExam(hospitalPreExam);
        }
    }

    /**
     * 根据初审员账户id获取关联的医疗机构编码
     *
     * @param preExamId 初审员账户id
     * @return 结果
     */
    @Override
    public List<HospitalBaseInfo> selectApplyNosByPreExamId(String preExamId){
        return hospitalPreExamMapper.selectApplyNosByPreExamId(preExamId);
    }

    @Override
    public List<ReviewManageVO> queryReviewManage(ReviewManageDTO reviewManageDTO) {
        return hospitalPreExamMapper.queryReviewManage(reviewManageDTO);
    }

    @Override
    public List<HospitalPreExam> selectHospitalPreExamByApplyNo(String applyNo) {
        return hospitalPreExamMapper.selectHospitalPreExamByApplyNo(applyNo);
    }

    @Override
    public int updateHospitalPreExamByApplyNo(HospitalPreExam hospitalPreExam) {
        return hospitalPreExamMapper.updateHospitalPreExamByApplyNo(hospitalPreExam);
    }

    @Override
    public List<String> selectHospitalPreExamIdsByApplyNo(String applyNo) {
        return hospitalPreExamMapper.selectHospitalPreExamIdsByApplyNo(applyNo);
    }

}
