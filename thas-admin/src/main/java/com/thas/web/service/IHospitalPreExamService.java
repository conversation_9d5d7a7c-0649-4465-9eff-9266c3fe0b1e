package com.thas.web.service;

import com.thas.web.domain.HospitalBaseInfo;
import com.thas.web.domain.HospitalPreExam;
import com.thas.web.dto.HosPlanUserInfoVO;
import com.thas.web.dto.ReviewManageDTO;
import com.thas.web.dto.ReviewManageVO;

import java.util.List;

/**
 * 医疗机构认证信息对应初审员Service接口
 *
 * <AUTHOR>
 * @date 2022-01-25
 */
public interface IHospitalPreExamService {


    List<HosPlanUserInfoVO> selectHosPlanUserInfoByApplyNo(String applyNo);

    /**
     * 查询医疗机构认证信息对应初审员
     *
     * @param id 医疗机构认证信息对应初审员主键
     * @return 医疗机构认证信息对应初审员
     */
    public HospitalPreExam selectHospitalPreExamById(Long id);

    /**
     * 查询医疗机构认证信息对应初审员列表
     *
     * @param hospitalPreExam 医疗机构认证信息对应初审员
     * @return 医疗机构认证信息对应初审员集合
     */
    public List<HospitalPreExam> selectHospitalPreExamList(HospitalPreExam hospitalPreExam);

    /**
     * 新增医疗机构认证信息对应初审员
     *
     * @param hospitalPreExam 医疗机构认证信息对应初审员
     * @return 结果
     */
    public int insertHospitalPreExam(HospitalPreExam hospitalPreExam);

    /**
     * 修改医疗机构认证信息对应初审员
     *
     * @param hospitalPreExam 医疗机构认证信息对应初审员
     * @return 结果
     */
    public int updateHospitalPreExam(HospitalPreExam hospitalPreExam);

    /**
     * 批量删除医疗机构认证信息对应初审员
     *
     * @param ids 需要删除的医疗机构认证信息对应初审员主键集合
     * @return 结果
     */
    public int deleteHospitalPreExamByIds(Long[] ids);

    /**
     * 删除医疗机构认证信息对应初审员信息
     *
     * @param id 医疗机构认证信息对应初审员主键
     * @return 结果
     */
    public int deleteHospitalPreExamById(Long id);

    HospitalPreExam selectHospitalPreExamByApplyNoAndAccountId(String applyNo, String accountId);

    HospitalPreExam selectHosPreExamByLeader(String applyNo, Integer leaderIs);

    void insertOrUpdateHospitalPreExam(HospitalPreExam hospitalPreExam);

    /**
     * 根据初审员账户id获取关联的医疗机构编码
     *
     * @param preExamId 初审员账户id
     * @return 结果
     */
    List<HospitalBaseInfo> selectApplyNosByPreExamId(String preExamId);

    List<ReviewManageVO> queryReviewManage(ReviewManageDTO reviewManageDTO);

    List<HospitalPreExam> selectHospitalPreExamByApplyNo(String applyNo);

    int updateHospitalPreExamByApplyNo(HospitalPreExam hospitalPreExam);

    /**
     * 根据医院applyNo,查询医疗机构认证信息对应审查员id
     *
     * @param applyNo 医院applyNo
     * @return 对应审查员ids
     */
    public List<String> selectHospitalPreExamIdsByApplyNo(String applyNo);
}
