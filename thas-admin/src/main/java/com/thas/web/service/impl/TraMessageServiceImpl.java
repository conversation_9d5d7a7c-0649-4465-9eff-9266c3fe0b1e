package com.thas.web.service.impl;

import com.thas.common.constant.Constants;
import com.thas.common.utils.DateUtils;
import com.thas.common.utils.SecurityUtils;
import com.thas.web.domain.TraMessage;
import com.thas.web.domain.TraMessageAccount;
import com.thas.web.mapper.TraLearnResourceMapper;
import com.thas.web.mapper.TraMessageAccountMapper;
import com.thas.web.mapper.TraMessageMapper;
import com.thas.web.service.ITraMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 留言管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-20
 */
@Service
public class TraMessageServiceImpl implements ITraMessageService {
    @Autowired
    private TraMessageMapper traMessageMapper;

    @Autowired
    private TraLearnResourceMapper traLearnResourceMapper;

    @Autowired
    private TraMessageAccountMapper messageAccountMapper;

    /**
     * 查询留言管理列表
     *
     * @param traMessage 留言管理
     * @return 留言管理
     */
    @Override
    public List<TraMessage> selectTraMessageList(TraMessage traMessage) {
        return traMessageMapper.selectTraMessageList(traMessage);
    }

    /**
     * 新增留言管理
     *
     * @param traMessage 留言管理
     * @return 结果
     */
    @Override
    public int insertTraMessage(TraMessage traMessage) {
        traMessage.setCreateTime(DateUtils.getNowDate());
        int result = traMessageMapper.insertTraMessage(traMessage);

        // 如果是回复,还需要修改留言的回复数
        if (Objects.nonNull(traMessage.getPid()) && traMessage.getPid() != 0) {
            traMessageMapper.replyNumPlus(traMessage.getPid());
        }
        return result;
    }

    /**
     * 批量删除留言管理
     *
     * @param ids 需要删除的留言管理主键
     * @return 结果
     */
    @Override
    public int deleteTraMessageByIds(Long[] ids) {
        return traMessageMapper.deleteTraMessageByIds(ids);
    }

    @Override
    public int doLike(Long messageId, String type) {
        TraMessageAccount traMessageAccount = new TraMessageAccount();
        traMessageAccount.setAccount(SecurityUtils.getUsername());
        traMessageAccount.setMessageId(messageId);
        switch (type) {
            case "ADD":
                messageAccountMapper.add(traMessageAccount);
                traMessageMapper.likeNumPlus(messageId);
                break;
            case "SUB":
                messageAccountMapper.delete(traMessageAccount);
                traMessageMapper.likeNumMini(messageId);
                break;
            default:
                return Constants.INT_ZERO;
        }
        return Constants.INT_ONE;
    }

    @Override
    public Long selectTraMessageCount(TraMessage traMessage) {
        return traMessageMapper.selectTraMessageCount(traMessage);
    }
}
