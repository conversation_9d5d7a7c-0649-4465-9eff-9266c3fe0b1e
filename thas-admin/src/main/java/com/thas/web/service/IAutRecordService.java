package com.thas.web.service;

import com.thas.web.domain.AutRecord;

/**
 * 认证记录Service接口
 *
 * <AUTHOR>
 * @date 2022-01-17
 */
public interface IAutRecordService {

    /**
     * 提交评审员认证审核记录信息
     *
     * @param autRecord 认证记录
     */
    void submitAutRecord(AutRecord autRecord);

    /**
     * 查询认证记录
     *
     * @param accountId 审核员id/评审员id
     * @return 认证记录
     */
    AutRecord selectAutRecordByAccountId(String accountId);

    /**
     * 修改认证记录
     *
     * @param autRecord 认证记录
     * @return 结果
     */
    void saveAutRecord(AutRecord autRecord);

}
