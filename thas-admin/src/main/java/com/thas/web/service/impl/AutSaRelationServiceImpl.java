package com.thas.web.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.thas.common.constant.Constants;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.core.domain.entity.SysUser;
import com.thas.common.core.text.Convert;
import com.thas.common.enums.*;
import com.thas.common.exception.ServiceException;
import com.thas.common.properties.AbstractSftpProperties;
import com.thas.common.utils.DateUtils;
import com.thas.common.utils.PageUtils;
import com.thas.common.utils.SecurityUtils;
import com.thas.system.domain.SysUserHospital;
import com.thas.system.domain.vo.ReviewerInfoVo;
import com.thas.system.service.ISysUserHospitalService;
import com.thas.web.domain.*;
import com.thas.web.domain.dto.ReviewFitMoveClauseReq;
import com.thas.web.domain.vo.AutSaRelationQueryRes;
import com.thas.web.domain.vo.TraQuestionnaireVO;
import com.thas.web.domain.vo.TraineesReviewRecVO;
import com.thas.web.dto.CstCertificationStandardVO;
import com.thas.web.mapper.*;
import com.thas.web.service.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 认证自评关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-17
 */
@Component
@Slf4j
public class AutSaRelationServiceImpl implements IAutSaRelationService {

    @Resource
    private CommonProcessMapper commonProcessMapper;

    @Resource
    private AutSaRelationMapper autSaRelationMapper;

    @Resource
    private IHospitalPlannedDistributionService hospitalPlannedDistributionService;

    @Resource
    private IHospitalReviewCycleService hospitalReviewCycleService;

    @Resource
    private ISysUserHospitalService sysUserHospitalService;

    @Resource
    private IHospitalBaseInfoService hospitalBaseInfoService;

    @Resource
    private AutSaAudMapper autSaAudMapper;

    @Resource
    private IAutSaAudBusinessDataService autSaAudBusinessDataService;

    @Resource
    private HospitalReviewerMapper hospitalReviewerMapper;

    @Resource
    private IHospitalPreExamService iHospitalPreExamService;

    @Resource
    private ICstCertificationStandardsService cstCertificationStandardsService;

    @Autowired
    private AbstractSftpProperties sftpImageProperties;

    @Autowired
    private TraQuestionnaireFeedBackRecordMapper traQuestionnaireFeedBackRecordMapper;

    @Autowired
    private TraQuestionnaireFeedBackServiceImpl traQuestionnaireFeedBackService;

    @Autowired
    private ITrainingEvaluateResultImpl trainingEvaluateResult;

    @Autowired
    private ReviewFitMoveClauseMapper reviewFitMoveClauseMapper;

    @Autowired
    private IUploadFileInfoService uploadFileInfoService;

    @Value("${outFileDownload.resourceUrl}")
    private String resourceUrl;

    /**
     * 根据条件查询认证自评关联信息
     * 非医院角色 需要直接使用自评编码查询相关信息
     * 医院角色: 只需要医院账户信息
     *
     * @param autSaRelation 认证自评关联信息查询条件
     * @return 认证自评关联信息
     */
    @Override
    public AjaxResult queryAutSaRelation(AutSaRelation autSaRelation) {
        // 获取角色权限字符串并设置账户id
        String roleKey = this.getRoleKeyAndSetAccountId(autSaRelation);
        AutSaRelation newAutSaRelation;
        if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(roleKey, AutSaAudRoleEnum.HOSPITAL)) {
            // 医院角色 根据用户id获取医院applyNo
            String hospitalApplyNo = this.getHospitalApplyNoByUserId(autSaRelation.getAccountId());
            // 判断医院是否已经完成评审周期分配+审核操作，没有完成，不允许生成自评编码  hospital_planned_distribution.cycleStatus=1
            this.checkHosIsCompCyAlocRev(hospitalApplyNo);
            // 根据医疗机构编码查询认证自评关联信息
            newAutSaRelation = this.selectAutSaRelationByHospitalApplyNo(hospitalApplyNo, true);
        } else if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(roleKey, AutSaAudRoleEnum.ASSESSOR, AutSaAudRoleEnum.INSPECTOR, AutSaAudRoleEnum.SENIOR_ASSESSOR)) {
            // 非医院角色 需要直接使用自评编码查询相关信息
            if (StringUtils.isBlank(autSaRelation.getAutCode())) {
                log.error("非医院角色账户：{}，入参自评编码必传", autSaRelation.getAccountId());
                throw new ServiceException(ServiceExceptionEnum.AUT_SA_RELATION_ERROR_1000004);
            }
            // 根据自评编码查询认证自评关联信息
            newAutSaRelation = this.selectAutSaRelationByAutCode(autSaRelation.getAutCode(), Constants.HospitalConstants.NUM_1);
        } else {
            log.error("账户：{} 角色 :{}错误，无权限查询", autSaRelation.getAccountId(), roleKey);
            throw new ServiceException(ServiceExceptionEnum.AUT_SA_AUD_ERROR_1000006);
        }
        // 校验医院计划周期时间
        this.getCycleLists(newAutSaRelation.getHospitalApplyNo(), newAutSaRelation.getAutCode(), newAutSaRelation.getAutStatus());
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("autSaRelation", newAutSaRelation);
        // 设置评审员+审查员用户分配的信息   条款ids
        List<String> distributeClauseIdList;
        if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(roleKey, AutSaAudRoleEnum.INSPECTOR)) {
            distributeClauseIdList = this.getPreExamDistributeClauseIds(newAutSaRelation.getHospitalApplyNo(), newAutSaRelation.getAccountId());
        } else if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(roleKey, AutSaAudRoleEnum.ASSESSOR)) {
            distributeClauseIdList = this.getReviewerDistributeClauseIds(newAutSaRelation.getHospitalApplyNo(), newAutSaRelation.getAccountId(), newAutSaRelation.getAutCsId());
        } else {
            distributeClauseIdList = Lists.newArrayList();
        }
        resultMap.put("distributeClauseIdList", distributeClauseIdList);
        //封装不适用款给前端过滤
        List<String> fitClauseIds = packFitClauseIds(newAutSaRelation.getAutCode());
        resultMap.put("fitClauseIds", fitClauseIds);
        //010302节点，查询审查驳回款
        if(AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(newAutSaRelation.getAutCode(),AutSaAudStatusEnum.FAR_CLAUSE_PROCESS)){
            AutSaAud autSaAud = new AutSaAud();
            autSaAud.setAutCode(newAutSaRelation.getAutCode());
            autSaAud.setSubmitType(AutSaAudSubmitTypeEnum.FAR_CLAUSE_RJ_E.getSubmitType());
            autSaAud.setStatus(Constants.INT_ONE);
            List<AutSaAud> autSaAuds = autSaAudMapper.selectAutSaAudList(autSaAud);
            if(CollectionUtils.isNotEmpty(autSaAuds)){
                resultMap.put("farClauseRjEIds", autSaAuds.stream().map(AutSaAud::getClauseId).collect(Collectors.toList()));
            }
        }
        return AjaxResult.success(resultMap);
    }

    private List<String> packFitClauseIds(String autCode) {
        if (StringUtils.isBlank(autCode)) {
            throw new ServiceException("查询不适用款信息时，自评编码不能为空");
        }
        ReviewFitMoveClauseReq reviewFitMoveClauseReq = new ReviewFitMoveClauseReq();
        reviewFitMoveClauseReq.setAutCode(autCode);
        reviewFitMoveClauseReq.setFitStatus(Long.valueOf(Constants.STR_NUM_1));
        List<ReviewFitMoveClause> reviewFitMoveClauseList = reviewFitMoveClauseMapper.qryReviewFitMoveClauseList(reviewFitMoveClauseReq);
        return reviewFitMoveClauseList.stream().map(ReviewFitMoveClause::getClauseId).collect(Collectors.toList());
    }

    /**
     * 获取审查员账户对应分配款id
     *
     * @param applyNo 医疗机构编码
     * @param userId  账户Id
     * @return 分配款id
     */
    private List<String> getPreExamDistributeClauseIds(String applyNo, String userId) {
        HospitalPreExam hospitalPreExam = iHospitalPreExamService.selectHospitalPreExamByApplyNoAndAccountId(applyNo, userId);
        if (hospitalPreExam == null || StringUtils.isBlank(hospitalPreExam.getClauseList())) {
            log.error("审查员账户：{} ,applyNo：{} ，分配的款项id列表为空", userId, applyNo);
            throw new ServiceException(ServiceExceptionEnum.PLAN_ERROR_1000007);
        }
        List<CstCertificationStandardVO> hospitalPreExamCsAllList = cstCertificationStandardsService.selectCstCertificationStandardVOByIds(hospitalPreExam.getClauseList());
        if (CollectionUtils.isEmpty(hospitalPreExamCsAllList)) {
            log.error("审查员账户：{} applyNo：{} ，分配的款项id列表在版本内无对应数据", userId, applyNo);
            throw new ServiceException(ServiceExceptionEnum.PLAN_ERROR_1000008);
        }
        return hospitalPreExamCsAllList.stream().map(a -> a.getClauseId().toString()).collect(Collectors.toList());
    }

    /**
     * 获取评审员账户对应分配款id
     *
     * @param applyNo 医疗机构编码
     * @param userId  账户Id
     * @return 分配款id
     */
    private List<String> getReviewerDistributeClauseIds(String applyNo, String userId, String versionId) {
        HospitalReviewer hospitalReviewer = hospitalReviewerMapper.selectHospitalReviewerByApplyNoAndAccountId(applyNo, userId);
        if (hospitalReviewer == null || StringUtils.isBlank(hospitalReviewer.getFieldIdList())) {
            log.error("评审员账户：{}，applyNo：{} ， 分配的领域列表为空", userId, applyNo);
            throw new ServiceException(ServiceExceptionEnum.PLAN_ERROR_1000007);
        }
        List<CstCertificationStandardVO> hospitalReviewerCsAllList = cstCertificationStandardsService.selectCstCertificationStandardsByDomainId(hospitalReviewer.getFieldIdList(), Long.valueOf(versionId));
        if (CollectionUtils.isEmpty(hospitalReviewerCsAllList)) {
            log.error("评审员账户：{} ，applyNo：{} ，分配的领域列表在版本内无对应数据", userId, applyNo);
            throw new ServiceException(ServiceExceptionEnum.PLAN_ERROR_1000008);
        }
        return hospitalReviewerCsAllList.stream().map(a -> a.getClauseId().toString()).collect(Collectors.toList());
    }

    /**
     * 获取角色权限字符串并设置账户id
     *
     * @param autSaRelation 关联信息
     * @return 角色
     */
    private String getRoleKeyAndSetAccountId(AutSaRelation autSaRelation) {
        SysUser sysUser = SecurityUtils.getLoginUser().getUser();
        if (sysUser == null || sysUser.getUserId() == null) {
            log.error("当前登录账户，用户信息不存在");
            throw new ServiceException(ServiceExceptionEnum.SYS_USER_ERROR_1000000);
        }
        if (StringUtils.isBlank(sysUser.getRoleKey())) {
            log.error("当前登录账户，用户roleKey不存在");
            throw new ServiceException(ServiceExceptionEnum.SYS_USER_ERROR_1000001);
        }
        autSaRelation.setAccountId(sysUser.getUserId().toString());
        return sysUser.getRoleKey();
    }

    /**
     * 根据用户id获取医院applyNo
     *
     * @param userId 用户id
     * @return applyNo
     */
    @Override
    public String getHospitalApplyNoByUserId(String userId) {
        // 查询 用户医院关系表 sys_user_hospital + 认证自评关联表: aut_sa_relation  获取医院账号对应的医院编号和自评关联信息
        SysUserHospital sysUserHospital = sysUserHospitalService.selectSysUserHospitalByUserId(Long.valueOf(userId));
        if (null == sysUserHospital || StringUtils.isBlank(sysUserHospital.getHospitalApplyNo())) {
            // 账户不存在或者 账户没有关联医院信息
            log.error("医院账户:{}没有关联医院信息", userId);
            throw new ServiceException(ServiceExceptionEnum.PLAN_ERROR_1000000);
        }
        return sysUserHospital.getHospitalApplyNo();
    }

    /**
     * 校验医疗机构是否完成评审周期分配和审核
     *
     * @param hosApplyNo 医疗机构applyNo
     */
    @Override
    public void checkHosIsCompCyAlocRev(String hosApplyNo) {
        // 根据医疗机构applyNo获取分配信息
        HospitalPlannedDistribution hospitalPlannedDistribution = this.getPlanDistByHosApplyNo(hosApplyNo);
        if (Constants.HospitalConstants.NUM_0.equals(hospitalPlannedDistribution.getCycleStatus())) {
            // 医疗结构对分配评审安排审核状态:0待审核 1计划通过 2被拒绝    未完成，需要判断评审周期是否完成分配
            List<HospitalReviewCycle> hospitalReviewCycles = hospitalReviewCycleService.selectHospitalReviewCycleByApplyNo(hosApplyNo);
            if (CollectionUtils.isNotEmpty(hospitalReviewCycles)) {
                log.error("checkHosIsCompCyAlocRev———— 医疗结构对评审周期分配待审核");
                throw new ServiceException(ServiceExceptionEnum.PLAN_ERROR_1000003);
            } else {
                log.error("checkHosIsCompCyAlocRev————评审周期计划审核为空，请联系管理员进行评审计划分配");
                throw new ServiceException(ServiceExceptionEnum.PLAN_ERROR_1000001);
            }
        }
    }

    /**
     * 根据医疗机构applyNo获取分配信息
     *
     * @param hosApplyNo 医疗机构编码
     * @return
     */
    private HospitalPlannedDistribution getPlanDistByHosApplyNo(String hosApplyNo) {
        HospitalPlannedDistribution hospitalPlannedDistribution = new HospitalPlannedDistribution();
        hospitalPlannedDistribution.setApplyNo(hosApplyNo);
        hospitalPlannedDistribution.setStatus(Constants.HospitalConstants.NUM_1);
        List<HospitalPlannedDistribution> plannedList = hospitalPlannedDistributionService.selectHospitalPlannedDistributionList(hospitalPlannedDistribution);
        if (CollectionUtils.isEmpty(plannedList)) {
            log.error("医疗结构:{} 未分配评审计划，请联系管理员进行评审计划分配", hosApplyNo);
            throw new ServiceException(ServiceExceptionEnum.PLAN_ERROR_1000001);
        }
        return plannedList.get(0);
    }

    /**
     * 根据医疗机构编码查询认证自评关联信息
     *
     * @param hospitalApplyNo 医院编码
     * @return 结果
     */
    @Override
    public AutSaRelation selectAutSaRelationByHospitalApplyNo(String hospitalApplyNo, boolean initFlag) {
        // 根据条件查询认证自评关联信息 没查到需要初始化数据
        AutSaRelation autSaRelation = new AutSaRelation();
        autSaRelation.setHospitalApplyNo(hospitalApplyNo);
        autSaRelation.setStatus(Constants.HospitalConstants.NUM_1);
        List<AutSaRelation> autSaRelations = this.selectAutSaRelationListByCondition(autSaRelation);
        autSaRelation = (CollectionUtils.isNotEmpty(autSaRelations)) ? autSaRelations.get(0) : (initFlag ? this.initAutSaRelation(hospitalApplyNo) : null);
        return autSaRelation;
    }

    /**
     * 初始化关联信息
     *
     * @param hosApplyNo
     * @return 关联信息
     */
    public AutSaRelation initAutSaRelation(String hosApplyNo) {
        // 自评编码为空，需要初始化认证自评关联表相关数据
        AutSaRelation hisAutSaRelation = new AutSaRelation();
        // 账户id
        hisAutSaRelation.setHospitalApplyNo(hosApplyNo);
        // 自评认证状态
        hisAutSaRelation.setAutStatus(AutSaAudStatusEnum.WAIT_SA_CLAUSE.getStatus());
        // 自评编码
        hisAutSaRelation.setAutCode(DateUtils.dateTimeNow() + autSaRelationMapper.selectAudRelationId());
        // 自评认证id
        Long versionId = this.getPlanDistByHosApplyNo(hosApplyNo).getVersionId();
        if (versionId == null) {
            log.error("医疗结构:{} 未关联版本号");
            throw new ServiceException(ServiceExceptionEnum.PLAN_ERROR_1000006);
        }
        hisAutSaRelation.setAutCsId(versionId.toString());
        // 是否有效
        hisAutSaRelation.setStatus(Constants.HospitalConstants.NUM_1);
        hisAutSaRelation.setEvaluateFlag(0);
        log.info("医院机构：{} 初始化关联信息:{}", hosApplyNo, JSON.toJSONString(hisAutSaRelation));
        int count = autSaRelationMapper.insertAutSaRelation(hisAutSaRelation);
        if (count <= 0) {
            log.error("医院机构：{}初始化关联信息时，落表初始化数据失败", hosApplyNo);
            throw new ServiceException(ServiceExceptionEnum.AUT_SA_RELATION_ERROR_1000001);
        }
        //如果通过更新评审周期表的自评编码
        HospitalReviewCycle hospitalReviewCycle = new HospitalReviewCycle();
        hospitalReviewCycle.setApplyNo(hosApplyNo);
        hospitalReviewCycle.setStatus(Constants.INT_ONE);
        hospitalReviewCycle.setAutCode(hisAutSaRelation.getAutCode());
        int i = hospitalReviewCycleService.updateHospitalReviewCycleByApplyNo(hospitalReviewCycle);
        if (i <= 0) {
            log.error("医院机构：{}更新评审周期表的自评编码失败", hosApplyNo);
            throw new ServiceException("更新评审周期表的自评编码失败", 500);
        }

        return hisAutSaRelation;
    }

    /**
     * 根据自评编码查询认证自评关联信息
     *
     * @param autCode 自评编码
     * @return 认证自评关联信息
     */
    @Override
    public AutSaRelation selectAutSaRelationByAutCode(String autCode, Integer status) {
        // 根据条件查询认证自评关联信息
        AutSaRelation autSaRelation = new AutSaRelation();
        autSaRelation.setAutCode(autCode);
        autSaRelation.setStatus(status);
        List<AutSaRelation> autSaRelations = this.selectAutSaRelationListByCondition(autSaRelation);
        if (CollectionUtils.isEmpty(autSaRelations)) {
            // 数据有问题，没有查到自评关联数据
            log.error("传入的自评编码：{} 数据有问题，没有查到自评关联数据", autCode);
            throw new ServiceException(ServiceExceptionEnum.AUT_SA_RELATION_ERROR_1000002);
        }
        return autSaRelations.get(0);
    }

    /**
     * 根据条件查询认证自评关联信息
     *
     * @param autSaRelation 认证自评关联
     * @return 认证自评关联
     */
    @Override
    public List<AutSaRelation> selectAutSaRelationListByCondition(AutSaRelation autSaRelation) {
        return autSaRelationMapper.selectAutSaRelationListByCondition(autSaRelation);
    }

    /**
     * 获取解析后的周期时间
     *
     * @param applyNo   医疗机构编码
     * @param curStatus 当前状态
     * @return 周期时间
     */
    @Override
    public List<String> getCycleLists(String applyNo, String autCode, String curStatus) {
        //改：评审流程中各个节点，不受评审计划里的结束时间限制；
        List<String> cycleLists = Lists.newArrayList();
//        // 判断当前节点对应的是否为驳回状态，如果为驳回状态，对应的周期时间按照驳回状态计算
//        // 获取驳回节点信息
//        List<AutSaAudBusinessData> autSaAudRejectNodeBusinessDatas = autSaAudBusinessDataService.selectAutSaAudBusinessData(autCode, AutSaAudBusinessCodeEnum.AUT_SA_AUD_REJECT_STATUS.getCode());
//        log.info("查询到自评编码：{} 对应驳回节点信息：{}", autCode, JSON.toJSONString(autSaAudRejectNodeBusinessDatas));
//        if (CollectionUtils.isNotEmpty(autSaAudRejectNodeBusinessDatas) && StringUtils.isNotBlank(autSaAudRejectNodeBusinessDatas.get(0).getData())) {
//            // 驳回节点不为空 使用驳回节点计算周期
//            log.info("有驳回数据，需要使用驳回节点对应的周期信息", autSaAudRejectNodeBusinessDatas.get(0).getData());
//            curStatus = autSaAudRejectNodeBusinessDatas.get(0).getData();
//        }
//        // 获取节点对应周期配置信息
//        AutSaAudStatusConfig cycleConfig = commonProcessMapper.selectAutSaAudStatusConfig(curStatus);
//        log.info("当前节点:{} 对应周期配置信息", curStatus, cycleConfig);
//        if (ObjectUtil.isNotNull(cycleConfig) && StringUtils.isNotBlank(cycleConfig.getCycleStage())) {
//            HospitalReviewCycle hospitalReviewCycle = hospitalReviewCycleService.selectByApplyNoAndStageValue(applyNo, cycleConfig.getCycleStage());
//            if (ObjectUtil.isNull(hospitalReviewCycle) || StringUtils.isBlank(hospitalReviewCycle.getCycle())) {
//                log.info("医疗机构:{} 节点：{} 周期阶段：{} 对应的周期时间为空", applyNo, curStatus, cycleConfig.getCycleStage());
//                throw new ServiceException(ServiceExceptionEnum.PLAN_ERROR_1000001);
//            }
//            String[] cycleArr = Convert.toStrArray(hospitalReviewCycle.getCycle());
//            if (cycleArr.length != Constants.HospitalConstants.NUM_2 || StringUtils.isBlank(cycleArr[Constants.HospitalConstants.NUM_0].trim()) || StringUtils.isBlank(cycleArr[Constants.HospitalConstants.NUM_1].trim())) {
//                // 数据有问题 当前周期阶段分配周期为空 请联系管理员进行评审计划分配
//                log.info("医疗机构：{} 节点：{} 周期阶段：{} 对应的评审周期时间：{} 格式不正确，解析异常", applyNo, curStatus, cycleConfig.getCycleStage(), hospitalReviewCycle.getCycle());
//                throw new ServiceException(ServiceExceptionEnum.PLAN_ERROR_1000001);
//            }
//            String nowDateStr = DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS);
//            String endDateStr = cycleArr[Constants.HospitalConstants.NUM_1].trim() + DateUtils.END_DATE_SUFFIX;
//            if (DateUtils.parseDate(nowDateStr).getTime() > DateUtils.parseDate(endDateStr).getTime()) {
//                // 当前时间超过计划结束时间  需要失效自评信息 失效计划分配信息 返回前端 请联系管理员进行评审计划分配
//                log.info("失效医疗机构：{} 对应的分配信息", applyNo);
//                hospitalBaseInfoService.invalidHosPlan(applyNo);
//                // 失效自评信息
//                AutSaRelation updateAutSaRelation = new AutSaRelation();
//                updateAutSaRelation.setHospitalApplyNo(applyNo);
//                updateAutSaRelation.setStatus(Constants.HospitalConstants.NUM_0);
//                // 失效自评关联信息
//                log.info("失效医疗机构：{}自评关联信息", applyNo);
//                this.updateAutSaRelation(updateAutSaRelation);
//                // 失效自评信息
//                log.info("失效自评编码；{}对应自评信息", autCode);
//                autSaAudMapper.invalidAutSaAud(autCode);
//
//                log.error("医疗机构：{} 当前节点时间 超出计划结束时间：{} ，需要失效对应评审分配计划信息，请联系管理员重新进行计划分配处理，", applyNo, nowDateStr, endDateStr);
//                throw new ServiceException(ServiceExceptionEnum.PLAN_ERROR_1000001);
//            }
//            cycleLists.add(cycleArr[Constants.HospitalConstants.NUM_0].trim() + DateUtils.START_DATE_SUFFIX);
//            cycleLists.add(nowDateStr);
//            cycleLists.add(endDateStr);
//        }
        return cycleLists;
    }

    /**
     * 获取解析后的周期时间
     *
     * @param applyNo 医疗机构编码
     * @return 周期时间
     */
    @Override
    public boolean invalidAutSaAudByCycle(String applyNo, AutSaRelation autSaRelation) {
        //改：评审流程中各个节点，不受评审计划里的结束时间限制；

//        String curStatus = AutSaAudStatusEnum.WAIT_SA_CLAUSE.getStatus();
//        // 没有自评信息，这时需要根据applyNo 获取计划分配信息，校验当前时间和自评开始时间 如果当前时间> 自评开始时间 失效当前申请
//        // 有自评信息，直接校验当前节点的
//        if (autSaRelation != null) {
//            curStatus = autSaRelation.getAutStatus();
//            List<AutSaAudBusinessData> autSaAudRejectNodeBusinessDatas = autSaAudBusinessDataService.selectAutSaAudBusinessData(autSaRelation.getAutCode(), AutSaAudBusinessCodeEnum.AUT_SA_AUD_REJECT_STATUS.getCode());
//            if (CollectionUtils.isNotEmpty(autSaAudRejectNodeBusinessDatas) && StringUtils.isNotBlank(autSaAudRejectNodeBusinessDatas.get(0).getData())) {
//                // 驳回节点不为空 使用驳回节点计算周期
//                log.info("有驳回数据，需要使用驳回节点对应的周期信息", autSaAudRejectNodeBusinessDatas.get(0).getData());
//                curStatus = autSaAudRejectNodeBusinessDatas.get(0).getData();
//            }
//        }
//        // 获取节点对应周期配置信息
//        AutSaAudStatusConfig cycleConfig = commonProcessMapper.selectAutSaAudStatusConfig(curStatus);
//        if (ObjectUtil.isNotNull(cycleConfig) && StringUtils.isNotBlank(cycleConfig.getCycleStage())) {
//            HospitalReviewCycle hospitalReviewCycle = hospitalReviewCycleService.selectByApplyNoAndStageValue(applyNo, cycleConfig.getCycleStage());
//            if (ObjectUtil.isNull(hospitalReviewCycle)) {
//                log.info("invalidAutSaAudByCycle---时间超时，直接失效数据，请联系管理员进行评审计划分配");
//                throw new ServiceException(ServiceExceptionEnum.PLAN_ERROR_1000001);
//            }
//            String[] cycleArr = Convert.toStrArray(hospitalReviewCycle.getCycle());
//            String nowDateStr = DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS);
//            String endDateStr = cycleArr[Constants.HospitalConstants.NUM_1].trim() + DateUtils.END_DATE_SUFFIX;
//            if (DateUtils.parseDate(nowDateStr).getTime() > DateUtils.parseDate(endDateStr).getTime()) {
//                hospitalBaseInfoService.invalidHosPlan(applyNo);
//                // 失效自评信息
//                AutSaRelation updateAutSaRelation = new AutSaRelation();
//                updateAutSaRelation.setHospitalApplyNo(applyNo);
//                updateAutSaRelation.setStatus(Constants.HospitalConstants.NUM_0);
//                // 失效自评关联信息
//                log.info("失效医疗机构：{}自评关联信息", applyNo);
//                this.updateAutSaRelation(updateAutSaRelation);
//                // 失效自评信息
//                if (autSaRelation != null) {
//                    autSaAudMapper.invalidAutSaAud(autSaRelation.getAutCode());
//                }
//                return true;
//            }
//        }
        return false;
    }


    /**
     * 更新认证自评关联
     *
     * @param autSaRelation 认证自评关联
     * @return 结果
     */
    @Override
    public int updateAutSaRelation(AutSaRelation autSaRelation) {
        autSaRelation.setUpdateTime(DateUtils.getNowDate());
        //封装ValuateFlag
        if (StringUtils.isNotEmpty(autSaRelation.getAutStatus())) {
            this.packeValuateFlag(autSaRelation);
        }
        return autSaRelationMapper.updateAutSaRelationByCondition(autSaRelation);
    }

    @Override
    public int deleteAutSaRelationByHospitalApplyNo(String hospitalApplyNo) {
        if(StringUtils.isBlank(hospitalApplyNo)){
            throw new ServiceException("deleteAutSaRelationByHospitalApplyNo入参医院号码不能为空");
        }
        return autSaRelationMapper.deleteAutSaRelationByHospitalApplyNo(hospitalApplyNo);
    }

    private void packeValuateFlag(AutSaRelation autSaRelation) {
        String autStatus = autSaRelation.getAutStatus();
        //如果翻转节点符合这三个节点，EvaluateFlag改为1
        //改为：满足030205-待提交评审结果 节点
        if (AutSaAudStatusEnum.SR_SUMMARY.getStatus().equals(autStatus)) {
            //如果为这三个节点且EvaluateFlag为0时，生成反馈文件记录表数据(只生成第一次翻转状态的数据)
            this.insertBatchTraQuestionnaireFeedBackRecord(autSaRelation);
            autSaRelation.setEvaluateFlag(Constants.INT_ONE);
        }
    }

    public void insertBatchTraQuestionnaireFeedBackRecord(AutSaRelation autSaRelation) {
        log.info("insertBatchTraQuestionnaireFeedBackRecord-现场评审-生成反馈文件记录表数据-开始，入参：[{}]", autSaRelation.toString());
        if (StringUtils.isNotEmpty(autSaRelation.getHospitalApplyNo()) || StringUtils.isNotEmpty(autSaRelation.getAutCode())) {
            List<TraQuestionnaireFeedBackRecord> traQuestionnaireFeedBackRecordList = new ArrayList<>();
            //根据入参医院编号或自评编码，查询认证自评关联表
            AutSaRelationQueryRes selectAutSaRelation = new AutSaRelationQueryRes();
            selectAutSaRelation.setHospitalApplyNo(autSaRelation.getHospitalApplyNo());
            selectAutSaRelation.setAutCode(autSaRelation.getAutCode());
            selectAutSaRelation.setStatus(Constants.INT_ONE);
            List<AutSaRelation> autSaRelations = autSaRelationMapper.selectAutSaRelationListByQueryRes(selectAutSaRelation);
            //如果为这三个节点且EvaluateFlag为0时，生成反馈文件记录表数据(只生成第一次翻转状态的数据)
            if (CollectionUtils.isNotEmpty(autSaRelations) && ObjectUtil.equal(autSaRelations.get(0).getEvaluateFlag(), Constants.INT_ZERO)) {
                AutSaRelation queryAutSaRelation = autSaRelations.get(0);
                //在医院通过现场评审更新状态时，生成对应的反馈问卷记录表数据，附件4：医院反馈表，附件8：评审员反馈表（多个评审员对应生成多份）
                //生成学员实践(带教)培训反馈表
                //获取问卷类型对应问卷id（1对多）
                List<Integer> typeList = new ArrayList<>();
                typeList.add(TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_6.getIntCode());
                typeList.add(TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_9.getIntCode());
                typeList.add(TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_8.getIntCode());
                List<TraQuestionnaireVO> traQuestionnaireVOS = trainingEvaluateResult.queryTraQuestionnaireList(typeList);

                //附件4：医院反馈表
                traQuestionnaireVOS.stream().filter(o -> ObjectUtil.equal(TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_6.getIntCode(), o.getType())).forEach(traQuestionnaireVO -> {
                    TraQuestionnaireFeedBackRecord traQuestionnaireFeedBackRecord = new TraQuestionnaireFeedBackRecord();
                    traQuestionnaireFeedBackRecord.setNeedId(queryAutSaRelation.getHospitalApplyNo());
                    traQuestionnaireFeedBackRecord.setAutCode(queryAutSaRelation.getAutCode());
                    traQuestionnaireFeedBackRecord.setFeedBackType(Long.valueOf(TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_6.getCode()));
                    traQuestionnaireFeedBackRecord.setQuestionnaireId(traQuestionnaireVO.getId());
                    traQuestionnaireFeedBackRecordList.add(traQuestionnaireFeedBackRecord);
                });

                //附件8：评审员反馈表（多个评审员对应生成多份）
                ReviewerInfoVo reviewerInfoVo = traQuestionnaireFeedBackService.packReviewerInfoVo(queryAutSaRelation.getHospitalApplyNo(), queryAutSaRelation.getAutCode());
                if (ObjectUtil.isNotEmpty(reviewerInfoVo) && CollectionUtils.isNotEmpty(reviewerInfoVo.getReviewerList())) {
                    traQuestionnaireVOS.stream().filter(o -> ObjectUtil.equal(TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_9.getIntCode(), o.getType())).forEach(traQuestionnaireVO ->
                        reviewerInfoVo.getReviewerList().forEach(reviewerInfo -> {
                            TraQuestionnaireFeedBackRecord reviewerRecord = new TraQuestionnaireFeedBackRecord();
                            reviewerRecord.setNeedId(reviewerInfo.getAccountId());
                            reviewerRecord.setAutCode(queryAutSaRelation.getAutCode());
                            reviewerRecord.setFeedBackType(Long.valueOf(TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_9.getCode()));
                            reviewerRecord.setQuestionnaireId(traQuestionnaireVO.getId());
                            traQuestionnaireFeedBackRecordList.add(reviewerRecord);
                        })
                    );
                }

                //生成：学员实践(带教)培训反馈表
                if (ObjectUtil.isNotEmpty(reviewerInfoVo) && CollectionUtils.isNotEmpty(reviewerInfoVo.getTraineesList())) {
                    traQuestionnaireVOS.stream().filter(o -> ObjectUtil.equal(TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_8.getIntCode(), o.getType())).forEach(traQuestionnaireVO ->
                            reviewerInfoVo.getTraineesList().forEach(traineesInfo -> {
                                TraQuestionnaireFeedBackRecord reviewerRecord = new TraQuestionnaireFeedBackRecord();
                                reviewerRecord.setNeedId(traineesInfo.getAccountId());
                                reviewerRecord.setAutCode(queryAutSaRelation.getAutCode());
                                reviewerRecord.setFeedBackType(Long.valueOf(TraQuestionnaireFeedBackTypeEnum.FEED_BACK_TYPE_8.getCode()));
                                reviewerRecord.setQuestionnaireId(traQuestionnaireVO.getId());
                                traQuestionnaireFeedBackRecordList.add(reviewerRecord);
                            })
                    );
                }

            }
            if (CollectionUtils.isNotEmpty(traQuestionnaireFeedBackRecordList)) {
                traQuestionnaireFeedBackRecordMapper.insertBatchTraQuestionnaireFeedBackRecord(traQuestionnaireFeedBackRecordList, Constants.INT_ONE);
            }
            log.info("insertBatchTraQuestionnaireFeedBackRecord-现场评审-生成反馈文件记录表数据-结束，落表数据：[{}]", JSON.toJSONString(traQuestionnaireFeedBackRecordList));

        }
    }

    /**
     * 查询认证自评关联列表
     *
     * @param autSaRelation 认证自评关联信息查询条件
     * @return 认证自评关联信息列表
     */
    @Override
    public List<AutSaRelationList> selectAllAutSaRelationList(AutSaRelation autSaRelation) {
        // 获取角色权限字符串并设置账户id
        String roleKey = this.getRoleKeyAndSetAccountId(autSaRelation);
        if (!AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(roleKey, AutSaAudRoleEnum.ADMIN, AutSaAudRoleEnum.COMMON_ADMIN)) {
            log.info("入参账号：{}角色：{} ，无权限查询认证自评关联列表信息", autSaRelation.getAccountId(), roleKey);
            throw new ServiceException(ServiceExceptionEnum.AUT_SA_AUD_ERROR_1000006);
        }
        PageUtils.startPage();
        List<AutSaRelationList> sutSaRelationList = autSaRelationMapper.selectAllAutSaRelationList(autSaRelation);
        if (CollectionUtils.isNotEmpty(sutSaRelationList)) {
            List<String> autCodeLists = sutSaRelationList.stream().map(AutSaRelationList::getAutCode).collect(Collectors.toList());
            // 评审结果
            List<AutSaAudBusinessData> reportBusinessDatas = autSaAudBusinessDataService.selectAutSaAudBusinessData(String.join(",", autCodeLists), AutSaAudBusinessCodeEnum.AUT_SA_AUD_REPORT.getCode());
            Map<String, List<AutSaAudBusinessData>> reportMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(reportBusinessDatas)) {
                reportMap = reportBusinessDatas.stream().collect(Collectors.groupingBy(AutSaAudBusinessData::getAutCode));
            }
            // 评审报告
            List<AutSaAudBusinessData> reviewReportBusinessDatas = autSaAudBusinessDataService.selectAutSaAudBusinessData(String.join(",", autCodeLists), AutSaAudBusinessCodeEnum.REVIEW_REPORT.getCode());
            Map<String, List<AutSaAudBusinessData>> reviewReportMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(reviewReportBusinessDatas)) {
                reviewReportMap = reviewReportBusinessDatas.stream().collect(Collectors.groupingBy(AutSaAudBusinessData::getAutCode));
            }
            Map<String, List<AutSaAudBusinessData>> finalReportMap = reportMap;
            Map<String, List<AutSaAudBusinessData>> finalReviewReportMap = reviewReportMap;
            sutSaRelationList.stream().forEach(a -> {
                List<AutSaAudBusinessData> reports = finalReportMap.get(a.getAutCode());
                if (CollectionUtils.isNotEmpty(reports) && StringUtils.isNotBlank(reports.get(0).getData())) {
                    a.setAutSaAudReport(JSON.parseObject(reports.get(0).getData(), AutSaAudReport.class));
                }
                List<AutSaAudBusinessData> reviewReports = finalReviewReportMap.get(a.getAutCode());
                if (CollectionUtils.isNotEmpty(reviewReports) && StringUtils.isNotBlank(reviewReports.get(0).getData())) {
                    Map<String, String> dataMap = JSON.parseObject(reviewReports.get(0).getData(), Map.class);
                    String filePath = MapUtils.getString(dataMap, "url");
                    String fileId = MapUtils.getString(dataMap, "fileId");
                    int index = filePath.indexOf("upload");
                    String url = resourceUrl + filePath.substring(index + 6);
                    a.setReviewReportPdfUrl(url);
                    a.setReviewReportPdfFileId(fileId);
                    List<FileInfoDTO> uploadFileInfoByIds = uploadFileInfoService.getUploadFileInfoByIds(fileId);
                    a.setReviewReportFileName(uploadFileInfoByIds.stream().map(FileInfoDTO::getOrigin).collect(Collectors.joining()));
                }
            });
        }
        return sutSaRelationList;
    }

    /**
     * 根据医院id，查询认证自评关联列表对应节点状态
     *
     * @param hospitalApplyNo 医院id
     * @return 认证自评关联信息列表
     */
    @Override
    public String selectAllAutSaRelationByHospitalApplyNo(String hospitalApplyNo) {
        String autStatus = autSaRelationMapper.selectAutSaRelationByHospitalApplyNo(hospitalApplyNo);
        if (!StringUtils.isNotEmpty(autStatus)) {
            throw new ServiceException(ServiceExceptionEnum.AUT_SA_RELATION_ERROR_1000017);
        }
        return autStatus;
    }

    @Override
    public List<TraineesReviewRecVO> selectAutSaRelationByAutStatus(List<String> autStatusList) {
        return autSaRelationMapper.selectAutSaRelationByAutStatus(autStatusList);
    }

    @Override
    public List<AutSaRelation> selectAutSaRelationListByQueryRes(AutSaRelationQueryRes autSaRelation) {

        return autSaRelationMapper.selectAutSaRelationListByQueryRes(autSaRelation);
    }

    /**
     * 查询学员是否有在途任务
     *
     * @param traineesAssessorId 学员id
     * @return 返回在途的数量
     */
    @Override
    public List<AutSaRelation> selectAutSaRelationByTraineesAssessorId(Long traineesAssessorId) {
        return autSaRelationMapper.selectAutSaRelationByTraineesAssessorId(traineesAssessorId);
    }
}
