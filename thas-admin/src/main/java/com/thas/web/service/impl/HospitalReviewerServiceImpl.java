package com.thas.web.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.thas.common.constant.Constants;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.enums.AutSaAudStatusEnum;
import com.thas.common.enums.AutSaAudSubmitTypeEnum;
import com.thas.common.enums.StatusProcessEnum;
import com.thas.common.exception.ServiceException;
import com.thas.common.utils.DateUtils;
import com.thas.common.utils.SecurityUtils;
import com.thas.web.domain.*;
import com.thas.web.domain.vo.FileInfoVO;
import com.thas.web.domain.vo.HospitalReviewerVo;
import com.thas.web.domain.vo.ReviewInterestVO;
import com.thas.web.dto.DelHosRevDTO;
import com.thas.web.dto.HosPlanUserInfoVO;
import com.thas.web.dto.ReviewManageDTO;
import com.thas.web.dto.ReviewManageVO;
import com.thas.web.dto.SysUserBaseInfo;
import com.thas.web.mapper.AutSaRelationMapper;
import com.thas.web.mapper.HospitalPlannedDistributionMapper;
import com.thas.web.mapper.HospitalReviewerMapper;
import com.thas.web.service.CommonService;
import com.thas.web.service.IHospitalPlannedDistributionService;
import com.thas.web.service.IHospitalReviewerService;
import com.thas.web.service.process.BaseProcessService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 医疗结构认证信息与评审员信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-25
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class HospitalReviewerServiceImpl implements IHospitalReviewerService {

    @Autowired
    private HospitalReviewerMapper hospitalReviewerMapper;

    @Autowired
    private IHospitalPlannedDistributionService hospitalPlannedDistributionService;

    @Autowired
    private CommonService commonService;

    @Resource
    private BaseProcessService baseProcessService;

    @Autowired
    private AutSaRelationMapper autSaRelationMapper;

    @Override
    public Map<String, List<HosPlanUserInfoVO>> selectHosPlanUserInfoByApplyNo(String applyNo, String autCode) {
        Map<String, List<HosPlanUserInfoVO>> res = new HashMap<>();
        List<HospitalReviewer> hospitalReviewerList;
        if (StringUtils.isNotBlank(autCode)) {
            hospitalReviewerList = hospitalReviewerMapper.selectHospitalReviewerByAutCode(autCode);
        } else {
            hospitalReviewerList = hospitalReviewerMapper.selectHospitalReviewerByApplyNo(applyNo);
        }
        if (CollectionUtil.isEmpty(hospitalReviewerList)) {
            return res;
        }
        // 判断是否分配将资深评审员 把资深评审员的accountId放入集合
        List<String> seniorAccountIdList = hospitalReviewerList.stream()
                .filter(hospitalReviewer ->
                        Constants.HospitalConstants.SENIOR_REVIEW.equals(hospitalReviewer.getFieldIdList()))
                .map(HospitalReviewer::getReviewerId).collect(Collectors.toList());

        // 评审学员列表
        Set<String> traAccountIdSet = hospitalReviewerList.stream()
                .filter(hospitalReviewer ->
                        Constants.HospitalConstants.TRAINEES_REVIEW.equals(hospitalReviewer.getFieldIdList()))
                .map(HospitalReviewer::getReviewerId).collect(Collectors.toSet());

        // 将是组长的评审员id放入一个集合
        List<String> leaderAccountIdList = hospitalReviewerList.stream()
                .filter(hospitalReviewer -> Constants.HospitalConstants.NUM_1.equals(hospitalReviewer.getLeaderIs()))
                .map(HospitalReviewer::getReviewerId).collect(Collectors.toList());

        List<HosPlanUserInfoVO> hosPlanUserInfoVOList = hospitalReviewerMapper.selectHosPlanUserInfoByAccountId(hospitalReviewerList);
        // 防止sql一致导致走缓存生成的对象一致，使用序列化生成新的对象
        List<HosPlanUserInfoVO> hosPlanUserInfoVOs = JSONObject.parseArray(JSONObject.toJSONString(hosPlanUserInfoVOList), HosPlanUserInfoVO.class);
        hosPlanUserInfoVOs.forEach(hosPlanUserInfoVO -> {
            if (leaderAccountIdList.contains(hosPlanUserInfoVO.getAccountId())) {
                hosPlanUserInfoVO.setLeaderIs(1);
            } else {
                hosPlanUserInfoVO.setLeaderIs(2);
            }
        });

        res.put("review", hosPlanUserInfoVOs);
        if (CollectionUtil.isNotEmpty(seniorAccountIdList)) {
            List<HosPlanUserInfoVO> seniorReviewList = hosPlanUserInfoVOs.stream()
                    .filter(hosPlanUserInfoVO -> seniorAccountIdList.contains(hosPlanUserInfoVO.getAccountId()))
                    .collect(Collectors.toList());
            res.put("seniorReview", seniorReviewList);
            // 将资深评审员从评审员列表中删除
            if (CollectionUtil.isNotEmpty(seniorReviewList)) {
                hosPlanUserInfoVOs.removeAll(seniorReviewList);
            }
        }

        if (CollectionUtil.isNotEmpty(traAccountIdSet)) {
            List<HosPlanUserInfoVO> traineesList = hosPlanUserInfoVOs.stream()
                    .filter(hosPlanUserInfoVO -> traAccountIdSet.contains(hosPlanUserInfoVO.getAccountId()))
                    .collect(Collectors.toList());
            res.put("traReview", traineesList);
            // 将评审学员从评审员列表中删除
            if (CollectionUtil.isNotEmpty(traineesList)) {
                hosPlanUserInfoVOs.removeAll(traineesList);
            }
        }
        return res;
    }

    @Override
    public List<Map<String, Object>> selectReviewerUserInfoByApplyNo(String applyNo) {

        return hospitalReviewerMapper.selectReviewerUserInfoByApplyNo(applyNo);
    }

    /**
     * 查询医疗结构认证信息与评审员信息
     *
     * @param id 医疗结构认证信息与评审员信息主键
     * @return 医疗结构认证信息与评审员信息
     */
    @Override
    public HospitalReviewer selectHospitalReviewerById(Long id) {
        return hospitalReviewerMapper.selectHospitalReviewerById(id);
    }

    /**
     * 查询医疗结构认证信息与评审员信息列表
     *
     * @param hospitalReviewer 医疗结构认证信息与评审员信息
     * @return 医疗结构认证信息与评审员信息
     */
    @Override
    public List<HospitalReviewer> selectHospitalReviewerList(HospitalReviewer hospitalReviewer) {
        return hospitalReviewerMapper.selectHospitalReviewerList(hospitalReviewer);
    }

    /**
     * 新增医疗结构认证信息与评审员信息
     *
     * @param hospitalReviewer 医疗结构认证信息与评审员信息
     * @return 结果
     */
    @Override
    public int insertHospitalReviewer(HospitalReviewer hospitalReviewer) {
        hospitalReviewer.setCreateTime(DateUtils.getNowDate());
        return hospitalReviewerMapper.insertHospitalReviewer(hospitalReviewer);
    }

    /**
     * 修改医疗结构认证信息与评审员信息
     *
     * @param hospitalReviewer 医疗结构认证信息与评审员信息
     * @return 结果
     */
    @Override
    public int updateHospitalReviewer(HospitalReviewer hospitalReviewer) {
        hospitalReviewer.setUpdateTime(DateUtils.getNowDate());
        return hospitalReviewerMapper.updateHospitalReviewer(hospitalReviewer);
    }


    @Override
    public int updateInterestFileId(HospitalReviewer hospitalReviewer) {
        int res = updateHospitalReviewer(hospitalReviewer);
        // 需要判断对应的id下的applyNo医疗机构评审记录的
        checkInterestFileIsFull(hospitalReviewer.getApplyNo());
        return res;
    }

    @Override
    public void checkInterestFileIsFull(String applyNo) {
        // 利益冲突评审记录是否已经全部提交完
        List<HospitalReviewer> hospitalReviewerList = hospitalReviewerMapper.selectCheckInterestFile(applyNo);
        if (CollUtil.isEmpty(hospitalReviewerList)) {
            return;
        }
        // 如果不为空则判断里面的所有利益冲突是否都提交
        boolean interestFlag = true;
        for (HospitalReviewer reviewer : hospitalReviewerList) {
            if (CharSequenceUtil.isEmpty(reviewer.getInterestFileId())) {
                interestFlag = false;
            }
        }
        if (interestFlag) {
            // 如果已经全部提交完成 则需要改变评审计划列表的中的对应评审员的状态
            HospitalPlannedDistribution hospitalPlannedDistribution = new HospitalPlannedDistribution();
            hospitalPlannedDistribution.setApplyNo(applyNo);
            hospitalPlannedDistribution.setReviewDisComplete(Constants.HospitalConstants.NUM_4);
            hospitalPlannedDistributionService.updateHospitalPlannedDistributionByApplyNo(hospitalPlannedDistribution);

            //全部提交完成且节点为030101时,翻转节点
            String autStatus = autSaRelationMapper.selectAutSaRelationByHospitalApplyNo(applyNo);
            if (StringUtils.isNotBlank(autStatus) &&
                    AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autStatus, AutSaAudStatusEnum.BENEFIT_CONFLICT_DECLARE)) {
                //入参
                //{"time":*************,"autCode":"*****************","accountId":"1","submitType":"node_filp","statusProcess":"PS",
                // "autSaAudLists":[{"autResult":1,"autDesc":"--"}]}
                AutSaAudSaveDTO autSaAudSaveDTO = new AutSaAudSaveDTO();
                autSaAudSaveDTO.setAutCode(hospitalReviewerList.get(0).getAutCode());
                autSaAudSaveDTO.setAccountId(SecurityUtils.getUserId().toString());
                autSaAudSaveDTO.setSubmitType(AutSaAudSubmitTypeEnum.NODE_FILP.getSubmitType());
                autSaAudSaveDTO.setStatusProcess(StatusProcessEnum.PS.getCode());
                List<AutSaAud> autSaAudLists = new ArrayList<>();
                AutSaAud autSaAud = new AutSaAud();
                autSaAud.setAutResult(Constants.STR_NUM_1);
                autSaAud.setAutDesc("--");
                autSaAudLists.add(autSaAud);
                autSaAudSaveDTO.setAutSaAudLists(autSaAudLists);
                baseProcessService.process(autSaAudSaveDTO);
            }

        }
    }

    @Override
    public FileInfoVO uploadAdminInterestFile(MultipartFile file, String applyNo, String reviewerId, String downLoadFileName) {
        HospitalReviewer hospitalReviewer = new HospitalReviewer();
        hospitalReviewer.setApplyNo(applyNo);
        hospitalReviewer.setReviewerId(reviewerId);
        log.info("管理员利益冲突表单个文件上传名称:{}", file.getOriginalFilename());
        FileInfoVO fileInfoVO = commonService.uploadFile(file, "1", "", downLoadFileName);
        if (fileInfoVO == null || StringUtils.isBlank(fileInfoVO.getFileId())) {
            throw new ServiceException("管理员上传利益冲突表文件失败");
        }
        hospitalReviewer.setAdminInterestFileId(fileInfoVO.getFileId());
        hospitalReviewerMapper.updateHospitalReviewerByApplyNo(hospitalReviewer);
        return fileInfoVO;
    }

    @Override
    public int urgentUpdateReviewer(HospitalReviewer hospitalReviewer) {
        if(StringUtils.isBlank(hospitalReviewer.getAutCode())){
            throw new ServiceException("autCode自评编码不能为空");
        }
        if(hospitalReviewer.getId() == null){
            throw new ServiceException("自增id不能为空");
        }
        if(StringUtils.isBlank(hospitalReviewer.getReviewerId())){
            throw new ServiceException("评审员id不能为空");
        }
        if(hospitalReviewer.getHasInterest() == null){
            hospitalReviewer.setHasInterest(0);
        }

        AutSaRelation qryAutSaRelation = new AutSaRelation();
        qryAutSaRelation.setAutCode(hospitalReviewer.getAutCode());
        qryAutSaRelation.setStatus(Constants.INT_ONE);
        List<AutSaRelation> autSaRelations = autSaRelationMapper.selectAutSaRelationListByCondition(qryAutSaRelation);
        if (CollectionUtil.isEmpty(autSaRelations) || autSaRelations.size() != 1 ){
            log.error("获取当前节点信息错误,为空或大于1条；查询条件：{}，查询结果：{}", JSON.toJSONString(qryAutSaRelation),JSON.toJSONString(autSaRelations));
            throw new ServiceException("获取当前节点信息错误！");
        }
        if(autSaRelations.get(0).getAutStatus() == null || !AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(
                autSaRelations.get(0).getAutStatus(),AutSaAudStatusEnum.WAIT_SR_CLAUSE)){
            throw new ServiceException("仅支持待现场评审时进行【紧急更换】，操作失败！");
        }

      return this.updateHospitalReviewer(hospitalReviewer);
    }

    @Override
    public List<HospitalReviewerVo> selectHospitalReviewerByTraineesAssessorReviewerId(String reviewerId) {
        return hospitalReviewerMapper.selectHospitalReviewerByTraineesAssessorReviewerId(reviewerId);
    }

    /**
     * 批量删除医疗结构认证信息与评审员信息
     *
     * @param ids 需要删除的医疗结构认证信息与评审员信息主键
     * @return 结果
     */
    @Override
    public int deleteHospitalReviewerByIds(Long[] ids) {
        return hospitalReviewerMapper.deleteHospitalReviewerByIds(ids);
    }

    /**
     * 删除医疗结构认证信息与评审员信息信息
     *
     * @param id 医疗结构认证信息与评审员信息主键
     * @return 结果
     */
    @Override
    public int deleteHospitalReviewerById(Long id) {
        return hospitalReviewerMapper.deleteHospitalReviewerById(id);
    }

    @Override
    public HospitalReviewer selectHospitalReviewerByLeader(String applyNo, Integer leaderIs) {
        return hospitalReviewerMapper.selectHospitalReviewerByLeader(applyNo, leaderIs);
    }

    @Override
    public HospitalReviewer selectHosRevByAlnAndAct(String applyNo, String accountId) {
        return hospitalReviewerMapper.selectHosRevByAlnAndAct(applyNo, accountId);
    }

    @Override
    public void insertOrUpdateHospitalReviewer(HospitalReviewer req) {
        // 获取当前登录用户
        Long userId = SecurityUtils.getUserId();
//        HospitalReviewer query = new HospitalReviewer();
//        query.setApplyNo(hospitalReviewer.getApplyNo());
//        query.setReviewerId(hospitalReviewer.getReviewerId());
//        query.setStatus(Constants.HospitalConstants.NUM_1);
//        List<HospitalReviewer> hospitalReviewerList = selectHospitalReviewerList(query);
//        if (CollUtil.isNotEmpty(hospitalReviewerList)) {
//            String fieldIdList = hospitalReviewer.getFieldIdList();
//            Integer leaderIs = hospitalReviewer.getLeaderIs();
//            hospitalReviewer = hospitalReviewerList.get(0);
//            if (CharSequenceUtil.isNotEmpty(fieldIdList)) {
//                String newFieldList = hospitalReviewer.getFieldIdList() + "," + fieldIdList;
//                hospitalReviewer.setFieldIdList(newFieldList);
//            }
//            hospitalReviewer.setLeaderIs(leaderIs);
//            hospitalReviewer.setUpdater(String.valueOf(userId));
//            updateHospitalReviewer(hospitalReviewer);
//        } else {
//            //插入时，添加自评编码
//            if(StringUtils.isBlank(hospitalReviewer.getAutCode())){
//                throw new ServiceException("分配评审员提交时，自评编码不能为空！");
//            }
//            hospitalReviewer.setCreator(String.valueOf(userId));
//            hospitalReviewer.setUpdater(String.valueOf(userId));
//            insertHospitalReviewer(hospitalReviewer);
//        }

        //分配评审员和确认组长时，触发此方法
        //1-1.查询当前医院评审员关联数据，获取各评审员分组数据
        //1-2.遍历各评审员的分组，如果有和入参相同的分组被完全取代就删除，取代一部分就更新，没有变化就不操作
        //2.入参无论如何都是新增，查询分更新和删除两组数据，合计好后统一操作
        List<HospitalReviewer> updateList = new ArrayList<>();
        List<HospitalReviewer> delList = new ArrayList<>();
        HospitalReviewer query = new HospitalReviewer();
        query.setApplyNo(req.getApplyNo());
        query.setStatus(Constants.HospitalConstants.NUM_1);
        List<HospitalReviewer> hospitalReviewerList = selectHospitalReviewerList(query);

        //3.如果分组入参为空，为【确认组长】操作，只更新组长状态
        if (StringUtils.isBlank(req.getFieldIdList()) && ObjectUtil.equal(req.getLeaderIs(), Constants.INT_ONE)) {
            HospitalReviewer leaderHospitalReviewer = hospitalReviewerList.stream().filter(o -> o.getReviewerId().equals(req.getReviewerId())).collect(Collectors.toList()).get(0);
            leaderHospitalReviewer.setLeaderIs(req.getLeaderIs());
            leaderHospitalReviewer.setUpdater(String.valueOf(userId));
            updateHospitalReviewer(leaderHospitalReviewer);
            return;
        }

        if (StringUtils.isBlank(req.getFieldIdList())) {
            throw new ServiceException("评审分配分组人员时，入参FieldIdList领域列表不能为空");
        }

        if (CollectionUtil.isNotEmpty(hospitalReviewerList)) {
            //刷选只有评审员角色的数据
            hospitalReviewerList = hospitalReviewerList.stream().filter(o ->
                    ObjectUtil.notEqual(o.getFieldIdList(), Constants.HospitalConstants.SENIOR_REVIEW) &&
                            ObjectUtil.notEqual(o.getFieldIdList(), Constants.HospitalConstants.TRAINEES_REVIEW)
            ).collect(Collectors.toList());
            //入参分组Id
            List<String> reqFieldIdList = Arrays.asList(req.getFieldIdList().split(","));
            //兼容测试环境一个评审员分配多个组场景
            hospitalReviewerList.forEach(o -> {
                List<String> qryFieldIdList = Arrays.asList(o.getFieldIdList().split(","));
                //相同的分组
                List<String> sameFieldId = new ArrayList<>();
                reqFieldIdList.forEach(reqFieldId -> {
                    qryFieldIdList.forEach(qryFieldId -> {
                        if (reqFieldId.equals(qryFieldId)) {
                            sameFieldId.add(qryFieldId);
                        }
                    });
                });
                //查询的数据不为空时，
                //更新：有相同的数据 且 查询的数据数量不等于相同
                //删除：有相同的数据 且 查询的数据等于相同数据
                //不操作：没有相同数据，不更新也不删除
                if (CollectionUtils.isNotEmpty(sameFieldId)) {
                    if (qryFieldIdList.size() != sameFieldId.size()) {
                        //更新
                        List<String> updateFieldIdList = new ArrayList<>(qryFieldIdList);
                        updateFieldIdList.removeAll(sameFieldId);
                        o.setFieldIdList(String.join(",", updateFieldIdList));
                        updateList.add(o);
                    } else {
                        //删除
                        delList.add(o);
                    }
                }
            });
        }

        //新增
        //插入时，添加自评编码
        if (StringUtils.isBlank(req.getAutCode())) {
            throw new ServiceException("分配评审员提交时，自评编码不能为空！");
        }
        req.setCreator(String.valueOf(userId));
        req.setUpdater(String.valueOf(userId));
        insertHospitalReviewer(req);

        if (CollectionUtil.isNotEmpty(updateList)) {
            updateList.forEach(this::updateHospitalReviewer);
        }
        if (CollectionUtil.isNotEmpty(delList)) {
            deleteHospitalReviewerByIds(delList.stream().map(HospitalReviewer::getId).toArray(Long[]::new));
        }

    }

    /**
     * 根据评审员账户id获取关联的医疗机构编码
     *
     * @param reviewerId   评审员账户id
     * @param roleType 评审角色标识
     * @return 结果
     */
    @Override
    public List<HospitalBaseInfo> selectApplyNosByReviewerId(String reviewerId, String roleType) {
        return hospitalReviewerMapper.selectApplyNosByReviewerId(reviewerId, roleType);
    }

    @Override
    public List<ReviewManageVO> queryReviewManage(ReviewManageDTO reviewManageDTO) {
        return hospitalReviewerMapper.queryReviewManage(reviewManageDTO);
    }

    @Override
    public int updateHospitalReviewerByApplyNo(HospitalReviewer hospitalReviewer) {
        return hospitalReviewerMapper.updateHospitalReviewerByApplyNo(hospitalReviewer);
    }

    @Override
    public List<SysUserBaseInfo> selectSeniorReviewerList(String roleKey) {

        return hospitalReviewerMapper.selectSeniorReviewerList(roleKey);
    }

    @Override
    public List<String> selectHospitalReviewerIdsByApplyNo(String applyNo) {
        return hospitalReviewerMapper.selectHospitalReviewerReviewerIdsByApplyNo(applyNo);
    }

    @Override
    public List<ReviewInterestVO> selectReviewInterestVOByAccountId(String accountId) {
        return hospitalReviewerMapper.selectReviewInterestVOByAccountId(accountId);
    }

    @Autowired
    private HospitalPlannedDistributionMapper hospitalPlannedDistributionMapper;

    @Override
    public AjaxResult delHosReviewer(DelHosRevDTO delHosRevDTO) {
        List<String> accountIdList = delHosRevDTO.getAccountIdList();
        String applyNo = delHosRevDTO.getApplyNo();
        hospitalReviewerMapper.deleteHospitalReviewerByReviewerIdsAndApplyNo(accountIdList, applyNo);
        // 修改评审计划列表中的评审员分配状态
        HospitalPlannedDistribution hospitalPlannedDistribution = new HospitalPlannedDistribution();
        hospitalPlannedDistribution.setApplyNo(applyNo);
        hospitalPlannedDistribution.setReviewDisComplete(Constants.HospitalConstants.NUM_2);
        int res = hospitalPlannedDistributionMapper.updateHospitalPlannedDistributionByApplyNo(hospitalPlannedDistribution);
        return AjaxResult.success(res);
    }
}
