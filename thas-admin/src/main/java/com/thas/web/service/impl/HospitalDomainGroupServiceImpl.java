package com.thas.web.service.impl;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.thas.common.enums.ServiceExceptionEnum;
import com.thas.common.exception.ServiceException;
import com.thas.generator.util.NumberGenUtils;
import com.thas.web.domain.CstDomainVO;
import com.thas.web.domain.vo.DomainGroupNode;
import com.thas.web.mapper.HospitalDomainGroupMapper;
import com.thas.web.service.ICstDomainService;
import com.thas.web.service.IHospitalDomainGroupService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 领域对应分组Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-04-22
 */
@Slf4j
@Service
public class HospitalDomainGroupServiceImpl implements IHospitalDomainGroupService {

    @Autowired
    private HospitalDomainGroupMapper hospitalDomainGroupMapper;

    @Autowired
    private ICstDomainService cstDomainService;

    @Override
    public void insertHospitalDomainGroup(CstDomainVO cstDomainVO) {
        // 添加完领域之后，会生成对应的自增id，这个id作为下一层主题的parent_id
        String id = String.valueOf(cstDomainVO.getId());
        //String id =  NumberGenUtils.genParentId();

        // 层级中包含主题，主题下包含小主题，小主题包含条款项，递归解析生成parent_id放到一个容器中
        List<DomainGroupNode> insertList = new ArrayList<>();
        List<DomainGroupNode> DomainGroupNodeList = cstDomainVO.getChildren();
        // 递归生成groupId和将生成的groupId作为子数据的父id
        genParentId(DomainGroupNodeList, insertList, id);
        // 批量插入数据库
        int res = hospitalDomainGroupMapper.batchInsertByDomainGroupNode(insertList);
        if (res <= 0) {
            throw new ServiceException(ServiceExceptionEnum.CST_VERSIONING_ERROR_1000008);
        }
        // 添加完成之后更新cst_domain表中的group_id_list字段
        int i = cstDomainService.saveGroupIdListById(insertList, cstDomainVO.getId());
        if (i <= 0) {
            throw new ServiceException(ServiceExceptionEnum.CST_VERSIONING_ERROR_1000008);
        }
    }

    @Override
    public void updateHospitalDomainGroup(CstDomainVO cstDomainVO) {
        // 修改当前领域的分组信息，先删除原有的信息，再添加。
        List<DomainGroupNode> DomainGroupNodeList = cstDomainVO.getChildren();
        if (CollectionUtil.isEmpty(DomainGroupNodeList)) {
            // 如果为空则直接返回即可。
            return;
        }
        String id = String.valueOf(cstDomainVO.getId());

        // 需要添加新生成的对象
        List<DomainGroupNode> insertList = new ArrayList<>();
        // 递归收集需要添加的对象集合
        genParentId(DomainGroupNodeList, insertList, id);

        // 需要删除的id集合
        List<String> delGroupIds = cstDomainVO.getDelGroupIds();
        log.info("需要删除的分组详情数量:{}", delGroupIds.size());
        hospitalDomainGroupMapper.deleteHospitalDomainGroupByGroupIds(delGroupIds);
        // 通过insertList批量添加
        log.info("通过insertList批量添加数量:{}", insertList.size());
        int res = hospitalDomainGroupMapper.batchInsertByDomainGroupNode(insertList);
        if (res <= 0) {
            throw new ServiceException(ServiceExceptionEnum.CST_VERSIONING_ERROR_1000009);
        }
        // 添加完成之后更新cst_domain表中的group_id_list字段
        int i = cstDomainService.saveGroupIdListById(insertList, cstDomainVO.getId());
        if (i <= 0) {
            throw new ServiceException(ServiceExceptionEnum.CST_VERSIONING_ERROR_1000009);
        }
    }

    /**
     * 递归收集树结构中的所有group_id放到一个集合中
     *
     * @param domainGroupNodeList 树
     * @param delGroupIds         集合
     */
    @Override
    public void genDelGroupIds(List<DomainGroupNode> domainGroupNodeList, List<String> delGroupIds) {
        if (CollectionUtil.isEmpty(domainGroupNodeList)) {
            return;
        }
        for (DomainGroupNode domainGroupNode : domainGroupNodeList) {
            genDelGroupIds(domainGroupNode.getChildren(), delGroupIds);
            String groupId = domainGroupNode.getGroupId();
            if (StrUtil.isNotEmpty(groupId)) {
                delGroupIds.add(groupId);
            }
        }
    }

    /**
     * 递归生成group_id，当前生成的group_id作为子节点的parent_id
     * 并且将树拆成一个一个对象放到容器中，方便批量插入操作
     *
     * @param DomainGroupNodeList 树
     * @param insertList          添加集合
     * @param parentId            parent_id
     */
    private void genParentId(List<DomainGroupNode> DomainGroupNodeList, List<DomainGroupNode> insertList, String parentId) {
        if (CollectionUtil.isEmpty(DomainGroupNodeList)) {
            return;
        }
        for (DomainGroupNode domainGroupNode : DomainGroupNodeList) {
            // 生成一个唯一id
            String id = NumberGenUtils.genParentId();
            domainGroupNode.setGroupId(id);
            domainGroupNode.setParentId(parentId);
            // 递归生成
            genParentId(domainGroupNode.getChildren(), insertList, id);
            insertList.add(domainGroupNode);
        }
    }

    /**
     * 查询领域对应对组中所有主题 小主题 条款id的数据
     *
     * @param domainIds 领域主键id 传空则查询所有
     * @param versionId 版本号
     */
    @Override
    public List<DomainGroupNode> selectDomainGroup(String domainIds, String versionId) {
        // 使用有效版本号去去领域表中查询对应的id
        // 如果domainId为空则查询所有的，不为空则查询对应的。
        List<DomainGroupNode> domainGroupNodes = cstDomainService.selectCstDomainGroup(domainIds, versionId);
        // 组装数据
        this.assembleGroupInfo(domainGroupNodes);
        return domainGroupNodes;
    }

    /**
     * 通过领域cst_domain中的分组信息去组装hospital_domain_group表中的
     * 大主题小主题条款等信息
     */
    @Override
    public void assembleGroupInfo(List<DomainGroupNode> domainGroupNodes) {
        if (CollectionUtil.isEmpty(domainGroupNodes)) {
            return;
        }
        // 查询出对应的分组中所有的数据 速度慢可以使用缓存
        log.info("领域分组详情组装开始，领域数量:{}", domainGroupNodes.size());
        long start = Instant.now().toEpochMilli();
        StringBuilder groupIdList = new StringBuilder();
        for (DomainGroupNode domainGroupNode : domainGroupNodes) {
            groupIdList.append(domainGroupNode.getGroupIdList());
            groupIdList.append(",");
        //    domainGroupNode.setGroupIdList(null);
        }
        groupIdList.deleteCharAt(groupIdList.length() - 1);
        List<DomainGroupNode> domainGroupNodeList = hospitalDomainGroupMapper.selectHospitalDomainGroupAll(groupIdList.toString());
        for (DomainGroupNode domainGroupNode : domainGroupNodes) {
            domainGroupNode.setChildren(groupToTree(domainGroupNodeList, domainGroupNode.getGroupId()));
        }
        long end = Instant.now().toEpochMilli();
        log.info("领域分组详情组装结束，耗时:{} ms", (end - start));
    }

    private List<DomainGroupNode> groupToTree(List<DomainGroupNode> domainGroupNodeList, String parentId) {
        return domainGroupNodeList.stream()
                .filter(hospitalDomainGroup -> hospitalDomainGroup.getParentId().equals(parentId))
                .peek(hospitalDomainGroup -> hospitalDomainGroup.setChildren(groupToTree(domainGroupNodeList, hospitalDomainGroup.getGroupId())))
                .collect(Collectors.toList());
    }
}
