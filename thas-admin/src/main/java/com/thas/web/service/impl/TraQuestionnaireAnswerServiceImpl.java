package com.thas.web.service.impl;


import cn.hutool.core.bean.BeanUtil;
import com.thas.common.constant.Constants;
import com.thas.common.enums.TraQuestionnaireFeedBackTypeEnum;
import com.thas.common.exception.ServiceException;
import com.thas.common.utils.SecurityUtils;
import com.thas.system.domain.vo.UserVo;
import com.thas.system.mapper.SysUserMapper;
import com.thas.system.service.ISysRoleService;
import com.thas.web.domain.TraQuestionnaireAnswer;
import com.thas.web.domain.TraQuestionnaireFeedBack;
import com.thas.web.domain.vo.TraQuestionnaireAnswerRes;
import com.thas.web.domain.vo.TraQuestionnaireAnswerVO;
import com.thas.web.mapper.TraQuestionnaireAnswerDetailsMapper;
import com.thas.web.mapper.TraQuestionnaireAnswerMapper;
import com.thas.web.mapper.TraQuestionnaireFeedBackMapper;
import com.thas.web.service.ITraQuestionnaireAnswerService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 调查问卷答卷Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-02-14
 */
@Service
public class TraQuestionnaireAnswerServiceImpl implements ITraQuestionnaireAnswerService {
    @Autowired
    private TraQuestionnaireAnswerMapper traQuestionnaireAnswerMapper;

    @Autowired
    private TraQuestionnaireAnswerDetailsMapper questionnaireAnswerDetailsMapper;

    @Autowired
    private ISysRoleService iSysRoleService;
    @Autowired
    private TraQuestionnaireFeedBackMapper traQuestionnaireFeedBackMapper;
    @Autowired
    private SysUserMapper sysUserMapper;


    /**
     * 查询调查问卷答卷
     *
     * @return 调查问卷答卷
     */
    @Override
    public TraQuestionnaireAnswerVO selectTraQuestionnaireAnswerById(String questionnaireId, String answerId) {
        String createId = null;
        if (!iSysRoleService.isAdmin()) {
            createId = SecurityUtils.getUsername();
        }
        TraQuestionnaireAnswerVO traQuestionnaireAnswerVOS = traQuestionnaireAnswerMapper.selectTraQuestionnaireAnswerById(questionnaireId, answerId, createId);
        return traQuestionnaireAnswerVOS;
    }

    /**
     * 查询调查问卷答卷列表
     *
     * @param traQuestionnaireAnswer 调查问卷答卷
     * @return 调查问卷答卷
     */
    @Override
    public List<TraQuestionnaireAnswerRes> selectTraQuestionnaireAnswerList(TraQuestionnaireAnswerRes traQuestionnaireAnswer) {
        List<TraQuestionnaireAnswerRes> traQuestionnaireAnswerResList = new ArrayList<>();
        //反馈问卷类型
        if (TraQuestionnaireFeedBackTypeEnum.getFeedBackTypeCodes().contains(Math.toIntExact(traQuestionnaireAnswer.getType()))) {
            if (traQuestionnaireAnswer.getQuestionnaireId() == null) {
                throw new ServiceException("调查问卷id不能为空!");
            }
            TraQuestionnaireFeedBack traQuestionnaireFeedBack = new TraQuestionnaireFeedBack();
            traQuestionnaireFeedBack.setFeedBackType(traQuestionnaireAnswer.getType());
            traQuestionnaireFeedBack.setQuestionnaireId(traQuestionnaireAnswer.getQuestionnaireId());
            List<TraQuestionnaireFeedBack> traQuestionnaireFeedBacks = traQuestionnaireFeedBackMapper.selectTraQuestionnaireFeedBackList(traQuestionnaireFeedBack);
            if (CollectionUtils.isNotEmpty(traQuestionnaireFeedBacks)) {
                List<String> userIds = traQuestionnaireFeedBacks.stream().map(o -> String.valueOf(o.getUserId())).distinct().collect(Collectors.toList());
                List<UserVo> userVos = sysUserMapper.selectUserByIds(userIds);
                if (CollectionUtils.isEmpty(userVos)) {
                    throw new ServiceException("查询用户信息为空!");
                }
                Map<Long, String> userMap = userVos.stream().collect(Collectors.toMap(UserVo::getUserId, UserVo::getNickName));
                for (TraQuestionnaireFeedBack o : traQuestionnaireFeedBacks) {
                    TraQuestionnaireAnswerRes traQuestionnaireAnswerRes = new TraQuestionnaireAnswerRes();
                    traQuestionnaireAnswerRes.setType(o.getFeedBackType());
                    traQuestionnaireAnswerRes.setJsonData(o.getJsonData());
                    traQuestionnaireAnswerRes.setId(o.getFeedBackId());
                    traQuestionnaireAnswerRes.setQuestionnaireId(o.getQuestionnaireId());
                    traQuestionnaireAnswerRes.setStatus(Math.toIntExact(o.getFlag()));
                    traQuestionnaireAnswerRes.setCreateId(String.valueOf(o.getUserId()));
                    traQuestionnaireAnswerRes.setCreateBy(userMap.get(o.getUserId()));
                    traQuestionnaireAnswerRes.setCreateTime(o.getUpdateTime());
                    traQuestionnaireAnswerRes.setAutCode(o.getAutCode());
                    traQuestionnaireAnswerResList.add(traQuestionnaireAnswerRes);
                }
            }

        } else {
            List<TraQuestionnaireAnswer> traQuestionnaireAnswers = traQuestionnaireAnswerMapper.selectTraQuestionnaireAnswerList(traQuestionnaireAnswer);
            traQuestionnaireAnswers.forEach(o -> {
                //反馈表的状态是0有效，1失效；问卷表0无效，1有效；  前端判断的是反馈表的状态，这里改问卷表的状态统一下
                o.setStatus(Constants.INT_ZERO == o.getStatus() ? Constants.INT_ONE : Constants.INT_ZERO);
            });
            traQuestionnaireAnswerResList = BeanUtil.copyToList(traQuestionnaireAnswers, TraQuestionnaireAnswerRes.class);
        }

        return traQuestionnaireAnswerResList;
    }

    /**
     * 新增调查问卷答卷
     *
     * @param traQuestionnaireAnswer 调查问卷答卷
     * @return 结果
     */
    @Override
    public int insertTraQuestionnaireAnswer(TraQuestionnaireAnswer traQuestionnaireAnswer) {
        if (traQuestionnaireAnswer.getQuestionnaireId() == 0 || traQuestionnaireAnswer.getQuestionnaireId() == null) {
            throw new ServiceException("调查问卷id不能为空！");
        }
        TraQuestionnaireAnswerVO traQuestionnaireAnswerVO = traQuestionnaireAnswerMapper.selectTraQuestionnaireAnswerById(traQuestionnaireAnswer.getQuestionnaireId().toString(), "", "");
        //答卷只有一份，不能重复提交
        if (ObjectUtils.isNotEmpty(traQuestionnaireAnswerVO)) {
            throw new ServiceException("答卷已提交，请勿重复提交！");
        }
        traQuestionnaireAnswer.setCreateBy(SecurityUtils.getUsername());
        traQuestionnaireAnswer.setUpdateBy(SecurityUtils.getUsername());
        traQuestionnaireAnswer.setCreateId(SecurityUtils.getUsername());
        int id = traQuestionnaireAnswerMapper.insertTraQuestionnaireAnswer(traQuestionnaireAnswer);
        if (CollectionUtils.isNotEmpty(traQuestionnaireAnswer.getData())) {
            traQuestionnaireAnswer.getData().forEach(traQuestionnaireAnswerDetails -> {
                traQuestionnaireAnswerDetails.setQuestionnaireAnswerId((long) id);
                questionnaireAnswerDetailsMapper.insertTraQuestionnaireAnswerDetails(traQuestionnaireAnswerDetails);
            });
        }
        return id;
    }

    @Override
    public int updateStatus(Integer status, Long id) {
        return traQuestionnaireAnswerMapper.updateStatus(status, id);
    }

    @Override
    public int delete(Long id) {
        String createId = SecurityUtils.getUsername();
        return traQuestionnaireAnswerMapper.delete(createId, id);
    }
}
