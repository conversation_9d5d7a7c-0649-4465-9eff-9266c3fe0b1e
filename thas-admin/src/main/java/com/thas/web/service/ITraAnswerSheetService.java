package com.thas.web.service;

import com.thas.web.domain.TraAnswerSheet;
import com.thas.web.domain.dto.TraAnswerSheetDTO;
import com.thas.web.domain.vo.TraAnswerSheetVO;

import java.util.List;

/**
 * 答卷Service接口
 *
 * <AUTHOR>
 * @date 2022-01-27
 */
public interface ITraAnswerSheetService {
    /**
     * 查询答卷
     *
     * @param id 答卷主键
     * @return 答卷
     */
    TraAnswerSheetVO selectTraAnswerSheetById(Long id);

    /**
     * 查询答卷列表
     *
     * @param traAnswerSheet 答卷
     * @return 答卷集合
     */
    List<TraAnswerSheetVO> selectTraAnswerSheetList(TraAnswerSheet traAnswerSheet);

    /**
     * 新增答卷
     *
     * @param traAnswerSheetDTO 答卷
     * @return 结果
     */
    int insertTraAnswerSheet(TraAnswerSheetDTO traAnswerSheetDTO);

    /**
     * 修改答卷
     *
     * @param traAnswerSheetDTO 答卷
     * @return 结果
     */
    int updateTraAnswerSheet(TraAnswerSheetDTO traAnswerSheetDTO);

    /**
     * 批量删除答卷
     *
     * @param ids 需要删除的答卷主键集合
     * @return 结果
     */
    int deleteTraAnswerSheetByIds(Long[] ids);

    /**
     * 删除答卷信息
     *
     * @param id 答卷主键
     * @return 结果
     */
    int deleteTraAnswerSheetById(Long id);
}
