package com.thas.web.service;

import java.util.List;
import com.thas.web.domain.HospitalPlannedDistribution;
import com.thas.web.domain.vo.HospitalPlannedDistributionVo;
import org.apache.ibatis.annotations.Param;

/**
 * 医疗机构分配计划详情Service接口
 *
 * <AUTHOR>
 * @date 2022-01-27
 */
public interface IHospitalPlannedDistributionService
{
    /**
     * 查询医疗机构分配计划详情
     *
     * @param id 医疗机构分配计划详情主键
     * @return 医疗机构分配计划详情
     */
    public HospitalPlannedDistribution selectHospitalPlannedDistributionById(Long id);

    HospitalPlannedDistribution selectHospitalPlannedDistributionByApplyNo(String applyNo);

    /**
     * 查询医疗机构分配计划详情列表
     *
     * @param hospitalPlannedDistribution 医疗机构分配计划详情
     * @return 医疗机构分配计划详情集合
     */
    public List<HospitalPlannedDistribution> selectHospitalPlannedDistributionList(HospitalPlannedDistribution hospitalPlannedDistribution);

    /**
     * 新增医疗机构分配计划详情
     *
     * @param hospitalPlannedDistribution 医疗机构分配计划详情
     * @return 结果
     */
    public int insertHospitalPlannedDistribution(HospitalPlannedDistribution hospitalPlannedDistribution);

    /**
     * 修改医疗机构分配计划详情
     *
     * @param hospitalPlannedDistribution 医疗机构分配计划详情
     * @return 结果
     */
    public int updateHospitalPlannedDistribution(HospitalPlannedDistribution hospitalPlannedDistribution);

    /**
     * 批量删除医疗机构分配计划详情
     *
     * @param ids 需要删除的医疗机构分配计划详情主键集合
     * @return 结果
     */
    public int deleteHospitalPlannedDistributionByIds(Long[] ids);

    /**
     * 删除医疗机构分配计划详情信息
     *
     * @param id 医疗机构分配计划详情主键
     * @return 结果
     */
    public int deleteHospitalPlannedDistributionById(Long id);

    /**
     * 提交审核结果
     * @return
     */
    public int submitAuditResult(HospitalPlannedDistribution hospitalPlannedDistribution);

    void insertOrUpdateHospitalPlannedDistribution(HospitalPlannedDistribution hospitalPlannedDistribution);


    int updateHospitalPlannedDistributionByApplyNo(HospitalPlannedDistribution hospitalPlannedDistribution);


    /**
     * 根据类型查询评审计划信息
     *
     * @param type 类型
     * @return 评审计划信息
     */
    HospitalPlannedDistributionVo selectPlannedDistributionInfoByType(String type);

    int updateHospitalPlannedDistributionByApplyNoList( List<String> applyNoList);
}
