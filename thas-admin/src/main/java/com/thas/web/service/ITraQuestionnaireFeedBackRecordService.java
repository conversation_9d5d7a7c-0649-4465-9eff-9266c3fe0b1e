package com.thas.web.service;

import com.thas.web.domain.TraQuestionnaireFeedBackRecord;

import java.util.List;


/**
 * 反馈问卷填写统计记录Service接口
 * 
 * <AUTHOR>
 * @date 2022-12-12
 */
public interface ITraQuestionnaireFeedBackRecordService 
{
    /**
     * 查询反馈问卷填写统计记录
     * 
     * @param recordId 反馈问卷填写统计记录主键
     * @return 反馈问卷填写统计记录
     */
    public TraQuestionnaireFeedBackRecord selectTraQuestionnaireFeedBackRecordByRecordId(Long recordId);

    /**
     * 查询反馈问卷填写统计记录列表
     * 
     * @param traQuestionnaireFeedBackRecord 反馈问卷填写统计记录
     * @return 反馈问卷填写统计记录集合
     */
    public List<TraQuestionnaireFeedBackRecord> selectTraQuestionnaireFeedBackRecordList(TraQuestionnaireFeedBackRecord traQuestionnaireFeedBackRecord);

    /**
     * 新增反馈问卷填写统计记录
     * 
     * @param traQuestionnaireFeedBackRecord 反馈问卷填写统计记录
     * @return 结果
     */
    public int insertTraQuestionnaireFeedBackRecord(TraQuestionnaireFeedBackRecord traQuestionnaireFeedBackRecord);

    /**
     * 修改反馈问卷填写统计记录
     * 
     * @param traQuestionnaireFeedBackRecord 反馈问卷填写统计记录
     * @return 结果
     */
    public int updateTraQuestionnaireFeedBackRecord(TraQuestionnaireFeedBackRecord traQuestionnaireFeedBackRecord);

    /**
     * 批量删除反馈问卷填写统计记录
     * 
     * @param recordIds 需要删除的反馈问卷填写统计记录主键集合
     * @return 结果
     */
    public int deleteTraQuestionnaireFeedBackRecordByRecordIds(Long[] recordIds);

    /**
     * 删除反馈问卷填写统计记录信息
     * 
     * @param recordId 反馈问卷填写统计记录主键
     * @return 结果
     */
    public int deleteTraQuestionnaireFeedBackRecordByRecordId(Long recordId);
}
