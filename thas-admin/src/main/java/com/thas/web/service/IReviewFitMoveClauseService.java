package com.thas.web.service;

import com.thas.web.domain.ReviewFitMoveClause;
import com.thas.web.domain.dto.MoveClauseRequest;
import com.thas.web.domain.dto.MoveClauseResponse;
import com.thas.web.domain.dto.ReviewClauseMoveRequest;
import com.thas.web.domain.dto.ReviewFitMoveClauseReq;
import com.thas.web.domain.vo.DomainGroupNode;
import com.thas.web.dto.HosPlanUserInfoVO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 测试评审不适用与挪动款Service接口
 *
 * <AUTHOR>
 * @date 2023-03-28
 */
public interface IReviewFitMoveClauseService {
    /**
     * 查询测试评审不适用与挪动款
     *
     * @param id 测试评审不适用与挪动款主键
     * @return 测试评审不适用与挪动款
     */
    public ReviewFitMoveClause selectReviewFitMoveClauseById(Long id);

    /**
     * 查询测试评审不适用与挪动款列表
     *
     * @param reviewFitMoveClause 测试评审不适用与挪动款
     * @return 测试评审不适用与挪动款集合
     */
    public List<ReviewFitMoveClause> selectReviewFitMoveClauseList(ReviewFitMoveClause reviewFitMoveClause);

    /**
     * 新增测试评审不适用与挪动款
     *
     * @param reviewFitMoveClause 测试评审不适用与挪动款
     * @return 结果
     */
    public int insertReviewFitMoveClause(ReviewFitMoveClause reviewFitMoveClause);

    /**
     * 修改测试评审不适用与挪动款
     *
     * @param reviewFitMoveClause 测试评审不适用与挪动款
     * @return 结果
     */
    public int updateReviewFitMoveClause(ReviewFitMoveClause reviewFitMoveClause);

    /**
     * 批量删除测试评审不适用与挪动款
     *
     * @param ids 需要删除的测试评审不适用与挪动款主键集合
     * @return 结果
     */
    public int deleteReviewFitMoveClauseByIds(Long[] ids);

    /**
     * 删除测试评审不适用与挪动款信息
     *
     * @param id 测试评审不适用与挪动款主键
     * @return 结果
     */
    public int deleteReviewFitMoveClauseById(Long id);

    void submitReviewFitMoveClause(ReviewFitMoveClauseReq reviewFitMoveClause);

    /**
     * 当前分组下所有条款置为不适用的大主题id
     *
     * @param autCode     自评编码
     * @param groupIdList 分组id
     * @param versionId   版本id
     * @return 大主题id
     */
    List<String> groupAllNotApplicableTheme(String autCode, List<String> groupIdList, String versionId);

    /**
     * 款项挪动
     *
     * @param reviewClauseMoveRequest 入参
     */
    void clauseMove(ReviewClauseMoveRequest reviewClauseMoveRequest);

    /**
     * 加入挪动逻辑，增加或者减少当前组下的款id
     *
     * @param autCode           自评编码
     * @param hosPlanUserInfoVO 评审员以及其对应的款信息
     */
    Set<String> moveClause(String autCode, HosPlanUserInfoVO hosPlanUserInfoVO);

    /**
     * 加入挪动逻辑，增加或者减少当前组下的款id
     *
     * @param autCode             自评编码
     * @param groupClauseIdSetMap <groupId, Set<clauseId>>
     */
    void moveClause(String autCode, Map<String, Set<String>> groupClauseIdSetMap);

    /**
     * 分组-大主题-小主题-款 加入挪动逻辑
     *
     * @param autCode      自评编码
     * @param domainGroups 分组-大主题-小主题-款 树
     */
    void moveClause(String autCode, List<DomainGroupNode> domainGroups);

    /**
     * 加入不适用逻辑，剔除不适用的款
     *
     * @param autCode             自评编码
     * @param groupClauseIdSetMap <groupId, Set<clauseId>>
     * @return 不适用数
     */
    Integer notApplicable(String autCode, Map<String, Set<String>> groupClauseIdSetMap);

    /**
     * 通过autCode获取当前挪动记录
     *
     * @param moveClauseRequest 入参
     * @return MoveClauseResponse
     */
    MoveClauseResponse moveClauseInfo(MoveClauseRequest moveClauseRequest);
}
