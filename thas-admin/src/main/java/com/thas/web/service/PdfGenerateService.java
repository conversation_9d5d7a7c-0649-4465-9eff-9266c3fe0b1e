package com.thas.web.service;

import com.thas.web.domain.FtlToPdfDTO;
import javax.servlet.http.HttpServletResponse;

/**
 * pdf生成 Service接口
 *
 * <AUTHOR>
 * @date 2022-04-19
 */
public interface PdfGenerateService {

    /**
     * 根据ftl模板生成对应的pdf文件
     *
     * @param ftlToPdfDTO 流程参数
     * @param response    响应
     */
    void ftlToPdf(FtlToPdfDTO ftlToPdfDTO, HttpServletResponse response);

    void bingFtlToPdf(FtlToPdfDTO ftlToPdfDTO, HttpServletResponse response);
}
