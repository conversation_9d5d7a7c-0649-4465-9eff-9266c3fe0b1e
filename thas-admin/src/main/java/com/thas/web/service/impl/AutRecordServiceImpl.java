package com.thas.web.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.thas.common.constant.Constants;
import com.thas.common.core.domain.entity.SysUser;
import com.thas.common.enums.AutSaAudResultEnum;
import com.thas.common.enums.AutSaAudRoleEnum;
import com.thas.common.enums.ServiceExceptionEnum;
import com.thas.common.exception.ServiceException;
import com.thas.common.utils.DateUtils;
import com.thas.common.utils.SecurityUtils;
import com.thas.system.service.ISysRoleService;
import com.thas.system.service.ISysUserService;
import com.thas.web.domain.AutRecord;
import com.thas.web.domain.HospitalBaseInfo;
import com.thas.web.domain.ReviewerBaseInfo;
import com.thas.web.dto.QueryBaseConditionDTO;
import com.thas.web.mapper.AutRecordMapper;
import com.thas.web.mapper.ReviewerBaseInfoMapper;
import com.thas.web.service.IAutRecordService;
import com.thas.web.service.IHospitalBaseInfoService;
import java.util.Date;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 认证记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-17
 */
@Slf4j
@Component
@Transactional(rollbackFor = Exception.class)
public class AutRecordServiceImpl implements IAutRecordService {

    @Autowired
    private AutRecordMapper autRecordMapper;

    @Autowired
    private ReviewerBaseInfoMapper reviewerBaseInfoMapper;

    @Autowired
    private IHospitalBaseInfoService iHospitalBaseInfoService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private ISysRoleService sysRoleService;

    /**
     * 提交医疗结构/评审员认证审核信息
     *
     * @param autRecord 认证记录
     * @return 结果
     */
    @Override
    public void submitAutRecord(AutRecord autRecord) {
        //管理员防重校验,当前医院认证状态不为待审核时报错，证明已有管理员审核
        if(sysRoleService.isAdmin()){
            QueryBaseConditionDTO queryBaseConditionDTO = new QueryBaseConditionDTO();
            queryBaseConditionDTO.setCommonId(autRecord.getAccountId());
            HospitalBaseInfo hospitalBaseInfo = iHospitalBaseInfoService.selectHospitalByApplyNo(queryBaseConditionDTO);
            //'认证状态:1待审核 2审核通过 3审核拒绝'
            if(ObjectUtil.isNotEmpty(hospitalBaseInfo) && ObjectUtil.notEqual(hospitalBaseInfo.getAuthStatus(),Constants.INT_ONE)){
                log.error("其他管理员已审核，请刷新页面查看,查询的数据为：{}",JSON.toJSONString(hospitalBaseInfo));
                throw new ServiceException("其他管理员已审核，请刷新页面查看");
            }
        }

        // 入参的accountId 可能是评审员userid(数字类型) 也可能是医疗机构编码（年月日+6位随机数）
        if (AutSaAudRoleEnum.HOSPITAL.getCode().intValue() == autRecord.getRole()) {
            // 医疗机构角色
            // 如果是医疗结构，则更新医疗结构审核状态
            if (iHospitalBaseInfoService.updateAuthStatusByApplyNo(autRecord) <= 0) {
                throw new ServiceException(ServiceExceptionEnum.HOSPITAL_ERROR_1000008);
            }
        } else if (AutSaAudRoleEnum.ASSESSOR.getCode().intValue() == autRecord.getRole() || AutSaAudRoleEnum.SENIOR_ASSESSOR.getCode().intValue() == autRecord.getRole()) {
            // 角色为评审员 或者 验证评审员
            // this.submitJudgeAutRecord(autRecord);
            // 流程变更走新得逻辑
            updateReviewerAuthStatus(autRecord);
        } else {
            log.info("入参角色：{} 不正确,电当前仅支持医疗机构和评审员/验证评审员操作", autRecord.getRole());
            throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000003);
        }
    }

    private void updateReviewerAuthStatus(AutRecord autRecord) {
        ReviewerBaseInfo reviewerBaseInfo = new ReviewerBaseInfo();
        reviewerBaseInfo.setAccountId(autRecord.getAccountId());
        reviewerBaseInfo.setAuthStatus((AutSaAudResultEnum.AGREE.getCode().intValue() == autRecord.getAudResult()) ? AutSaAudResultEnum.AGREE.getCode() : AutSaAudResultEnum.DISAGREE.getCode());
        reviewerBaseInfo.setSkipFlag(autRecord.getSkipFlag());
        reviewerBaseInfo.setAuthDesc(autRecord.getAudDesc());
        reviewerBaseInfo.setAuthPersonId(autRecord.getAudId());
        reviewerBaseInfo.setAuthPersonName(SecurityUtils.getNickName());
        reviewerBaseInfo.setAuthDate(DateUtil.now());
        reviewerBaseInfoMapper.updateReviewerBaseInfoByAccountId(reviewerBaseInfo);
    }

    /**
     * 提交评审员审核信息
     *
     * @param autRecord
     */
    private void submitJudgeAutRecord(AutRecord autRecord) {
        // 根据id和id类型判断是否有认证记录
        AutRecord hisAutRecord = this.selectAutRecordByAccountId(autRecord.getAccountId());
        if (null == hisAutRecord) {
            // 没有历史记录，说明前序流程 线上学习和线下培训没有完成，本次提交失败
            log.error("审核人员：{} 提交评审员:{}认证审核记录 未完成前序流程", autRecord.getAudId(), autRecord.getAccountId());
            throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000001);
        }
        // 说明已经做过前序流程
        AutRecord updateAutRecord = new AutRecord();
        updateAutRecord.setAccountId(autRecord.getAccountId());
        // 审核人员id
        updateAutRecord.setAudId(autRecord.getAudId());
        // 审核说明
        updateAutRecord.setAudDesc(autRecord.getAudDesc());
        // 审核结果
        updateAutRecord.setAudResult(autRecord.getAudResult());
        // 审核提交时间
        updateAutRecord.setAudSubmitTime(new Date());
        // 认证审核状态 审核人员审核结果通过，则认证通过，不通过则认证不通过
        updateAutRecord.setAutStatus((AutSaAudResultEnum.AGREE.getCode().intValue() == autRecord.getAudResult()) ? AutSaAudResultEnum.AGREE.getCode() : AutSaAudResultEnum.DISAGREE.getCode());
        updateAutRecord.setUpdateTime(DateUtils.getNowDate());
        // 更新审核记录信息
        log.info("评审员:{} 认证审核提交 更新审核数据:{}", autRecord.getAccountId(), JSON.toJSONString(updateAutRecord));
        int result = autRecordMapper.updateAutRecordByCondition(updateAutRecord);
        log.info("评审员:{} 认证审核提交 更新审核数据:{}", autRecord.getAccountId(), result);
        if (result <= 0) {
            // 更新成功 返回更新后的认证信息
            throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000000);
        }
        if (AutSaAudResultEnum.AGREE.getCode().intValue() == autRecord.getAudResult()) {
            // 如果管理员操作评审员审核通过，则将评审员填写得基本信息更新到sys_user表中
            String accountId = autRecord.getAccountId();
            ReviewerBaseInfo reviewerBaseInfo = reviewerBaseInfoMapper.selectReviewerBaseInfoByAccountId(accountId);
            if (Objects.nonNull(reviewerBaseInfo)) {
                SysUser sysUser = new SysUser();
                sysUser.setUserId(Long.valueOf(accountId));
                sysUser.setNickName(reviewerBaseInfo.getReviewerName());
                sysUser.setPhonenumber(reviewerBaseInfo.getReviewerMobile());
                sysUser.setEmail(reviewerBaseInfo.getReviewerEmail());
                Integer gender = reviewerBaseInfo.getReviewerGender();
                String sex;
                if (gender == 1) {
                    sex = "0";
                } else if (gender == 2) {
                    sex = "1";
                } else {
                    sex = "2";
                }
                sysUser.setSex(sex);
                int res = sysUserService.updateUserProfile(sysUser);
                log.info("评审员评审通过修改sys_user结果:{}", res);
            }
        }
    }

    /**
     * 初始化评审员认证数据
     * @param accountId
     */
    private void initAutRecord(String accountId){
        AutRecord initAutRecord = new AutRecord();
        initAutRecord.setAccountId(accountId);
        initAutRecord.setAutStatus(Constants.HospitalConstants.NUM_0);
        initAutRecord.setStatus(Constants.HospitalConstants.NUM_1);
        initAutRecord.setCreateTime(DateUtils.getNowDate());
        log.info("评审员:{} 初始化认证数据:{}", accountId, JSON.toJSONString(initAutRecord));
        int count = autRecordMapper.insertAutRecord(initAutRecord);
        log.info("评审员:{} 初始化认证数据结果:{}", accountId, count);
        if (count <= 0) {
            log.error("评审员:{}  初始化认证数据失败", accountId);
            throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000000);
        }
    }

    /**
     * 查询认证记录
     *
     * @param accountId 评审员id
     * @return 认证记录
     */
    @Override
    public AutRecord selectAutRecordByAccountId(String accountId) {
        return autRecordMapper.selectAutRecordByAccountId(accountId);
    }

    /**
     * 保存评审员认证记录信息（线上学习+线下培训）  --当前只有线上学习，完成所有线上学习答卷才会调用该方法
     * 参数检验注意调用方（类内部调用该方法校验不生效）
     *
     * @param autRecord 认证记录
     * @return 结果
     */
    @Override
    public void saveAutRecord(AutRecord autRecord) {
        String roleKey = sysUserService.getSysUserInfo(autRecord.getAccountId()).getRoleKey();
        if (!AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(roleKey, AutSaAudRoleEnum.ASSESSOR, AutSaAudRoleEnum.SENIOR_ASSESSOR)) {
            log.error("账户:{} 对应角色：{}不是评审员/验证评审员", autRecord.getAccountId(), roleKey);
            throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000003);
        }
        // 删除原有历史认证记录
        autRecordMapper.deleteAutRecordByAccountId(autRecord.getAccountId());
        // 初始化评审员认证数据
        this.initAutRecord(autRecord.getAccountId());
        // 保存成功后，更新提交状态
        reviewerBaseInfoMapper.updateSubmitStatusByAccountId(autRecord.getAccountId());
    }

}
