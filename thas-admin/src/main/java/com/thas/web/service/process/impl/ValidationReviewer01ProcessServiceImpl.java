package com.thas.web.service.process.impl;

import com.thas.common.constant.Constants;
import com.thas.common.constant.Constants.HospitalConstants;
import com.thas.common.enums.AutSaAudStatusEnum;
import com.thas.common.enums.AutSaAudSubmitTypeEnum;
import com.thas.common.utils.StringUtils;
import com.thas.web.domain.AutSaAud;
import com.thas.web.domain.AutSaAudList;
import com.thas.web.domain.AutSaAudQueryDTO;
import com.thas.web.domain.AutSaAudSaveDTO;
import com.thas.web.domain.FileInfoDTO;
import com.thas.web.domain.vo.AutSaAudDetailVO;
import com.thas.web.domain.vo.FileInfoVO;
import com.thas.web.mapper.AutSaAudMapper;
import com.thas.web.service.CommonService;
import com.thas.web.service.IUploadFileInfoService;
import com.thas.web.service.process.BaseProcessService;
import com.thas.web.service.process.CommonProcessService;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 验证评审员评审流程服务 Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-04-01
 */
@Component("validationReviewer01ProcessService")
@Slf4j
public class ValidationReviewer01ProcessServiceImpl implements BaseProcessService {

    @Resource
    private CommonProcessService commonProcessService;

    @Override
    public void process(AutSaAudSaveDTO req) {
        long startTime = System.currentTimeMillis();
        log.info("validationReviewer01ProcessService.process ------ 开始");
        if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(req.getSubmitType(), AutSaAudSubmitTypeEnum.TR_CLAUSE)) {
            // 校验款信息
            commonProcessService.checkClause(req);
            if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.WAIT_TR_CLAUSE)) {
                // 验证评审员待评审
                commonProcessService.processInitialScene(req);
            } else if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.TR_CLAUSE_PROCESS)) {
                // 验证评审员评审中
                commonProcessService.processClauseScene(req);
            }
        } else if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(req.getSubmitType(), AutSaAudSubmitTypeEnum.TR_CLAUSE_CONFIRM)) {
            // 事实准确性审查确认   直接翻转节点
            commonProcessService.processConfirmSkip(req);
        }
        log.info("validationReviewer01ProcessService.process ------ 结束 耗时：{}", System.currentTimeMillis() - startTime);
    }

    @Autowired
    private AutSaAudMapper autSaAudMapper;

    @Autowired
    private CommonService commonService;

    @Autowired
    private IUploadFileInfoService uploadFileInfoService;

    @Override
    public List<AutSaAudList> queryList(AutSaAudQueryDTO req) {
        List<AutSaAudList> autSaAudLists = new ArrayList<>();
        AutSaAudList autSaAudList = new AutSaAudList();

        // 公共反参
        commonProcessService.getGroupProgressList(autSaAudList, req);

        // 查询 AutSaAud
        queryAutSaAud(req, autSaAudList);

        autSaAudLists.add(autSaAudList);

        return commonProcessService.listSorted(autSaAudLists);
    }

    private void queryAutSaAud(AutSaAudQueryDTO req, AutSaAudList autSaAudList) {
        AutSaAud autSaAud = new AutSaAud();
        autSaAud.setAutCode(req.getAutSaRelation().getAutCode());
        autSaAud.setSubmitType(AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SUBMIT.getSubmitType());
        autSaAud.setStatus(HospitalConstants.NUM_1);
        List<AutSaAud> autSaAudLists = autSaAudMapper.selectAutSaAudList(autSaAud);
        if (CollectionUtils.isEmpty(autSaAudLists)) {
            return;
        }

        List<String> farClausemSubmitList = new ArrayList<>();
        StringBuilder ids = new StringBuilder();
        for (AutSaAud saAudList : autSaAudLists) {
            ids.append(saAudList.getFileIds());
            String[] fileIds = saAudList.getFileIds().split(",");
            for (String fileId : fileIds) {
                if (StringUtils.isEmpty(fileId)) {
                    continue;
                }
                farClausemSubmitList.add(fileId);
            }
        }

        if (CollectionUtils.isEmpty(farClausemSubmitList)) {
            return;
        }

        List<FileInfoDTO> fileInfoDTOList = uploadFileInfoService.getUploadFileInfoByIds(ids.toString());
        List<FileInfoVO> fileInfoVOList = commonService.fileInfoDtoToVo(fileInfoDTOList);
        Map<String, List<FileInfoVO>> fileDetailMap = fileInfoVOList.stream().map(o->{
            o.setFileType(AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SUBMIT.getSubmitType());
            return o;
        }).collect(Collectors.groupingBy(FileInfoVO::getFileId));

        autSaAudList.setFarClausemSubmitList(farClausemSubmitList);
        if(MapUtils.isNotEmpty(autSaAudList.getFileDetailMap())){
            fileDetailMap.putAll(autSaAudList.getFileDetailMap());
        }
        autSaAudList.setFileDetailMap(fileDetailMap);
    }

    @Override
    public AutSaAudDetailVO queryDetail(AutSaAudQueryDTO req) {
        return null;
    }

}
