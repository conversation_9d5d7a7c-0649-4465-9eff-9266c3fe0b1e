package com.thas.web.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSON;
import com.thas.common.core.domain.CstCertificationStandardsExcel;
import com.thas.common.core.domain.CstEvaluationCriterionExcel;
import com.thas.common.core.domain.CstVersioningExcel;
import com.thas.common.enums.ImportFileTypeEnum;
import com.thas.common.enums.ServiceExceptionEnum;
import com.thas.common.exception.ServiceException;
import com.thas.common.utils.DateUtils;
import com.thas.common.utils.SecurityUtils;
import com.thas.web.mapper.ImportServiceMapper;
import com.thas.web.service.ImportService;
import java.io.InputStream;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.ValidatorFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 通用导入服务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Transactional(rollbackFor = Exception.class)
public class ImportServiceImpl implements ImportService {

    @Resource
    private ImportServiceMapper importServiceMapper;

    final static String REGULATION_FILE ="regulationFile"; //相关法律法规、规范性文件
    final static String CLAUSE_NO ="clauseNo"; //款数
    final static String INTERNATIONAL_REFERENCE ="internationalReference"; //国际参考文献

    /**
     * 处理导入的文件
     *
     * @param in 输入流
     * @param importFileTypeEnum 导入参数枚举
     * @return
     * @throws Exception
     */
    @Override
    public void importExcel(InputStream in, ImportFileTypeEnum importFileTypeEnum, String title){
        log.info("importExcel ---begin---importFileTypeEnum:{} ", importFileTypeEnum);
        try{
            ExcelReader writer = ExcelUtil.getReader(in);
            Map<String ,List<Object>> result = new HashMap();
            String[] classNamesArr = importFileTypeEnum.getClassName().split(",");
            String[] methodNamesArr = importFileTypeEnum.getMethodName().split(",");
            // 生成对应的版本号 时间戳
            String versionId = DateUtils.dateTimeNow("yyMMdd") + importServiceMapper.selectCstVersioningId();
            List<Sheet> sheets = writer.getSheets();
            for (int i = 0; i < sheets.size(); i++) {
                log.info("当前sheet序号：{} ", i);
                writer.setSheet(sheets.get(i));
                if(i > classNamesArr.length || i > methodNamesArr.length){
                    break;
                }
                // 解析文档保存到表内
                Class clazz = Class.forName(classNamesArr[i]);
//                Field[] fields = clazz.getDeclaredFields();
//                List<String> fieldList = Arrays.stream(fields).map(k -> k.getName()).collect(Collectors.toList());
//                log.info("当前配置实体字段：{} ", String.join(",", fieldList));
//                if(null != fields && fields.length > 0){
//                    for (Field field: fields) {
//                        writer.addHeaderAlias(field.getName().trim(), field.getName().trim());
//                    }
//                }
//                writer.read()
//                List<Object> lists = writer.read(1, 2, clazz);
                ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
                List<Object> lists = (List) writer.read(1, 2, 2147483647).stream().map(a -> {
                    Map<String, String> tranferMap = a.entrySet().stream().collect(
                            Collectors.toMap(
                                    o -> o.getKey().trim(),
                                    o -> (o.getValue() == null) ? "" : o.getValue().toString().trim(),
                                    (aa, bb) -> bb));
                    return this.tranferAndCheckMapToBean(factory, tranferMap, clazz);
                }).collect(Collectors.toList());

                // 将解析出的参数进行落表保存
                Method method = this.getClass().getDeclaredMethod(methodNamesArr[i], List.class, String.class);
                method.invoke(this, lists, versionId);
                log.info("当前sheet -------导入成功------");
            }
            log.info("importExcel ------保存---cst_versioning------");
            // 将当前导入的模板信息保存到 版本管理模板 cst_versioning
            CstVersioningExcel cstVersioning = new CstVersioningExcel();
            cstVersioning.setVersionName(title);
            cstVersioning.setStatus("0");
            cstVersioning.setVersionId(versionId);
            cstVersioning.setUserId(SecurityUtils.getLoginUser().getUserId().toString());
            importServiceMapper.saveCstVersioning(cstVersioning);

            log.info("importExcel ------end------");
        }catch(Exception e){
            log.error("importExcel ---异常---e:{}",e.getMessage());
            throw new ServiceException("importExcel ---异常---e:" + e.getMessage());
        }
    }

    /**
     * 测试-导入-优化内容
     *
     * @param in 输入流
     * @param importFileTypeEnum 导入参数枚举
     * @param response
     * @return
     * @throws Exception
     */
    @Override
    public void testImportExcel(InputStream in, ImportFileTypeEnum importFileTypeEnum, String title, HttpServletResponse response){
        log.info("importExcel ---begin---importFileTypeEnum:{} ", importFileTypeEnum);
        //导出修改好的文件内容
        ExcelWriter export = null;
        ServletOutputStream out= null;

        try{
            ExcelReader writer = ExcelUtil.getReader(in);
            String[] classNamesArr = importFileTypeEnum.getClassName().split(",");
            String[] methodNamesArr = importFileTypeEnum.getMethodName().split(",");
            // 生成对应的版本号 时间戳
            String versionId = DateUtils.dateTimeNow("yyMMdd") + importServiceMapper.selectCstVersioningId();
            List<Sheet> sheets = writer.getSheets();

            export = ExcelUtil.getWriter(true);
            out = response.getOutputStream();


            for (int i = 0; i < sheets.size(); i++) {
                export.setSheet(sheets.get(i).getSheetName());
                log.info("当前sheet序号：{} ", i);
                writer.setSheet(sheets.get(i));
                if(i > classNamesArr.length || i > methodNamesArr.length){
                    break;
                }
                // 解析文档保存到表内
                Class clazz = Class.forName(classNamesArr[i]);
                ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
                List<Object> lists = (List) writer.read(1, 2, 2147483647).stream().map(a -> {
                    Map<String, String> tranferMap = a.entrySet().stream().collect(
                            Collectors.toMap(
                                    o -> o.getKey().trim(),
                                    o -> (o.getValue() == null) ? "" : o.getValue().toString().trim(),
                                    (aa, bb) -> bb));
                    Object checkResultObj = this.tranferAndCheckMapToBean(factory, tranferMap, clazz);

                    //内容优化：拆分到对应款，去掉内容前后空格，拆分后去除序号
                    checkResultObj = contentOptimization(checkResultObj,tranferMap,clazz);
                    return checkResultObj;
                }).collect(Collectors.toList());



                //导出优化的数据
                //设置文件头部内容
                List<List<Object>> readList = writer.read(0, 1);
                List<Object> c = readList.get(0);
                List<Object> e = readList.get(1);
                    for (int j = 0; j < c.size() ; j++) {
                        export.addHeaderAlias(e.get(j)+"",e.get(j)+"");

                    }
                export.setOnlyAlias(true);
                export.writeHeadRow(c);

                // 一次性写出内容，使用默认样式，强制输出标题
                export.write(lists, true);
                //response为HttpServletResponse对象
                response.setContentType("application/vnd.ms-excel;charset=utf-8");
                //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
                response.setHeader("Content-Disposition","attachment;filename=test.xls");
            }

            log.info("importExcel ------end------");
        }catch(Exception e){
            log.error("importExcel ---异常---e:{}",e.getMessage());
            throw new ServiceException("importExcel ---异常---e:" + e.getMessage());

        }finally {
            //最终关闭导出的流
            export.flush(out, true);
            // 关闭writer，释放内存
            export.close();
            //此处记得关闭输出Servlet流
            IoUtil.close(out);
        }
    }

    private Object contentOptimization(Object checkResultObj, Map<String, String> map, Class clazz) {
//        final int[] num = {1};
        if(ObjectUtil.isNotNull(checkResultObj)){

            if(map.containsKey(REGULATION_FILE) && StringUtils.isNotBlank(map.get(REGULATION_FILE))){
                String regulationFile = map.get(REGULATION_FILE);
                List<String> regulationFileList = Arrays.asList(regulationFile.split("\n"));

                if(StringUtils.isNotBlank(map.get(CLAUSE_NO))) {
                    regulationFileList =  regulationFileList.stream().
                            filter(f -> f.contains(map.get(CLAUSE_NO))).
                            map(m -> {
//                                String newRegulationFile = num[0] + "、" + StringUtils.substringAfter(m,"、" );
                                String newRegulationFile = StringUtils.substringAfter(m,"、" );
//                                num[0]++;
                                return newRegulationFile ;
                            }).collect(Collectors.toList());
                    String joinRegulationFile =  StringUtils.join(regulationFileList,"\n");
                    map.put(REGULATION_FILE, joinRegulationFile);
//                    num[0] = 1;
                }else{
                    log.info("标准认证-导入-clauseNo款式值为空");
                }
            }

            if(map.containsKey(INTERNATIONAL_REFERENCE) && StringUtils.isNotBlank(map.get(INTERNATIONAL_REFERENCE))){
                String internationalReference = map.get(INTERNATIONAL_REFERENCE);
                List<String> internationalReferenceList = Arrays.asList(internationalReference.split("\n"));
                if(StringUtils.isNotBlank(map.get(CLAUSE_NO))) {
                    internationalReferenceList =  internationalReferenceList.stream().
                            filter(f -> f.contains(map.get(CLAUSE_NO))).
                            map(m -> {
//                                String newInternationalReference = num[0] + "." + StringUtils.substringAfter(m,"." );
                                String newInternationalReference =  StringUtils.substringAfter(m,"." );
//                                num[0]++;
                                return newInternationalReference ;
                            }).collect(Collectors.toList());
                    String joinInternationalReference =  StringUtils.join(internationalReferenceList,"\n");
                    map.put(INTERNATIONAL_REFERENCE, joinInternationalReference);
//                    num[0] = 1;
                }
            }

           return JSON.parseObject(JSON.toJSONString(map),clazz);
            //map.remove(map.keySet());
        }
        return checkResultObj;
    }

    /**
     * 将Map转为Bean并校验相关数据
     * @param factory
     * @param tranferMap
     * @param clazz
     * @return
     */
    private Object tranferAndCheckMapToBean(ValidatorFactory factory, Map<String, String> tranferMap, Class clazz){
        Object obj = BeanUtil.mapToBean(tranferMap, clazz, false, CopyOptions.create());
        Set<ConstraintViolation<Object>> constraintViolationSet = factory.getValidator().validate(obj);
        if (CollectionUtils.isNotEmpty(constraintViolationSet)) {
            List<String> errorMessages = constraintViolationSet.stream().map(a -> a.getMessage()).collect(Collectors.toList());
            log.error("导入认证标准异常，异常原因：{} ", String.join(",", errorMessages));
            throw new ServiceException(ServiceExceptionEnum.CST_VERSIONING_ERROR_1000001);
        }
        return obj;
    }

    /**
     * 保存认证标准模板 cst_certification_standards
     * @param list
     * @param versionId 版本号
     * @return
     */
    private void saveCstCertificationStandards(List<Object> list, String versionId){
        log.info("saveCstCertificationStandards ---begin---list.size:{} ", list.size());
        if(CollectionUtils.isNotEmpty(list)){
            // 章数列表
            Set<String> chapterList = new HashSet();
            // 节数列表
            Set<String> sectionList = new HashSet();
            // 条数列表
            Set<String> articleList = new HashSet();
            // 设置相关参数默认值
            List<CstCertificationStandardsExcel> cstCertificationStandardsList = list.stream().map(k -> {
                CstCertificationStandardsExcel cstCertificationStandards = (CstCertificationStandardsExcel) k;
                // 版本号
                cstCertificationStandards.setVersionId(versionId);
                // 章id chapterId
                String chapterNo = cstCertificationStandards.getChapterNo();
                if(StringUtils.isNotBlank(chapterNo) && StringUtils.isNotBlank(chapterNo.trim())){
                    chapterList.add(chapterNo.trim());
                    cstCertificationStandards.setChapterId(String.valueOf(chapterList.size()));
                }
                // 节id sectionId
                String sectionNo = cstCertificationStandards.getSectionNo();
                if(StringUtils.isNotBlank(sectionNo) && StringUtils.isNotBlank(sectionNo.trim())){
                    sectionList.add(sectionNo.trim());
                    cstCertificationStandards.setSectionId(String.valueOf(sectionList.size()));
                }
                // 条id articleId
                String articleNo = cstCertificationStandards.getArticleNo();
                if(StringUtils.isNotBlank(articleNo) && StringUtils.isNotBlank(articleNo.trim())){
                    articleList.add(articleNo.trim());
                    cstCertificationStandards.setArticleId(String.valueOf(articleList.size()));
                }
                // 款id clauseId
                cstCertificationStandards.setClauseId(cstCertificationStandards.getId());
                // 状态 status
                cstCertificationStandards.setStatus("1");
                return cstCertificationStandards;
            }).collect(Collectors.toList());

            List<List<CstCertificationStandardsExcel>> partitionList = ListUtils.partition(cstCertificationStandardsList, 1000);
            for (List<CstCertificationStandardsExcel> partition: partitionList) {
                // 落表保存认证标准模板
                importServiceMapper.saveBatchCstCertificationStandards(partition);
            }
        }
        log.info("saveCstCertificationStandards ---end--- ");
    }

    /**
     * 保存 评估标准模板 cst_evaluation_criterion
     * @param list
     * @param versionId 版本号
     * @return
     */
    private void saveCstEvaluationCriterion(List<Object> list, String versionId){
        log.info("saveCstEvaluationCriterion ----begin--- list.size:{} ", list.size());
        if(CollectionUtils.isNotEmpty(list)){
            // 设置相关参数默认值
            // 设置相关参数默认值
            List<CstEvaluationCriterionExcel> cstEvaluationCriterionList = list.stream().map(k -> {
                CstEvaluationCriterionExcel cstEvaluationCriterion = (CstEvaluationCriterionExcel) k;
                // 版本号
                cstEvaluationCriterion.setVersionId(versionId);
                // 状态 status
                cstEvaluationCriterion.setStatus("1");
                return cstEvaluationCriterion;
            }).collect(Collectors.toList());
            List<List<CstEvaluationCriterionExcel>> partitionList = ListUtils.partition(cstEvaluationCriterionList, 1000);
            for (List<CstEvaluationCriterionExcel> partition: partitionList) {
                // 落表保存评估标准模板
                importServiceMapper.saveBatchCstEvaluationCriterion(partition);
            }
        }
        log.info("saveCstEvaluationCriterion ---end--- ");
    }

}
