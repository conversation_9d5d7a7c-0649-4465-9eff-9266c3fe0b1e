package com.thas.web.service;

import java.util.List;
import com.thas.web.domain.HospitalAuthContact;

/**
 * 医疗机构被授权人信息Service接口
 * 
 * <AUTHOR>
 * @date 2022-01-19
 */
public interface IHospitalAuthContactService 
{
    /**
     * 查询医疗机构被授权人信息
     * 
     * @param id 医疗机构被授权人信息主键
     * @return 医疗机构被授权人信息
     */
    public HospitalAuthContact selectHospitalAuthContactById(Long id);

    /**
     * 查询医疗机构被授权人信息列表
     * 
     * @param hospitalAuthContact 医疗机构被授权人信息
     * @return 医疗机构被授权人信息集合
     */
    public List<HospitalAuthContact> selectHospitalAuthContactList(HospitalAuthContact hospitalAuthContact);

    /**
     * 新增医疗机构被授权人信息
     * 
     * @param hospitalAuthContact 医疗机构被授权人信息
     * @return 结果
     */
    public int insertHospitalAuthContact(HospitalAuthContact hospitalAuthContact);

    /**
     * 修改医疗机构被授权人信息
     * 
     * @param hospitalAuthContact 医疗机构被授权人信息
     * @return 结果
     */
    public int updateHospitalAuthContact(HospitalAuthContact hospitalAuthContact);

    /**
     * 批量删除医疗机构被授权人信息
     * 
     * @param ids 需要删除的医疗机构被授权人信息主键集合
     * @return 结果
     */
    public int deleteHospitalAuthContactByIds(Long[] ids);

    /**
     * 删除医疗机构被授权人信息信息
     * 
     * @param id 医疗机构被授权人信息主键
     * @return 结果
     */
    public int deleteHospitalAuthContactById(Long id);
}
