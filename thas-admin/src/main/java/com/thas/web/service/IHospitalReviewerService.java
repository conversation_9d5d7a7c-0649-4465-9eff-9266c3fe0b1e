package com.thas.web.service;

import com.thas.common.core.domain.AjaxResult;
import com.thas.web.domain.HospitalBaseInfo;
import com.thas.web.domain.HospitalReviewer;
import com.thas.web.domain.vo.FileInfoVO;
import com.thas.web.domain.vo.HospitalReviewerVo;
import com.thas.web.domain.vo.ReviewInterestVO;
import com.thas.web.dto.DelHosRevDTO;
import com.thas.web.dto.HosPlanUserInfoVO;
import com.thas.web.dto.ReviewManageDTO;
import com.thas.web.dto.ReviewManageVO;
import com.thas.web.dto.SysUserBaseInfo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 医疗结构认证信息与评审员信息Service接口
 *
 * <AUTHOR>
 * @date 2022-01-25
 */
public interface IHospitalReviewerService {

    Map<String, List<HosPlanUserInfoVO>> selectHosPlanUserInfoByApplyNo(String applyNo, String autCode);

    /**
     * 通过applyNo查询对应评审员信息
     *
     * @param applyNo 医疗机构认证记录编码
     * @return 信息列表
     */
    List<Map<String, Object>> selectReviewerUserInfoByApplyNo(String applyNo);

    /**
     * 查询医疗结构认证信息与评审员信息
     *
     * @param id 医疗结构认证信息与评审员信息主键
     * @return 医疗结构认证信息与评审员信息
     */
    HospitalReviewer selectHospitalReviewerById(Long id);

    /**
     * 查询医疗结构认证信息与评审员信息列表
     *
     * @param hospitalReviewer 医疗结构认证信息与评审员信息
     * @return 医疗结构认证信息与评审员信息集合
     */
    List<HospitalReviewer> selectHospitalReviewerList(HospitalReviewer hospitalReviewer);

    /**
     * 新增医疗结构认证信息与评审员信息
     *
     * @param hospitalReviewer 医疗结构认证信息与评审员信息
     * @return 结果
     */
    int insertHospitalReviewer(HospitalReviewer hospitalReviewer);

    /**
     * 修改医疗结构认证信息与评审员信息
     *
     * @param hospitalReviewer 医疗结构认证信息与评审员信息
     * @return 结果
     */
    int updateHospitalReviewer(HospitalReviewer hospitalReviewer);

    /**
     * 批量删除医疗结构认证信息与评审员信息
     *
     * @param ids 需要删除的医疗结构认证信息与评审员信息主键集合
     * @return 结果
     */
    int deleteHospitalReviewerByIds(Long[] ids);

    /**
     * 删除医疗结构认证信息与评审员信息信息
     *
     * @param id 医疗结构认证信息与评审员信息主键
     * @return 结果
     */
    int deleteHospitalReviewerById(Long id);

    HospitalReviewer selectHospitalReviewerByLeader(String applyNo, Integer leaderIs);

    HospitalReviewer selectHosRevByAlnAndAct(String applyNo, String accountId);

    void insertOrUpdateHospitalReviewer(HospitalReviewer hospitalReviewer);

    /**
     * 根据评审员账户id获取关联的医疗机构编码
     *
     * @param reviewerId 评审员账户id
     * @param seniorReview 资深评审员标识
     * @return 结果
     */
    List<HospitalBaseInfo> selectApplyNosByReviewerId(String reviewerId, String seniorReview);

    List<ReviewManageVO> queryReviewManage(ReviewManageDTO reviewManageDTO);

    int updateHospitalReviewerByApplyNo(HospitalReviewer hospitalReviewer);

    List<SysUserBaseInfo> selectSeniorReviewerList(String roleKey);

    /**
     * 根据医院applyNo,查询医疗结构认证信息对应评审员ids
     *
     * @param applyNo 医疗结构认证信息与评审员信息主键
     * @return 对应评审员ids
     */
    public List<String> selectHospitalReviewerIdsByApplyNo(String applyNo);

    List<ReviewInterestVO> selectReviewInterestVOByAccountId(String accountId);

    AjaxResult delHosReviewer(DelHosRevDTO delHosRevDTO);

    int updateInterestFileId(HospitalReviewer hospitalReviewer);

    void checkInterestFileIsFull(String applyNo);

    FileInfoVO uploadAdminInterestFile(MultipartFile file, String applyNo,String reviewerId,String downLoadFileName);

    int urgentUpdateReviewer(HospitalReviewer hospitalReviewer);

    List<HospitalReviewerVo> selectHospitalReviewerByTraineesAssessorReviewerId(String reviewerId);
}
