package com.thas.web.service.impl;

import com.thas.common.enums.StatisticDataLoadEnum;
import com.thas.common.exception.ServiceException;
import com.thas.common.utils.StringUtils;
import com.thas.web.dto.StatisticReportRequest;
import com.thas.web.service.StatisticDataLoadService;
import com.thas.web.service.StatisticService;
import com.thas.web.utils.SpringContextUtil;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022/10/31
 */
@Service
public class StatisticServiceImpl implements StatisticService {

    @Override
    public Map<String, Object> report(StatisticReportRequest request) {
        String code = request.getCode();
        String beanName = StatisticDataLoadEnum.getBeanName(code);
        if (StringUtils.isEmpty(beanName)) {
            throw new ServiceException(String.format("code[%s]获取beanName为空", code), 400);
        }
        Object dataLoadService = SpringContextUtil.getBean(beanName);
        return ((StatisticDataLoadService) dataLoadService).load(request.getParam());
    }
}
