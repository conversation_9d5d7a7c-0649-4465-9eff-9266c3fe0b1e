package com.thas.web.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageInfo;
import com.thas.common.constant.Constants;
import com.thas.common.constant.HttpStatus;
import com.thas.common.core.domain.entity.SysRole;
import com.thas.common.core.page.TableDataInfo;
import com.thas.common.exception.ServiceException;
import com.thas.common.utils.DateUtils;
import com.thas.common.utils.PageUtils;
import com.thas.common.utils.SecurityUtils;
import com.thas.system.service.ISysRoleService;
import com.thas.web.domain.FileShare;
import com.thas.web.dto.DelFileShareRequest;
import com.thas.web.dto.FileShareCreateRequest;
import com.thas.web.dto.QryFileShareRequest;
import com.thas.web.dto.QryFileShareResponse;
import com.thas.web.dto.UpdFileShareTempDataDto;
import com.thas.web.mapper.FileShareMapper;
import com.thas.web.service.CommonService;
import com.thas.web.service.IFileShareService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 管理员文件分享关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-10-27
 */
@Service
public class FileShareServiceImpl implements IFileShareService {

    /**
     * 临时userId
     */
    public static final long TEMP_USER_ID = -1L;

    /**
     * 临时nickName
     */
    public static final String TEMP_NICK_NAME = "临时用户名字";

    @Autowired
    private ISysRoleService sysRoleService;

    @Autowired
    private FileShareMapper fileShareMapper;

    @Autowired
    private CommonService commonService;

    /**
     * 查询管理员文件分享关联
     *
     * @param roleKey 管理员文件分享关联主键
     * @return 管理员文件分享关联
     */
    @Override
    public FileShare selectFileShareByRoleKey(String roleKey) {
        return fileShareMapper.selectFileShareByRoleKey(roleKey);
    }

    /**
     * 查询管理员文件分享关联列表
     *
     * @param fileShare 管理员文件分享关联
     * @return 管理员文件分享关联
     */
    @Override
    public List<FileShare> selectFileShareList(FileShare fileShare) {
        return fileShareMapper.selectFileShareList(fileShare);
    }

    @Override
    public List<FileShare> selectFileShareList(List<Long> fileIdList) {
        return fileShareMapper.selectFileShareListByFileId(fileIdList);
    }

    /**
     * 新增管理员文件分享关联
     *
     * @param fileShare 管理员文件分享关联
     * @return 结果
     */
    @Override
    public int insertFileShare(FileShare fileShare) {
        fileShare.setCreateTime(DateUtils.getNowDate());
        return fileShareMapper.insertFileShare(fileShare);
    }

    /**
     * 保存数据
     *
     * @param fileShareList 入参entity集合
     */
    @Override
    public int batchInsertFileShare(List<FileShare> fileShareList) {
        return fileShareMapper.batchInsertFileShare(fileShareList);
    }

    /**
     * 修改管理员文件分享关联
     *
     * @param fileShare 管理员文件分享关联
     * @return 结果
     */
    @Override
    public int updateFileShare(FileShare fileShare) {
        fileShare.setUpdateTime(DateUtils.getNowDate());
        return fileShareMapper.updateFileShare(fileShare);
    }

    /**
     * 批量删除管理员文件分享关联
     *
     * @param roleKeys 需要删除的管理员文件分享关联主键
     * @return 结果
     */
    @Override
    public int deleteFileShareByRoleKeys(String[] roleKeys) {
        return fileShareMapper.deleteFileShareByRoleKeys(roleKeys);
    }

    /**
     * 删除管理员文件分享关联信息
     *
     * @param roleKey 管理员文件分享关联主键
     * @return 结果
     */
    @Override
    public int deleteFileShareByRoleKey(String roleKey) {
        return fileShareMapper.deleteFileShareByRoleKey(roleKey);
    }

    @Override
    public void fileShareBingCreate(FileShareCreateRequest request) {
        if (StringUtils.isEmpty(request.getOwnerId())) {
            throw new ServiceException("文件持有人id不能为空", 400);
        }
        List<FileShare> fileShareList = this.assFileShare(request);

        doSaveFileShareData(fileShareList, request);
    }

    @Override
    public void fileShareCreate(FileShareCreateRequest request) {
        // 校验入参
        checkFileShareCreateParam(request);

        // 组装需要添加的entity
        List<FileShare> fileShareList = this.assFileShare(request);

        doSaveFileShareData(fileShareList, request);
    }

    /**
     * 保存数据
     */
    private void doSaveFileShareData(List<FileShare> fileShareList, FileShareCreateRequest request) {
        // 先删除历史数据
        this.doDeleteOldData(request);

        if (CollectionUtils.isEmpty(fileShareList)) {
            return;
        }
        // 保存
        this.batchInsertFileShare(fileShareList);
    }


    @Override
    public TableDataInfo qryFileShare(QryFileShareRequest request) {
        if (Objects.isNull(request)) {
            request = new QryFileShareRequest();
        }
        boolean isAdmin = sysRoleService.isAdmin();
        if (isAdmin) {
            // 如果是管理员，加了筛选条件角色，就使用条件角色，如果没有增加则使用管理员的角色
            String curRoleKey = StringUtils.isNotEmpty(request.getRoleKey())
                    ? request.getRoleKey() : Constants.HospitalConstants.ROLE_COMMON_ADMIN;
            request.setCurRoleKey(curRoleKey);
        } else {
            // 如果不是管理员，获取当前登录角色，如果是管理员则查询所有的。
            SysRole sysRole = SecurityUtils.getSysRole();
            if (Objects.isNull(sysRole)) {
                throw new ServiceException("当前登录用户角色非法！");
            }
            request.setCurRoleKey(sysRole.getRoleKey());
            // 并且只能获取当前上传人上传的文件或者管理员分享的文件
            request.setOwnerId(String.valueOf(SecurityUtils.getUserId()));
        }
        // 分页设置
        PageUtils.startPage();
        List<Map<String, Object>> shareFileList = fileShareMapper.qryFileShare(request);

        // 手动设置分页
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(shareFileList);
        rspData.setTotal(new PageInfo(shareFileList).getTotal());

        // 组装参数返回
        List<QryFileShareResponse> qryFileShareResponseList = this.assFileShareList(shareFileList, isAdmin);
        rspData.setRows(qryFileShareResponseList);
        return rspData;
    }

    @Override
    public void updateByOwnerId(UpdFileShareTempDataDto request) {
        fileShareMapper.updateByOwnerId(request);
    }

    @Override
    public void updateByOwnerIdAndRoleKey(UpdFileShareTempDataDto request) {
        fileShareMapper.updateByOwnerIdAndRoleKey(request);
    }

    @Override
    public void delFileShareByFileId(DelFileShareRequest request) {
        // 判断一下当前登录用户，如果不是管理员，则只能删除文件所属人是自己的文件。
        if (!sysRoleService.isAdmin()) {
            request.setOwnerId(String.valueOf(SecurityUtils.getUserId()));
        }

        fileShareMapper.delFileShareByFileIdList(request);
    }

    /**
     * 组装参数返回
     *
     * @param shareFileList 查询的数据
     * @param isAdmin       是否超级管理员
     * @return QryFileShareResponse
     */
    private List<QryFileShareResponse> assFileShareList(List<Map<String, Object>> shareFileList, boolean isAdmin) {
        List<QryFileShareResponse> responseList = new ArrayList<>();
        for (Map<String, Object> fileShareMap : shareFileList) {
            QryFileShareResponse response = new QryFileShareResponse();
            response.setRoleKey(MapUtil.getStr(fileShareMap, "roleKey"));
            response.setUpLoaderId(MapUtil.getLong(fileShareMap, "upLoaderId"));
            response.setUpLoaderName(MapUtil.getStr(fileShareMap, "upLoaderName"));
            Date upLoadTime = MapUtil.getDate(fileShareMap, "upLoadTime");
            response.setUpLoadTime(DateUtil.format(upLoadTime, DatePattern.NORM_DATETIME_PATTERN));
            response.setFileId(MapUtil.getLong(fileShareMap, "fileId"));
            response.setFileName(MapUtil.getStr(fileShareMap, "fileName"));
            response.setSource(MapUtil.getStr(fileShareMap, "source"));
            response.setReviewerName(MapUtil.getStr(fileShareMap, "reviewerName"));
            response.setHospitalName(MapUtil.getStr(fileShareMap, "hospitalName"));
            // 获取url
            String platForm = MapUtil.getStr(fileShareMap, "platForm");
            String path = MapUtil.getStr(fileShareMap, "url");
            String url = commonService.getFileUrl(platForm, path);
            response.setUrl(url);
            responseList.add(response);
        }

        // 如果当前是管理员查询，需要区分每一个文件所属的角色
        if (isAdmin) {
            this.assFileRoles(responseList);
        }
        return responseList;
    }

    /**
     * 查询出文件对应所属角色
     *
     * @param responseList 文件
     */
    private void assFileRoles(List<QryFileShareResponse> responseList) {
        if (CollectionUtils.isEmpty(responseList)) {
            return;
        }
        List<Long> fileIdList = responseList.stream()
                .map(QryFileShareResponse::getFileId)
                .collect(Collectors.toList());

        // 查询出所有的文件共享信息
        List<FileShare> fileShareList = selectFileShareList(fileIdList);
        Map<Long, List<FileShare>> fileShareListMap = fileShareList.stream()
                .collect(Collectors.groupingBy(FileShare::getFileId));

        for (QryFileShareResponse response : responseList) {
            List<FileShare> fileShares = fileShareListMap.get(response.getFileId());
            if (CollectionUtils.isEmpty(fileShares)) {
                continue;
            }
            List<String> roleKeyList = fileShares.stream()
                    .map(FileShare::getRoleKey)
                    .collect(Collectors.toList());

            response.setRoleKeyList(roleKeyList);
        }
    }

    /**
     * 组装entity
     *
     * @param request 入参
     * @return FileShare
     */
    private List<FileShare> assFileShare(FileShareCreateRequest request) {
        // 获取当前登录用户id，用户名字
        Long userId;
        String nickName;
        try {
            userId = SecurityUtils.getUserId();
            nickName = SecurityUtils.getNickName();
        } catch (Exception e) {
            // 获取当前登录用户的id name如果是免登则会报错，获取异常，不处理。
            userId = TEMP_USER_ID;
            nickName = TEMP_NICK_NAME;
        }
        Set<String> roleKeySet = request.getRoleKey();

        // 默认加一个管理员角色，标识此文件管理员也能查询
        roleKeySet.add(Constants.HospitalConstants.ROLE_COMMON_ADMIN);
        String source = request.getSource();
        List<Long> fileIdList = request.getFileIdList();
        List<FileShare> fileShareList = new ArrayList<>();
        for (String roleKey : roleKeySet) {
            for (Long fileId : fileIdList) {
                FileShare fileShare = new FileShare();
                fileShare.setRoleKey(roleKey);
                fileShare.setFileId(fileId);
                fileShare.setUpLoadTime(DateUtils.getNowDate());
                fileShare.setUpLoaderId(userId);
                fileShare.setUpLoaderName(nickName);
                fileShare.setSource(source);
                fileShare.setOwnerId(request.getOwnerId());

                fileShare.setReviewerId(request.getReviewerId());
                fileShare.setReviewerName(request.getReviewerName());
                fileShare.setApplyNo(request.getApplyNo());
                fileShare.setHospitalName(request.getHospitalName());
                fileShareList.add(fileShare);
            }
        }

        this.queryUploader(request, fileShareList);

        return fileShareList;
    }

    /**
     * 如果是分享，则不修改文件上传人和上传时间
     *
     * @param fileShareList entity
     */
    private void queryUploader(FileShareCreateRequest request, List<FileShare> fileShareList) {
        List<Long> fileIdList = request.getFileIdList();
        Set<String> roleKey = request.getRoleKey();
        List<FileShare> fileShares = fileShareMapper.selectFileShareListByFileId(fileIdList);
        if (CollUtil.isEmpty(fileShares)) {
            return;
        }
        Map<String, FileShare> fileShareMap = fileShares.stream()
                .filter(fileShare -> roleKey.contains(fileShare.getRoleKey()))
                .collect(Collectors.toMap(fileShare -> fileShare.getRoleKey() + fileShare.getFileId(), v -> v));

        for (FileShare fileShare : fileShareList) {
            FileShare fs = fileShareMap.get(fileShare.getRoleKey() + fileShare.getFileId());
            if (Objects.isNull(fs)) {
                return;
            }
            fileShare.setUpLoaderId(fs.getUpLoaderId());
            fileShare.setUpLoaderName(fs.getUpLoaderName());
        }
    }

    /**
     * 删除历史数据
     */
    private void doDeleteOldData(FileShareCreateRequest request) {
        // 通过文件id先删除全量的数据，之后再插入
        List<Long> fileIdList = request.getFileIdList();
        DelFileShareRequest delFileShareRequest = new DelFileShareRequest();
        delFileShareRequest.setFileIdList(fileIdList);
        fileShareMapper.delFileShareByFileIdList(delFileShareRequest);
    }

    /**
     * 校验入参
     *
     * @param request 入参
     */
    private void checkFileShareCreateParam(FileShareCreateRequest request) {
        // 获取一下当前的登录角色，如果不是管理员，roleKey只能有当前角色和管理员角色
        SysRole sysRole = SecurityUtils.getSysRole();
        String curRoleKey = sysRole.getRoleKey();
        if (!Constants.HospitalConstants.ROLE_ADMIN.equals(curRoleKey)
                && !Constants.HospitalConstants.ROLE_COMMON_ADMIN.equals(curRoleKey)) {
            Set<String> roleKeySet = request.getRoleKey();
            roleKeySet.clear();
            roleKeySet.add(curRoleKey);
        }
        Set<String> roleKeySet
                = request.getRoleKey();
        // 校验当前传入角色是否合法
        for (String roleKey : roleKeySet) {
            if (!Constants.FILE_SHARE_ROLE_SET.contains(roleKey)) {
                throw new ServiceException(String.format("roleKey[%s]不合法", roleKey), 500);
            }
        }
    }
}
