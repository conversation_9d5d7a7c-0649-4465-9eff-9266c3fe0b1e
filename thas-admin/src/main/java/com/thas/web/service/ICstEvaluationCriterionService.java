package com.thas.web.service;

import com.thas.common.core.domain.CstEvaluationCriterionExcel;
import java.util.List;
import com.thas.web.domain.CstEvaluationCriterion;
import java.util.Map;

/**
 * 评估标准模板Service接口
 *
 * <AUTHOR>
 * @date 2022-01-21
 */
public interface ICstEvaluationCriterionService
{
    /**
     * 查询评估标准模板
     *
     * @param id 评估标准模板主键
     * @return 评估标准模板
     */
    public CstEvaluationCriterion selectCstEvaluationCriterionById(Long id);

    /**
     * 查询评估标准模板列表
     *
     * @param cstEvaluationCriterion 评估标准模板
     * @return 评估标准模板集合
     */
    public List<CstEvaluationCriterion> selectCstEvaluationCriterionList(CstEvaluationCriterion cstEvaluationCriterion);

    /**
     * 新增评估标准模板
     *
     * @param cstEvaluationCriterion 评估标准模板
     * @return 结果
     */
    public int insertCstEvaluationCriterion(CstEvaluationCriterion cstEvaluationCriterion);

    /**
     * 修改评估标准模板
     *
     * @param cstEvaluationCriterion 评估标准模板
     * @return 结果
     */
    public int updateCstEvaluationCriterion(CstEvaluationCriterion cstEvaluationCriterion);

    /**
     * 批量删除评估标准模板
     *
     * @param ids 需要删除的评估标准模板主键集合
     * @return 结果
     */
    public int deleteCstEvaluationCriterionByIds(Long[] ids);

    /**
     * 删除评估标准模板信息
     *
     * @param id 评估标准模板主键
     * @return 结果
     */
    public int deleteCstEvaluationCriterionById(Long id);

    /**
     * 通过版本id和款id修改多条记录
     * @param cstEvaluationCriteriaList
     * @return
     */
    public int updateListByVersionIdAndClauseId(List<CstEvaluationCriterion> cstEvaluationCriteriaList);

    /**
     * 查询单条认证标准对应的评价列表信息
     * @param cstEvaluationCriterionExcel 查询条件
     * @return  评价列表信息
     */
    Map<Long, List<CstEvaluationCriterion>> queryCriterionList(CstEvaluationCriterionExcel cstEvaluationCriterionExcel);
}
