package com.thas.web.service;

import java.util.List;
import com.thas.web.domain.MessageTemplate;

/**
 * 消息模板Service接口
 *
 * <AUTHOR>
 * @date 2022-01-25
 */
public interface IMessageTemplateService
{
    /**
     * 查询消息模板
     *
     * @param id 消息模板主键
     * @return 消息模板
     */
    public MessageTemplate selectMessageTemplateById(Long id);

    /**
     * 查询消息模板列表
     *
     * @param messageTemplate 消息模板
     * @return 消息模板集合
     */
    public List<MessageTemplate> selectMessageTemplateList(MessageTemplate messageTemplate);

    /**
     * 新增消息模板
     *
     * @param messageTemplate 消息模板
     * @return 结果
     */
    public int insertMessageTemplate(MessageTemplate messageTemplate);

    /**
     * 修改消息模板
     *
     * @param messageTemplate 消息模板
     * @return 结果
     */
    public int updateMessageTemplate(MessageTemplate messageTemplate);

    /**
     * 批量删除消息模板
     *
     * @param ids 需要删除的消息模板主键集合
     * @return 结果
     */
    public int deleteMessageTemplateByIds(Long[] ids);

    /**
     * 删除消息模板信息
     *
     * @param id 消息模板主键
     * @return 结果
     */
    public int deleteMessageTemplateById(Long id);
}
