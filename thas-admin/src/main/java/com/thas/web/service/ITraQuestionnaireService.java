package com.thas.web.service;

import com.thas.web.domain.SendDetailRes;
import com.thas.web.domain.SendInfoRes;
import com.thas.web.domain.TraQuestionnaire;
import com.thas.web.domain.TraQuestionnaireFeedBackRecord;
import com.thas.web.domain.dto.TraQuestionnaireDTO;
import com.thas.web.domain.vo.TraQuestionnaireVO;

import java.util.List;

/**
 * 调查问卷Service接口
 *
 * <AUTHOR>
 * @date 2022-02-14
 */
public interface ITraQuestionnaireService {
    /**
     * 查询调查问卷
     *
     * @param id 调查问卷主键
     * @return 调查问卷
     */
    TraQuestionnaireVO selectTraQuestionnaireById(Long id);

    /**
     * 查询调查问卷列表
     *
     * @param traQuestionnaireDTO 调查问卷
     * @return 调查问卷集合
     */
    List<TraQuestionnaireVO> selectTraQuestionnaireList(TraQuestionnaireDTO traQuestionnaireDTO);

    /**
     * 新增调查问卷
     *
     * @param traQuestionnaire 调查问卷
     * @return 结果
     */
    void insertOrUpdateTraQuestionnaire(TraQuestionnaire traQuestionnaire);

    /**
     * 批量删除调查问卷
     *
     * @param ids 需要删除的调查问卷主键集合
     * @return 结果
     */
    int deleteTraQuestionnaireByIds(Long[] ids);

    int updateStatus(Integer status, Long id);

    List<SendInfoRes> sendInfo(Long id);

    String sendSubmit(List<SendInfoRes> sendInfoResList);

    List<SendDetailRes> sendDetail(TraQuestionnaireFeedBackRecord req);
}
