package com.thas.web.service.impl;

import java.util.List;
import com.thas.common.utils.DateUtils;
import com.thas.web.domain.TraQuestionnaireFeedBackRecord;
import com.thas.web.mapper.TraQuestionnaireFeedBackRecordMapper;
import com.thas.web.service.ITraQuestionnaireFeedBackRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 反馈问卷填写统计记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-12-12
 */
@Service
public class TraQuestionnaireFeedBackRecordServiceImpl implements ITraQuestionnaireFeedBackRecordService
{
    @Autowired
    private TraQuestionnaireFeedBackRecordMapper traQuestionnaireFeedBackRecordMapper;

    /**
     * 查询反馈问卷填写统计记录
     * 
     * @param recordId 反馈问卷填写统计记录主键
     * @return 反馈问卷填写统计记录
     */
    @Override
    public TraQuestionnaireFeedBackRecord selectTraQuestionnaireFeedBackRecordByRecordId(Long recordId)
    {
        return traQuestionnaireFeedBackRecordMapper.selectTraQuestionnaireFeedBackRecordByRecordId(recordId);
    }

    /**
     * 查询反馈问卷填写统计记录列表
     * 
     * @param traQuestionnaireFeedBackRecord 反馈问卷填写统计记录
     * @return 反馈问卷填写统计记录
     */
    @Override
    public List<TraQuestionnaireFeedBackRecord> selectTraQuestionnaireFeedBackRecordList(TraQuestionnaireFeedBackRecord traQuestionnaireFeedBackRecord)
    {
        return traQuestionnaireFeedBackRecordMapper.selectTraQuestionnaireFeedBackRecordList(traQuestionnaireFeedBackRecord);
    }

    /**
     * 新增反馈问卷填写统计记录
     * 
     * @param traQuestionnaireFeedBackRecord 反馈问卷填写统计记录
     * @return 结果
     */
    @Override
    public int insertTraQuestionnaireFeedBackRecord(TraQuestionnaireFeedBackRecord traQuestionnaireFeedBackRecord)
    {
        traQuestionnaireFeedBackRecord.setCreateTime(DateUtils.getNowDate());
        return traQuestionnaireFeedBackRecordMapper.insertTraQuestionnaireFeedBackRecord(traQuestionnaireFeedBackRecord);
    }

    /**
     * 修改反馈问卷填写统计记录
     * 
     * @param traQuestionnaireFeedBackRecord 反馈问卷填写统计记录
     * @return 结果
     */
    @Override
    public int updateTraQuestionnaireFeedBackRecord(TraQuestionnaireFeedBackRecord traQuestionnaireFeedBackRecord)
    {
        traQuestionnaireFeedBackRecord.setUpdateTime(DateUtils.getNowDate());
        return traQuestionnaireFeedBackRecordMapper.updateTraQuestionnaireFeedBackRecord(traQuestionnaireFeedBackRecord);
    }

    /**
     * 批量删除反馈问卷填写统计记录
     * 
     * @param recordIds 需要删除的反馈问卷填写统计记录主键
     * @return 结果
     */
    @Override
    public int deleteTraQuestionnaireFeedBackRecordByRecordIds(Long[] recordIds)
    {
        return traQuestionnaireFeedBackRecordMapper.deleteTraQuestionnaireFeedBackRecordByRecordIds(recordIds);
    }

    /**
     * 删除反馈问卷填写统计记录信息
     * 
     * @param recordId 反馈问卷填写统计记录主键
     * @return 结果
     */
    @Override
    public int deleteTraQuestionnaireFeedBackRecordByRecordId(Long recordId)
    {
        return traQuestionnaireFeedBackRecordMapper.deleteTraQuestionnaireFeedBackRecordByRecordId(recordId);
    }
}
