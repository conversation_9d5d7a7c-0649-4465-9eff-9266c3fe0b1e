package com.thas.web.service.impl;

import java.util.List;
import java.util.Objects;

import com.thas.common.enums.ServiceExceptionEnum;
import com.thas.common.exception.ServiceException;
import com.thas.common.utils.DateUtils;
import com.thas.generator.util.NumberGenUtils;
import com.thas.web.domain.ReviewerTmpAcc;
import com.thas.web.mapper.ReviewerTmpAccMapper;
import com.thas.web.service.IReviewerTmpAccService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 评审员临时accountId，与证件号关联，分配账号之后删除记录。Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-05-17
 */
@Service
public class ReviewerTmpAccServiceImpl implements IReviewerTmpAccService {

    @Autowired
    private ReviewerTmpAccMapper reviewerTmpAccMapper;

    @Override
    public String genTmpAccountId(String certificateNumber) {
        // 查询表中是否有记录
        ReviewerTmpAcc reviewerTmpAcc = reviewerTmpAccMapper.selectReviewerTmpAccByCertificateNumber(certificateNumber);
        if (Objects.isNull(reviewerTmpAcc)) {
            reviewerTmpAcc = new ReviewerTmpAcc();
            reviewerTmpAcc.setCertificateNumber(certificateNumber);
            String accountId = NumberGenUtils.hospitalApplyNoGen();
            reviewerTmpAcc.setAccountId(accountId);
            int res = reviewerTmpAccMapper.insertReviewerTmpAcc(reviewerTmpAcc);
            if (res <= 0 ) {
                throw new ServiceException(ServiceExceptionEnum.REVIEWER_ERROR_1000010);
            }
        }
        return reviewerTmpAcc.getAccountId();
    }

    @Override
    public String getTmpAccountId(String certificateNumber) {
        ReviewerTmpAcc reviewerTmpAcc = reviewerTmpAccMapper.selectReviewerTmpAccByCertificateNumber(certificateNumber);
        if (Objects.isNull(reviewerTmpAcc)) {
            return "";
        }
        return reviewerTmpAcc.getAccountId();
    }

    /**
     * 查询评审员临时accountId，与证件号关联，分配账号之后删除记录。
     *
     * @param certificateNumber 评审员临时accountId，与证件号关联，分配账号之后删除记录。主键
     * @return 评审员临时accountId，与证件号关联，分配账号之后删除记录。
     */
    @Override
    public ReviewerTmpAcc selectReviewerTmpAccByCertificateNumber(String certificateNumber) {
        return reviewerTmpAccMapper.selectReviewerTmpAccByCertificateNumber(certificateNumber);
    }

    /**
     * 查询评审员临时accountId，与证件号关联，分配账号之后删除记录。列表
     *
     * @param reviewerTmpAcc 评审员临时accountId，与证件号关联，分配账号之后删除记录。
     * @return 评审员临时accountId，与证件号关联，分配账号之后删除记录。
     */
    @Override
    public List<ReviewerTmpAcc> selectReviewerTmpAccList(ReviewerTmpAcc reviewerTmpAcc) {
        return reviewerTmpAccMapper.selectReviewerTmpAccList(reviewerTmpAcc);
    }

    /**
     * 新增评审员临时accountId，与证件号关联，分配账号之后删除记录。
     *
     * @param reviewerTmpAcc 评审员临时accountId，与证件号关联，分配账号之后删除记录。
     * @return 结果
     */
    @Override
    public int insertReviewerTmpAcc(ReviewerTmpAcc reviewerTmpAcc) {
        reviewerTmpAcc.setCreateTime(DateUtils.getNowDate());
        return reviewerTmpAccMapper.insertReviewerTmpAcc(reviewerTmpAcc);
    }

    /**
     * 修改评审员临时accountId，与证件号关联，分配账号之后删除记录。
     *
     * @param reviewerTmpAcc 评审员临时accountId，与证件号关联，分配账号之后删除记录。
     * @return 结果
     */
    @Override
    public int updateReviewerTmpAcc(ReviewerTmpAcc reviewerTmpAcc) {
        reviewerTmpAcc.setUpdateTime(DateUtils.getNowDate());
        return reviewerTmpAccMapper.updateReviewerTmpAcc(reviewerTmpAcc);
    }

    /**
     * 批量删除评审员临时accountId，与证件号关联，分配账号之后删除记录。
     *
     * @param certificateNumbers 需要删除的评审员临时accountId，与证件号关联，分配账号之后删除记录。主键
     * @return 结果
     */
    @Override
    public int deleteReviewerTmpAccByCertificateNumbers(String[] certificateNumbers) {
        return reviewerTmpAccMapper.deleteReviewerTmpAccByCertificateNumbers(certificateNumbers);
    }

    /**
     * 删除评审员临时accountId，与证件号关联，分配账号之后删除记录。信息
     *
     * @param certificateNumber 评审员临时accountId，与证件号关联，分配账号之后删除记录。主键
     * @return 结果
     */
    @Override
    public int deleteReviewerTmpAccByCertificateNumber(String certificateNumber) {
        return reviewerTmpAccMapper.deleteReviewerTmpAccByCertificateNumber(certificateNumber);
    }
}
