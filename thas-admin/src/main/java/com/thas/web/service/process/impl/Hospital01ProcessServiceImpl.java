package com.thas.web.service.process.impl;

import com.thas.common.enums.AutSaAudBusinessCodeEnum;
import com.thas.common.enums.AutSaAudStatusEnum;
import com.thas.common.enums.AutSaAudSubmitTypeEnum;
import com.thas.web.domain.AutSaAudList;
import com.thas.web.domain.AutSaAudQueryDTO;
import com.thas.web.domain.AutSaAudSaveDTO;
import com.thas.web.domain.vo.AutSaAudDetailVO;
import com.thas.web.service.process.BaseProcessService;
import com.thas.web.service.process.CommonProcessService;
import java.util.Collections;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Component;

/**
 * 医院自评流程服务 Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-04-01
 */
@Component("hospital01ProcessService")
@Slf4j
public class Hospital01ProcessServiceImpl implements BaseProcessService {

    @Resource
    private CommonProcessService commonProcessService;

    @Override
    public void process(AutSaAudSaveDTO req) {
        long startTime = System.currentTimeMillis();
        log.info("hospital01ProcessService.process ------ 开始");
        if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(req.getSubmitType(), AutSaAudSubmitTypeEnum.SA_CLAUSE)) {
            // 条款自评  -- 跟新操作
            commonProcessService.checkClause(req);
            commonProcessService.batchUpdateAutSaAud(req.getAutSaAudLists());
            commonProcessService.generateReport(req.getAutCode(), req.getAutSaRelation().getAutCsId(), String.join(",", Collections.singletonList(AutSaAudSubmitTypeEnum.SA_CLAUSE.getSubmitType())), AutSaAudBusinessCodeEnum.AUT_SA_AUD_SA_REPORT.getCode(), Lists.newArrayList());
        } else if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(req.getSubmitType(), AutSaAudSubmitTypeEnum.SA_CLAUSE_CONFIRM)) {
            // 确认自评结果  -- 直接翻转节点
            commonProcessService.processConfirmScene(req);
        } else if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(req.getSubmitType(), AutSaAudSubmitTypeEnum.SA_REPORT_DESC)) {
            // 自评描述
            if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.SA_REPORT_DESC)) {
                // 自评描述 -- 直接翻转节点
                commonProcessService.processSummaryScene(req);
            } else if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.SA_SUMMARY)) {
                // 自评总结  -- 跟新操作
                commonProcessService.processSummary(req);
            }
        } else if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(req.getSubmitType(), AutSaAudSubmitTypeEnum.SA_SUMMARY)) {
            // 自评总结  -- 直接翻转节点
            commonProcessService.processSummaryScene(req);
        }
        log.info("hospital01ProcessService.process ------ 结束 耗时：{}", System.currentTimeMillis() - startTime);
    }

    @Override
    public List<AutSaAudList> queryList(AutSaAudQueryDTO req) {
        return null;
    }

    @Override
    public AutSaAudDetailVO queryDetail(AutSaAudQueryDTO req) {
        return null;
    }

}
