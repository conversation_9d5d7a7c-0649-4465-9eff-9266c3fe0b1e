package com.thas.web.service;

import com.thas.system.domain.vo.RoleAndUserVo;
import com.thas.system.domain.vo.TrainerValidDataQueryVo;
import com.thas.web.domain.TrainingEvaluateResult;
import com.thas.web.domain.dto.TrainingEvaluateResultReq;
import com.thas.web.domain.vo.TrainingEvaluateResultRes;
import com.thas.web.domain.vo.TrainingEvaluateResultSubmitRes;

import java.util.List;

public interface ITrainingEvaluateResultService {

    List<TrainerValidDataQueryVo> trainerValidDataQuery(String roleName);

    /**
     * 查询培训评估结果
     *
     * @param resultId 培训评估结果主键
     * @return 培训评估结果
     */
    public TrainingEvaluateResult selectTrainingEvaluateResultByResultId(Long resultId);

    /**
     * 查询培训评估结果列表
     *
     * @param trainingEvaluateResult 培训评估结果
     * @return 培训评估结果集合
     */
    public List<TrainingEvaluateResult> selectTrainingEvaluateResultList(TrainingEvaluateResult trainingEvaluateResult);

    /**
     * 新增培训评估结果
     *
     * @param trainingEvaluateResult 培训评估结果
     * @return 结果
     */
    public int insertTrainingEvaluateResult(TrainingEvaluateResult trainingEvaluateResult);

    /**
     * 修改培训评估结果
     *
     * @param trainingEvaluateResult 培训评估结果
     * @return 结果
     */
    public int updateTrainingEvaluateResult(TrainingEvaluateResult trainingEvaluateResult);

    /**
     * 批量删除培训评估结果
     *
     * @param resultIds 需要删除的培训评估结果主键集合
     * @return 结果
     */
    public int deleteTrainingEvaluateResultByResultIds(Long[] resultIds);

    /**
     * 删除培训评估结果信息
     *
     * @param resultId 培训评估结果主键
     * @return 结果
     */
    public int deleteTrainingEvaluateResultByResultId(Long resultId);

    TrainingEvaluateResultReq theoryQuery(TrainingEvaluateResultRes res);

    void theorySubmit(TrainingEvaluateResultSubmitRes res);

    TrainingEvaluateResultReq siteReviewQuery(TrainingEvaluateResultRes res);

    List<TrainingEvaluateResult> selectTrainingEvaluateResultListByConclusion(String Conclusion);
}
