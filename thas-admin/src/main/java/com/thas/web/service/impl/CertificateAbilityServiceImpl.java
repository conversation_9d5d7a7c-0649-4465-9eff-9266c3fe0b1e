package com.thas.web.service.impl;

import com.thas.common.utils.DateUtils;
import com.thas.web.domain.CertificateAbility;
import com.thas.web.mapper.CertificateAbilityMapper;
import com.thas.web.service.ICertificateAbilityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 对应证书关联Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-01-19
 */
@Service
public class CertificateAbilityServiceImpl implements ICertificateAbilityService
{
    @Autowired
    private CertificateAbilityMapper certificateAbilityMapper;

    /**
     * 查询对应证书关联
     * 
     * @param id 对应证书关联主键
     * @return 对应证书关联
     */
    @Override
    public CertificateAbility selectCertificateAbilityById(Long id)
    {
        return certificateAbilityMapper.selectCertificateAbilityById(id);
    }

    @Override
    public List<CertificateAbility> selectCertificateAbilityByAccountId(String accountId) {
        return certificateAbilityMapper.selectCertificateAbilityByProcessCode(accountId);
    }

    /**
     * 查询对应证书关联列表
     * 
     * @param certificateAbility 对应证书关联
     * @return 对应证书关联
     */
    @Override
    public List<CertificateAbility> selectCertificateAbilityList(CertificateAbility certificateAbility)
    {
        return certificateAbilityMapper.selectCertificateAbilityList(certificateAbility);
    }

    /**
     * 新增对应证书关联
     * 
     * @param certificateAbility 对应证书关联
     * @return 结果
     */
    @Override
    public int insertCertificateAbility(CertificateAbility certificateAbility)
    {
        certificateAbility.setCreateTime(DateUtils.getNowDate());
        return certificateAbilityMapper.insertCertificateAbility(certificateAbility);
    }

    /**
     * 修改对应证书关联
     * 
     * @param certificateAbility 对应证书关联
     * @return 结果
     */
    @Override
    public int updateCertificateAbility(CertificateAbility certificateAbility)
    {
        certificateAbility.setUpdateTime(DateUtils.getNowDate());
        return certificateAbilityMapper.updateCertificateAbility(certificateAbility);
    }

    /**
     * 批量删除对应证书关联
     * 
     * @param ids 需要删除的对应证书关联主键
     * @return 结果
     */
    @Override
    public int deleteCertificateAbilityByIds(Long[] ids)
    {
        return certificateAbilityMapper.deleteCertificateAbilityByIds(ids);
    }

    /**
     * 删除对应证书关联信息
     * 
     * @param id 对应证书关联主键
     * @return 结果
     */
    @Override
    public int deleteCertificateAbilityById(Long id)
    {
        return certificateAbilityMapper.deleteCertificateAbilityById(id);
    }

    @Override
    public void updateStatusByProcessCode(String accountId, int status) {
        certificateAbilityMapper.updateStatusByProcessCode(accountId, status);
    }
}
