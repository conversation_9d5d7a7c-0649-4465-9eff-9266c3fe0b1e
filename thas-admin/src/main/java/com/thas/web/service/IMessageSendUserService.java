package com.thas.web.service;

import java.util.List;
import com.thas.web.domain.MessageSendUser;
import com.thas.web.domain.dto.MessageSendRecordDTO;

/**
 * 消息发送指定用户关联Service接口
 *
 * <AUTHOR>
 * @date 2022-01-25
 */
public interface IMessageSendUserService
{
    /**
     * 查询消息发送指定用户关联
     *
     * @param sendMessageId 消息发送指定用户关联主键
     * @return 消息发送指定用户关联
     */
    public MessageSendUser selectMessageSendUserBySendMessageId(Long sendMessageId);

    /**
     * 查询消息发送指定用户关联列表
     *
     * @param messageSendUser 消息发送指定用户关联
     * @return 消息发送指定用户关联集合
     */
    public List<MessageSendUser> selectMessageSendUserList(MessageSendUser messageSendUser);

    /**
     * 新增消息发送指定用户关联
     *
     * @param messageSendUser 消息发送指定用户关联
     * @return 结果
     */
    public int insertMessageSendUser(MessageSendUser messageSendUser);

    /**
     * 修改消息发送指定用户关联
     *
     * @param messageSendUser 消息发送指定用户关联
     * @return 结果
     */
    public int updateMessageSendUser(MessageSendUser messageSendUser);

    /**
     * 批量删除消息发送指定用户关联
     *
     * @param sendMessageIds 需要删除的消息发送指定用户关联主键集合
     * @return 结果
     */
    public int deleteMessageSendUserBySendMessageIds(Long[] sendMessageIds);

    /**
     * 删除消息发送指定用户关联信息
     *
     * @param sendMessageId 消息发送指定用户关联主键
     * @return 结果
     */
    public int deleteMessageSendUserBySendMessageId(Long sendMessageId);


}
