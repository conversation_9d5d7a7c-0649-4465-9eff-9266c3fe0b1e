package com.thas.web.service;

import com.thas.web.domain.TraLearnResource;
import com.thas.web.domain.vo.TraLearnResourceVO;

import java.util.List;

/**
 * 学习资源Service接口
 *
 * <AUTHOR>
 * @date 2022-01-20
 */
public interface ITraLearnResourceService {
    /**
     * 查询学习资源
     *
     * @param id 学习资源主键
     * @return 学习资源
     */
    public TraLearnResourceVO selectTraLearnResourceById(Long id);

    /**
     * 查询学习资源列表
     *
     * @param traLearnResource 学习资源
     * @return 学习资源集合
     */
    public List<TraLearnResource> selectTraLearnResourceList(TraLearnResource traLearnResource);

    /**
     * 新增学习资源
     *
     * @param traLearnResource 学习资源
     * @return 结果
     */
    public int insertTraLearnResource(TraLearnResource traLearnResource);

    /**
     * 修改学习资源
     *
     * @param traLearnResource 学习资源
     * @return 结果
     */
    public int updateTraLearnResource(TraLearnResource traLearnResource);

    /**
     * 批量删除学习资源
     *
     * @param ids 需要删除的学习资源主键集合
     * @return 结果
     */
    public int deleteTraLearnResourceByIds(Long[] ids);

    /**
     * 删除学习资源信息
     *
     * @param id 学习资源主键
     * @return 结果
     */
    public int deleteTraLearnResourceById(Long id);

    int learnNumPlus(Long id);

}
