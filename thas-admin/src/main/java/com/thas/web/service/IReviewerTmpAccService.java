package com.thas.web.service;

import com.thas.web.domain.ReviewerTmpAcc;

import java.util.List;

/**
 * 评审员临时accountId，与证件号关联，分配账号之后删除记录。Service接口
 *
 * <AUTHOR>
 * @date 2022-05-17
 */
public interface IReviewerTmpAccService {

    /**
     * 评审员临时提交数据获取一个临时的accountId
     * @param certificateNumber 证件号
     * @return 临时accountId
     */
    String genTmpAccountId(String certificateNumber);


    String getTmpAccountId(String certificateNumber);

    /**
     * 查询评审员临时accountId，与证件号关联，分配账号之后删除记录。
     *
     * @param certificateNumber 评审员临时accountId，与证件号关联，分配账号之后删除记录。主键
     * @return 评审员临时accountId，与证件号关联，分配账号之后删除记录。
     */
    ReviewerTmpAcc selectReviewerTmpAccByCertificateNumber(String certificateNumber);

    /**
     * 查询评审员临时accountId，与证件号关联，分配账号之后删除记录。列表
     *
     * @param reviewerTmpAcc 评审员临时accountId，与证件号关联，分配账号之后删除记录。
     * @return 评审员临时accountId，与证件号关联，分配账号之后删除记录。集合
     */
    List<ReviewerTmpAcc> selectReviewerTmpAccList(ReviewerTmpAcc reviewerTmpAcc);

    /**
     * 新增评审员临时accountId，与证件号关联，分配账号之后删除记录。
     *
     * @param reviewerTmpAcc 评审员临时accountId，与证件号关联，分配账号之后删除记录。
     * @return 结果
     */
    int insertReviewerTmpAcc(ReviewerTmpAcc reviewerTmpAcc);

    /**
     * 修改评审员临时accountId，与证件号关联，分配账号之后删除记录。
     *
     * @param reviewerTmpAcc 评审员临时accountId，与证件号关联，分配账号之后删除记录。
     * @return 结果
     */
    int updateReviewerTmpAcc(ReviewerTmpAcc reviewerTmpAcc);

    /**
     * 批量删除评审员临时accountId，与证件号关联，分配账号之后删除记录。
     *
     * @param certificateNumbers 需要删除的评审员临时accountId，与证件号关联，分配账号之后删除记录。主键集合
     * @return 结果
     */
    int deleteReviewerTmpAccByCertificateNumbers(String[] certificateNumbers);

    /**
     * 删除评审员临时accountId，与证件号关联，分配账号之后删除记录。信息
     *
     * @param certificateNumber 评审员临时accountId，与证件号关联，分配账号之后删除记录。主键
     * @return 结果
     */
    int deleteReviewerTmpAccByCertificateNumber(String certificateNumber);

}
