package com.thas.web.service;

import com.thas.web.domain.HospitalReviewerRes;
import com.thas.web.domain.vo.AutSaAudDetailVO;
import com.thas.web.domain.vo.FileInfoVO;
import com.thas.web.dto.ReviewerShareReq;

import java.util.List;

/**
 * 评审共享服务
 *
 * <AUTHOR>
 */
public interface IReviewerShareService {
    void submit(ReviewerShareReq reviewerShareReq);

    List<HospitalReviewerRes> list(ReviewerShareReq reviewerShareReq);

    AutSaAudDetailVO detail(ReviewerShareReq reviewerShareReq);

    void pass(ReviewerShareReq reviewerShareReq);

    void update(ReviewerShareReq reviewerShareReq);

    FileInfoVO report(ReviewerShareReq reviewerShareReq);

    void temPass(ReviewerShareReq reviewerShareReq);

    void checkRoleIsReviewerLeader(ReviewerShareReq reviewerShareReq);

    void checkSubmitByRoleAndAutStatus(ReviewerShareReq reviewerShareReq);

    int submitEvaluate(ReviewerShareReq req);
}
