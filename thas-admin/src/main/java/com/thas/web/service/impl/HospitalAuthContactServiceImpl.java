package com.thas.web.service.impl;

import java.util.List;
import com.thas.common.utils.DateUtils;
import com.thas.web.domain.HospitalAuthContact;
import com.thas.web.mapper.HospitalAuthContactMapper;
import com.thas.web.service.IHospitalAuthContactService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 医疗机构被授权人信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-01-19
 */
@Service
public class HospitalAuthContactServiceImpl implements IHospitalAuthContactService
{
    @Autowired
    private HospitalAuthContactMapper hospitalAuthContactMapper;

    /**
     * 查询医疗机构被授权人信息
     * 
     * @param id 医疗机构被授权人信息主键
     * @return 医疗机构被授权人信息
     */
    @Override
    public HospitalAuthContact selectHospitalAuthContactById(Long id)
    {
        return hospitalAuthContactMapper.selectHospitalAuthContactById(id);
    }

    /**
     * 查询医疗机构被授权人信息列表
     * 
     * @param hospitalAuthContact 医疗机构被授权人信息
     * @return 医疗机构被授权人信息
     */
    @Override
    public List<HospitalAuthContact> selectHospitalAuthContactList(HospitalAuthContact hospitalAuthContact)
    {
        return hospitalAuthContactMapper.selectHospitalAuthContactList(hospitalAuthContact);
    }

    /**
     * 新增医疗机构被授权人信息
     * 
     * @param hospitalAuthContact 医疗机构被授权人信息
     * @return 结果
     */
    @Override
    public int insertHospitalAuthContact(HospitalAuthContact hospitalAuthContact)
    {
        hospitalAuthContact.setCreateTime(DateUtils.getNowDate());
        return hospitalAuthContactMapper.insertHospitalAuthContact(hospitalAuthContact);
    }

    /**
     * 修改医疗机构被授权人信息
     * 
     * @param hospitalAuthContact 医疗机构被授权人信息
     * @return 结果
     */
    @Override
    public int updateHospitalAuthContact(HospitalAuthContact hospitalAuthContact)
    {
        hospitalAuthContact.setUpdateTime(DateUtils.getNowDate());
        return hospitalAuthContactMapper.updateHospitalAuthContact(hospitalAuthContact);
    }

    /**
     * 批量删除医疗机构被授权人信息
     * 
     * @param ids 需要删除的医疗机构被授权人信息主键
     * @return 结果
     */
    @Override
    public int deleteHospitalAuthContactByIds(Long[] ids)
    {
        return hospitalAuthContactMapper.deleteHospitalAuthContactByIds(ids);
    }

    /**
     * 删除医疗机构被授权人信息信息
     * 
     * @param id 医疗机构被授权人信息主键
     * @return 结果
     */
    @Override
    public int deleteHospitalAuthContactById(Long id)
    {
        return hospitalAuthContactMapper.deleteHospitalAuthContactById(id);
    }
}
