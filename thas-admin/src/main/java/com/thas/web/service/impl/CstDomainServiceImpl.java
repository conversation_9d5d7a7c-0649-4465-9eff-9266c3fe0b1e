package com.thas.web.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.thas.common.constant.Constants;
import com.thas.common.core.redis.RedisCache;
import com.thas.common.enums.ServiceExceptionEnum;
import com.thas.common.exception.ServiceException;
import com.thas.common.utils.DateUtils;
import com.thas.common.utils.StringUtils;
import com.thas.web.domain.AutSaRelation;
import com.thas.web.domain.CstCertificationStandards;
import com.thas.web.domain.CstDomain;
import com.thas.web.domain.CstDomainVO;
import com.thas.web.domain.CstVersioning;
import com.thas.web.domain.vo.DomainGroupNode;
import com.thas.web.dto.UnDomainListVO;
import com.thas.web.mapper.CstDomainMapper;
import com.thas.web.mapper.CstVersioningMapper;
import com.thas.web.service.IAutSaRelationService;
import com.thas.web.service.ICstCertificationStandardsService;
import com.thas.web.service.ICstDomainService;
import com.thas.web.service.IHospitalDomainGroupService;
import com.thas.web.service.IReviewerFieldInfoService;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 领域Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-21
 */
@Service
@Slf4j
public class CstDomainServiceImpl implements ICstDomainService {
    @Resource
    private CstDomainMapper cstDomainMapper;

    @Resource
    private CstVersioningMapper cstVersioningMapper;

    @Resource
    private ICstCertificationStandardsService cstCertificationStandardsService;

    @Resource
    private IAutSaRelationService autSaRelationService;

    @Resource
    private IReviewerFieldInfoService reviewerFieldInfoService;

    @Resource
    private IHospitalDomainGroupService hospitalDomainGroupService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 查询领域
     *
     * @param id        领域主键
     * @param versionId 版本id
     * @return 领域
     */
    @Override
    public Map selectCstDomainById(Long id, String versionId) {
        Map result = MapUtil.newHashMap();
        CstCertificationStandards cstCertificationStandards = new CstCertificationStandards();
        cstCertificationStandards.setDomainId(id);
        Long vId;
        List<CstVersioning> cstVersioningList = cstVersioningMapper.selectCstVersioningListByDomainId(id);
        if (id == 0) {
            // 新增领域时 id 传0  versionId返回未分配的版本号
            if (CollectionUtils.isEmpty(cstVersioningList)) {
                log.error("当前认证标准已全部分配完成,不能进行新增领域操作");
                throw new ServiceException(ServiceExceptionEnum.CST_VERSIONING_ERROR_1000003);
            }
            List<String> versionIds = cstVersioningList.stream().map(a -> a.getVersionId().toString()).collect(Collectors.toList());
            if (StringUtils.isNotBlank(versionId) && !versionIds.contains(versionId)) {
                log.error("当前入参认证标准:{}不在未分配领域:{}内,不能进行新增领域操作", versionId, String.join(",", versionIds));
                throw new ServiceException(ServiceExceptionEnum.CST_VERSIONING_ERROR_1000004);
            }

            if (StrUtil.isNotBlank(versionId)) {
                vId = Long.valueOf(versionId);
            } else {
                vId = cstVersioningList.get(0).getVersionId();
            }
        } else {
            // 编辑领域时  id传对应的领域id  versionId使用当前版本号
            CstDomain cstDomain = this.getDomainInfo(id);
            vId = cstDomain.getVersionId();
            result.put("cstDomain", cstDomain);
            // 查找对应领域分组详情
            List<DomainGroupNode> children = hospitalDomainGroupService.selectDomainGroup(String.valueOf(id), versionId);
            List<String> delGroupIds = new ArrayList<>();
            hospitalDomainGroupService.genDelGroupIds(children, delGroupIds);
            result.put("children", children);
            result.put("delGroupIds", delGroupIds);
        }
        cstCertificationStandards.setVersionId(vId);
        List<CstCertificationStandards> cstCertificationStandardsList = cstCertificationStandardsService.selectCstCertificationStandardsList(cstCertificationStandards);
        List<Long> clauseIds = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(cstCertificationStandardsList)){
            clauseIds = cstCertificationStandardsList.stream().map(a -> a.getClauseId()).collect(Collectors.toList());
        }
        result.put("versionId", vId);
        result.put("standardsList", clauseIds);
        result.put("versionList", cstVersioningList);
        return result;
    }

    /**
     * 查询领域列表
     *
     * @param cstDomain 领域
     * @return 领域
     */
    @Override
    public List<CstDomain> selectCstDomainList(CstDomain cstDomain) {
        return cstDomainMapper.selectCstDomainList(cstDomain);
    }

    /**
     * 新增领域
     *
     * @param cstDomainVO 领域
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer insertCstDomain(CstDomainVO cstDomainVO) {
        CstDomain cstDomain = BeanUtil.copyProperties(cstDomainVO, CstDomain.class);
        // 插入领域表，同时获取自增领域id
        cstDomain.setCreateTime(DateUtils.getNowDate());
        String clauseIds = StrUtil.EMPTY;
        if (CollectionUtil.isNotEmpty(cstDomainVO.getClauseIds())) {
            // 校验款id是否归属当前版本
            this.validClauseId(cstDomain.getVersionId(), cstDomainVO.getClauseIds());
            // 关联款项数量
            cstDomain.setCstCount(cstDomainVO.getClauseIds().size());
            // list转为逗号分割
            clauseIds = cstDomainVO.getClauseIds().stream().map(String::valueOf).collect(Collectors.joining(","));
        }
        if(StringUtils.isBlank(cstDomain.getDomainName()) && StringUtils.isNotEmpty(cstDomain.getGroupName())){
            cstDomain.setDomainName(cstDomain.getGroupName());
        }
        cstDomain.setStatus("0");
        Integer rows = cstDomainMapper.insertCstDomain(cstDomain);
        cstDomainVO.setId(cstDomain.getId());
        String domainId = StrUtil.toString(cstDomain.getId());

        // 根据clauseIds和versionId，去更新款项表的领域id
        if (StrUtil.isNotBlank(clauseIds)) {
            Map paramMap = MapUtil.newHashMap();
            paramMap.put("domainId", domainId);
            paramMap.put("versionId", cstDomainVO.getVersionId());
            paramMap.put("clauseIds", clauseIds);
            Integer rows2 = cstCertificationStandardsService.updateByClauseIdAndVersionIds(paramMap);
        }
        // 将领域对应的分组主题小主题条款等
        hospitalDomainGroupService.insertHospitalDomainGroup(cstDomainVO);
        return rows;
    }

    @Override
    public List<DomainGroupNode> selectGroupNodeByNotInIds(String domainIds) {
        return cstDomainMapper.selectGroupNodeByNotInIds(domainIds);
    }

    @Override
    public List<DomainGroupNode> selectCstDomainGroup(String domainIds, String versionId) {
        return cstDomainMapper.selectCstDomainGroup(domainIds, versionId);
    }

    @Override
    public int saveGroupIdListById(List<DomainGroupNode> insertList, Long id) {
        StringBuilder groupIdList = new StringBuilder();
        for (DomainGroupNode domainGroupNode : insertList) {
            groupIdList.append(domainGroupNode.getGroupId());
            groupIdList.append(",");
        }
        groupIdList.deleteCharAt(groupIdList.length() - 1);
        CstDomain cstDomain = new CstDomain();
        cstDomain.setId(id);
        cstDomain.setGroupIdList(groupIdList.toString());
        return cstDomainMapper.updateCstDomain(cstDomain);
    }

    /**
     * 修改领域
     *
     * @param cstDomainVO 领域
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateCstDomain(CstDomainVO cstDomainVO) {
        CstDomain cstDomain = new CstDomain();
        if (CollectionUtils.isNotEmpty(cstDomainVO.getClauseIds())) {
            // 领域修改时，如果条款有改动，需要校验是否在途
            cstDomain = this.getDomainInfo(cstDomainVO.getId());
            // 需要领域，需要校验领域对应的医院评估版本是否有在途信息（对应的版本号在自评关联表是否有有效数据）
            this.validDomainIsInTransitData(cstDomain.getVersionId());
            // 校验款id是否归属当前版本
            this.validClauseId(cstDomain.getVersionId(), cstDomainVO.getClauseIds());

            // 删除评估标准对应的领域id信息（将对应领域id修改为0）
            CstCertificationStandards cstCertificationStandards = new CstCertificationStandards();
            cstCertificationStandards.setVersionId(cstDomain.getVersionId());
            cstCertificationStandards.setDomainId(cstDomain.getId());
            cstCertificationStandardsService.updateByDomainIdAndVersionId(cstCertificationStandards);
            // list转为逗号分割
            Map paramMap = MapUtil.newHashMap();
            paramMap.put("domainId", cstDomain.getId());
            paramMap.put("versionId", cstDomain.getVersionId());
            paramMap.put("clauseIds", cstDomainVO.getClauseIds().stream().map(String::valueOf).collect(Collectors.joining(",")));
            cstCertificationStandardsService.updateByClauseIdAndVersionIds(paramMap);

            // 关联款项数量
            cstDomain.setCstCount(cstDomainVO.getClauseIds().size());
        }

        // 修改领域对应分组下的详情
        hospitalDomainGroupService.updateHospitalDomainGroup(cstDomainVO);

        cstDomain.setId(cstDomainVO.getId());
        cstDomain.setRemarks(cstDomainVO.getRemarks());
        cstDomain.setDomainName(cstDomainVO.getDomainName());
        cstDomain.setGroupName(cstDomainVO.getGroupName());
        cstDomain.setState(cstDomainVO.getState());
        cstDomain.setUpdateTime(DateUtils.getNowDate());
        cstDomain.setGroupIdList(null);
        if(StringUtils.isBlank(cstDomain.getDomainName()) && StringUtils.isNotEmpty(cstDomain.getGroupName())){
            cstDomain.setDomainName(cstDomain.getGroupName());
        }
        // 启动或关闭操作保存CstDomain数据
        return cstDomainMapper.updateCstDomain(cstDomain);
    }

    /**
     * 删除领域信息
     *
     * @param id 领域主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteCstDomainById(Long id) {
        // 获取领域信息
        CstDomain cstDomain = this.getDomainInfo(id);
        // 删除领域，需要校验领域对应的医院评估版本是否有在途信息（对应的版本号在自评关联表是否有有效数据）
        this.validDomainIsInTransitData(cstDomain.getVersionId());
        // 删除评估标准对应的领域id信息（将对应领域id修改为0）
        CstCertificationStandards cstCertificationStandards = new CstCertificationStandards();
        cstCertificationStandards.setVersionId(Long.valueOf(cstDomain.getVersionId()));
        cstCertificationStandards.setDomainId(id);
        cstCertificationStandardsService.updateByDomainIdAndVersionId(cstCertificationStandards);
        // 删除评审员领域关联表内领域信息（将对应数据状态修改为2）
        reviewerFieldInfoService.updateStatusByFieldCode(id.toString(),Constants.HospitalConstants.STR_NUM_2);
        // 清楚缓存数据
        redisCache.deleteObject(Constants.STANDARDS_DETAIL + cstDomain.getVersionId());
        // 删除领域信息表（将对应数据状态修改为1）
        cstDomain.setStatus(Constants.HospitalConstants.STR_NUM_1);
        return cstDomainMapper.updateCstDomain(cstDomain);
    }

    /**
     * 校验领域id是否有在途信息
     *
     * @param versionId 评估标准版本id
     */
    private void validDomainIsInTransitData(Long versionId) {
        // 判断领域是否在途
        AutSaRelation autSaRelation = new AutSaRelation();
        autSaRelation.setAutCsId(versionId.toString());
        autSaRelation.setStatus(Constants.HospitalConstants.NUM_1);
        List<AutSaRelation> autSaRelations = autSaRelationService.selectAutSaRelationListByCondition(autSaRelation);
        if (CollectionUtils.isNotEmpty(autSaRelations)) {
            log.error("领域id：对应的评估标准版本：{} 存在在途数据，不能对应进行删除/更新操作", versionId);
            throw new ServiceException(ServiceExceptionEnum.CST_VERSIONING_ERROR_1000002);
        }
    }

    /**
     * 获取领域信息
     *
     * @param id 领域id
     * @return 领域信息
     */
    private CstDomain getDomainInfo(Long id) {
        CstDomain cstDomain = cstDomainMapper.selectCstDomainById(id);
        if (cstDomain == null) {
            log.error("根据领域id：{} 查询到的领域信息为空", id);
            throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000002);
        }
        return cstDomain;
    }

    /**
     * 校验款id是否归属当前版本
     *
     * @param versionId
     * @param clauseIds
     */
    private void validClauseId(Long versionId, List<String> clauseIds) {
        // 查询版本对应的款id信息
        List<CstCertificationStandards> cstCertificationStandards = cstCertificationStandardsService.selectAllClauseIdByVersionId(versionId.toString());
        if (CollectionUtils.isEmpty(cstCertificationStandards)) {
            log.error("未查询到版本:{}对应款项信息", versionId);
            throw new ServiceException(ServiceExceptionEnum.CST_VERSIONING_ERROR_1000005);
        }
        List<String> allClauseId = cstCertificationStandards.stream().map(a -> a.getClauseId().toString()).collect(Collectors.toList());
        clauseIds.forEach(a -> {
            if (!allClauseId.contains(a)) {
                log.error("当前版本：{} 没有款id:{}", versionId, a);
                throw new ServiceException(ServiceExceptionEnum.CST_VERSIONING_ERROR_1000005);
            }
        });
    }

}
