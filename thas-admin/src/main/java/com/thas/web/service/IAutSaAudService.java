package com.thas.web.service;

import com.thas.web.domain.AutSaAud;
import com.thas.web.domain.MemberRevisionInfo;
import com.thas.web.domain.vo.AutSaAudDetailListVO;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

/**
 * 认证自评审核Service接口
 *
 * <AUTHOR>
 * @date 2022-01-17
 */
public interface IAutSaAudService {

    /**
     * 获取当前自评审核信息
     *
     * @return
     */
    Map<String, Object> selectCurrentStageSaAud();

    /**
     * 获取医院的全部自评审核信息记录
     *
     * @return
     */
    List<AutSaAudDetailListVO> selectAutSaAudDetailList(String autCode);

    /**
     * 根据自评编码和提交类型查询相关数据
     *
     * @param autCode     自评编码
     * @param submitTypes 提交类型
     * @return 审核信息
     */
    List<AutSaAud> selectAutSaAudListByAutCodeAndTypes(String autCode, String submitTypes);

    /**
     * 获取组员修订信息列表
     * @param autCode 自评编码
     * @return 组员修订信息列表
     */
    List<MemberRevisionInfo> selectMemberRevisionInfos(String autCode);

    /**
     * 更新组员修订信息备注
     * @param memberRevisionInfo 修订信息
     */
    void updateMemberRevisionInfo(MemberRevisionInfo memberRevisionInfo);

}
