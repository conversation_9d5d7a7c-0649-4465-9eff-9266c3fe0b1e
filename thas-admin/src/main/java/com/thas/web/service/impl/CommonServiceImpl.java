package com.thas.web.service.impl;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.json.JSONUtil;
import com.thas.common.constant.Constants;
import com.thas.common.core.domain.entity.SysRole;
import com.thas.common.core.domain.entity.SysUser;
import com.thas.common.crypto.CryptoImpl;
import com.thas.common.crypto.ICrypto;
import com.thas.common.crypto.enums.AlgorithmEnum;
import com.thas.common.enums.FileTemplateEnum;
import com.thas.common.enums.ServiceExceptionEnum;
import com.thas.common.exception.ServiceException;
import com.thas.common.properties.AbstractSftpProperties;
import com.thas.common.properties.FtpProperties;
import com.thas.common.utils.*;
import com.thas.common.utils.file.FileUploadUtils;
import com.thas.common.utils.file.FileUtils;
import com.thas.common.utils.uuid.IdUtils;
import com.thas.framework.config.ServerConfig;
import com.thas.generator.util.NumberGenUtils;
import com.thas.system.service.ISysRoleService;
import com.thas.system.service.ISysUserService;
import com.thas.web.domain.FileInfoDTO;
import com.thas.web.domain.vo.FileInfoVO;
import com.thas.web.domain.vo.ReviewInterestVO;
import com.thas.web.dto.SysUserBaseInfo;
import com.thas.web.service.CommonService;
import com.thas.web.service.ICstVersioningService;
import com.thas.web.service.IHospitalPlannedDistributionService;
import com.thas.web.service.IUploadFileInfoService;

import java.io.OutputStream;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

/**
 * 认证记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-17
 */
@Slf4j
@Component
public class CommonServiceImpl implements CommonService {

    @Resource
    private IUploadFileInfoService iuploadFileInfoService;

    @Resource
    private ServerConfig serverConfig;

//    @Resource
    private FtpProperties ftpProperties;

    @Autowired
    private ISysRoleService iSysRoleService;

    @Autowired
    private ISysUserService iSysUserService;

    @Autowired
    private AbstractSftpProperties sftpImageProperties;

    @Autowired
    private AbstractSftpProperties sftpVideoProperties;

    @Autowired
    private IHospitalPlannedDistributionService iHospitalPlannedDistributionService;

    @Autowired
    private ICstVersioningService iCstVersioningService;

    @Autowired
    private ICrypto crypto;

    @Value("${outFileDownload.prefix}")
    private String prefix;
    @Value("${outFileDownload.fileUrl}")
    private String fileUrl;
    @Value("${outFileDownload.resourceUrl}")
    private String resourceUrl;
    @Value("${privacy.crypto.sm4Key}")
    private String sm4Key;

    @Override
    public FileInfoVO uploadFile(MultipartFile file, String type, String fileNameSuffix,String downLoadFileName) {
        this.checkFile(file,downLoadFileName);
        try {
            return uploadByType(file, type,fileNameSuffix,downLoadFileName);
        } catch (Exception e) {
            log.error("上传文件:{} 异常,e:{}", file.getOriginalFilename(), e.getMessage());
         //   throw new ServiceException("上传文件" + file.getOriginalFilename() + " 异常, massage:" + e.getMessage());
            throw new ServiceException(e.getMessage());
        }
    }


    /**
     * 校验文件格式
     *
     * @param file file资源
     * @param downLoadFileName
     */
    private void checkFile(MultipartFile file, String downLoadFileName) {
        // 获取文件后缀
        String originalFilename = file.getOriginalFilename();
        assert originalFilename != null;
        String suffix = originalFilename.substring(originalFilename.lastIndexOf("."));
        if (!Constants.FILE_SUFFIX.contains(suffix)) {
            throw new ServiceException("文件格式不合法!");
        }
        if(StringUtils.isNotEmpty(downLoadFileName)){
            String suffix1 = downLoadFileName.substring(downLoadFileName.lastIndexOf("."));
            if (!Constants.FILE_SUFFIX.contains(suffix1)) {
                throw new ServiceException(String.format("下载文件名格式不合法，应为：[%s]",Constants.FILE_SUFFIX.toString()) );
            }
        }


    }

    private FileInfoVO uploadByType(MultipartFile file, String type, String fileNameSuffix, String downLoadFileName) throws Exception {
        // 获取sftp配置文件
        AbstractSftpProperties sftpProperties = getSftpProperties(type);
        if (file.getSize() <= 0) {
            throw new ServiceException(ServiceExceptionEnum.FILE_ERROR_1000001);
        }

        if (file.getSize() > Integer.parseInt(sftpProperties.getMaxFileSize())) {
            throw new ServiceException(ServiceExceptionEnum.FILE_ERROR_1000002);
        }
        // 获取文件路径
        String path = NumberGenUtils.genFilePath(sftpProperties.getBasePath());
        // 获取文件后缀
        String originalFilename = file.getOriginalFilename();
        assert originalFilename != null;
        String suffix = originalFilename.substring(originalFilename.lastIndexOf("."));
        // 获取文件名称
        String fileName = originalFilename.substring(0, originalFilename.lastIndexOf(".")) + fileNameSuffix +
                "_" + NumberGenUtils.genFileName(suffix);
        try (SftpUtil sftpUtil = new SftpUtil(sftpProperties)) {
            long start = Instant.now().toEpochMilli();
            sftpUtil.upload(path, fileName, file.getInputStream());
            long end = Instant.now().toEpochMilli();
            log.info("耗时:{}ms", end - start);
        } catch (Exception e) {
            log.error("上传文件失败:{}", e.getMessage());
            throw e;
        }
        String filePath = path + fileName;
        log.info("文件上传成功，文件路径:{}", filePath);
        FileInfoDTO fileInfoDTO = new FileInfoDTO();
        // 文件名 ,文件名{$fileNameSuffix}.PDF
        fileInfoDTO.setOrigin(originalFilename.substring(0, originalFilename.lastIndexOf(".")) + fileNameSuffix + suffix);
        // 文件存储路径
        fileInfoDTO.setPath(filePath);
        // 文件来源
        fileInfoDTO.setPlatform(sftpProperties.getPlatform());
        // 文件类型
        fileInfoDTO.setType(FileUploadUtils.getExtension(file));
        //下载文件名
        fileInfoDTO.setDownLoadFileName(downLoadFileName);
        fileInfoDTO = iuploadFileInfoService.saveUploadFileInfo(fileInfoDTO);
        return fileInfoDtoToVo(fileInfoDTO);
    }

    @Override
    public FileInfoVO uploadFile1(MultipartFile file) {
        try {
//            // 上传文件路径
//            String filePath = ThasConfig.getUploadPath();
//            // 上传并返回新文件名称
//            String path = FileUploadUtils.upload1(filePath, file);

            // 文件上传路径
            String filePath = ftpProperties.getUploadPath() + "/" + DateUtils.datePath();
            String fileName = file.getOriginalFilename();
            String extension = FileUploadUtils.getExtension(file);
            fileName = fileName.substring(0, fileName.lastIndexOf(".")) + "_" + IdUtils.fastUUID() + "." + extension;
            log.info("文件上传到ftp服务器地址：{}", filePath + "/" + fileName);
            long startTime = System.currentTimeMillis();
            if (!FtpUtil.uploadFile(ftpProperties, filePath, fileName, file.getInputStream())) {
                // 上传失败
                log.error("上传文件到ftp服务器：{} 异常", file.getOriginalFilename());
                throw new ServiceException("上传文件：" + file.getOriginalFilename() + "到ftp服务器 异常");
            }
//            String path =  ftpProperties.getHost()+":"+ftpProperties.getPort() + filePath + "/" +fileName;
            log.info("文件上传成功，文件路径：{}, 上传耗时:{}", filePath + "/" + fileName, System.currentTimeMillis() - startTime);

            FileInfoDTO fileInfoDTO = new FileInfoDTO();
            // 文件名
            fileInfoDTO.setOrigin(file.getOriginalFilename());
            // 文件存储路径
            fileInfoDTO.setPath(filePath + "/" + fileName);
            // 文件来源
            fileInfoDTO.setPlatform("0");
            // 文件类型
            fileInfoDTO.setType(extension);
            fileInfoDTO = iuploadFileInfoService.saveUploadFileInfo(fileInfoDTO);
            return this.fileInfoDtoToVo(fileInfoDTO);
        } catch (Exception e) {
            log.error("上传文件：{} 异常,e:{}", file.getOriginalFilename(), e.getMessage());
            throw new ServiceException("上传文件" + file.getOriginalFilename() + " 异常");
        }
    }


//    /**
//     * 批量文件上传
//     *
//     * @param files
//     * @return
//     */
//    @Override
//    public List<FileInfoVO> uploadFiles(MultipartFile[] files) {
//        //校验文件信息
//        checkFiles(files);
//        // 上传文件
//        List<FileInfoVO> list = Lists.newArrayList();
//        for (MultipartFile file : files) {
//            // 上传文件
//            list.add(this.uploadFile(file));
//        }
//        return list;
//    }

    /**
     * 下载文件
     *
     * @param fileId 文件id
     */
    @Override
    public void downloadFile(String fileId, HttpServletResponse response) {
        // 根据fileId直接查询文件信息
        FileInfoDTO fileInfoDTO = iuploadFileInfoService.getUploadFileInfoById(Integer.valueOf(fileId));
        if (Objects.isNull(fileInfoDTO)) {
            throw new ServiceException("入参fileId:{"+ fileId +"}没有查询到对应文件信息");
        }
        writeFile(fileInfoDTO, response);
    }

    @Override
    public void fileTemplateDownLoad(String fileTemplate, HttpServletResponse response) {
        // 通过模板code获取对应的文件路径
        FileTemplateEnum templateEnum = FileTemplateEnum.getPathByTemplateCode(fileTemplate);
        if (Objects.isNull(templateEnum)) {
            throw new ServiceException(ServiceExceptionEnum.FILE_ERROR_1000008);
        }
        FileInfoDTO fileInfoDTO = new FileInfoDTO();
        fileInfoDTO.setPath(templateEnum.getFilePath());
        fileInfoDTO.setPlatform(templateEnum.getType());
        writeFile(fileInfoDTO, response);
    }

    private void writeFile(FileInfoDTO fileInfoDTO, HttpServletResponse response){
        try {
            // 数据库资源地址
            // String downloadPath = fileInfoDTO.getPath();
            // 本地资源路径
            // String localPath = ThasConfig.getProfile();
            // downloadPath = localPath + StringUtils.substringAfter(downloadPath, Constants.RESOURCE_PREFIX);
            // 下载名称
            // String downloadName = StringUtils.substringBefore(StringUtils.substringAfterLast(downloadPath, "/"), "_");
            String type = fileInfoDTO.getPlatform();
            AbstractSftpProperties sftpProperties = getSftpProperties(type);
            String filePath = fileInfoDTO.getPath();
            String downloadName = filePath.substring(filePath.lastIndexOf("/") + 1);
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, downloadName);
            // FileUtils.writeBytes(downloadPath, response.getOutputStream());
            try (SftpUtil s = new SftpUtil(sftpProperties)) {
                byte[] bytes = s.download2Byte(filePath);
                OutputStream out = response.getOutputStream();
                out.write(bytes);
                out.flush();
            }
        } catch (Exception e) {
            log.error("文件下载异常 e:{}", e.getMessage());
            throw new ServiceException(ServiceExceptionEnum.FILE_ERROR_1000003);
        }
    }

    /**
     * 下载文件
     *
     * @param fileId 文件id
     */
    @Override
    public void downloadFile1(String fileId, HttpServletResponse response) {
        // 根据fileId直接查询文件信息
        FileInfoDTO fileInfoDTO = iuploadFileInfoService.getUploadFileInfoById(Integer.valueOf(fileId));
        if (null == fileInfoDTO) {
            throw new ServiceException("入参fileId 没有查询到对应文件信息");
        }
        try {
            log.info("下载ftp文件：{}，开始");
            long startTime = System.currentTimeMillis();
            FtpUtil.downloadFile(ftpProperties, fileInfoDTO.getPath(), fileInfoDTO.getOrigin(), response);
            log.info("下载ftp文件：{}，完成，耗时：{}", fileInfoDTO.getPath(), System.currentTimeMillis() - startTime);
//            // 数据库资源地址
//            String downloadPath = fileInfoDTO.getPath();
//            // 本地资源路径
//            String localPath = ThasConfig.getProfile();
//            // 文件路径
//            downloadPath = localPath + StringUtils.substringAfter(downloadPath, Constants.RESOURCE_PREFIX);
//            // 下载名称
//            String downloadName = StringUtils.substringBefore(StringUtils.substringAfterLast(downloadPath, "/"), "_");
//            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
//            FileUtils.setAttachmentResponseHeader(response, downloadName);
//            FileUtils.writeBytes(downloadPath, response.getOutputStream());
        } catch (Exception e) {
            log.error("文件下载异常 e:{}", e.getMessage());
        }
    }

    /**
     * 文件批量上传校验
     *
     * @param multipartFiles
     */
    private static void checkFiles(MultipartFile[] multipartFiles) {
        //校验文件个数
        int size = multipartFiles.length;
        if (size > 500) {
            log.error("文件数量不能超过500个");
            throw new ServiceException("文件数量不能超过500个");
        }
        //校验文件大小
        for (MultipartFile multipartFile : multipartFiles) {
            long fileSize = multipartFile.getSize();
            if (fileSize == 0) {
                log.error("文件大小不能为0KB");
                throw new ServiceException("文件大小不能为0KB");
            }
            if (fileSize > 1024 * 1024 * 100) {
                log.error("文件大小不能超过100M");
                throw new ServiceException("文件大小不能超过100M");
            }
        }
    }

    @Override
    public String getFileUrl(String paltForm, String path) {
        /*AbstractSftpProperties sftpProperties = getSftpProperties(paltForm);
        String host = "";
        if (Objects.nonNull(sftpProperties)) {
            host = sftpProperties.getHost();
        }*/
        int index = path.indexOf("upload");
        return resourceUrl + path.substring(index + 6);
    }

    /**
     * 文件FileInfoDTO类型转为FileInfoVO类型
     *
     * @param fileInfoDTO Dto格式
     * @return VO格式
     */
    @Override
    public FileInfoVO fileInfoDtoToVo(FileInfoDTO fileInfoDTO) {
        FileInfoVO fileInfoVO = new FileInfoVO();
        // 拼接url
        // 根据来源获取对应的ip
        String type = fileInfoDTO.getPlatform();
        AbstractSftpProperties sftpProperties = getSftpProperties(type);
        /*String host = "";
        if (Objects.nonNull(sftpProperties)) {
            host = sftpProperties.getHost();
        }*/
        String filePath = fileInfoDTO.getPath();
        int index = filePath.indexOf("upload");
        String url = resourceUrl + filePath.substring(index + 6);
        fileInfoVO.setUrl(url);
        fileInfoVO.setFileId(fileInfoDTO.getId().toString());
        fileInfoVO.setFileName(fileInfoDTO.getOrigin());
        //下载文件名如果为空，取文件名
        String downLoadFileName = fileInfoDTO.getDownLoadFileName();
        fileInfoVO.setDownLoadFileName(StringUtils.isEmpty(downLoadFileName) ? fileInfoDTO.getOrigin() : downLoadFileName);
        //加密
        String encryptUrl = crypto.encrypt(AlgorithmEnum.SM4, sm4Key, filePath.substring(index + 7));
        //外部下载链接 {resourceUrl}{prefix}{filePath} http://**************:1020/prod-api/resource/get/image/pdf/20230609104652256/XXX.pdf
        String outFileDownloadUrl = resourceUrl+ "/prod-api" + prefix + Constants.SLASH + encryptUrl;

        fileInfoVO.setOutFileDownloadUrl(outFileDownloadUrl);
        return fileInfoVO;
    }

    /**
     * 文件FileInfoDTO类型转为FileInfoVO类型
     *
     * @param fileInfoDtos Dto格式 列表
     * @return VO格式 列表
     */
    @Override
    public List<FileInfoVO> fileInfoDtoToVo(List<FileInfoDTO> fileInfoDtos) {
        return fileInfoDtos.stream().map(this::fileInfoDtoToVo).collect(Collectors.toList());
    }

    @Override
    public SysRole selectSysRole() {
        return SecurityUtils.getLoginUser().getUser().getRoles().get(0);
    }

    @Override
    public List<SysUser> selectUserByRoleKey(String roleKey, boolean flag) {
        return iSysUserService.selectUserByRoleKey(roleKey, flag);
    }

    @Override
    public List<SysUserBaseInfo> getBaseInfo(List<SysUser> sysUserList) {
        List<SysUserBaseInfo> res = new ArrayList<>();
        for (SysUser sysUser : sysUserList) {
            SysUserBaseInfo sysUserBaseInfo = new SysUserBaseInfo();
            sysUserBaseInfo.setAccountId(sysUser.getUserId().toString());
            sysUserBaseInfo.setUserName(sysUser.getUserName());
            sysUserBaseInfo.setName(sysUser.getNickName());
            res.add(sysUserBaseInfo);
        }
        return res;
    }


    @Override
    public void reviewInterestVOFilePathToUrl(List<ReviewInterestVO> reviewInterestVOList) {
        for (ReviewInterestVO reviewInterestVO : reviewInterestVOList) {
            String interestFileId = reviewInterestVO.getInterestFileId();
            if (StringUtils.isNotEmpty(interestFileId)) {
                String url = reviewInterestVO.getUrl();
                String platFrom = reviewInterestVO.getPlatFrom();
                FileInfoDTO fileInfoDTO = new FileInfoDTO();
                fileInfoDTO.setId(Long.valueOf(reviewInterestVO.getFileId()));
                fileInfoDTO.setPlatform(platFrom);
                fileInfoDTO.setPath(url);
                FileInfoVO fileInfoVO = fileInfoDtoToVo(fileInfoDTO);
                reviewInterestVO.setUrl(fileInfoVO.getUrl());
            }
        }
    }

    @Override
    public <T> T readerJsonFile(String path, Class<T> clz) {
        String jsonStr = ResourceUtil.readUtf8Str(path);
        return JSONUtil.parseObj(jsonStr).toBean(clz);
    }

    @Override
    public AbstractSftpProperties getSftpProperties(String type) {
        if (Constants.HospitalConstants.STR_NUM_1.equals(type)) {
            return sftpImageProperties;
        } else if (Constants.HospitalConstants.STR_NUM_2.equals(type)) {
            return sftpVideoProperties;
        }
        return null;
    }

}
