package com.thas.web.service;

import com.thas.common.core.domain.AjaxResult;
import com.thas.common.core.domain.entity.SysUser;
import com.thas.web.domain.CstOfflineTrainingManagement;
import com.thas.web.domain.CstReviewerOfflineTraining;
import com.thas.web.domain.QueryCstOfflineTrainingDTO;
import com.thas.web.domain.ReviewerBaseInfo;
import com.thas.web.domain.vo.OfflineTrainingRegisteredVo;
import com.thas.web.domain.vo.ReviewInterestVO;
import com.thas.web.domain.vo.TraineesReviewRecVO;
import com.thas.web.dto.*;

import java.util.List;

/**
 * 评审员基本信息Service接口
 *
 * <AUTHOR>
 * @date 2022-01-20
 */
public interface IReviewerBaseInfoService {


    AjaxResult reviewerBaseInfoSubmit(ReviewerBaseInfoDTO reviewerBaseInfoDTO, boolean isTmp);

    AjaxResult reviewerBaseInfoTmpSubmit(ReviewerBaseInfoDTO reviewerBaseInfoDTO);

    AjaxResult querySubmitStatus(QueryBaseConditionDTO queryBaseConditionDTO);

    List<QueryReviewerListDTO> queryReviewerList(QueryReviewerListDTO queryReviewerListDTO);

    List<ReviewerBaseInfo> queryReviewerTmpList(ReviewerBaseInfo reviewerBaseInfo);

    AjaxResult reviewerAccountDistribute(SysUser sysUser);

    ReviewerBaseInfoDTO queryReviewerDetail(String accountId);

    void updateSubmitStatusByAccountId(String accountId);

    int selectCountByAccountId(String accountId);

    int insertOrUpdateByAccountId(ReviewerBaseInfo reviewerBaseInfo);

    List<ReviewManageVO> queryReviewManage(ReviewManageDTO reviewManageDTO);

    AjaxResult offlineTrainingSubmit(List<String> idList);

    /**
     * 管理员针对线下培训审核对应已参与的评审员
     * @param revOfflineTraDTO 入参
     * @return 出参
     */
    AjaxResult reviewOfflineTra(RevOfflineTraDTO revOfflineTraDTO);

    List<CstOfflineTrainingManagement> queryCstOfflineTrainingList(QueryCstOfflineTrainingDTO queryCstOfflineTrainingDTO);

    int updateReviewerBaseInfoByUser(SysUser user);
    /**
     * 通过accountId判断在reviewer_base_info里面是否有有效的认证记录
     *
     * @param accountId 相当于sys_user中的user_id
     * @return true/false
     */
    boolean hasAuthReviewInfo(String accountId);

    void traineesToReviewer(QueryCstOfflineTrainingDTO queryCstOfflineTrainingDTO);

    List<TraineesReviewRecVO> traineesReviewList();

    AjaxResult offlineTrainingApply(List<CstReviewerOfflineTraining> cstReviewerOfflineTrainingList);

    List<OfflineTrainingRegisteredVo> offlineTrainingRegistered(String trainingId);

    ReviewerBaseInfo selectReviewerBaseInfoByAccountId(String accountId);

    List<ReviewInterestVO> interestList();

    /**
     * 导出评审员基本信息
     *
     * @param request 入参
     */
    void reviewerListExport(ReviewerExportRequest request);

    void updateReviewerRole(UpdateReviewerRoleReq request);

    /**
     * 临时查询评审员信息
     *
     * @param tempRevInfoRequest 请求入参
     * @return ReviewerBaseInfoDTO
     */
    ReviewerBaseInfoDTO bingQryReviewerDetail(TempRevInfoRequest tempRevInfoRequest);

    /**
     * 评审员数据再次提交
     *
     * @param request 请求入参
     * @return 提交结果
     */
    AjaxResult revAgainSubmit(AgainRevInfoSubmitRequest request);

    /**
     * 校验基本参数
     *
     * @param checkParamOnlyRequest 入参
     */
    void checkParamOnly(CheckParamOnlyRequest checkParamOnlyRequest);

    void checkHosBaseInfo(CheckParamOnlyRequest checkParamOnlyRequest);
}
