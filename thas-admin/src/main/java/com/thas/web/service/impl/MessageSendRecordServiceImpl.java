package com.thas.web.service.impl;

import java.security.GeneralSecurityException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import cn.hutool.core.util.ObjectUtil;
import com.thas.common.constant.Constants;
import com.thas.common.exception.ServiceException;
import com.thas.common.utils.DateUtils;
import com.thas.common.utils.SecurityUtils;
import com.thas.common.utils.StringUtils;
import com.thas.common.utils.sms.SMSSendTool;
import com.thas.system.domain.vo.UserVo;
import com.thas.web.domain.MessageReceiveRecord;
import com.thas.web.domain.MessageSendUser;
import com.thas.web.domain.MessageTemplate;
import com.thas.web.domain.dto.MessageSendRecordDTO;
import com.thas.web.mapper.MessageReceiveRecordMapper;
import com.thas.web.mapper.MessageSendUserMapper;
import com.thas.web.mapper.MessageTemplateMapper;
import com.thas.web.utils.EmailUtil;
import lombok.extern.java.Log;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.thas.web.mapper.MessageSendRecordMapper;
import com.thas.web.domain.MessageSendRecord;
import com.thas.web.service.IMessageSendRecordService;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.mail.MessagingException;
import javax.mail.Session;
import javax.mail.internet.MimeMessage;

/**
 * 消息发送记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-25
 */
@Service
@Slf4j
public class MessageSendRecordServiceImpl implements IMessageSendRecordService {
    @Autowired
    private MessageSendRecordMapper messageSendRecordMapper;
    @Autowired
    private MessageTemplateMapper messageTemplateMapper;
    @Autowired
    private MessageSendUserMapper messageSendUserMapper;
    @Autowired
    private MessageReceiveRecordMapper messageReceiveRecordMapper;

    @Value("${email.emailNo}")
    private String emailNo;
    @Value("${email.password}")
    private String password;
    @Value("${sms.environment}")
    private String environment;
    @Value("${email.SMTPService}")
    private String SMTPService;
    @Value("${email.environment}")
    private String emailEnvironment;

    /**
     * 查询消息发送记录
     *
     * @param id 消息发送记录主键
     * @return 消息发送记录
     */
    @Override
    public MessageSendRecord selectMessageSendRecordById(Long id) {
        return messageSendRecordMapper.selectMessageSendRecordById(id);
    }

    /**
     * 查询消息发送记录列表
     *
     * @param messageSendRecord 消息发送记录
     * @return 消息发送记录
     */
    @Override
    public List<MessageSendRecord> selectMessageSendRecordList(MessageSendRecord messageSendRecord) {
        return messageSendRecordMapper.selectMessageSendRecordList(messageSendRecord);
    }

    /**
     * 新增消息发送记录
     *
     * @param messageSendRecord 消息发送记录
     * @return 结果
     */
    @Override
    public int insertMessageSendRecord(MessageSendRecord messageSendRecord) {
        messageSendRecord.setCreateTime(DateUtils.getNowDate());
        return messageSendRecordMapper.insertMessageSendRecord(messageSendRecord);
    }

    /**
     * 修改消息发送记录
     *
     * @param messageSendRecord 消息发送记录
     * @return 结果
     */
    @Override
    public int updateMessageSendRecord(MessageSendRecord messageSendRecord) {
        messageSendRecord.setUpdateTime(DateUtils.getNowDate());
        return messageSendRecordMapper.updateMessageSendRecord(messageSendRecord);
    }

    /**
     * 批量删除消息发送记录
     *
     * @param ids 需要删除的消息发送记录主键
     * @return 结果
     */
    @Override
    public int deleteMessageSendRecordByIds(Long[] ids) {
        return messageSendRecordMapper.deleteMessageSendRecordByIds(ids);
    }

    /**
     * 删除消息发送记录信息
     *
     * @param id 消息发送记录主键
     * @return 结果
     */
    @Override
    public int deleteMessageSendRecordById(Long id) {
        return messageSendRecordMapper.deleteMessageSendRecordById(id);
    }


    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public boolean sendMessageToUser(MessageSendRecordDTO messageSendRecordDTO) throws GeneralSecurityException, MessagingException {
        //判断是否选择模板
        Long messageTemplateId = messageSendRecordDTO.getMessageTemplateId();
        String content = "";
        String sendType = "";
        if (ObjectUtils.isNotEmpty(messageTemplateId) && StringUtils.isBlank(messageSendRecordDTO.getSendType()) &&
                StringUtils.isBlank(messageSendRecordDTO.getContent())) {
            //获取模板的内容以及发送类型
            MessageTemplate messageTemplate = messageTemplateMapper.selectMessageTemplateById(messageTemplateId);
            sendType = messageTemplate.getSendType();
            content = messageTemplate.getContent();
        } else {
            content = messageSendRecordDTO.getContent();
            sendType = messageSendRecordDTO.getSendType();
        }
        if (StringUtils.isEmpty(content)) {
            throw new ServiceException("内容不能为空！");
        }
        if (StringUtils.isEmpty(sendType)) {
            throw new ServiceException("发送类型不能为空！");
        }

        MessageSendRecord messageSendRecord = new MessageSendRecord();
        messageSendRecord.setSendType(sendType);
        messageSendRecord.setContent(content);
        messageSendRecord.setStatus("1");
        messageSendRecord.setCreateTime(DateUtils.getNowDate());
        if (ObjectUtil.equal(messageSendRecordDTO.getTaskStatus(), Constants.STR_NUM_0)) {
            messageSendRecord.setCreateBy(SecurityUtils.getUsername());
        }

        //插入发送记录表
        messageSendRecordMapper.insertMessageSendRecord(messageSendRecord);
        Long sendRecordId = messageSendRecord.getId();
        //插入发送用户关联表
        List<UserVo> userVoList = Lists.newArrayList();
        userVoList.addAll(messageSendRecordDTO.getUserVoList());
        List<String> ccEmailNo = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(messageSendRecordDTO.getCcUserVoList())) {
            userVoList.addAll(messageSendRecordDTO.getCcUserVoList());
            ccEmailNo = messageSendRecordDTO.getCcUserVoList().stream()
                    .filter(o -> StringUtils.isNotEmpty(o.getEmail()))
                    .map(UserVo::getEmail).collect(Collectors.toList());
        }
        List<MessageSendUser> messageSendUserList = new ArrayList<>();
        List<String> emailList = new ArrayList<>();
        List<String> phoneNumberList = new ArrayList<>();
        List<MessageReceiveRecord> messageReceiveRecordList = new ArrayList<>();
        String finalContent = content;
        userVoList.forEach(userVo -> {
            //消息接收记录
            MessageReceiveRecord messageReceiveRecord = new MessageReceiveRecord();
            messageReceiveRecord.setUserId(userVo.getUserId());
            messageReceiveRecord.setContent(finalContent);
            messageReceiveRecord.setCreateTime(DateUtils.getNowDate());
            if (ObjectUtil.equal(messageSendRecordDTO.getTaskStatus(), Constants.STR_NUM_0)) {
                messageSendRecord.setCreateBy(SecurityUtils.getUsername());
            }
            messageReceiveRecordList.add(messageReceiveRecord);
            phoneNumberList.add(userVo.getPhonenumber());
            messageSendUserList.add(new MessageSendUser(sendRecordId, userVo.getUserId()));
        });
        messageSendUserMapper.insertMessageSendUserList(messageSendUserList);
        //判断发送类型（系统发送0/邮件发送1/手机短信发送2）
        if (sendType.contains(Constants.STR_NUM_0)) {
            //系统发送就是插入消息接收表
            messageReceiveRecordMapper.insertMessageReceiveRecordList(messageReceiveRecordList);
        }
        if (sendType.contains(Constants.STR_NUM_1)) {
            //配置生产环境时，发送短信
            if (emailEnvironment.equals("test")) {
                log.info("测试环境暂时未开通邮箱发送功能！收件人邮箱：{}，邮箱内容：{}，邮箱标题：{}", emailList, content, messageSendRecordDTO.getEmailTitle());
            } else {
                emailList = messageSendRecordDTO.getUserVoList().stream().map(UserVo::getEmail).distinct().collect(Collectors.toList());
                ccEmailNo = ccEmailNo.stream().distinct().collect(Collectors.toList());
                if (CollectionUtils.isEmpty(emailList)) {
                    throw new ServiceException("收件人为空，发送邮箱失败！");
                }
                //邮件发送（往用户邮箱发送）
                Session session = EmailUtil.createSession(SMTPService);
                MimeMessage message = EmailUtil.createSimpleMail(session, emailList, content,
                        StringUtils.isNotEmpty(messageSendRecordDTO.getEmailTitle()) ? messageSendRecordDTO.getEmailTitle() : "管理员发送", emailNo, ccEmailNo);
                EmailUtil.sendMessage(session, message, SMTPService, emailNo, password);
            }
        }

        if (sendType.contains(Constants.STR_NUM_2)) {
            if(environment.equals("test")){
                log.info("测试环境暂时未开通手机短信功能！手机号为：{}，内容为：{}",phoneNumberList,content);
                return true;
            }
            AtomicReference<Boolean> b = new AtomicReference<>(false);
            //手机短信发送
            phoneNumberList.forEach(phoneNumber -> {
                String result = SMSSendTool.sendSMS(phoneNumber, finalContent);
                if (!"success".equals(result)) {
                    log.info("【通知模板管理-发布通知】手机号码" + phoneNumber + "，接收短信失败！");
                } else {
                    b.set(true);
                }
            });
            //短信发送失败(单个用户不影响其他用户，全部失败抛异常)
            if (!b.get()) {
                throw new ServiceException("短信发送失败！");
            }
        }
        return true;
    }

    @Override
    public void sendMsgFromSms(String msg, String phone) {
        if(environment.equals("test")){
            log.info("测试环境暂时未开通手机短信功能！手机号为：{}，内容为：{}",phone,msg);
            return;
        }
        List<String> phoneNumberList = Collections.singletonList(phone);
        AtomicReference<Boolean> b = new AtomicReference<>(false);
        //手机短信发送
        phoneNumberList.forEach(phoneNumber -> {
            String result = SMSSendTool.sendSMS(phoneNumber, msg);
            if (!"success".equals(result)) {
                log.error("手机号码:{}，发送短信失败！", phoneNumber);
            } else {
                b.set(true);
            }
        });
        //短信发送失败(单个用户不影响其他用户，全部失败抛异常)
        if (Boolean.FALSE.equals(b.get())) {
            throw new ServiceException("短信发送失败！");
        }
    }

    @Override
    public void sendMsgFromEmail(String msg, String email, String emailTitle) {
        // 邮件发送（往用户邮箱发送）
        if (emailEnvironment.equals("test")) {
            log.info("测试环境暂时未开通邮箱发送功能！收件人邮箱：{}，邮箱内容：{}，邮箱标题：{}", email, msg, emailTitle);
            return;
        }
        Session session;
        try {
            session = EmailUtil.createSession(SMTPService);
        } catch (GeneralSecurityException e) {
            log.error("邮箱通知创建session异常:{}", e.getMessage(), e);
            throw new ServiceException("邮箱通知创建session异常");
        }
        List<String> emailList = Collections.singletonList(email);
        MimeMessage message;
        try {
            message = EmailUtil.createSimpleMail(session, emailList, msg, emailTitle, emailNo, null);
        } catch (MessagingException e) {
            log.error("邮箱通知创建message异常:{}", e.getMessage(), e);
            throw new ServiceException("邮箱通知创建message异常");
        }
        try {
            EmailUtil.sendMessage(session, message, SMTPService, emailNo, password);
        } catch (MessagingException e) {
            log.error("发送邮件异常:{}", e.getMessage(), e);
            throw new ServiceException("发送邮件异常");
        }
    }
}
