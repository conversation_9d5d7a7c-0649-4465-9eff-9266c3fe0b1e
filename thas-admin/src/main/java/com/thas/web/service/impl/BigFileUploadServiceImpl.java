package com.thas.web.service.impl;

import com.thas.common.config.ThasConfig;
import com.thas.common.constant.Constants;
import com.thas.common.enums.AutSaAudBusinessCodeEnum;
import com.thas.common.enums.ServiceExceptionEnum;
import com.thas.common.exception.ServiceException;
import com.thas.common.properties.AbstractSftpProperties;
import com.thas.common.utils.SftpUtil;
import com.thas.generator.util.NumberGenUtils;
import com.thas.web.domain.AutSaAudBusinessData;
import com.thas.web.domain.FileInfoDTO;
import com.thas.web.domain.UploadChunkInfo;
import com.thas.web.domain.UploadResult;
import com.thas.web.service.BigFileUploadService;
import com.thas.web.service.IUploadFileInfoService;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.LinkOption;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.time.Instant;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 大文件上传Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-10
 */
@Slf4j
@Component
public class BigFileUploadServiceImpl implements BigFileUploadService {

    @Resource
    private IUploadFileInfoService uploadFileInfoService;

    @Resource
    private AutSaAudBusinessDataServiceImpl autSaAudBusinessDataService;

    @Autowired
    private AbstractSftpProperties sftpVideoProperties;

    @Value("${outFileDownload.resourceUrl}")
    private String resourceUrl;

    /**
     * 校验文件已上传分片信息
     *
     * @param uploadChunkInfo 上传分块信息
     * @return 上传结果
     */
    @Override
    public UploadResult checkChunk(UploadChunkInfo uploadChunkInfo) {
        // 1. 首先判断以前是否有上传sftp成功 （查表获取对应信息）：
        //      没有数据走2
        //      有数据,说明以前已经上传成功过文件内容， 根据文件名+md5值判断是否是之前是否有上传该文件名的文件，有 返回对应的fileId信息和路径，没有，需要将当前文件名+md5值落表保存生成新的fileId和路径返回前端，
        // 2. 判断是否有合并文件：
        //      没有合并文件走3
        //      有合并文件,说明之前分片文件全部上传本地，但未上传sftp，返回前端合并标识，前端调用合并文件接口，上传合并文件至sftp服务器，返回对应的fileId信息和路径
        // 3. 判断是否有分片文件上传:
        //      没有分片文件信息，返回前端空数据，前端需要从头开始进行分片上传
        //      有分片文件上传，返回对应的分片文件信息，前端跳过这些分片，继续上传后续分片
        String sftpUrl = null;
        List<AutSaAudBusinessData> autSaAudBusinessDataList = autSaAudBusinessDataService.selectAutSaAudBusinessData(uploadChunkInfo.getIdentifier(), AutSaAudBusinessCodeEnum.UPLOAD_PIECE_INFO.getCode());
        if (CollectionUtils.isNotEmpty(autSaAudBusinessDataList)) {
            // 如果该文件之前有上传完成     取出文件对应url信息   没有上传完成，则为空
            sftpUrl = autSaAudBusinessDataList.get(0).getData();
        }
        UploadResult uploadResult = new UploadResult();
        if (StringUtils.isBlank(sftpUrl)) {
            // 如果sftpUrl为空，再次查询已上传分片信息 直接从本地读取已上传分片信息
            getUploadedFileInfo(uploadChunkInfo.getFilename(), uploadChunkInfo.getIdentifier(), uploadChunkInfo.getTotalChunks(), uploadResult);
        } else {
            //  说明之前有上传过本地，并已经上传到了sftp服务器  秒传
            getFileInfo(uploadChunkInfo.getFilename(), sftpUrl, uploadResult);
        }
        log.info("checkChunk uploadResult:{}", uploadResult);
        return uploadResult;
    }

    /**
     * 获取已上传文件信息
     *
     * @param fileName     文件名
     * @param identifier   文件唯一标识
     * @param totalChunks  总分片文件数
     * @param uploadResult 已上传分片文件信息
     */
    private void getUploadedFileInfo(String fileName, String identifier, int totalChunks, UploadResult uploadResult) {
        // 文件夹路径
        String folderUrl = ThasConfig.getUploadPath() + "/" + identifier;
        try {
            List<String> uploadedChunkLists = Lists.newArrayList();
            if(!Files.exists(Paths.get(folderUrl))){
                log.info("文件:{}不存在", folderUrl);
                return;
            }
            List<Path> filePaths = Files.list(Paths.get(folderUrl)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filePaths)) {
                for (Path path : filePaths) {
                    String pathFileName = path.getFileName().toString();
                    if (pathFileName.equals(fileName)) {
                        // 文件存在，直接返回前端继续合并文件
                        uploadResult.setMergeFile(true);
                        return;
                    } else {
                        uploadedChunkLists.add(pathFileName.substring(pathFileName.lastIndexOf("-") + 1));
                    }
                }
            }
            if (uploadedChunkLists.size() == totalChunks) {
                // 已经全部上传， 需要通知前端调用合并接口，进行上传sftp操作
                uploadResult.setMergeFile(true);
            } else {
                // 没有完成上传，需要通知前端已经上传的分片信息，前端跳过这些分片，继续上传后续分片信息，达到断点续传功能
                uploadResult.setUploadedChunks(uploadedChunkLists);
            }
        } catch (Exception e) {
            log.error("读取文件夹：{} 内文件异常 e:{}", folderUrl, e.getMessage());
            throw new ServiceException(ServiceExceptionEnum.FILE_ERROR_1000007);
        }
    }

    /**
     * 根据文件名和对应sftp路径获取文件信息
     *
     * @param fileName     文件名
     * @param sftpUrl      sftp路径
     * @param uploadResult 已上传分片文件信息
     */
    private void getFileInfo(String fileName, String sftpUrl, UploadResult uploadResult) {
        log.info("getFileInfo 开始 fileName：{}，sftpUrl：{}", fileName, sftpUrl);
        FileInfoDTO fileInfoDTO = new FileInfoDTO();
        fileInfoDTO.setOrigin(fileName);
        fileInfoDTO.setPath(sftpUrl);
        fileInfoDTO.setPlatform("2");
        List<FileInfoDTO> fileInfoList = uploadFileInfoService.selectUploadFileInfoList(fileInfoDTO);
        uploadResult.setSftpUrl(resourceUrl + sftpUrl.substring(sftpUrl.indexOf(Constants.UPLOAD_KEY) + 6));
        uploadResult.setSkipUpload(true);
        if (CollectionUtils.isNotEmpty(fileInfoList)) {
            // 之前上传过一样的文件，直接 秒传
            uploadResult.setFileId(fileInfoList.get(0).getId().toString());
        } else {
            // 之前上传过该文件，但文件名不一致 直接使用文件名和sftp路径 获取fileId
            fileInfoDTO.setType(fileName.substring(fileName.lastIndexOf(Constants.POINT_SEPARATOR)));
            fileInfoDTO = uploadFileInfoService.saveUploadFileInfo(fileInfoDTO);
            uploadResult.setFileId(fileInfoDTO.getId().toString());
        }
    }

    /**
     * 上传分片文件
     *
     * @param uploadChunkInfo 分片文件信息
     * @return 文件信息
     */
    @Override
    public UploadResult chunkUpload(UploadChunkInfo uploadChunkInfo) {
        log.info("chunkUpload 上传文件：{} 分片文件：{}  开始", uploadChunkInfo.getFilename(), uploadChunkInfo.getChunkNumber());
        //首先判断之前是否有进行分片上传，并且校验当前上传的分片之前是否有上传
        UploadResult uploadResult = this.checkChunk(uploadChunkInfo);
        if (uploadResult.isSkipUpload() || uploadResult.isMergeFile()) {
            // 已经完成上传 ，不用在上传
            log.info("当前文件：{} 已经完成上传", uploadChunkInfo.getFilename());
            return uploadResult;
        }

        List<String> uploadedChunkLists = uploadResult.getUploadedChunks();
        if (CollectionUtils.isNotEmpty(uploadedChunkLists)) {
            if (uploadedChunkLists.contains(String.valueOf(uploadChunkInfo.getChunkNumber()))) {
                // 如果之前已上传过该分片，直接返回前端，继续下一分片上传
                log.info("当前分片文件：{} 已经上传过", uploadChunkInfo.getChunkNumber());
                return uploadResult;
            }
            if (uploadedChunkLists.size() >= uploadChunkInfo.getTotalChunks()) {
                log.error("分片信息异常，已上传分片数：{}，大于或等于 入参总分片数：{}", uploadedChunkLists.size(), uploadChunkInfo.getTotalChunks());
                throw new ServiceException(ServiceExceptionEnum.FILE_ERROR_1000000);
            }
        }else{
            uploadedChunkLists = Lists.newArrayList();
        }

        // 上传分片文件
        this.uploadFile(uploadChunkInfo);

        // 组织反参
        uploadedChunkLists.add(uploadChunkInfo.getChunkNumber().toString());
        if (uploadedChunkLists.size() == uploadChunkInfo.getTotalChunks()) {
            // 已经全部上传， 需要通知前端调用合并接口，进行上传sftp操作
            uploadResult.setMergeFile(true);
        } else {
            // 没有完成上传，需要通知前端已经上传的分片信息，前端跳过这些分片，继续上传后续分片信息，达到断点续传功能
            uploadResult.setUploadedChunks(uploadedChunkLists);
        }
        return uploadResult;
    }

    /**
     * 上传分片文件
     *
     * @param uploadChunkInfo 分片文件信息
     */
    private void uploadFile(UploadChunkInfo uploadChunkInfo) {
        try {
            // 将文件上传到本地
            byte[] bytes = uploadChunkInfo.getFile().getBytes();
            Path path = Paths.get(this.generatePath(uploadChunkInfo));
            // 文件写入指定路径
            long startTime = System.currentTimeMillis();
            Files.write(path, bytes);
            long startTime1 = System.currentTimeMillis();
            log.info("上传分片文件：{} 完成 耗时：{}", path, startTime1 - startTime);
        } catch (Exception e) {
            log.error("上传文件：{} 分片：{} 异常 e：{}", uploadChunkInfo.getFilename(), uploadChunkInfo.getChunkNumber(), e.getMessage());
            throw new ServiceException(ServiceExceptionEnum.FILE_ERROR_1000000);
        }
    }

    /**
     * 生成上传分片文件路径
     *
     * @param uploadChunkInfo
     * @return
     */
    private String generatePath(UploadChunkInfo uploadChunkInfo) throws IOException {
        StringBuilder sb = new StringBuilder();
        sb.append(ThasConfig.getUploadPath()).append("/").append(uploadChunkInfo.getIdentifier());
        //判断 uploadFolder/identifier 路径是否存在，不存在则创建
        if (!Files.isWritable(Paths.get(sb.toString()))) {
            log.info("path not exist,create path: {}", sb);
            Files.createDirectories(Paths.get(sb.toString()));
        }
        return sb.append("/")
                .append(uploadChunkInfo.getFilename())
                .append("-")
                .append(uploadChunkInfo.getChunkNumber()).toString();
    }

    /**
     * 合并文件
     *
     * @param uploadChunkInfo 分片文件信息
     * @return 文件信息
     */
    @Override
    public UploadResult mergeFile(UploadChunkInfo uploadChunkInfo) {
        // 文件名
        String fileName = uploadChunkInfo.getFilename();
        // 文件夹路径
        String folderUrl = ThasConfig.getUploadPath() + "/" + uploadChunkInfo.getIdentifier();
        // 合并后的文件路径
        String mergeFileUrl = ThasConfig.getUploadPath() + "/" + uploadChunkInfo.getIdentifier() + "/" + fileName;

        // 合并文件，需要判断分片文件是否都已经上传
        this.merge(fileName, folderUrl, mergeFileUrl, uploadChunkInfo.getTotalSize());

        // sftp服务器上传路径
        String path = NumberGenUtils.genFilePath(sftpVideoProperties.getBasePath());
        // 将合并后的文件上传sftp
        String sftpUrl = this.uploadFileToSFTP(fileName, path, mergeFileUrl);

        // 上传成功后，将上传到sftp的url更新到数据表中，
        this.saveUploadPieceInfo(uploadChunkInfo.getIdentifier(), sftpUrl);

        // 删除合并文件
        this.delFile(new File(folderUrl));

        UploadResult uploadResult = new UploadResult();
        // 保存到文件信息表中
        getFileInfo(uploadChunkInfo.getFilename(), sftpUrl, uploadResult);
        return uploadResult;
    }

    /**
     * 合并文件
     *
     * @param fileName     文件名
     * @param folderUrl    分片文件保存路径
     * @param mergeFileUrl 合并文件路径
     * @param totalSize    文件总大小
     */
    private void merge(String fileName, String folderUrl, String mergeFileUrl, long totalSize) {
        try {
            log.info("mergeFile.merge 开始 fileName：{}，folderUrl：{}， mergeFileUrl：{}，totalSize：{}", fileName, folderUrl, mergeFileUrl, totalSize);
            if (Files.exists(Paths.get(mergeFileUrl), new LinkOption[]{LinkOption.NOFOLLOW_LINKS})) {
                // 文件存在，判断文件大小是否和文件总大小一致，如果一致说明之前合并成功，否则需要继续合并文件
                if (Files.size(Paths.get(mergeFileUrl)) == totalSize) {
                    log.info("文件:{} 已合并成功", mergeFileUrl);
                    return;
                }
            } else {
                //不存在的话，创建文件 进行合并
                Files.createFile(Paths.get(mergeFileUrl));
            }
            long startTime = System.currentTimeMillis();
            // 获取要合并的文件路径列表，排序
            List<Path> filePaths = Files.list(Paths.get(folderUrl))
                    .filter(path -> !path.getFileName().toString().equals(fileName))
                    .sorted((o1, o2) -> {
                        String p1 = o1.getFileName().toString();
                        String p2 = o2.getFileName().toString();
                        return Integer.valueOf(p2.substring(p2.lastIndexOf("-"))).compareTo(Integer.valueOf(p1.substring(p1.lastIndexOf("-"))));
                    }).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filePaths)) {
                log.error("合并文件时，合并后的文件:{} 大小：{} 和上传文件大小：{} 不一致", mergeFileUrl, Files.size(Paths.get(mergeFileUrl)), totalSize);
                //合并失败 直接抛异常
                throw new ServiceException(ServiceExceptionEnum.FILE_ERROR_1000006);
            }
            for (Path path : filePaths) {
                //以追加的形式写入文件
                Files.write(Paths.get(mergeFileUrl), Files.readAllBytes(path), StandardOpenOption.APPEND);
                //合并后删除该块
                Files.delete(path);
            }
            log.info("分片文件合并 完成 耗时：{}", System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("合并文件:{} 异常 e:{}", folderUrl, e.getMessage());
            //合并失败 直接抛异常
            throw new ServiceException(ServiceExceptionEnum.FILE_ERROR_1000006);
        }
    }

    /**
     * 上传到sftp服务器
     *
     * @param fileName     文件名
     * @param path         sftp服务器上传路径
     * @param mergeFileUrl 合并文件路径
     */
    private String uploadFileToSFTP(String fileName, String path, String mergeFileUrl) {
        log.info("mergeFile.uploadFileToSFTP 开始 fileName：{}，path：{}， mergeFileUrl：{}", fileName, path, mergeFileUrl);
        // 获取文件后缀
        String suffix = fileName.substring(fileName.lastIndexOf("."));
        // 获取文件名称
        String newFileName = NumberGenUtils.genFileName(suffix);
        try (SftpUtil sftpUtil = new SftpUtil(sftpVideoProperties)) {
            long start = Instant.now().toEpochMilli();
            sftpUtil.upload(path, newFileName, new FileInputStream(mergeFileUrl));
            long end = Instant.now().toEpochMilli();
            log.info("合并后的文件:{} 上传SFTP服务器 耗时:{}", mergeFileUrl, end - start);
        } catch (Exception e) {
            log.error("合并文件后上传文件到sftp服务器失败:{}", e.getMessage());
            throw new ServiceException(ServiceExceptionEnum.FILE_ERROR_1000006);
        }
        return path + newFileName;
    }

    /**
     * 保存分片上传信息
     *
     * @param identifier 文件唯一标识
     * @param sftpUrl    文件保存在sftp服务器的路径
     */
    private void saveUploadPieceInfo(String identifier, String sftpUrl) {
        log.info("mergeFile.saveUploadPieceInfo 开始 identifier：{}，sftpUrl：{}", identifier, sftpUrl);
        AutSaAudBusinessData autSaAudBusinessData = new AutSaAudBusinessData();
        autSaAudBusinessData.setAutCode(identifier);
        autSaAudBusinessData.setBusinessCode(AutSaAudBusinessCodeEnum.UPLOAD_PIECE_INFO.getCode());
        autSaAudBusinessData.setData(sftpUrl);
        int count = autSaAudBusinessDataService.saveAutSaAudBusinessData(autSaAudBusinessData);
        log.info("保存上传分片数据信息 结果：{}", count);
    }

    /**
     * 删除指定文件
     *
     * @param file 文件
     */
    private void delFile(File file) {
        if (file.exists()) {
            if (file.isDirectory()) {
                File[] files = file.listFiles();
                for (File f : files) {
                    delFile(f);
                }
            }
            file.delete();
        }
    }

}
