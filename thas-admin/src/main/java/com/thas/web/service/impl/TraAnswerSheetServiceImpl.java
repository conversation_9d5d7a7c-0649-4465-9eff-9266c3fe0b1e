package com.thas.web.service.impl;

import com.thas.common.constant.Constants;
import com.thas.common.exception.ServiceException;
import com.thas.common.utils.SecurityUtils;
import com.thas.system.service.ISysUserService;
import com.thas.web.domain.TraAccountExam;
import com.thas.web.domain.TraAnswerDetails;
import com.thas.web.domain.TraAnswerSheet;
import com.thas.web.domain.TraExamDetails;
import com.thas.web.domain.dto.TraAnswerSheetDTO;
import com.thas.web.domain.vo.TraAnswerSheetVO;
import com.thas.web.domain.vo.TraExamDetailsVO;
import com.thas.web.mapper.TraAnswerDetailsMapper;
import com.thas.web.mapper.TraAnswerSheetMapper;
import com.thas.web.service.ITraAccountExamService;
import com.thas.web.service.ITraAnswerSheetService;
import com.thas.web.service.ITraExamPaperService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 答卷Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-27
 */
@Service
public class TraAnswerSheetServiceImpl implements ITraAnswerSheetService {
    @Autowired
    private TraAnswerSheetMapper traAnswerSheetMapper;

    @Autowired
    private ITraExamPaperService traExamPaperService;

    @Autowired
    private TraAnswerDetailsMapper traAnswerDetailsMapper;

    @Autowired
    private ITraAccountExamService traAccountExamService;

    @Resource
    private ISysUserService userService;

    /**
     * 查询答卷
     *
     * @param id 答卷主键
     * @return 答卷
     */
    @Override
    public TraAnswerSheetVO selectTraAnswerSheetById(Long id) {
        return traAnswerSheetMapper.selectTraAnswerSheetById(id);
    }

    /**
     * 查询答卷列表
     *
     * @param traAnswerSheet 答卷
     * @return 答卷
     */
    @Override
    public List<TraAnswerSheetVO> selectTraAnswerSheetList(TraAnswerSheet traAnswerSheet) {
        // 校验用户是否有数据权限
        userService.checkUserPermission(traAnswerSheet.getCreateId());
        return traAnswerSheetMapper.selectTraAnswerSheetList(traAnswerSheet);
    }

    /**
     * 新增答卷
     *
     * @param traAnswerSheetDTO 答卷
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertTraAnswerSheet(TraAnswerSheetDTO traAnswerSheetDTO) {

        Long examPaperId = traAnswerSheetDTO.getExamPaperId();
        // 校验当前考卷id是否是有效的
        TraExamDetailsVO traExamDetailsVO = traExamPaperService.selectTraExamPaperById(examPaperId);
        if (Objects.isNull(traExamDetailsVO) || !Constants.HospitalConstants.NUM_1.equals(traExamDetailsVO.getStatus())) {
            throw new ServiceException("考卷不存在，请刷新重试！");
        }

        TraAnswerSheet traAnswerSheet = getTraAnswerSheet(traAnswerSheetDTO);
        int i = traAnswerSheetMapper.insertTraAnswerSheet(traAnswerSheet);

        for (TraAnswerDetails detail : traAnswerSheetDTO.getDetails()) {
            detail.setAnswerSheetId(traAnswerSheet.getId());
            traAnswerDetailsMapper.insertTraAnswerDetails(detail);
        }

        // 新增答卷去答卷表修改答卷数
        traExamPaperService.updateAnswerSheetNum(traAnswerSheetDTO.getExamPaperId(), "ADD");

        // 考完答卷后需要记录
        TraAccountExam traAccountExam = new TraAccountExam();
        traAccountExam.setLearnResourceId(traAnswerSheetDTO.getLearnResourceId());
        traAccountExam.setExamPaperId(traAnswerSheetDTO.getExamPaperId());
        traAccountExam.setAccount(SecurityUtils.getUsername());
        traAccountExam.setStatus(traAnswerSheet.getStatus());
        traAccountExamService.saveOrUpdate(traAccountExam);
        return i;
    }

    private TraAnswerSheet getTraAnswerSheet(TraAnswerSheetDTO traAnswerSheetDTO) {
        TraAnswerSheet traAnswerSheet = new TraAnswerSheet();
        if (traAnswerSheet.getId() == null) {
            traAnswerSheet.setCreateId(SecurityUtils.getUsername());
            // 总得分
            long score = 0L;
            // 获取考卷详情
            TraExamDetailsVO examDetailsVO = traExamPaperService.selectTraExamPaperById(traAnswerSheetDTO.getExamPaperId());
            // 及格分数
            long passScore = Long.parseLong(examDetailsVO.getPassScore());
            // 正确答案和题目编号
            Map<Long, String> correctMap = examDetailsVO.getDetails().stream()
                    .collect(Collectors.toMap(TraExamDetails::getId, TraExamDetails::getCorrectAnswer));
            // 题目编号和该题得分
            Map<Long, String> scoreMap = examDetailsVO.getDetails().stream()
                    .collect(Collectors.toMap(TraExamDetails::getId, TraExamDetails::getScore));
            // 判断答卷获得总分以及是否及格
            for (TraAnswerDetails detail : traAnswerSheetDTO.getDetails()) {
                if (detail.getAnswer().equals(correctMap.get(detail.getExamDetailsId()))) {
                    score += Long.parseLong(scoreMap.get(detail.getExamDetailsId()));
                    detail.setScore(scoreMap.get(detail.getExamDetailsId()));
                    detail.setCorrect(1);
                }
            }
            traAnswerSheet.setScore(score);
            traAnswerSheet.setStatus(score >= passScore ? 1 : 0);
        }
        traAnswerSheet.setUpdateId(SecurityUtils.getUsername());
        traAnswerSheet.setExamPaperId(traAnswerSheetDTO.getExamPaperId());
        return traAnswerSheet;
    }

    /**
     * 修改答卷
     *
     * @param traAnswerSheetDTO 答卷
     * @return 结果
     */
    @Override
    public int updateTraAnswerSheet(TraAnswerSheetDTO traAnswerSheetDTO) {
        TraAnswerSheet traAnswerSheet = getTraAnswerSheet(traAnswerSheetDTO);
        int i = traAnswerSheetMapper.updateTraAnswerSheet(traAnswerSheet);

        for (TraAnswerDetails detail : traAnswerSheetDTO.getDetails()) {
            detail.setAnswerSheetId(traAnswerSheetDTO.getId());
            traAnswerDetailsMapper.updateTraAnswerDetails(detail);
        }
        return i;
    }

    /**
     * 批量删除答卷
     *
     * @param ids 需要删除的答卷主键
     * @return 结果
     */
    @Override
    public int deleteTraAnswerSheetByIds(Long[] ids) {
        return traAnswerSheetMapper.deleteTraAnswerSheetByIds(ids);
    }

    /**
     * 删除答卷信息
     *
     * @param id 答卷主键
     * @return 结果
     */
    @Override
    public int deleteTraAnswerSheetById(Long id) {
        return traAnswerSheetMapper.deleteTraAnswerSheetById(id);
    }
}
