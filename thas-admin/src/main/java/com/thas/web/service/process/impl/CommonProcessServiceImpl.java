package com.thas.web.service.process.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.thas.common.constant.Constants;
import com.thas.common.constant.Constants.HospitalConstants;
import com.thas.common.core.domain.entity.SysUser;
import com.thas.common.core.text.Convert;
import com.thas.common.enums.AutSaAudBusConfItemTypeEnum;
import com.thas.common.enums.AutSaAudBusinessCodeEnum;
import com.thas.common.enums.AutSaAudPageTypeEnum;
import com.thas.common.enums.AutSaAudResultEnum;
import com.thas.common.enums.AutSaAudRiskImpactEnum;
import com.thas.common.enums.AutSaAudRiskLevelEnum;
import com.thas.common.enums.AutSaAudRiskPossibilityEnum;
import com.thas.common.enums.AutSaAudRoleEnum;
import com.thas.common.enums.AutSaAudStatusEnum;
import com.thas.common.enums.AutSaAudSubmitTypeEnum;
import com.thas.common.enums.FtlToPdfEnum;
import com.thas.common.enums.MessageContentFormatEnum;
import com.thas.common.enums.ServiceExceptionEnum;
import com.thas.common.enums.StatusProcessEnum;
import com.thas.common.exception.ServiceException;
import com.thas.common.properties.AbstractSftpProperties;
import com.thas.common.utils.DateUtils;
import com.thas.common.utils.SecurityUtils;
import com.thas.system.domain.vo.UserVo;
import com.thas.system.mapper.SysUserMapper;
import com.thas.system.service.ISysUserService;
import com.thas.web.domain.*;
import com.thas.web.domain.dto.MessageSendRecordDTO;
import com.thas.web.domain.dto.ReviewFitMoveClauseReq;
import com.thas.web.domain.dto.SelectStandardsByClauseIdsDTO;
import com.thas.web.domain.vo.*;
import com.thas.web.dto.CstCertificationStandardVO;
import com.thas.web.dto.HosPlanUserInfoVO;
import com.thas.web.dto.HosReviewPlanVO;
import com.thas.web.dto.QueryBaseConditionDTO;
import com.thas.web.mapper.AutSaAudBusinessDataMapper;
import com.thas.web.mapper.AutSaAudMapper;
import com.thas.web.mapper.CommonProcessMapper;
import com.thas.web.mapper.CstCertificationStandardsMapper;
import com.thas.web.mapper.HospitalBaseInfoMapper;
import com.thas.web.mapper.HospitalReviewerMapper;
import com.thas.web.mapper.ReviewFitMoveClauseMapper;
import com.thas.web.service.CommonService;
import com.thas.web.service.IAutSaAudBusinessDataService;
import com.thas.web.service.IAutSaRelationService;
import com.thas.web.service.ICstCertificationStandardsService;
import com.thas.web.service.IHospitalDomainGroupService;
import com.thas.web.service.IHospitalPreExamService;
import com.thas.web.service.IMessageSendRecordService;
import com.thas.web.service.IReviewFitMoveClauseService;
import com.thas.web.service.IUploadFileInfoService;
import com.thas.web.service.PdfGenerateService;
import com.thas.web.service.process.BaseProcessService;
import com.thas.web.service.process.CommonProcessService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.mail.MessagingException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.GeneralSecurityException;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.thas.web.service.impl.PdfGenerateServiceImpl.temProcessEnums;

/**
 * 审查员审查流程服务 Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-04-01
 */
@Component("commonProcessService")
@Slf4j
public class CommonProcessServiceImpl implements CommonProcessService {

    @Resource
    private CommonProcessMapper commonProcessMapper;

    @Resource
    private AutSaAudMapper autSaAudMapper;

    @Resource
    private HospitalReviewerMapper hospitalReviewerMapper;

    @Resource
    private IAutSaRelationService autSaRelationService;

    @Resource
    private IAutSaAudBusinessDataService autSaAudBusinessDataService;

    @Resource
    private ICstCertificationStandardsService cstCertificationStandardsService;

    @Resource
    private IHospitalPreExamService iHospitalPreExamService;

    @Resource
    private ISysUserService sysUserService;

    @Resource
    private IUploadFileInfoService uploadFileInfoService;

    @Resource
    private CommonService commonService;

    @Resource
    @Lazy
    private PdfGenerateService pdfGenerateService;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private IMessageSendRecordService messageSendRecordService;

    @Autowired
    private AbstractSftpProperties sftpImageProperties;

    @Resource
    private IHospitalDomainGroupService hospitalDomainGroupService;

    @Resource
    private HospitalBaseInfoMapper hospitalBaseInfoMapper;

    @Resource
    private ReviewFitMoveClauseMapper reviewFitMoveClauseMapper;

    @Resource
    private BaseProcessService baseProcessService;

    @Resource
    private AutSaAudBusinessDataMapper autSaAudBusinessDataMapper;

    @Resource
    private CstCertificationStandardsMapper cstCertificationStandardsMapper;
    @Autowired
    private IReviewFitMoveClauseService reviewFitMoveClauseService;
    @Autowired
    private IUploadFileInfoService iUploadFileInfoService;

    @Value("${outFileDownload.resourceUrl}")
    private String resourceUrl;

    //评审中发现不足
    private static final String AUT_EVALUATE = "autEvaluate";
    // 对受评医院的整改建议
    private static final String AUT_PROPOSAL = "autProposal";

    /**
     * 校验提交流程周期时间
     *
     * @param applyNo   医疗机构编码
     * @param autCode   自评编码
     * @param curStatus 当前节点状态
     */
    @Override
    public void checkSubmitCycle(String applyNo, String autCode, String curStatus) {
        // 查提交流程，需校验周期时间最后的时间 还需要校验提交时间是否在周期时间内
        List<String> cycleLists = autSaRelationService.getCycleLists(applyNo, autCode, curStatus);
        if (CollectionUtils.isNotEmpty(cycleLists)) {
            if (!DateUtils.isBetweenDate(DateUtils.parseDate(cycleLists.get(Constants.HospitalConstants.NUM_0)), DateUtils.parseDate(cycleLists.get(Constants.HospitalConstants.NUM_2)), DateUtils.parseDate(cycleLists.get(Constants.HospitalConstants.NUM_1)))) {
                // 提交类型时当前时间不在计划范围内 不允许提交
                log.info("医疗机构：{} 节点提交时间：{}不在计划开始时间：{}--计划结束时间：{} 内", applyNo, cycleLists.get(1), cycleLists.get(0), cycleLists.get(2));
                throw new ServiceException(ServiceExceptionEnum.PLAN_ERROR_1000009);
            }
        }
    }

    /**
     * 校验查询流程周期时间
     *
     * @param applyNo   医疗机构编码
     * @param curStatus 当前节点状态
     */
    @Override
    public void checkQueryCycle(String applyNo, String autCode, String curStatus) {
        // 查询流程，仅需校验周期时间最后的时间
        autSaRelationService.getCycleLists(applyNo, autCode, curStatus);
    }

    /**
     * 校验提交权限--(校验用户是否已分配到医疗机构名下)
     *
     * @param req 流程参数
     */
    @Override
    public void checkSubmitPermission(AutSaAudSaveDTO req) {
        String curStatus = req.getAutSaRelation().getAutStatus();
        // 获取用户角色权限字符串
        String roleKey = sysUserService.getSysUserInfo(req.getAccountId()).getRoleKey();
        // 初审驳回需要使用医院端配置信息 其他使用节点前2位
        String statusPerfix;
        if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(curStatus, AutSaAudStatusEnum.FR_SUMMARY_REJECT)) {
            statusPerfix = "01";
        } else if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(curStatus, AutSaAudStatusEnum.BENEFIT_CONFLICT_DECLARE)) {
            statusPerfix = "03";
        } else {
            statusPerfix = curStatus.substring(Constants.HospitalConstants.NUM_0, Constants.HospitalConstants.NUM_2);
        }
        AutSaAudBusinessConfig statusPrefixConfig = commonProcessMapper.selectAutSaAudBusinessConfig(AutSaAudBusConfItemTypeEnum.STATUS_PREFIX_TO_ROLE_KEY.getItemType(), statusPerfix);
        if (statusPrefixConfig == null || StringUtils.isBlank(statusPrefixConfig.getItemArr1()) || !Arrays.asList(Convert.toStrArray(statusPrefixConfig.getItemArr1())).contains(roleKey)) {
            log.error("当前节点状态：{}，对应前缀:{} ，的提交角色不正确", curStatus, statusPerfix);
            throw new ServiceException(ServiceExceptionEnum.AUT_SA_AUD_ERROR_1000006);
        }
        req.setStatusPrefixConfig(statusPrefixConfig);
        if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(roleKey, AutSaAudRoleEnum.ADMIN, AutSaAudRoleEnum.COMMON_ADMIN)) {
            log.info("当前提交的角色是管理员，不需要校验提交权限");
            return;
        }
        if (commonProcessMapper.checkSubmitPermission(req.getAccountId(), statusPerfix, req.getAutSaRelation().getHospitalApplyNo()) == Constants.HospitalConstants.NUM_0) {
            log.error("当前节点状态：{}，前缀:{}，对应的提交权限不正确", curStatus, statusPerfix);
            throw new ServiceException(ServiceExceptionEnum.AUT_SA_AUD_ERROR_1000006);
        }

    }

    /**
     * 校验提交的款id
     *
     * @param req 流程参数
     */
    @Override
    public void checkClause(AutSaAudSaveDTO req) {
        //提交类型为：*_skip且{0304011-评审修改中}和0305011节点，无需校验
        if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.FAR_CLAUSE_M, AutSaAudStatusEnum.TR_CLAUSE_M) &&
                AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(req.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SKIP, AutSaAudSubmitTypeEnum.TR_CLAUSE_M_SKIP)
        ) {
            return;
        }

        // 校验入参条款id列表必传
        List<AutSaAud> autSaAudLists = req.getAutSaAudLists();
        if (CollectionUtils.isEmpty(autSaAudLists) || autSaAudLists.stream().anyMatch(a -> StringUtils.isBlank(a.getClauseId()) || StringUtils.isBlank(a.getAutResult()))) {
            log.error("自评编码:{}, 提交类型:{}, 数据没有款id或者审核结果 异常", req.getAutCode(), req.getSubmitType());
            throw new ServiceException(ServiceExceptionEnum.AUT_SA_AUD_ERROR_1000002);
        }
        // 查询版本对应的款信息
        List<CstCertificationStandards> allCstCertificationStandards = cstCertificationStandardsService.selectAllClauseIdByVersionId(req.getAutSaRelation().getAutCsId());
        List<String> cstClauseList = allCstCertificationStandards.stream().map(a -> a.getClauseId().toString()).collect(Collectors.toList());
        // 校验入参id归属于用户
        if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(req.getStatusPrefixConfig().getItemArr1(), AutSaAudRoleEnum.HOSPITAL, AutSaAudRoleEnum.SENIOR_ASSESSOR)) {
            // 医院端、验证评审员   校验入参caseId 归属于对应版本
            log.info("医院端、验证评审员 仅需要校验id归属于对应版本即可");
        } else if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(req.getStatusPrefixConfig().getItemArr1(), AutSaAudRoleEnum.INSPECTOR)) {
            // 审查员校验入参caseId 归属于 款项id列表 clause_list
            HospitalPreExam hospitalPreExam = iHospitalPreExamService.selectHospitalPreExamByApplyNoAndAccountId(req.getAutSaRelation().getHospitalApplyNo(), req.getAccountId());
            if (StringUtils.isBlank(hospitalPreExam.getClauseList())) {
                log.error("分配的条款为空");
                throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000002);
            }
            if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(req.getSubmitType(), AutSaAudSubmitTypeEnum.FR_REPORT_R_CLAUSE) ||
                    AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.FR_CLAUSE_CONFIRM, AutSaAudStatusEnum.SR_REPORT_M)) {
                // 审查复查 评审报告审查  修改评审报告  要校验是否组长
                if (hospitalPreExam.getLeaderIs() == null || hospitalPreExam.getLeaderIs().intValue() != Constants.HospitalConstants.NUM_1) {
                    // 提交类型 需要组长提交
                    log.error("需要组长提交");
                    throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000002);
                }
            } else {
                List<CstCertificationStandardVO> hospitalPreExamCsAllList = cstCertificationStandardsService.selectCstCertificationStandardVOByIds(hospitalPreExam.getClauseList());
                if (CollectionUtils.isEmpty(hospitalPreExamCsAllList)) {
                    log.error("账户：{} 审查员分配信息无对应条款", req.getAccountId());
                    throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000002);
                }
                cstClauseList = hospitalPreExamCsAllList.stream().map(a -> a.getClauseId().toString()).collect(Collectors.toList());
            }
        } else if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(req.getStatusPrefixConfig().getItemArr1(), AutSaAudRoleEnum.ASSESSOR)) {
            // 评审员校验入参caseId 对应domianId 归属于 分配的领域列表 field_id_list
            // 如果用户id不为空，则查对应的评审员对应的领域以及对应的条款
            HospitalReviewer hospitalReviewer = hospitalReviewerMapper.selectHospitalReviewerByApplyNoAndAccountId(req.getAutSaRelation().getHospitalApplyNo(), req.getAccountId());
            if (StringUtils.isBlank(hospitalReviewer.getFieldIdList())) {
                log.error("领域列表为空");
                throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000002);
            }
            if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(req.getSubmitType(), AutSaAudSubmitTypeEnum.SR_V_CLAUSE, AutSaAudSubmitTypeEnum.SR_CLAUSE_M,
                    AutSaAudSubmitTypeEnum.FAR_V_CLAUSE_M, AutSaAudSubmitTypeEnum.TR_V_CLAUSE_M) ||
                    AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.WAIT_SR_V_CLAUSE, AutSaAudStatusEnum.SR_V_CLAUSE_PROCESS)) {
                if (hospitalReviewer.getLeaderIs() == null || hospitalReviewer.getLeaderIs().intValue() != Constants.HospitalConstants.NUM_1) {
                    // 提交类型 需要组长提交
                    log.error("需要组长提交");
                    throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000002);
                }
            } else {
                List<String> fieldList = Arrays.asList(Convert.toStrArray(hospitalReviewer.getFieldIdList()));
//                cstClauseList = allCstCertificationStandards.stream().filter(a -> fieldList.contains(a.getDomainId().toString())).map(a -> a.getClauseId().toString()).collect(Collectors.toList());
                cstClauseList = inputOutputClause(req.getAutSaRelation().getAutCode(), allCstCertificationStandards, fieldList);
            }
            // 校验评审初查不通过的基本款数据需要校验风险项
            Map<String, List<CstCertificationStandards>> allCstCertificationStandardMap = allCstCertificationStandards.stream().collect(Collectors.groupingBy(a -> a.getClauseId().toString()));
            if (autSaAudLists.stream().anyMatch(a -> this.checkRiskPossibilityAndRiskImpact(a, allCstCertificationStandardMap))) {
                log.error("评审初查+自评 提交时，基础款不达标，对应风险影响项：{} 和 风险可能性选项 必填并且需要在枚举值内");
                throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000002);
            }
        } else {
            // 其他角色直接抛异常
            log.error("其他角色：{} 未配置提交操作", req.getStatusPrefixConfig().getItemArr1());
            throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000002);
        }
        // 校验入参的条款id是否有效
        List<String> finalCstClauseList = cstClauseList;
        if (!AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.FAR_CLAUSE_M, AutSaAudStatusEnum.TR_CLAUSE_M)
                && autSaAudLists.stream().anyMatch(a -> !finalCstClauseList.contains(a.getClauseId()))) {
            // 入参传入的款id 存在不属于当前版本的id
            log.error("提交的数据存在款id 在当前认证标准版本内不存在或者未分配该账户id下");
            throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000002);
        }
    }

    private List<String> inputOutputClause(String autCode,
                                           List<CstCertificationStandards> allCstCertificationStandards,
                                           List<String> fieldList) {

        Set<String> groupIdSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(fieldList)) {
            groupIdSet.addAll(fieldList);
        }

        Map<String, Set<String>> groupClauseIdSetMap = allCstCertificationStandards.stream()
                .filter(cst -> groupIdSet.contains(cst.getDomainId().toString()))
                .collect(Collectors.groupingBy(cst -> String.valueOf(cst.getDomainId()),
                        Collectors.mapping(cst -> String.valueOf(cst.getClauseId()), Collectors.toSet()))
                );

        reviewFitMoveClauseService.moveClause(autCode, groupClauseIdSetMap);

        Set<String> result = new HashSet<>();
        for (Set<String> clauseIdSet : groupClauseIdSetMap.values()) {
            if (CollectionUtils.isEmpty(clauseIdSet)) {
                continue;
            }
            result.addAll(clauseIdSet);
        }
        return new ArrayList<>(result);
    }

    private boolean checkRiskPossibilityAndRiskImpact(AutSaAud autSaAud, Map<String, List<CstCertificationStandards>> allCstCertificationStandardMap) {
        if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(autSaAud.getSubmitType(), AutSaAudSubmitTypeEnum.SR_CLAUSE) && AutSaAudResultEnum.checkIsAutSaAudResultEnum(autSaAud.getAutResult(), AutSaAudResultEnum.NOT_STANDARD)) {
            if (Constants.HospitalConstants.STR_NUM_1.equals(allCstCertificationStandardMap.get(autSaAud.getClauseId()).get(0).getIsStar())) {
                return !AutSaAudRiskPossibilityEnum.checkAutSaAudRiskPossibilityEnum(autSaAud.getRiskPossibility()) || !AutSaAudRiskImpactEnum.checkAutSaAudRiskImpactEnum(autSaAud.getRiskImpact());
            }
        }
        return false;
    }

    /**
     * 处理节点初始场景   --节点直接翻转到下一场景（待--》中）没有逻辑判断处理
     *
     * @param req 流程参数
     */
    @Override
    public void processInitialScene(AutSaAudSaveDTO req) {
        long startTime = System.currentTimeMillis();
        log.info("processInitialScene ------ 开始");
        //管理员不适用款数量
        int size = 0;
        //【030201-待评审】或【030203-评审待复查】 节点
        if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(),
                AutSaAudStatusEnum.WAIT_SR_CLAUSE, AutSaAudStatusEnum.WAIT_SR_V_CLAUSE)) {
            //判断不适用款是否为空
            List<ReviewFitMoveClause> reviewFitMoveClauseList = qryReviewFitMoveClause(req.getAutSaRelation().getAutCode());
            if (CollectionUtils.isNotEmpty(reviewFitMoveClauseList)) {
                size = reviewFitMoveClauseList.size();
                //不适用款
                List<String> fitClauseIds = reviewFitMoveClauseList.stream().map(ReviewFitMoveClause::getClauseId).collect(Collectors.toList());
                //新增
                this.fitClauseBatchInsertAutSaAud(fitClauseIds, req);
            }
        }
        int initCount = commonProcessMapper.selectCountByType(req.getAutCode(), req.getSubmitType());
        log.info("医疗机构：{},对应节点：{},初始化数据数量:{},管理员不适用款数量:{}", req.getAutCode(), req.getAutSaAudStatusConfig().getCurrentStatus(), initCount, size);
        if (Constants.HospitalConstants.NUM_0 != initCount - size) {
            // 初始场景下当前提交类型数不为0，节点或者数据有问题
            log.error("节点：{} 初始化数据数量不为0，当前节点或者数据有问题，请进一步排查", req.getAutSaAudStatusConfig().getCurrentStatus());
            throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000002);
        }
        // 款上限
        int totalCount = MapUtils.getInteger(cstCertificationStandardsService.selectCountByVersionId(req.getAutSaRelation().getAutCsId()), "clauseCount");
        if (req.getAutSaAudLists().size() > totalCount - size) {
            // 初始场景提交数大于等于总款数
            log.error("节点：{} 初始场景提交数:{}大于总款数：{}", req.getAutSaAudStatusConfig().getCurrentStatus(), req.getAutSaAudLists().size(), totalCount);
            throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000002);
        }
        // 插入自评审核表 aut_sa_aud
        this.batchInsertAutSaAud(req.getAutSaAudLists());
        // 更新关联表 aut_sa_relation
        StatusProcessEnum statusProcessEnum = (req.getAutSaAudLists().size() == totalCount) ? StatusProcessEnum.SKIP : StatusProcessEnum.PS;
        if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaAudStatusConfig().getCurrentStatus(), AutSaAudStatusEnum.WAIT_SR_V_CLAUSE)) {
            // 评审待复查 跳转有多种情况
            if ((req.getAutSaAudLists().size() == totalCount - size)) {
                // 复查条数达到限值， 需要判断是否有驳回信息 更新节点配置信息
                String nextStatus = this.getNextStatus(req.getAutSaAudStatusConfig().getNextStatusConfig(), StatusProcessEnum.PS);
                req.setAutSaAudStatusConfig(commonProcessMapper.selectAutSaAudStatusConfig(nextStatus));
                // 复查驳回处理
                this.vClauseRjProcess(req, req.getAutSaAudLists());
                return;
            }
        }
        this.updateAutSaRelation(this.getNextStatus(req.getAutSaAudStatusConfig().getNextStatusConfig(), statusProcessEnum), req.getAutCode());
        if (req.getAutSaAudLists().size() == totalCount - size) {
            // 特殊节点生成自评报告
            this.specialGenerateReport(req.getAutCode(), req.getAutSaRelation().getAutCsId(), req.getAutSaRelation().getAutStatus(), req.getHospitalName());
        }
        log.info("processInitialScene ------ 结束 耗时：{}", System.currentTimeMillis() - startTime);
    }

    private String getFitClauseSubmitType(String autStatus) {
        if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autStatus, AutSaAudStatusEnum.WAIT_SR_CLAUSE)) {
            return AutSaAudSubmitTypeEnum.SR_CLAUSE.getSubmitType();

        } else if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autStatus, AutSaAudStatusEnum.WAIT_SR_V_CLAUSE)) {
            return AutSaAudSubmitTypeEnum.SR_V_CLAUSE.getSubmitType();
        } else {
            throw new ServiceException("不支持当前节点：" + autStatus + "落表管理员不适用款数据");
        }
    }

    /**
     * 保存不适用款数据
     *
     * @param fitClauseIds 不适用款Ids
     * @param req
     */
    private void fitClauseBatchInsertAutSaAud(List<String> fitClauseIds, AutSaAudSaveDTO req) {
        if (CollectionUtils.isNotEmpty(fitClauseIds)) {
            //判断是为【030201-待评审】节点或【030203-评审待复查】，保存sr_clause或sr_v_clause提交类型数据
            String fitClauseSubmitType = getFitClauseSubmitType(req.getAutSaRelation().getAutStatus());
            List<AutSaAud> insertAutSaAudList = new ArrayList<>();
            fitClauseIds.forEach(clauseId -> {
                AutSaAud autSaAud = new AutSaAud();
                autSaAud.setAutCode(req.getAutSaRelation().getAutCode());
                autSaAud.setSubmitType(fitClauseSubmitType);
                autSaAud.setClauseId(clauseId);
                autSaAud.setAccountId(req.getAccountId());
                autSaAud.setAutResult(String.valueOf(AutSaAudResultEnum.ADMIN_NOT_APPLICABLE.getCode()));
                autSaAud.setCreateId(Long.valueOf(req.getAccountId()));
                insertAutSaAudList.add(autSaAud);
            });
            this.batchInsertAutSaAud(insertAutSaAudList);
        }
    }

    private List<ReviewFitMoveClause> qryReviewFitMoveClause(String autCode) {
        ReviewFitMoveClauseReq reviewFitMoveClauseReq = new ReviewFitMoveClauseReq();
        reviewFitMoveClauseReq.setAutCode(autCode);
        reviewFitMoveClauseReq.setFitStatus(Long.valueOf(Constants.STR_NUM_1));
        return reviewFitMoveClauseMapper.qryReviewFitMoveClauseList(reviewFitMoveClauseReq);

    }

    /**
     * 处理款提交场景 -- 提交数据后，判断已提交的款数据数量是否达到上限，达到则翻转节点，否则节点不变
     *
     * @param req 流程参数
     */
    @Override
    public void processClauseScene(AutSaAudSaveDTO req) {
        long startTime = System.currentTimeMillis();
        log.info("processClauseScene ------ 开始");
        // 款上限
        int totalCount = MapUtils.getInteger(cstCertificationStandardsService.selectCountByVersionId(req.getAutSaRelation().getAutCsId()), "clauseCount");
        // 处理条或者款提交结果
        List<AutSaAud> newAutSaAuds = this.processClauseOrArticle(req, totalCount);
        if (newAutSaAuds.size() == totalCount) {
            //当前节点评审完，生成预评审报告
            // {0302041-评审复查驳回 -->030204-评审复查中}
            if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.SR_V_CLAUSE_REJECT)) {
                this.temReviewReport(req.getAutCode(), req.getAutSaRelation().getAutCsId());
            }
            // 已提交数据 + 当前提交的数据 == 条款上限，节点翻转    更新关联表 aut_sa_relation
            this.updateAutSaRelation(this.getNextStatus(req.getAutSaAudStatusConfig().getNextStatusConfig(), StatusProcessEnum.PS), req.getAutCode());
            // 特殊节点生成自评报告
            this.specialGenerateReport(req.getAutCode(), req.getAutSaRelation().getAutCsId(), req.getAutSaRelation().getAutStatus(), req.getHospitalName());
        }
        log.info("processClauseScene ------ 结束 耗时：{}", System.currentTimeMillis() - startTime);
    }

    /**
     * 处理条或者款提交结果
     *
     * @param req        流程参数
     * @param totalCount 条/款上限
     * @return 结果
     */
    @Override
    public List<AutSaAud> processClauseOrArticle(AutSaAudSaveDTO req, int totalCount) {
        List<AutSaAud> hisClauseOrArticleAutSaAuds = autSaAudMapper.selectAutSaAudListByAutCodeAndTypes(req.getAutCode(), req.getSubmitType());
        log.info("医疗机构：{} 对应节点：{} 已提交类型：{} 数据数量:{}, 条款上限:{}", req.getAutCode(), req.getAutSaAudStatusConfig().getCurrentStatus(), req.getSubmitType(), hisClauseOrArticleAutSaAuds.size(), totalCount);
        // 更新条款信息
        return this.updateClauseAutSaAud(req.getAutSaAudLists(), hisClauseOrArticleAutSaAuds, totalCount);
    }

    /**
     * 更新条款信息
     *
     * @param autSaAudLists    当前提交的数据
     * @param hisAutSaAudLists 已提交数据
     * @param totalCount       条款总数
     * @return
     */
    @Override
    public List<AutSaAud> updateClauseAutSaAud(List<AutSaAud> autSaAudLists, List<AutSaAud> hisAutSaAudLists, int totalCount) {
        // 已提交数据的款id
        List<String> hisClauseIds = hisAutSaAudLists.stream().map(k -> k.getClauseId()).collect(Collectors.toList());
        // 更新条款列表
        List<AutSaAud> updateList = autSaAudLists.stream().filter(k -> hisClauseIds.contains(k.getClauseId())).collect(Collectors.toList());
        // 插入条款列表
        List<AutSaAud> insertList = autSaAudLists.stream().filter(k -> !hisClauseIds.contains(k.getClauseId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(insertList)) {
            if (insertList.size() + hisClauseIds.size() > totalCount) {
                // 已提交数据 + 当前提交的数据 > 条款上限，直接异常
                log.error("更新操作，节点已提交数据数量：{} + 当前提交的数据数量：{} > 条款上限数量：{}", hisClauseIds.size(), insertList.size(), totalCount);
                throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000002);
            }
            // 条款新增操作   自评审核表 aut_sa_aud
            this.batchInsertAutSaAud(insertList);
            hisAutSaAudLists.addAll(insertList);
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            // 条款更新操作   自评审核表 aut_sa_aud
            this.batchUpdateAutSaAud(updateList);
            Map<String, List<AutSaAud>> autSaAudMap = updateList.stream().collect(Collectors.groupingBy(AutSaAud::getClauseId));
            hisAutSaAudLists = hisAutSaAudLists.stream().map(k -> {
                if (autSaAudMap.containsKey(k.getClauseId())) {
                    return autSaAudMap.get(k.getClauseId()).get(0);
                } else {
                    return k;
                }
            }).collect(Collectors.toList());
        }
        return hisAutSaAudLists;
    }

    /**
     * 处理总结提交场景（总结提交无节点复杂翻转逻辑） --数据做校验
     *
     * @param req 流程参数
     */
    @Override
    public void processSummaryScene(AutSaAudSaveDTO req) {
        long startTime = System.currentTimeMillis();
        log.info("processSummaryScene ------ 开始");
        // 校验是否为组长，仅组长可进行总结+确认操作
        this.specialSummaryAndConfirm(req);
        // 处理总结提交
        this.processSummary(req);
        // 更新关联表 aut_sa_relation
        String nextStatus = this.getNextStatus(req.getAutSaAudStatusConfig().getNextStatusConfig(), StatusProcessEnum.PS);
        if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.SA_SUMMARY)) {
            List<AutSaAudBusinessData> autSaAudRejectNodeBusinessDatas = autSaAudBusinessDataService.selectAutSaAudBusinessData(req.getAutCode(), AutSaAudBusinessCodeEnum.AUT_SA_AUD_REJECT_STATUS.getCode());
            if (CollectionUtils.isNotEmpty(autSaAudRejectNodeBusinessDatas) && StringUtils.isNotBlank(autSaAudRejectNodeBusinessDatas.get(0).getData())) {
                // 驳回节点不为空 说明当前是初审驳回流程
                nextStatus = this.getNextStatus(req.getAutSaAudStatusConfig().getNextStatusConfig(), StatusProcessEnum.SKIP);
            }
        }
//        //跳转到上会节点时：审查组长020402节点提交通过后，删除上会提交类型的文件业务数据（相当于失效上会纪要文件数据）
//        if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(nextStatus, AutSaAudStatusEnum.SR_REPORT_CONFIRM_MEET)) {
//            autSaAudBusinessDataMapper.deleteAutSaAudBusinessData(req.getAutSaRelation().getAutCode(), AutSaAudBusinessCodeEnum.MEET_REPORT.getCode());
//            log.info("当前自评编码为：{}，已删除上会纪要文件", req.getAutSaRelation().getAutCode());
//        }
        if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(req.getSubmitType(), AutSaAudSubmitTypeEnum.TR_SUMMARY)) {
            //为010303或040103节点转下个节点时，保存驳回提交类型数据到业务数据表， 然后失效审查驳回提交类型数据
            saveBusinessDataAndInvalidRjClauseIds(req, AutSaAudSubmitTypeEnum.TR_CLAUSE_RJ_E.getSubmitType());
            //为010303或040103节点转下个节点时，删除far_clause和tr_clause的业务数据
            this.delAutSaAudBusinessData(req.getAutSaRelation().getAutCode(), AutSaAudBusinessCodeEnum.AUT_SA_AUD_REJECT.getCode());
        } else if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(req.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SUBMIT)) {
            //提交评审修改节点，保存驳回提交类型数据到业务数据表， 然后失效审查驳回提交类型数据
            saveBusinessDataAndInvalidRjClauseIds(req, AutSaAudSubmitTypeEnum.FAR_CLAUSE_RJ_E_M.getSubmitType());
            //提交评审修改节点，删除对应的业务数据
            this.delAutSaAudBusinessData(req.getAutSaRelation().getAutCode(), AutSaAudBusinessCodeEnum.AUT_SA_AUD_REJECT.getCode());

        }

        this.updateAutSaRelation(nextStatus, req.getAutCode());
        // 当前节点生成评审报告
        this.specialGenerateReport(req.getAutCode(), req.getAutSaRelation().getAutCsId(), req.getAutSaRelation().getAutStatus(), req.getHospitalName());
        log.info("processSummaryScene ------ 结束 耗时：{}", System.currentTimeMillis() - startTime);
    }

    /**
     * 特殊处理总结和确认场景 --用户为医院端 + 审查组长 + 评审组长 才可操作
     *
     * @param req
     */
    private void specialSummaryAndConfirm(AutSaAudSaveDTO req) {
        String roleKey = req.getStatusPrefixConfig().getItemArr1();
        if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(roleKey, AutSaAudRoleEnum.INSPECTOR)) {
            // 审查员需要为组长
            HospitalPreExam hospitalPreExam = iHospitalPreExamService.selectHospitalPreExamByApplyNoAndAccountId(req.getAutSaRelation().getHospitalApplyNo(), req.getAccountId());
            if (hospitalPreExam.getLeaderIs() == null || hospitalPreExam.getLeaderIs().intValue() != Constants.HospitalConstants.NUM_1) {
                // 提交类型 需要组长提交
                log.error("需要组长提交");
                throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000002);
            }
        } else if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(roleKey, AutSaAudRoleEnum.ASSESSOR)) {
            // 评审员需要为组长
            HospitalReviewer hospitalReviewer = hospitalReviewerMapper.selectHospitalReviewerByApplyNoAndAccountId(req.getAutSaRelation().getHospitalApplyNo(), req.getAccountId());
            if (hospitalReviewer.getLeaderIs() == null || hospitalReviewer.getLeaderIs().intValue() != Constants.HospitalConstants.NUM_1) {
                // 提交类型 需要组长提交
                log.error("需要组长提交");
                throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000002);
            }
        }

    }

    /**
     * 处理确认提交场景（总结提交无节点复杂翻转逻辑）--数据不做校验
     *
     * @param req 流程参数
     */
    @Override
    public void processConfirmScene(AutSaAudSaveDTO req) {
        //入参为审查组长驳回提交类型，插入或更新评审数据，不翻转节点；
        if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(req.getSubmitType(), AutSaAudSubmitTypeEnum.TR_CLAUSE_RJ_E,
                AutSaAudSubmitTypeEnum.FAR_CLAUSE_RJ_E, AutSaAudSubmitTypeEnum.FAR_CLAUSE_RJ_E_M)) {
            List<String> clauseIds = req.getAutSaAudLists().stream().map(AutSaAud::getClauseId).collect(Collectors.toList());
            log.info("自评编码为：{}，当前节点为：{}，审查驳回了{}款", req.getAutSaRelation().getAutCode(), req.getAutSaRelation().getAutStatus(), clauseIds);
            // 款上限
            int totalCount = MapUtils.getInteger(cstCertificationStandardsService.selectCountByVersionId(req.getAutSaRelation().getAutCsId()), "clauseCount");
            // 处理条或者款提交结果
            this.processClauseOrArticle(req, totalCount);
            return;
        }
        StatusProcessEnum statusProcessEnum = StatusProcessEnum.PS;
        String rjSubmitType = null;
        List<AutSaAudSubmitTypeEnum> invalidSubmitTypeEnums = new ArrayList<>();
        AutSaAudSubmitTypeEnum rjSubmitTypeEnum = null;
        // 入参为TR或FAR总结提交类型时，插入或更新评审数据，判断是否有驳回提交类型数据，如果有翻转驳回节点
        if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(req.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_SUMMARY_CONFIRM)) {
            //事实准确性
            rjSubmitTypeEnum = AutSaAudSubmitTypeEnum.FAR_CLAUSE_RJ_E;
            //失效审查组长拒绝款对应的far_clause数据
            invalidSubmitTypeEnums.add(AutSaAudSubmitTypeEnum.FAR_CLAUSE);
            rjSubmitType = String.join(",", AutSaAudSubmitTypeEnum.FAR_CLAUSE_RJ_E.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE.getSubmitType());
        } else if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(req.getSubmitType(), AutSaAudSubmitTypeEnum.TR_SUMMARY_CONFIRM)) {
            //审查验证
            rjSubmitTypeEnum = AutSaAudSubmitTypeEnum.TR_CLAUSE_RJ_E;
            //失效审查组长拒绝款对应的tr_clause数据
            invalidSubmitTypeEnums.add(AutSaAudSubmitTypeEnum.TR_CLAUSE);
            rjSubmitType = String.join(",", AutSaAudSubmitTypeEnum.TR_CLAUSE_RJ_E.getSubmitType(), AutSaAudSubmitTypeEnum.TR_CLAUSE.getSubmitType());
        } else if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(req.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_SUMMARY_CONFIRM_M)) {
            //评审修改审查确认
            rjSubmitTypeEnum = AutSaAudSubmitTypeEnum.FAR_CLAUSE_RJ_E_M;
            //失效审查组长拒绝款对应的数据
            invalidSubmitTypeEnums.add(AutSaAudSubmitTypeEnum.FAR_CLAUSE_M);
            invalidSubmitTypeEnums.add(AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SKIP);
            invalidSubmitTypeEnums.add(AutSaAudSubmitTypeEnum.FAR_V_CLAUSE_M);
            rjSubmitType = String.join(",", AutSaAudSubmitTypeEnum.FAR_CLAUSE_RJ_E_M.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_M.getSubmitType(),
                    AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SKIP.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_V_CLAUSE_M.getSubmitType());
        } else if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(req.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_SUMMARY)) {
            //为010303或040103节点转下个节点时，如果有拒绝数,保存驳回提交类型数据到业务数据表， 然后失效审查驳回提交类型数据
            saveBusinessDataAndInvalidRjClauseIds(req, AutSaAudSubmitTypeEnum.FAR_CLAUSE_RJ_E.getSubmitType());
            //为010303或040103节点转下个节点时，删除far_clause和tr_clause的业务数据
            this.delAutSaAudBusinessData(req.getAutSaRelation().getAutCode(), AutSaAudBusinessCodeEnum.AUT_SA_AUD_REJECT.getCode());
        } else if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(req.getSubmitType(), AutSaAudSubmitTypeEnum.FR_REPORT_SUBMIT)) {
            //审查提交自评终稿时，更新自评报告
            FtlToPdfDTO ftlToPdfDTO = new FtlToPdfDTO();
            ftlToPdfDTO.setFtlTemplateCode(FtlToPdfEnum.AUD_SA_REPORT.getCode());
            HashMap<String, Object> hashMap = new HashMap<>();
            if (StringUtils.isBlank(req.getAutCode())) {
                throw new ServiceException("自评终稿提交-入参autCode不能为空！");
            }
            hashMap.put("autCode", req.getAutCode());
            ftlToPdfDTO.setOtherParam(hashMap);
            ftlToPdfDTO.setFtlToPdfEnum(FtlToPdfEnum.AUD_SA_REPORT);

            pdfGenerateService.ftlToPdf(ftlToPdfDTO, null);

        } else if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(req.getSubmitType(), AutSaAudSubmitTypeEnum.SR_T_REPORT_SUBMIT)) {
            //更新业务数据文件信息
            this.SubmitTypeSrTReportSubmitOperate(req);
        }
        if (rjSubmitType != null && rjSubmitTypeEnum != null && CollectionUtils.isNotEmpty(invalidSubmitTypeEnums)) {
            List<AutSaAud> rjAutSaAudList = autSaAudMapper.selectAutSaAudListByAutCodeAndTypes(req.getAutCode(), rjSubmitType);
            //判断是否有拒绝款，获取转节点枚举
            statusProcessEnum = getRjStatusProcessEnum(rjAutSaAudList, rjSubmitTypeEnum);
            //删除驳回业务数据
            this.delAutSaAudBusinessData(req.getAutSaRelation().getAutCode(), AutSaAudBusinessCodeEnum.EXAMINER_RJ.getCode());
            //如果有审查驳回款，失效审查组长拒绝款对应的数据，保存到业务数据表，用于前端确认是否修改操作
            this.invalidAutSaAudAndSaveBusinessData(rjAutSaAudList, rjSubmitTypeEnum, invalidSubmitTypeEnums, req.getAutCode());
        }
        this.processConfirm(req, statusProcessEnum);
    }

    private void SubmitTypeSrTReportSubmitOperate(AutSaAudSaveDTO req) {
        if (CollectionUtils.isNotEmpty(req.getAutSaAudLists()) && StringUtils.isNotBlank(req.getAutSaAudLists().get(0).getFileIds())) {
            //场景只能为单个报告
            String fileIds = req.getAutSaAudLists().get(0).getFileIds();
            //更新业务数据文件信息
            List<FileInfoDTO> uploadFileInfoByIds = uploadFileInfoService.getUploadFileInfoByIds(fileIds);
            if (CollectionUtils.isEmpty(uploadFileInfoByIds) || uploadFileInfoByIds.size() > 1) {
                throw new ServiceException(String.format("当前节点[%s]获取报告信息错误，查询的文件Id为:[%s],", req.getAutSaRelation().getAutCode(), fileIds));
            }
            Map<String, String> dataMap = new HashMap();
            dataMap.put("url", uploadFileInfoByIds.get(0).getPath());
            dataMap.put("fileId", fileIds);
            AutSaAudBusinessData autSaAudBusinessData = new AutSaAudBusinessData();
            autSaAudBusinessData.setAutCode(req.getAutSaRelation().getAutCode());
            autSaAudBusinessData.setBusinessCode(FtlToPdfEnum.VERIFY_REVIEW_REPORT.getCode());
            autSaAudBusinessData.setData(JSON.toJSONString(dataMap));
            autSaAudBusinessData.setUpdateTime(DateUtils.getNowDate());
            autSaAudBusinessDataService.saveAutSaAudBusinessData(autSaAudBusinessData);
        }
    }

    /**
     * 如果有审查驳回款，失效审查组长拒绝款对应的far_clause或tr_clause数据，保存到业务数据表，用于前端确认是否修改操作
     * 失效自评数据，保存业务数据
     *
     * @param autSaAudList      包括修改类型和拒绝类型数据
     * @param rjSubmitType      审查组长拒绝驳回枚举
     * @param invalidSubmitType 需要失效的提交类型枚举
     * @param autCode
     */
    private void invalidAutSaAudAndSaveBusinessData(List<AutSaAud> autSaAudList, AutSaAudSubmitTypeEnum rjSubmitType, List<AutSaAudSubmitTypeEnum> invalidSubmitType, String autCode) {
        if (CollectionUtils.isNotEmpty(autSaAudList)) {
            String invalidSubmitTypeStr = invalidSubmitType.stream().map(AutSaAudSubmitTypeEnum::getSubmitType).collect(Collectors.joining(","));
            log.info("失效审查组长拒绝款--->开始，数据为{}，拒绝类型为：{}，修改类型为{}，自评编码为{}", autSaAudList, rjSubmitType, invalidSubmitTypeStr, autCode);
            //拒绝款
            List<String> rjFarClauseList = autSaAudList.stream().filter(o -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(
                    o.getSubmitType(), rjSubmitType)).map(AutSaAud::getClauseId).collect(Collectors.toList());
            //修改款有数据且匹配拒绝款
            List<AutSaAud> upClauseList = autSaAudList.stream().filter(o -> rjFarClauseList.contains(o.getClauseId()) && AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum1(
                    o.getSubmitType(), invalidSubmitType)).collect(Collectors.toList());
            //如果没用提出修改，不用保存和失效
            if (CollectionUtils.isNotEmpty(upClauseList)) {
                //保存
                this.saveAutSaAudBusinessData(autCode, AutSaAudBusinessCodeEnum.AUT_SA_AUD_REJECT.getCode(), JSON.toJSONString(upClauseList));
                //失效
                String rjClauseIds = upClauseList.stream().map(AutSaAud::getClauseId).collect(Collectors.joining(","));
                autSaAudMapper.batchInvalidAutSaAudByClauseIds(autCode, invalidSubmitTypeStr, rjClauseIds);
            }
            log.info("失效审查组长拒绝款--->结束，拒绝类型为：{}，修改类型为{}，自评编码为{}", rjSubmitType, invalidSubmitTypeStr, autCode);
        }
    }

    /**
     * 保存审查驳回提交类型数据到业务数据表，失效认证自评审查驳回提交类型数据
     *
     * @param req
     * @param rjSubmitType
     */
    private void saveBusinessDataAndInvalidRjClauseIds(AutSaAudSaveDTO req, String rjSubmitType) {
        List<AutSaAud> rjAutSaAudList = autSaAudMapper.selectAutSaAudListByAutCodeAndTypes(req.getAutCode(), rjSubmitType);
        if (CollectionUtils.isNotEmpty(rjAutSaAudList)) {
            log.info("自评编码为：{}，当前节点为：{}，保存驳回提交类型数据到业务数据表,失效审查驳回提交类型数据--->开始", req.getAutSaRelation().getAutCode(), req.getAutSaRelation().getAutStatus());
            this.saveAutSaAudBusinessData(req.getAutSaRelation().getAutCode(), AutSaAudBusinessCodeEnum.EXAMINER_RJ.getCode(), JSON.toJSONString(rjAutSaAudList));
            autSaAudMapper.batchInvalidAutSaAudByClauseIds(req.getAutSaRelation().getAutCode(), rjSubmitType, null);
            log.info("自评编码为：{}，当前节点为：{}，保存驳回提交类型数据到业务数据表,失效审查驳回提交类型数据--->结束", req.getAutSaRelation().getAutCode(), req.getAutSaRelation().getAutStatus());
        }
    }

    /**
     * @param rjAutSaAudList 拒绝的评审数据
     * @param farClauseRjE   拒绝的提交类型
     * @return
     */
    private StatusProcessEnum getRjStatusProcessEnum(List<AutSaAud> rjAutSaAudList, AutSaAudSubmitTypeEnum farClauseRjE) {
        List<AutSaAud> farRjAutSaAudList = rjAutSaAudList.stream().filter(o -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(
                o.getSubmitType(), farClauseRjE)).collect(Collectors.toList());
        return CollectionUtils.isEmpty(farRjAutSaAudList) ? StatusProcessEnum.PS : StatusProcessEnum.RJ;
    }

    /**
     * 处理确认并跳过场景（总结提交无节点复杂翻转逻辑）--数据不做校验
     *
     * @param req 流程参数
     */
    @Override
    public void processConfirmSkip(AutSaAudSaveDTO req) {
        this.processConfirm(req, StatusProcessEnum.SKIP);
    }

    private void processConfirm(AutSaAudSaveDTO req, StatusProcessEnum statusProcessEnum) {
        long startTime = System.currentTimeMillis();
        log.info("processConfirm ------ 开始");
        this.specialSummaryAndConfirm(req);
        AutSaAud autSaAud = new AutSaAud();
        autSaAud.setSubmitType(req.getSubmitType());
        autSaAud.setAutCode(req.getAutCode());
        autSaAud.setAccountId(req.getAccountId());
        if (CollectionUtils.isNotEmpty(req.getAutSaAudLists())) {
            String fileIds = req.getAutSaAudLists().get(0).getFileIds();
            String autDesc = req.getAutSaAudLists().get(0).getAutDesc();
            if (StringUtils.isNotBlank(fileIds)) {
                autSaAud.setFileIds(fileIds);
            }
            if (StringUtils.isNotBlank(autDesc)) {
                autSaAud.setAutDesc(autDesc);
            }
        }
        req.setAutSaAudLists(Arrays.asList(autSaAud));
        // 处理确认提交
        this.processSummary(req);
        // 更新关联表 aut_sa_relation
        this.updateAutSaRelation(this.getNextStatus(req.getAutSaAudStatusConfig().getNextStatusConfig(), statusProcessEnum), req.getAutCode());
        log.info("processConfirm ------ 结束 耗时：{}", System.currentTimeMillis() - startTime);
    }

    /**
     * 处理总结提交
     *
     * @param req 流程参数
     */
    @Override
    public void processSummary(AutSaAudSaveDTO req) {
        long startTime = System.currentTimeMillis();
        log.info("processSummary ------ 开始");
        if (CollectionUtils.isEmpty(req.getAutSaAudLists())) {
            // 总结提交场景提交数据列表为空
            log.error("节点：{} 总结提交场景提交数据列表为空", req.getAutSaAudStatusConfig().getCurrentStatus());
            throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000002);
        }
        if (req.getAutSaAudLists().size() > Constants.HospitalConstants.NUM_1) {
            // 总结提交场景提交数大于1
            log.error("节点：{} 总结提交场景提交数:{} 大于1", req.getAutSaAudStatusConfig().getCurrentStatus(), req.getAutSaAudLists().size());
            throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000002);
        }
        List<AutSaAud> hisSummaryAutSaAuds = autSaAudMapper.selectAutSaAudListByAutCodeAndTypes(req.getAutCode(), req.getSubmitType());
        if (CollectionUtils.isNotEmpty(hisSummaryAutSaAuds)) {
            // 款提交场景下当前提交类型数不为0，数据跟新操作
            this.batchUpdateAutSaAud(req.getAutSaAudLists());
        } else {
            // 款提交场景下当前提交类型数为0，数据 插入操作  插入自评审核表 aut_sa_aud
            this.batchInsertAutSaAud(req.getAutSaAudLists());
        }
        log.info("processSummary ------ 结束 耗时：{}", System.currentTimeMillis() - startTime);
    }

    /**
     * 排除查询数据再新增,返回查询与新增的数据
     *
     * @param req
     */
    public List<AutSaAud> qryInsertAutSaAud(AutSaAudSaveDTO req) {
        if (CollectionUtils.isEmpty(req.getAutSaAudLists()) || StringUtils.isEmpty(req.getAutCode()) || StringUtils.isEmpty(req.getSubmitType())) {
            log.error("入参认证自评数据为空，入参数据为：" + req.toString());
            throw new ServiceException("入参认证自评数据为空");
        }
        if (!req.getAutSaAudLists().stream().map(AutSaAud::getSubmitType).allMatch(submitType -> req.getSubmitType().equals(submitType))) {
            log.error("入参提交类型与查询的提交类型数据不一致，入参提交类型有：" + Arrays.toString(req.getAutSaAudLists().stream().map(AutSaAud::getSubmitType).toArray())
                    + "查询提交类型是：" + req.getSubmitType());
            throw new ServiceException("排除查询数据新增认证自评数据有误");
        }
        List<AutSaAud> newAutSaAudList = new ArrayList<>();
        List<AutSaAud> reqAutSaAudList = req.getAutSaAudLists();
        List<AutSaAud> qryAutSaAudList = autSaAudMapper.selectAutSaAudListByAutCodeAndTypes(req.getAutCode(), req.getSubmitType());
        List<AutSaAud> insertList;
        if (CollectionUtils.isNotEmpty(qryAutSaAudList)) {
            List<String> qryClauseId = qryAutSaAudList.stream().map(AutSaAud::getClauseId).collect(Collectors.toList());
            //新增数据,找出表里没有的数据新增
            insertList = reqAutSaAudList.stream().filter(o -> !qryClauseId.contains(o.getClauseId())).collect(Collectors.toList());
            newAutSaAudList.addAll(qryAutSaAudList);
        } else {
            insertList = reqAutSaAudList;
        }
        if (CollectionUtils.isEmpty(insertList)) {
            throw new ServiceException("[拒绝修改]款已提交，请勿重复提交！");
        }

        this.batchInsertAutSaAud(insertList);
        newAutSaAudList.addAll(insertList);
        return newAutSaAudList;
    }

    /**
     * 处理款提交和驳回场景 -- 提交数据后，判断已提交的款数据数量是否达到上限，达到则判断是否有驳回操作，驳回需要翻转到驳回节点并驳回对应数据(复查驳回到初查)
     *
     * @param req 流程参数
     */
    @Override
    public void processClauseAndRJ04Scene(AutSaAudSaveDTO req) {
        long startTime = System.currentTimeMillis();
        log.info("processClauseAndRJ04Scene ------ 开始");
        // 款上限
        int totalCount = MapUtils.getInteger(cstCertificationStandardsService.selectCountByVersionId(req.getAutSaRelation().getAutCsId()), "clauseCount");
        // 处理条或者款提交结果
        List<AutSaAud> newAutSaAuds = this.processClauseOrArticle(req, totalCount);
        if (newAutSaAuds.size() == totalCount) {
            // 复查驳回处理
            this.vClauseRjProcess(req, newAutSaAuds);
        }
        log.info("processClauseAndRJ04Scene ------ 结束 耗时：{}", System.currentTimeMillis() - startTime);
    }

    private void vClauseRjProcess(AutSaAudSaveDTO req, List<AutSaAud> newAutSaAuds) {
        // 判断是否有驳回操作，驳回需要翻转到驳回节点并驳回对应数据(驳回到初查)
        List<String> rejectClauseIds = newAutSaAuds.stream().filter(a -> AutSaAudResultEnum.checkIsAutSaAudResultEnum(a.getAutResult(),
                AutSaAudResultEnum.FIRST_TRIAL_REJECTED)).map(a -> a.getClauseId()).collect(Collectors.toList());
        String nextStatus;
        if (CollectionUtils.isNotEmpty(rejectClauseIds)) {
            // 根据驳回条款获取对应的驳回信息(初查+复查)
            AutSaAud rejectAutSaAud = new AutSaAud();
            rejectAutSaAud.setAutCode(req.getAutCode());
            rejectAutSaAud.setStatus(Constants.HospitalConstants.NUM_1);
            String rejectClauseIdsStr = String.join(",", rejectClauseIds);
            rejectAutSaAud.setClauseId(rejectClauseIdsStr);
            String rejectSubmitTypesStr = String.join(",", Arrays.asList(req.getSubmitType(), req.getSubmitType().replace("_v_", "_")));
            rejectAutSaAud.setSubmitType(rejectSubmitTypesStr);
            List<AutSaAud> rejectInfo = autSaAudMapper.selectAutSaAudList(rejectAutSaAud);
            // 翻转到驳回状态，需要将对应的初审初查数据和评审初查数据设为无效
            this.batchInvalidAutSaAudByClauseIds(req.getAutCode(), rejectSubmitTypesStr, rejectClauseIdsStr);
            // 将驳回信息落表保存    初审复查 --- 初审初查     评审复查  --- 评审初查 条款驳回信息
            this.saveAutSaAudBusinessData(req.getAutCode(), AutSaAudBusinessCodeEnum.AUT_SA_AUD_REJECT.getCode(), JSON.toJSONString(rejectInfo));
            // 节点往下走，翻转到驳回节点
            nextStatus = this.getNextStatus(req.getAutSaAudStatusConfig().getNextStatusConfig(), StatusProcessEnum.RJ);
            //复查审批完，取出驳回的款，邮箱和手机短信通知驳回款对应的组员
            this.emailAndSmsNotificationToUsers(rejectClauseIds, req);
        } else {
            // 清除驳回数据
            autSaAudBusinessDataService.deleteAutSaAudBusinessData(req.getAutCode(), AutSaAudBusinessCodeEnum.AUT_SA_AUD_REJECT.getCode());
            // 节点往下走，不用翻转到驳回节点
            nextStatus = this.getNextStatus(req.getAutSaAudStatusConfig().getNextStatusConfig(), StatusProcessEnum.PS);
            // 当前节点生成评审报告
            this.specialGenerateReport(req.getAutCode(), req.getAutSaRelation().getAutCsId(), req.getAutSaRelation().getAutStatus(), req.getHospitalName());
        }
        log.info("节点翻转到：{}", nextStatus);
        // 更新关联表 aut_sa_relation
        this.updateAutSaRelation(nextStatus, req.getAutCode());
    }

    public void emailAndSmsNotificationToUsers(List<String> rejectClauseIds, AutSaAudSaveDTO req) {
        //根据驳回的款ids，查询cst_certification_standards表，取出clause_no,做通知信息
        List<String> clauseNos = cstCertificationStandardsService.selectClauseNosByClauseIds(rejectClauseIds, req.getAutSaRelation().getAutCsId());
        if (CollectionUtils.isEmpty(clauseNos)) {
            throw new ServiceException("根据驳回的款ids，查询clause_no为空！");
        }

        //根据评审员组长账户id和医院认证编码，获取对应的组员账户id
        if (hospitalReviewerMapper.selectHospitalReviewerByReviewerIdAndApplyNo(req.getAccountId(), req.getAutSaRelation().getHospitalApplyNo()) == 0) {
            //当前用户不是组长
            log.error("复审驳回-当前操作用户不是组长，此节点应是组长复审！用户id为：{}", req.getAccountId());
            throw new ServiceException("当前操作用户不是评审组长，操作失败");
        }
        //排除组长，学员，验证评审员，只获取组员信息
        List<String> crewIds = hospitalReviewerMapper.selectHospitalReviewerIsCrewIdByApplyNo(req.getAutSaRelation().getHospitalApplyNo());
        if (CollectionUtils.isEmpty(crewIds)) {
            throw new ServiceException(String.format("根据医院编码:[%s]，获取医院与评审员管理信息错误！", req.getAutSaRelation().getHospitalApplyNo()));
        }
        log.info("排除组长，学员，验证评审员，只获取组员信息的用户Ids为[{}],", crewIds);
        //根据自评编码和sr_clause提交类型和驳回款Ids和组员信息，查询认证自评审核表，获取此款对应人员，排除组长不发送
        AutSaAud qryAutSaAud = new AutSaAud();
        qryAutSaAud.setAutCode(req.getAutSaRelation().getAutCode());
        qryAutSaAud.setSubmitType(AutSaAudSubmitTypeEnum.SR_CLAUSE.getSubmitType());
        qryAutSaAud.setClauseId(String.join(",", rejectClauseIds));
        qryAutSaAud.setCrewIds(String.join(",", crewIds));
        List<AutSaAud> autSaAuds = autSaAudMapper.selectAutSaAudList(qryAutSaAud);
        List<String> accountIds = autSaAuds.stream().map(AutSaAud::getAccountId).distinct().collect(Collectors.toList());
        log.info("拒绝的款ids[{}],需要发送的用户Ids[{}],", rejectClauseIds, accountIds);
        if (CollectionUtils.isEmpty(accountIds)) {
            log.info("拒绝的款都为评审组长负责的款，无需发送通知！");
            return;
        }
        if (ObjectUtils.isNotEmpty(accountIds)) {
            //查询用户表，获取组员的邮箱与电话
            List<UserVo> userList = sysUserMapper.selectUserByIds(accountIds);
            if (ObjectUtils.isNotEmpty(userList)) {
                //内容暂时为：驳回的款为：功能1.1.1,功能1.1.2 ...,请重新审核！
                StringBuilder content = new StringBuilder("驳回的款为：");
                //邮箱，短信都发送(1,2)
                String sendType = Constants.STR_NUM_1 + "," + Constants.STR_NUM_2;
                clauseNos.forEach(clauseNo ->
                        content.append(clauseNo).append(",")
                );
                String content1 = content.append("请重新审核！").toString();
                //发送短信与邮箱
                MessageSendRecordDTO messageSendRecordDTO = new MessageSendRecordDTO();
                messageSendRecordDTO.setContent(content1);
                messageSendRecordDTO.setSendType(sendType);
                messageSendRecordDTO.setUserVoList(userList);
                try {
                    long time1 = System.currentTimeMillis();
                    messageSendRecordService.sendMessageToUser(messageSendRecordDTO);
                    log.info("复审驳回-发送短信与邮箱耗时：" + (System.currentTimeMillis() - time1) + "ms");
                } catch (GeneralSecurityException e) {
                    log.error("复审驳回-发送短信链接客户端异常:" + e.getMessage());
                    //throw new RuntimeException("复审驳回-发送短信与邮箱异常："+e.getMessage());
                } catch (MessagingException e) {
                    log.error("复审驳回-发送短信异常:" + e.getMessage());
                    //throw new RuntimeException("复审驳回-发送短信与邮箱异常："+e.getMessage());
                }
            } else {
                throw new ServiceException(com.thas.common.utils.StringUtils.format("组员账号:{},已停用，不发送邮箱与短信", accountIds.toString()));
            }
        }
    }

    /**
     * 特殊节点生成报告
     *
     * @param autCode      自评编码
     * @param versionId    版本号
     * @param autStatus    节点
     * @param hospitalName
     */
    private void specialGenerateReport(String autCode, String versionId, String autStatus, String hospitalName) {
        if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autStatus, AutSaAudStatusEnum.WAIT_SA_CLAUSE, AutSaAudStatusEnum.SA_CLAUSE_PROCESS, AutSaAudStatusEnum.FR_SUMMARY_REJECT)) {
            // 自评完成，直接生成自评报告
            this.generateReport(autCode, versionId, String.join(",", Arrays.asList(AutSaAudSubmitTypeEnum.SA_CLAUSE.getSubmitType())), AutSaAudBusinessCodeEnum.AUT_SA_AUD_SA_REPORT.getCode(), Lists.newArrayList());
        } else if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autStatus, AutSaAudStatusEnum.WAIT_SR_V_CLAUSE, AutSaAudStatusEnum.SR_V_CLAUSE_PROCESS,
                AutSaAudStatusEnum.FR_REPORT_F_M, AutSaAudStatusEnum.FAR_V_CLAUSE_M, AutSaAudStatusEnum.TR_V_CLAUSE_M, AutSaAudStatusEnum.SR_REPORT_M)) {
            // 评审复查完成，并且没有评审复查驳回信息，直接生成评审报告
            this.generateReport(autCode, versionId,
                    String.join(",", Arrays.asList(AutSaAudSubmitTypeEnum.SR_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.SR_CLAUSE_M.getSubmitType(),
                            AutSaAudSubmitTypeEnum.FAR_CLAUSE_M.getSubmitType(), AutSaAudSubmitTypeEnum.TR_CLAUSE_M.getSubmitType(),
                            AutSaAudSubmitTypeEnum.SR_REPORT_M.getSubmitType())),
                    AutSaAudBusinessCodeEnum.AUT_SA_AUD_REPORT.getCode(), Lists.newArrayList());
        } else if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autStatus, AutSaAudStatusEnum.FR_REPORT_F_M_SUMMARY, AutSaAudStatusEnum.FAR_CLAUSE_M_SUMMARY,
                AutSaAudStatusEnum.TR_CLAUSE_M_SUMMARY, AutSaAudStatusEnum.SR_REPORT_M_SUMMARY)) {
            // 验证评审数据总结 评审修改总结  修改评审报告总结    需要后端生成最新的评审报告
            this.generatePdf(autCode, versionId);
        }
        if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autStatus, AutSaAudStatusEnum.TR_SUMMARY, AutSaAudStatusEnum.TR_CLAUSE_M_SUMMARY)) {
            //验证评审员评审总结 生成验证评审报告
            this.verifyReviewReport(autCode, versionId, hospitalName);
        }
        //当前节点评审完，生成预评审报告
        //{030301-修改评审报告 -->030302-修改评审报告总结},{0304011-评审修改中 -->030402-评审修改审核中}, {0305011-修改验证评审数据 -->030502-验证评审数据审核}
        if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autStatus, AutSaAudStatusEnum.FR_REPORT_F_M, AutSaAudStatusEnum.FAR_CLAUSE_M, AutSaAudStatusEnum.TR_CLAUSE_M)) {
            this.temReviewReport(autCode, versionId);
        }

    }

    /**
     * 生成预评审报告
     *
     * @param autCode   自评编码
     * @param versionId 版本号
     */
    @Override
    public void temReviewReport(String autCode, String versionId) {
        //{030202-评审中 -->030203-评审待复查}，所有评审员评审完转复查节点时生成评审报告
        // {0302041-评审复查驳回 -->030204-评审复查中 -->转下个节点}，评审复查中转下个节点-更新报告
        //{030301-修改评审报告 -->030302-修改评审报告总结},{0304011-评审修改中 -->030402-评审修改审核中}, {0305011-修改验证评审数据 -->030502-验证评审数据审核}
        //生成预评审报告业务数据AutSaAudBusiness
        this.generateReport(autCode, versionId,
                String.join(",", Arrays.asList(AutSaAudSubmitTypeEnum.SR_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.SR_CLAUSE_M.getSubmitType(),
                        AutSaAudSubmitTypeEnum.FAR_CLAUSE_M.getSubmitType(), AutSaAudSubmitTypeEnum.TR_CLAUSE_M.getSubmitType(),
                        AutSaAudSubmitTypeEnum.SR_REPORT_M.getSubmitType())),
                AutSaAudBusinessCodeEnum.TEM_REPORT.getCode(), Lists.newArrayList());
        // 生成最新的预评审报告 保存到sftp服务器和upload_file_info表
        FtlToPdfDTO ftlToPdfDTO = new FtlToPdfDTO();
        ftlToPdfDTO.setFtlTemplateCode(FtlToPdfEnum.TEM_REVIEW_REPORT.getCode());
        Map<String, Object> otherParam = new HashMap<>();
        otherParam.put("autCode", autCode);
        ftlToPdfDTO.setOtherParam(otherParam);
        pdfGenerateService.ftlToPdf(ftlToPdfDTO, null);
    }

    public void verifyReviewReport(String autCode, String versionId, String hospitalName) {
        //040103-验证评审员评审总结,030503-验证评审数据总结
        //生成验证评审报告业务数据AutSaAudBusiness
        this.generateVerifyReviewReportData(autCode, versionId,
                String.join(",", Arrays.asList(
                        AutSaAudSubmitTypeEnum.TR_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.SR_CLAUSE.getSubmitType(),
                        AutSaAudSubmitTypeEnum.TR_CLAUSE_M.getSubmitType(), AutSaAudSubmitTypeEnum.TR_CLAUSE_M_SKIP.getSubmitType(),
                        AutSaAudSubmitTypeEnum.FAR_CLAUSE_M.getSubmitType(), AutSaAudSubmitTypeEnum.TR_SUMMARY.getSubmitType())),
                AutSaAudBusinessCodeEnum.VERIFY_REVIEW_REPORT_DATA.getCode(), Lists.newArrayList());
        // 生成最新的预评审报告 保存到sftp服务器和upload_file_info表
        FtlToPdfDTO ftlToPdfDTO = new FtlToPdfDTO();
        ftlToPdfDTO.setFtlTemplateCode(FtlToPdfEnum.VERIFY_REVIEW_REPORT.getCode());
        Map<String, Object> otherParam = new HashMap<>();
        otherParam.put("autCode", autCode);
        otherParam.put("pdfFileName", String.format("%s-验证报告.pdf", hospitalName));
        ftlToPdfDTO.setOtherParam(otherParam);
        pdfGenerateService.ftlToPdf(ftlToPdfDTO, null);
    }

    public void generateVerifyReviewReportData(String autCode, String versionId, String submitTypes, String businessCode, List<AutSaAud> autSaAuds) {
        if (CollectionUtils.isEmpty(autSaAuds)) {
            autSaAuds = autSaAudMapper.selectAutSaAudListByAutCodeAndTypes(autCode, submitTypes);
        }
        //附表2数据，查tr_summary,获取描述字段数据封装，描述字段结构：
        //{"autEvaluate":"","autAdvantage":"","autProposal":"","autResult":"2","autSuggestion":"1","autDesc":""}
        List<AutSaAud> trSummaryList = autSaAuds.stream().filter(o ->
                AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(o.getSubmitType(), AutSaAudSubmitTypeEnum.TR_SUMMARY)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(trSummaryList) || trSummaryList.size() > 1) {
            log.error("自评编码为：{}，查询自评TR_SUMMARY提交类型数据错误,查询结果为{}", autCode, JSON.toJSONString(autSaAuds));
            throw new ServiceException("查询评审验证总结数据异常，请联系管理员");
        }
        VerifyReviewReportVo verifyReviewReportVo = JSON.parseObject(trSummaryList.get(0).getAutDesc(), VerifyReviewReportVo.class);

        //附件1
        //查询验证评审疑问的tr_clause提交类型是否数据，如有查询评审员的sr_clause提交类型数据
        Map<String, AutSaAud> trClauseMap = autSaAuds.stream().filter(o ->
                AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(o.getSubmitType(), AutSaAudSubmitTypeEnum.TR_CLAUSE)).
                collect(Collectors.toMap(AutSaAud::getClauseId, o -> o));
        if (MapUtils.isNotEmpty(trClauseMap)) {
            //查询款id对应款号
            List<CstCertificationStandards> cstCertificationStandards = queryStandardsByClauseIdsAndVersionId(trClauseMap, versionId);
            Map<String, String> clauseMap = cstCertificationStandards.stream().collect(Collectors.toMap(o -> String.valueOf(o.getClauseId()), o -> o.getClauseNo()));
            //评审员回应数据，查tr_clause_m,tr_clause_m_skip,两者取最新的数据
            Map<String, AutSaAud> trClauseMOrSkipMap = autSaAuds.stream().filter(o ->
                    StringUtils.isNotBlank(o.getClauseId()) && AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(o.getSubmitType(),
                            AutSaAudSubmitTypeEnum.TR_CLAUSE_M, AutSaAudSubmitTypeEnum.TR_CLAUSE_M_SKIP))
                    .collect(Collectors.groupingBy(AutSaAud::getClauseId)).entrySet()
                    .stream().map(a -> a.getValue().stream().sorted(Comparator.comparing(AutSaAud::getUpdateTime).reversed())
                            .collect(Collectors.toList()).get(0)).collect(Collectors.toMap(AutSaAud::getClauseId, o -> o));
            log.info("自评编码为：{},评审员回应数据:[{}]", autCode, JSON.toJSONString(trClauseMOrSkipMap));

            List<VerifyReviewReportVo.FirstAttachmentVo> firstAttachmentVoList = new ArrayList<>();
            List<AutSaAud> srOrFarClauseList = autSaAuds.stream().filter(o -> trClauseMap.containsKey(o.getClauseId()) &&
                    AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(o.getSubmitType(), AutSaAudSubmitTypeEnum.SR_CLAUSE, AutSaAudSubmitTypeEnum.FAR_CLAUSE_M))
                    .collect(Collectors.groupingBy(AutSaAud::getClauseId)).entrySet()
                    .stream().map(a -> a.getValue().stream().sorted(Comparator.comparing(AutSaAud::getUpdateTime).reversed()).collect(Collectors.toList()).get(0))
                    .collect(Collectors.toList());
            srOrFarClauseList.forEach(srOrFarClause -> {
                VerifyReviewReportVo.FirstAttachmentVo firstAttachmentVo = new VerifyReviewReportVo.FirstAttachmentVo();
                firstAttachmentVo.setClauseNo(clauseMap.get(srOrFarClause.getClauseId()));
                firstAttachmentVo.setReviewScoreResult(srOrFarClause.getAutResult());
                //srClause描述结构：{"autAdvantage":"-"}
                firstAttachmentVo.setReviewScoreDesc(JSON.parseObject(srOrFarClause.getAutDesc(), VerifyReviewReportVo.FirstAttachmentVo.ReviewAnswerVo.class));
                firstAttachmentVo.setVerifyReviewDoubtDesc(trClauseMap.get(srOrFarClause.getClauseId()).getAutDesc());
                if (MapUtils.isNotEmpty(trClauseMOrSkipMap)) {
                    //第一次生成报告时，没有此数据，要判空
                    //trClauseMOrSkip提交类型描述数据结构：{"autAdvantage":"","autEvaluate":"","autProposal":"","autImprove":"","autDesc":""}
                    VerifyReviewReportVo.FirstAttachmentVo.ReviewAnswerVo reviewAnswerVo = new VerifyReviewReportVo.FirstAttachmentVo.ReviewAnswerVo();
                    reviewAnswerVo = JSON.parseObject(trClauseMOrSkipMap.get(srOrFarClause.getClauseId()).getAutDesc(), VerifyReviewReportVo.FirstAttachmentVo.ReviewAnswerVo.class);
                    reviewAnswerVo.setReviewAnswerResult(trClauseMOrSkipMap.get(srOrFarClause.getClauseId()).getAutResult());
                    firstAttachmentVo.setReviewAnswerVo(reviewAnswerVo);
                }
                firstAttachmentVoList.add(firstAttachmentVo);
            });
            verifyReviewReportVo.setFirstAttachmentVoList(firstAttachmentVoList);
        }

        // 保存报告信息
        this.saveAutSaAudBusinessData(autCode, businessCode, JSON.toJSONString(verifyReviewReportVo));

    }

    private List<CstCertificationStandards> queryStandardsByClauseIdsAndVersionId(Map<String, AutSaAud> trClauseMap, String versionId) {
        SelectStandardsByClauseIdsDTO selectStandardsByClauseIdsDTO = new SelectStandardsByClauseIdsDTO();
        selectStandardsByClauseIdsDTO.setVersionId(Long.valueOf(versionId));
        List<Long> clauseIds = trClauseMap.keySet().stream().map(Long::valueOf).collect(Collectors.toList());
        selectStandardsByClauseIdsDTO.setClauseIds(clauseIds);
        List<CstCertificationStandards> cstCertificationStandards = cstCertificationStandardsService.selectByClauseIdsAndVersionId(selectStandardsByClauseIdsDTO);
        if (CollectionUtils.isEmpty(cstCertificationStandards) || cstCertificationStandards.size() != clauseIds.size()) {
            log.error("根据【{}】条件，查询认证标准模板数据错误,查询结果为{}", JSON.toJSONString(selectStandardsByClauseIdsDTO), JSON.toJSONString(cstCertificationStandards));
            throw new ServiceException("查询数据异常，请联系管理员");
        }
        return cstCertificationStandards;
    }

    @Override
    public FileInfoVO getTemReviewReportFileInfo(String autCode) {

        AutSaRelation autSaRelation = autSaRelationService.selectAutSaRelationByAutCode(autCode, Constants.INT_ONE);

        if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autSaRelation.getAutStatus(), AutSaAudStatusEnum.WAIT_SR_V_CLAUSE, AutSaAudStatusEnum.SR_V_CLAUSE_PROCESS, AutSaAudStatusEnum.SR_V_CLAUSE_REJECT,
                AutSaAudStatusEnum.FR_REPORT_F_M_SUMMARY, AutSaAudStatusEnum.FAR_V_CLAUSE_M, AutSaAudStatusEnum.TR_V_CLAUSE_M, AutSaAudStatusEnum.SR_CLAUSE_PROCESS
        )) {
            List<AutSaAudBusinessData> autSaAudBusinessData = autSaAudBusinessDataService.selectAutSaAudBusinessData(autCode, AutSaAudBusinessCodeEnum.TEM_REVIEW_REPORT.getCode());
            if (CollectionUtils.isEmpty(autSaAudBusinessData)) {
                log.info(String.format("根据节点:[%s]和业务编码：[%s],获取自评业务数据为空！", autCode, AutSaAudBusinessCodeEnum.TEM_REVIEW_REPORT.getCode()));
                return null;
            }
            if (StringUtils.isEmpty(autSaAudBusinessData.get(0).getData())) {
                throw new ServiceException(String.format("根据节点:[%s]和业务编码：[%s],获取自评业务数据为空！", autCode, AutSaAudBusinessCodeEnum.TEM_REVIEW_REPORT.getCode()));
            }
            log.info("预报告文件信息为" + autSaAudBusinessData.get(0).getData());
            FileInfoVO fileInfoVO = JSON.parseObject(autSaAudBusinessData.get(0).getData(), FileInfoVO.class);
            List<FileInfoVO> fileInfoVOList = commonService.fileInfoDtoToVo(uploadFileInfoService.getUploadFileInfoByIds(fileInfoVO.getFileId()));
            if (CollectionUtils.isEmpty(fileInfoVOList)) {
                throw new ServiceException(String.format("根据文件id:[%s],查询文件信息为空！", fileInfoVO.getFileId()));
            }
            fileInfoVO = fileInfoVOList.get(0);
            fileInfoVO.setUpdateTime(autSaAudBusinessData.get(0).getUpdateTime());
            return fileInfoVO;
        } else {
            throw new ServiceException("不支持当前节点查看预评审报告！");
        }
    }

    @Override
    public List<AutSaAudList> listSorted(List<AutSaAudList> resultList) {
        if (CollectionUtils.isNotEmpty(resultList)) {
            //已自评完成时间排序，如果都为空，以audSecondSubmitTime评审提交时间排序
            List<AutSaAudList> saAudSubmitTimeList = resultList.stream().filter(o -> o.getSaAudSubmitTime() != null).sorted(
                    Comparator.comparing(AutSaAudList::getSaAudSubmitTime).reversed()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(saAudSubmitTimeList)) {
                List<AutSaAudList> audSecondSubmitTimeList = resultList.stream().filter(o -> o.getAudSecondSubmitTime() != null).sorted(
                        Comparator.comparing(AutSaAudList::getAudSecondSubmitTime).reversed()).collect(Collectors.toList());
                resultList = resultList.stream().filter(o -> o.getAudSecondSubmitTime() == null).collect(Collectors.toList());
                resultList.addAll(audSecondSubmitTimeList);
                return resultList;
            }
            resultList = resultList.stream().filter(o -> o.getSaAudSubmitTime() == null).collect(Collectors.toList());
            resultList.addAll(saAudSubmitTimeList);
        }
        return resultList;
    }

    @Override
    public AutSaAudList queryTraineesAssessorAutSaAudList(AutSaAudQueryDTO req, List<HosPlanUserInfoVO> memberList) {
        if (CollectionUtils.isEmpty(memberList)) {
            log.error("自评编码：{},用户：{},获取对应组员列表信息为空", req.getAutSaRelation().getAutCode(), req.getAccountId());
            throw new ServiceException("获取对应组员列表信息为空");
        }
        AutSaAudList autSaAudList = new AutSaAudList();
        List<String> clauseIds = new ArrayList<>();
        //分组评审员Id不为空时，返回对应信息
        if (StringUtils.isNotBlank(req.getDomainAccountId())) {
            List<HosPlanUserInfoVO> collect = memberList.stream().filter(o -> ObjectUtil.equal(o.getAccountId(), req.getDomainAccountId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collect)) {
                log.error("自评编码：{},用户：{},获取对应组员分组信息为空", req.getAutSaRelation().getAutCode(), req.getDomainAccountId());
                throw new ServiceException("获取对应组员分组信息为空");
            }
            HosPlanUserInfoVO hosPlanUserInfoVO = collect.get(0);
            clauseIds.addAll(hosPlanUserInfoVO.getCstCertificationStandardVOList().stream().map(o -> String.valueOf(o.getClauseId())).collect(Collectors.toList()));
            // 款项挪动,当为评审员，需要移除不回显
            this.clauseMoveTraineesAssessor(req, clauseIds);
        } else {
            memberList.stream().map(HosPlanUserInfoVO::getCstCertificationStandardVOList).forEach(cstCertificationStandardList -> {
                clauseIds.addAll(cstCertificationStandardList.stream().map(o -> String.valueOf(o.getClauseId())).collect(Collectors.toList()));
            });
        }
        //当有不适用款，对应款排除
        this.checkRemoveReviewFitMoveClauseTraineesAssessor(req, clauseIds);
        if (CollectionUtils.isEmpty(clauseIds)) {
            log.error("自评编码：{},学员id：{},分组评审员id：{}, 获取条款信息为空", req.getAutSaRelation().getAutCode(), req.getAccountId(), req.getDomainAccountId());
            throw new ServiceException("获取条款信息为空");
        }
        autSaAudList.setDistributeClauseIdList(clauseIds);

        return autSaAudList;

    }

    @Override
    public void generatePdf(String autCode, String versionId) {
        this.generateReport(autCode, versionId,
                String.join(",", Arrays.asList(AutSaAudSubmitTypeEnum.SR_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.SR_CLAUSE_M.getSubmitType(),
                        AutSaAudSubmitTypeEnum.FAR_CLAUSE_M.getSubmitType(), AutSaAudSubmitTypeEnum.TR_CLAUSE_M.getSubmitType(),
                        AutSaAudSubmitTypeEnum.SR_REPORT_M.getSubmitType(), AutSaAudSubmitTypeEnum.SR_REPORT_M_SUMMARY.getSubmitType())),
                AutSaAudBusinessCodeEnum.AUT_SA_AUD_REPORT.getCode(), Lists.newArrayList());
        // 需要后端生成最新的评审报告 保存到sftp服务器
        FtlToPdfDTO ftlToPdfDTO = new FtlToPdfDTO();
        ftlToPdfDTO.setFtlTemplateCode(FtlToPdfEnum.REVIEW_REPORT.getCode());
        Map<String, Object> otherParam = new HashMap<>();
        otherParam.put("autCode", autCode);
        ftlToPdfDTO.setOtherParam(otherParam);
        pdfGenerateService.ftlToPdf(ftlToPdfDTO, null);
    }

    /**
     * 处理待修改场景(评审组长查看待修改条款)
     *
     * @param req 流程参数
     */
//    @Override
//    public void processWaitModifyScene(AutSaAudSaveDTO req) {
//        long startTime = System.currentTimeMillis();
//        log.info("processWaitModifyScene ------ 开始");
//        // 校验是否为评审组长，仅评审组长可进行查看待修改条款操作
//        this.specialSummaryAndConfirm(req);
//        StatusProcessEnum statusProcessEnum = StatusProcessEnum.PS;
//        if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(req.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SKIP, AutSaAudSubmitTypeEnum.TR_CLAUSE_M_SKIP)) {
//            // 拒绝修改
//            if (!AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.FAR_CLAUSE_W_M, AutSaAudStatusEnum.TR_CLAUSE_W_M)) {
//                log.error("提交拒绝修改：{} 时，节点是： {},不满足条件", req.getSubmitType(), req.getAutSaRelation().getAutStatus());
//                throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000002);
//            }
//        } else {
//            // 调用该场景仅用于判断是否有待修改项（事实准确性审查修改项  和 验证评审员审查修改项）
//            List<AutSaAud> hisClauseAutSaAuds = autSaAudMapper.selectAutSaAudListByAutCodeAndTypes(req.getAutCode(), req.getSubmitType().replaceAll("_w_m", ""));
//            if (CollectionUtils.isNotEmpty(hisClauseAutSaAuds) && hisClauseAutSaAuds.stream().anyMatch(a -> AutSaAudResultEnum.DISAGREE.getCode().toString().equals(a.getAutResult()))) {
//                // 有待修改项，节点直接翻转到修改节点
//                statusProcessEnum = StatusProcessEnum.RJ;
//            }
//        }
//        String nextStatus = this.getNextStatus(req.getAutSaAudStatusConfig().getNextStatusConfig(), statusProcessEnum);
//        AutSaAud autSaAud = new AutSaAud();
//        autSaAud.setSubmitType(req.getSubmitType());
//        autSaAud.setAutCode(req.getAutCode());
//        autSaAud.setAccountId(req.getAccountId());
//        if (CollectionUtils.isNotEmpty(req.getAutSaAudLists()) && StringUtils.isNotBlank(req.getAutSaAudLists().get(0).getAutDesc())) {
//            autSaAud.setAccountId(req.getAutSaAudLists().get(0).getAutDesc());
//        }
//        req.setAutSaAudLists(Arrays.asList(autSaAud));
//        // 处理查看待修改 数据
//        this.processSummary(req);
//        // 更新关联表 aut_sa_relation
//        this.updateAutSaRelation(nextStatus, req.getAutCode());
//        log.info("processWaitModifyScene ------ 结束 耗时：{}", System.currentTimeMillis() - startTime);
//    }
    @Override
    public void processWaitModifyScene(AutSaAudSaveDTO req) {
        long startTime = System.currentTimeMillis();
        log.info("processWaitModifyScene ------ 开始");
        // 校验是否为评审组长，仅评审组长可进行查看待修改条款操作
        this.specialSummaryAndConfirm(req);
        StatusProcessEnum statusProcessEnum = StatusProcessEnum.PS;
        //是否需要翻转节点
        String nextStatus = "";
        AutSaAud autSaAud = new AutSaAud();
        if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(req.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SKIP, AutSaAudSubmitTypeEnum.TR_CLAUSE_M_SKIP)) {
            // 拒绝修改
            if (!AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.FAR_CLAUSE_W_M, AutSaAudStatusEnum.TR_CLAUSE_W_M
                    , AutSaAudStatusEnum.FAR_CLAUSE_M, AutSaAudStatusEnum.TR_CLAUSE_M)) {
                log.error("提交拒绝修改：{} 时，节点是： {},不满足条件", req.getSubmitType(), req.getAutSaRelation().getAutStatus());
                throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000002);
            }
            //拒绝修改，插入认证自评表，翻转节点
            nextStatus = clauseMSkipOperate(req, statusProcessEnum);
        } else {
            // 调用该场景仅用于判断是否有待修改项（事实准确性审查修改项  和 验证评审员审查修改项）
            List<AutSaAud> hisClauseAutSaAuds = autSaAudMapper.selectAutSaAudListByAutCodeAndTypes(req.getAutCode(), req.getSubmitType().replace("_w_m", ""));
            if (CollectionUtils.isNotEmpty(hisClauseAutSaAuds) && hisClauseAutSaAuds.stream().anyMatch(a -> AutSaAudResultEnum.DISAGREE.getCode().toString().equals(a.getAutResult()))) {
                // 有待修改项，节点直接翻转到修改节点
                statusProcessEnum = StatusProcessEnum.RJ;
            }
            nextStatus = this.getNextStatus(req.getAutSaAudStatusConfig().getNextStatusConfig(), statusProcessEnum);
            if (StringUtils.isEmpty(nextStatus)) {
                throw new ServiceException("获取下个节点数据错误");
            }
            autSaAud.setSubmitType(req.getSubmitType());
            autSaAud.setAutCode(req.getAutCode());
            autSaAud.setAccountId(req.getAccountId());
            if (CollectionUtils.isNotEmpty(req.getAutSaAudLists()) && StringUtils.isNotBlank(req.getAutSaAudLists().get(0).getAutDesc())) {
                autSaAud.setAccountId(req.getAutSaAudLists().get(0).getAutDesc());
            }
            req.setAutSaAudLists(Arrays.asList(autSaAud));
            // 处理查看待修改 数据
            this.processSummary(req);
        }

        if (StringUtils.isNotBlank(nextStatus)) {
            // 更新关联表 aut_sa_relation
            this.updateAutSaRelation(nextStatus, req.getAutCode());
        }

        log.info("processWaitModifyScene ------ 结束 耗时：{}", System.currentTimeMillis() - startTime);
    }

    private String clauseMSkipOperate(AutSaAudSaveDTO req, StatusProcessEnum statusProcessEnum) {
        String nextStatus = "";
        //评审修改或验证修改skip类型，获取对应驳回和已修改的提交类型数据
        String rjSubmitType = AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(req.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SKIP) ?
                String.join(",", AutSaAudSubmitTypeEnum.FAR_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_M.getSubmitType()) :
                String.join(",", AutSaAudSubmitTypeEnum.TR_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.TR_CLAUSE_M.getSubmitType());
        //获取入参是否有数据
        List<AutSaAud> autSaAudLists = req.getAutSaAudLists();
        //获取驳回款和已修改数据，对应新增’far_clause_m_skip或tr_clause_m_skip‘提交类型数据
        List<AutSaAud> autSaAudList = autSaAudMapper.selectAutSaAudListByAutCodeAndTypes(req.getAutCode(), rjSubmitType);
        List<AutSaAud> rjAutSaAuds = new ArrayList<>();
        List<AutSaAud> modifiedAutSaAuds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(autSaAudList)) {
            //驳回款
            rjAutSaAuds = autSaAudList.stream().filter(o -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(
                    o.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE, AutSaAudSubmitTypeEnum.TR_CLAUSE)).collect(Collectors.toList());
            //已修改
            modifiedAutSaAuds = autSaAudList.stream().filter(o -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(
                    o.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_M, AutSaAudSubmitTypeEnum.TR_CLAUSE_M)).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(autSaAudLists)) {
            //没数据证明拒绝所有修改，跳转下个节点；
            List<AutSaAud> insertAutSaAudList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(rjAutSaAuds)) {
                insertAutSaAudList = rjAutSaAuds.stream().map(AutSaAud::getClauseId).map(rjClauseId -> {
                            AutSaAud insertAutSaAud = new AutSaAud();
                            insertAutSaAud.setAutCode(req.getAutCode());
                            insertAutSaAud.setSubmitType(req.getSubmitType());
                            insertAutSaAud.setClauseId(rjClauseId);
                            insertAutSaAud.setAccountId(req.getAccountId());
                            insertAutSaAud.setAutResult(Constants.STR_NUM_0);
                            if (CollectionUtils.isNotEmpty(req.getAutSaAudLists()) && StringUtils.isNotBlank(req.getAutSaAudLists().get(0).getAutDesc())) {
                                insertAutSaAud.setAutDesc(req.getAutSaAudLists().get(0).getAutDesc());
                            }
                            return insertAutSaAud;
                        }
                ).collect(Collectors.toList());
            } else {
                AutSaAud autSaAud = new AutSaAud();
                autSaAud.setSubmitType(req.getSubmitType());
                autSaAud.setAutCode(req.getAutCode());
                autSaAud.setAccountId(req.getAccountId());
                if (CollectionUtils.isNotEmpty(req.getAutSaAudLists()) && StringUtils.isNotBlank(req.getAutSaAudLists().get(0).getAutDesc())) {
                    autSaAud.setAutDesc(req.getAutSaAudLists().get(0).getAutDesc());
                }
                insertAutSaAudList.add(autSaAud);
            }
            req.setAutSaAudLists(insertAutSaAudList);
            //排除查询数据,然后新增，返回新增和查询数据
            this.qryInsertAutSaAud(req);
            //所有都拒绝修改后，当节点为{0304011-评审修改}中和{0305011-修改验证评审数据}时，直接跳转到SKIP下个节点
            statusProcessEnum = getAllRjUpdateNextStatus(req, statusProcessEnum);
            nextStatus = this.getNextStatus(req.getAutSaAudStatusConfig().getNextStatusConfig(), statusProcessEnum);
        } else {
            //插入入参数据,返回新数据
            List<AutSaAud> newAutSaAuds = qryInsertAutSaAud(req);
            //1-2-2比较驳回款与'拒绝修改-tr_clause_m_skip'款加‘提交修改-tr_clause_m’款是否一致，一致证明拒绝了所有修改，翻转节点{030404-提交评审修改},{030601-提交评审报告}
            //翻转节点条件：驳回款 == 拒绝修改 + 提交修改
            if (rjAutSaAuds.size() == newAutSaAuds.size() + modifiedAutSaAuds.size()) {
                statusProcessEnum = getAllRjUpdateNextStatus(req, statusProcessEnum);
                nextStatus = this.getNextStatus(req.getAutSaAudStatusConfig().getNextStatusConfig(), statusProcessEnum);
            }
        }
        return nextStatus;
    }

    /**
     * 所有都拒绝修改后，当节点为{0304011-评审修改}中和{0305011-修改验证评审数据}时，获取SKIP下个节点
     */
    private StatusProcessEnum getAllRjUpdateNextStatus(AutSaAudSaveDTO req, StatusProcessEnum statusProcessEnum) {
        if (req.getAutSaRelation() == null || StringUtils.isEmpty(req.getAutSaRelation().getAutStatus())) {
            throw new ServiceException("自评认证状态为空！");
        }
        if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.FAR_CLAUSE_M, AutSaAudStatusEnum.TR_CLAUSE_M)) {
            return StatusProcessEnum.SKIP;
        }
        return statusProcessEnum;

    }

    /**
     * 处理修改场景(评审员修改条款)
     *
     * @param req 流程参数
     */
    @Override
    public void processModifyScene(AutSaAudSaveDTO req) {
        long startTime = System.currentTimeMillis();
        log.info("processModifyScene ------ 开始");
        //当节点为{0304011-评审修改}或{0305011-修改验证评审数据}时，且入参提交类型为*_skip
        if (StringUtils.isNotBlank(req.getSubmitType()) && AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(
                req.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SKIP, AutSaAudSubmitTypeEnum.TR_CLAUSE_M_SKIP)
                && AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.FAR_CLAUSE_M, AutSaAudStatusEnum.TR_CLAUSE_M)
        ) {
            this.processWaitModifyScene(req);
            log.info("processModifyScene --转-->>>processWaitModifyScene 结束 耗时：{}", System.currentTimeMillis() - startTime);
            return;
        }

        //当节点为{0304011-评审修改}或{0305011-修改验证评审数据}时,评审组长才可操作
        this.specialSummaryAndConfirm(req);

        if (req.getAutSaAudLists().stream().anyMatch(a -> StringUtils.isBlank(a.getAutResult()))) {
            log.error("入参审核结果：autResult 为空");
            throw new ServiceException(ServiceExceptionEnum.AUT_SA_AUD_ERROR_1000002);
        }
        String querySubmitType = "";
        if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.FR_REPORT_F_M)) {
            // 第一次修改评审报告 --修改评审报告
            querySubmitType = String.join(",", Arrays.asList(AutSaAudSubmitTypeEnum.FR_REPORT_R_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.SR_CLAUSE_M.getSubmitType()));
        } else if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.FAR_CLAUSE_M)) {
            // 评审修改中 -- 第二次修改
            querySubmitType = String.join(",", Arrays.asList(AutSaAudSubmitTypeEnum.FAR_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_M.getSubmitType(),
                    AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SKIP.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_RJ_E_M.getSubmitType()));
        } else if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.FAR_V_CLAUSE_M)) {
            // 评审修改审核中 --第二次修改
            querySubmitType = String.join(",", Arrays.asList(AutSaAudSubmitTypeEnum.FAR_V_CLAUSE_M.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_M.getSubmitType()));
        } else if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.TR_CLAUSE_M)) {
            // 修改验证评审数据  --第三次修改
            querySubmitType = String.join(",", Arrays.asList(AutSaAudSubmitTypeEnum.TR_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.TR_CLAUSE_M.getSubmitType(),
                    AutSaAudSubmitTypeEnum.TR_CLAUSE_M_SKIP.getSubmitType()));
        } else if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.TR_V_CLAUSE_M)) {
            // 验证评审数据审核  --第三次修改
            querySubmitType = String.join(",", Arrays.asList(AutSaAudSubmitTypeEnum.TR_V_CLAUSE_M.getSubmitType(), AutSaAudSubmitTypeEnum.TR_CLAUSE_M.getSubmitType()));
        }
        List<AutSaAud> queryClauses = autSaAudMapper.selectAutSaAudListByAutCodeAndTypes(req.getAutCode(), querySubmitType);
        List<String> rejectClauses = Lists.newArrayList();
        List<AutSaAud> submit = Lists.newArrayList();
        //拒绝修改数据
        List<AutSaAud> rjSkipList = Lists.newArrayList();
        if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.FR_REPORT_F_M)) {
            // 第一次修改评审报告 --修改评审报告
            rejectClauses = queryClauses.stream().filter(a -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType(), AutSaAudSubmitTypeEnum.FR_REPORT_R_CLAUSE) &&
                    AutSaAudResultEnum.checkIsAutSaAudResultEnum(a.getAutResult(), AutSaAudResultEnum.DISAGREE)).map(a -> a.getClauseId()).collect(Collectors.toList());
            submit = queryClauses.stream().filter(a -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType(), AutSaAudSubmitTypeEnum.SR_CLAUSE_M)).collect(Collectors.toList());
        } else if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.FAR_CLAUSE_M)) {
            // 评审修改中 -- 第二次修改 ,兼容审查组长驳回款
            rejectClauses = queryClauses.stream().filter(a -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE,
                    AutSaAudSubmitTypeEnum.FAR_CLAUSE_RJ_E_M) && AutSaAudResultEnum.checkIsAutSaAudResultEnum(a.getAutResult(), AutSaAudResultEnum.DISAGREE)).
                    map(AutSaAud::getClauseId).distinct().collect(Collectors.toList());
            submit = queryClauses.stream().filter(a -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_M)).collect(Collectors.toList());
            rjSkipList = queryClauses.stream().filter(a -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SKIP)).collect(Collectors.toList());
        } else if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.FAR_V_CLAUSE_M)) {
            // 评审修改审核中 --第二次修改
            rejectClauses = queryClauses.stream().filter(a -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_M)).map(a -> a.getClauseId()).collect(Collectors.toList());
            submit = queryClauses.stream().filter(a -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_V_CLAUSE_M)).collect(Collectors.toList());
        } else if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.TR_CLAUSE_M)) {
            // 修改验证评审数据  --第三次修改
            rejectClauses = queryClauses.stream().filter(a -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType(), AutSaAudSubmitTypeEnum.TR_CLAUSE) &&
                    AutSaAudResultEnum.checkIsAutSaAudResultEnum(a.getAutResult(), AutSaAudResultEnum.DISAGREE)).map(a -> a.getClauseId()).collect(Collectors.toList());
            submit = queryClauses.stream().filter(a -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType(), AutSaAudSubmitTypeEnum.TR_CLAUSE_M)).collect(Collectors.toList());
            rjSkipList = queryClauses.stream().filter(a -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType(), AutSaAudSubmitTypeEnum.TR_CLAUSE_M_SKIP)).collect(Collectors.toList());
        } else if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.TR_V_CLAUSE_M)) {
            // 验证评审数据审核  --第三次修改
            rejectClauses = queryClauses.stream().filter(a -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType(), AutSaAudSubmitTypeEnum.TR_CLAUSE_M)).map(a -> a.getClauseId()).collect(Collectors.toList());
            submit = queryClauses.stream().filter(a -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType(), AutSaAudSubmitTypeEnum.TR_V_CLAUSE_M)).collect(Collectors.toList());
        }
        List<String> finalRejectClauses = rejectClauses;
        if (req.getAutSaAudLists().stream().anyMatch(a -> !finalRejectClauses.contains(a.getClauseId()))) {
            log.error("入参的款项id，不在驳回范围内");
            throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000002);
        }

        //如果拒绝修改款不为空，入参的款不能等于拒绝修改的款
        if (CollectionUtils.isNotEmpty(rjSkipList)) {
            List<String> rjSkipClauseIds = rjSkipList.stream().map(AutSaAud::getClauseId).collect(Collectors.toList());
            if (req.getAutSaAudLists().stream().anyMatch(a -> rjSkipClauseIds.contains(a.getClauseId()))) {
                log.error("入参的款项：{}，已[拒绝修改]，评审不能修改提交！", req.getAutSaAudLists().toString());
                throw new ServiceException("已[拒绝修改]，评审不能修改提交！");
            }
        }

        //查询已提交的款
        List<String> submitClauses = submit.stream().map(a -> a.getClauseId()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(submitClauses) && req.getAutSaAudLists().stream().anyMatch(a -> submitClauses.contains(a.getClauseId()))) {
            log.error("入参的款项id，之前已提交修改，不支持再次改动");
            throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000002);
        }
        //最终需修改的拒绝款数：拒绝款-拒绝修改款
        List<AutSaAud> newAutSaAuds = this.updateClauseAutSaAud(req.getAutSaAudLists(), submit, rejectClauses.size() - rjSkipList.size());
        if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.FR_REPORT_F_M)) {
            //在{030301-修改评审报告}驳回修改转节点时，生成评审业务数据记录驳回修改的款
            AutSaAudBusinessData autSaAudBusinessData = new AutSaAudBusinessData();
            autSaAudBusinessData.setAutCode(req.getAutCode());
            autSaAudBusinessData.setBusinessCode(AutSaAudBusinessCodeEnum.SR_CLAUSE_M_DATA.getCode());
            autSaAudBusinessData.setData(JSON.toJSONString(newAutSaAuds));
            autSaAudBusinessDataService.saveAutSaAudBusinessData(autSaAudBusinessData);

            // 第一次修改评审报告 --修改评审报告  需要将修改结果对应跟新到评审初查上
            List<AutSaAud> newAutSaAudLists = req.getAutSaAudLists().stream().map(a -> {
                a.setSubmitType(AutSaAudSubmitTypeEnum.SR_CLAUSE.getSubmitType());
                return a;
            }).collect(Collectors.toList());
            this.batchUpdateAutSaAud(newAutSaAudLists);
        }
        String nextStatus = null;
        //最终需修改的拒绝款数：拒绝款-拒绝修改款
        if (newAutSaAuds.size() == rejectClauses.size() - rjSkipList.size()) {
            log.info("评审报告审查环节驳回的款项均修改完成");
            if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.FAR_V_CLAUSE_M, AutSaAudStatusEnum.TR_V_CLAUSE_M)) {
                //评审修改审核中 --第二次修改  验证评审数据审核  --第三次修改
                List<AutSaAud> disAgreeClauses = newAutSaAuds.stream().filter(a -> AutSaAudResultEnum.checkIsAutSaAudResultEnum(a.getAutResult(), AutSaAudResultEnum.DISAGREE)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(disAgreeClauses)) {
                    // 审核结果有不同意，驳回该款， 节点跳转至驳回节点
                    String rejectClauseIdsStr = String.join(",", disAgreeClauses.stream().map(a -> a.getClauseId()).collect(Collectors.toList()));
                    String rejectSubmitTypesStr = String.join(",", Arrays.asList(req.getSubmitType(), req.getSubmitType().replace("_v_", "_")));
                    this.batchInvalidAutSaAudByClauseIds(req.getAutCode(), rejectSubmitTypesStr, rejectClauseIdsStr);
                    // 将驳回信息保存
                    this.saveAutSaAudBusinessData(req.getAutCode(), AutSaAudBusinessCodeEnum.AUT_SA_AUD_REJECT.getCode(), JSON.toJSONString(disAgreeClauses));
                    nextStatus = this.getNextStatus(req.getAutSaAudStatusConfig().getNextStatusConfig(), StatusProcessEnum.RJ);
                } else {
                    // 清除驳回数据
                    autSaAudBusinessDataService.deleteAutSaAudBusinessData(req.getAutCode(), AutSaAudBusinessCodeEnum.AUT_SA_AUD_REJECT.getCode());
                    this.specialGenerateReport(req.getAutCode(), req.getAutSaRelation().getAutCsId(), req.getAutSaAudStatusConfig().getCurrentStatus(), req.getHospitalName());
                    nextStatus = this.getNextStatus(req.getAutSaAudStatusConfig().getNextStatusConfig(), StatusProcessEnum.PS);
                }
            } else if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.FAR_CLAUSE_M, AutSaAudStatusEnum.TR_CLAUSE_M)) {
                //0304011评审修改中和0305011修改验证评审数据
                //翻转状态为通过到下个节点
                nextStatus = this.getNextStatus(req.getAutSaAudStatusConfig().getNextStatusConfig(), StatusProcessEnum.PS);
                this.updateAutSaRelation(nextStatus, req.getAutCode());
                //修改完，获取far_clause_m数据，走{030402-评审修改审核中}插入数据
                AutSaAudSaveDTO autSaAudSaveDTO = new AutSaAudSaveDTO();
                autSaAudSaveDTO.setAutCode(req.getAutSaRelation().getAutCode());
                autSaAudSaveDTO.setSubmitType(AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.FAR_CLAUSE_M) ?
                        AutSaAudSubmitTypeEnum.FAR_V_CLAUSE_M.getSubmitType() : AutSaAudSubmitTypeEnum.TR_V_CLAUSE_M.getSubmitType());
                autSaAudSaveDTO.setAccountId(req.getAccountId());
                //都为通过
                newAutSaAuds.forEach(o -> o.setAutResult(Constants.STR_NUM_1));
                autSaAudSaveDTO.setAutSaAudLists(newAutSaAuds);
                baseProcessService.process(autSaAudSaveDTO);
                //返回后为{0304011评审修改中或0305011修改验证评审数据}节点逻辑，不需再翻转状态
                return;
            } else {
                this.specialGenerateReport(req.getAutCode(), req.getAutSaRelation().getAutCsId(), req.getAutSaAudStatusConfig().getCurrentStatus(), req.getHospitalName());
                nextStatus = this.getNextStatus(req.getAutSaAudStatusConfig().getNextStatusConfig(), StatusProcessEnum.PS);
            }
        }
        if (StringUtils.isNotBlank(nextStatus)) {
            // 更新关联表 aut_sa_relation
            this.updateAutSaRelation(nextStatus, req.getAutCode());
        }
        log.info("processModifyScene ------ 结束 耗时：{}", System.currentTimeMillis() - startTime);
    }

    /**
     * 批量插入自评审核信息
     *
     * @param autSaAudLists 认证自评审核
     */
    @Override
    public void batchInsertAutSaAud(List<AutSaAud> autSaAudLists) {
        if (autSaAudMapper.batchInsertAutSaAud(autSaAudLists) <= 0) {
            log.error("插入表 aut_sa_aud 操作失败");
            throw new ServiceException(ServiceExceptionEnum.AUT_SA_AUD_ERROR_1000004);
        }
    }

    /**
     * 批量插入自评审核信息
     *
     * @param autSaAudLists 认证自评审核
     */
    @Override
    public void batchUpdateAutSaAud(List<AutSaAud> autSaAudLists) {
        if (autSaAudMapper.batchUpdateAutSaAud(autSaAudLists) <= 0) {
            log.error("插入表 aut_sa_aud 操作失败");
            throw new ServiceException(ServiceExceptionEnum.AUT_SA_AUD_ERROR_1000004);
        }
    }

    /**
     * 更新自评审核关联信息
     *
     * @param updateStatus 节点
     * @param autCode      自评编码
     */
    @Override
    public void updateAutSaRelation(String updateStatus, String autCode) {
        log.info("当前自评编码：{} 翻转节点到：{}", autCode, updateStatus);
        AutSaRelation updateAutSaRelation = new AutSaRelation();
        updateAutSaRelation.setAutStatus(updateStatus);
        updateAutSaRelation.setAutCode(autCode);
        if (autSaRelationService.updateAutSaRelation(updateAutSaRelation) <= 0) {
            log.error("翻转 自评编码：{} 到节点状态:{} 异常 ", autCode, updateStatus);
            throw new ServiceException(ServiceExceptionEnum.AUT_SA_RELATION_ERROR_1000003);
        }
    }

    /**
     * 更新自评审核关联信息
     *
     * @param updateStatus 节点
     * @param applyNo      医疗机构编码
     */
    @Override
    public void updateAutSaRelationByApplyNo(String updateStatus, String applyNo) {
        log.info("当前医疗机构编码：{} 翻转节点到：{}", applyNo, updateStatus);
        AutSaRelation updateAutSaRelation = autSaRelationService.selectAutSaRelationByHospitalApplyNo(applyNo, false);
        updateAutSaRelation.setAutStatus(updateStatus);
        if (autSaRelationService.updateAutSaRelation(updateAutSaRelation) <= 0) {
            log.error("翻转 医疗机构编码：{} 到节点状态:{} 异常 ", applyNo, updateStatus);
            throw new ServiceException(ServiceExceptionEnum.AUT_SA_RELATION_ERROR_1000003);
        }
    }

    /**
     * 批量失效自评审核信息
     *
     * @param autCode   自评编码
     * @param types     类型
     * @param clauseIds 款id
     * @return
     */
    @Override
    public void batchInvalidAutSaAudByClauseIds(String autCode, String types, String clauseIds) {
        if (autSaAudMapper.batchInvalidAutSaAudByClauseIds(autCode, types, clauseIds) <= 0) {
            log.error("插入表 aut_sa_aud 操作失败");
            throw new ServiceException(ServiceExceptionEnum.AUT_SA_AUD_ERROR_1000004);
        }
    }

    /**
     * 根据下一节点标识获取下一节点
     *
     * @param nextStatusConfig  下一节点配置
     * @param statusProcessEnum 节点流程枚举值
     * @return 下一节点
     */
    @Override
    public String getNextStatus(String nextStatusConfig, StatusProcessEnum statusProcessEnum) {
        if (StringUtils.isBlank(nextStatusConfig) || statusProcessEnum == null) {
            return "";
        }
        return MapUtils.getString(JSON.parseObject(nextStatusConfig, Map.class), statusProcessEnum.getCode(), "");
    }

    /**
     * 保存自评审核业务数据
     *
     * @param autCode      自评编码
     * @param businessCode 环节
     * @param data         业务数据
     */
    @Override
    public void saveAutSaAudBusinessData(String autCode, String businessCode, String data) {
        // 保存自评审核业务数据
        AutSaAudBusinessData autSaAudBusinessData = new AutSaAudBusinessData();
        autSaAudBusinessData.setAutCode(autCode);
        autSaAudBusinessData.setBusinessCode(businessCode);
        autSaAudBusinessData.setData(data);
        autSaAudBusinessData.setUpdateTime(DateUtils.getNowDate());
        List<AutSaAudBusinessData> oldAutSaAudBusinessDatas = autSaAudBusinessDataService.selectAutSaAudBusinessData(autCode, businessCode);
        log.info("保存/更新自评审核业务数据 autCode:{}, businessCode：{},data:{} ", autCode, businessCode, data);
        int count = 0;
        if (CollectionUtils.isEmpty(oldAutSaAudBusinessDatas)) {
            // 保存自评审核业务数据
            count = autSaAudBusinessDataService.saveAutSaAudBusinessData(autSaAudBusinessData);
        } else {
            count = autSaAudBusinessDataService.updateAutSaAudBusinessData(autSaAudBusinessData);
        }
        log.info("保存/更新自评审核业务数据 结果：{}", count);
    }

    @Override
    public void delAutSaAudBusinessData(String autCode, String businessCode) {
        autSaAudBusinessDataService.deleteAutSaAudBusinessData(autCode, businessCode);
    }

    /**
     * 校验页面展示类型
     * 审查员 + 审查员组长  初查 复查  查看评审报告
     * 评审员 + 评审员组长    初查 复查  查看待修改条款
     * 验证评审员    查看评审报告
     *
     * @param req 流程参数
     */
    @Override
    public void checkPageType(AutSaAudQueryDTO req) {
        // 校验角色和页面类型  与前端沟通定义
        AutSaAudBusinessConfig pageTypeConfig = commonProcessMapper.selectAutSaAudBusinessConfig(AutSaAudBusConfItemTypeEnum.PAGE_TYPE_TO_ROLE_STATUS.getItemType(), req.getPageType());
        if (pageTypeConfig == null || StringUtils.isBlank(pageTypeConfig.getItemArr1())) {
            log.error("入参账户：{} 页面展示类型：{} 未配置或权限字符串配置为空 ", req.getAccountId(), req.getPageType());
            throw new ServiceException(ServiceExceptionEnum.AUT_SA_AUD_ERROR_1000006);
        }
        // 获取用户角色权限字符串
        String roleKey = sysUserService.getSysUserInfo(req.getAccountId()).getRoleKey();
        if (!Arrays.asList(Convert.toStrArray(pageTypeConfig.getItemArr1())).contains(roleKey)) {
            // 入参角色和类型 无权限查询
            log.info("入参账户：{} 页面展示类型：{} 配置的权限字符串不包含当前用户角色，无权限查询列表信息", req.getAccountId(), req.getPageType());
            throw new ServiceException(ServiceExceptionEnum.AUT_SA_AUD_ERROR_1000006);
        }
        req.setRoleKey(roleKey);
        req.setShowStatus(pageTypeConfig.getItemArr2());
    }

    /**
     * 解析医疗机构计划分配详情获取组员信息
     *
     * @param hosReviewPlanVO 分配信息
     * @param roleKey         角色权限字符串
     * @return 分配详情信息
     */
    @Override
    public List<HosPlanUserInfoVO> getHosPlanMemberList(HosReviewPlanVO hosReviewPlanVO, String roleKey) {
        if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(roleKey, AutSaAudRoleEnum.INSPECTOR)) {
            // 审查员 + 审查员组长
            return hosReviewPlanVO.getPreExamList();
        } else if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(roleKey, AutSaAudRoleEnum.SENIOR_ASSESSOR)) {
            // 验证评审员
            return hosReviewPlanVO.getSeniorReviewerUser();
        } else {
            // 评审员 + 评审员组长
            return hosReviewPlanVO.getReviewerList();
        }
    }

    /**
     * 校验是否是组长
     *
     * @param hosPlanUserInfoVO 用户分配的信息
     * @return 是否为组长
     */
    @Override
    public boolean checkIsLeader(HosPlanUserInfoVO hosPlanUserInfoVO) {
        return hosPlanUserInfoVO != null && hosPlanUserInfoVO.getLeaderIs() != null && hosPlanUserInfoVO.getLeaderIs().intValue() == Constants.HospitalConstants.NUM_1;
    }

    /**
     * 获取小组管理信息
     *
     * @param autSaAudList 审核信息
     * @param req          流程数据
     */
    @Override
    public void getGroupProgressList(AutSaAudList autSaAudList, AutSaAudQueryDTO req) {
        // 获取查询用提交类型
        String submitType = this.getQuerySubmitType(req, true);
        AutSaRelation autSaRelation = req.getAutSaRelation();
        List<HosPlanUserInfoVO> memberList = req.getMemberList();
        // 是否复查页面
        boolean isVPage = AutSaAudPageTypeEnum.checkIsAutSaAudPageTypeEnum(req.getPageType(), AutSaAudPageTypeEnum.FR_V_CLAUSE_PAGE, AutSaAudPageTypeEnum.SR_V_CLAUSE_PAGE);
        // 初查条数          待初审条数 =  获取到分配给审核员对应的条款总数 - 已初查条数
        List<AutSaAud> firstAudList = Lists.newArrayList();
        // 复查条数          待组长复查条数=  获取到分配给审核员对应的条款总数 - 已初审复查条数
        List<AutSaAud> secondAudList = Lists.newArrayList();
        // 事实准确性审查 数据
        List<AutSaAud> reportAudList = Lists.newArrayList();
        // 验证评审员评审 数据
        List<AutSaAud> seniorReviewerAudList = Lists.newArrayList();
        // 评审员修改审查 数据
        List<String> reviewerModificationReviewlist = Lists.newArrayList();
        // 评审员修改验证评审员评审 数据
        List<String> reviewerModificationSeniorReviewerReviewAudList = Lists.newArrayList();
        // 拒绝修改 数据
        List<String> clauseMSkipList = Lists.newArrayList();

        // 组长的待复查条数，需要用版本对应的总条数 - 已复查条数
        int totalCount = MapUtils.getInteger(cstCertificationStandardsService.selectCountByVersionId(autSaRelation.getAutCsId()), "clauseCount");
        // 查询出当前自评编码对应的自评审核信息
        List<AutSaAud> autSaAuds = autSaAudMapper.selectAutSaAudListByAutCodeAndTypes(autSaRelation.getAutCode(), submitType);

        //角色为审查员，查询是否有【确认初审结果】数据
        Boolean frClauseConfirmFlag = queryFrClauseConfirmFlag(req);

        if (CollectionUtils.isNotEmpty(autSaAuds)) {
            autSaAuds.stream().forEach(a -> {
                if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(req.getRoleKey(), AutSaAudRoleEnum.SENIOR_ASSESSOR) &&
                        AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType(), AutSaAudSubmitTypeEnum.TR_CLAUSE)) {
                    // 验证评审员 审查信息
                    firstAudList.add(a);
                } else if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(req.getRoleKey(), AutSaAudRoleEnum.INSPECTOR)) {
                    // 审查员 审查信息
                    if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType(), AutSaAudSubmitTypeEnum.FR_CLAUSE)) {
                        firstAudList.add(a);
                    }
                } else {
                    // 评审员 审查信息
                    if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType(), AutSaAudSubmitTypeEnum.SR_CLAUSE)) {
                        firstAudList.add(a);
                    } else if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType(), AutSaAudSubmitTypeEnum.SR_V_CLAUSE)) {
                        secondAudList.add(a);
                    } else if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE, AutSaAudSubmitTypeEnum.FAR_CLAUSE_RJ_E_M)) {
                        reportAudList.add(a);
                    } else if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType(), AutSaAudSubmitTypeEnum.TR_CLAUSE)) {
                        seniorReviewerAudList.add(a);
                    } else if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_M)) {
                        reviewerModificationReviewlist.add(a.getClauseId());
                    } else if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType(), AutSaAudSubmitTypeEnum.TR_CLAUSE_M)) {
                        reviewerModificationSeniorReviewerReviewAudList.add(a.getClauseId());
                    } else if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SKIP, AutSaAudSubmitTypeEnum.TR_CLAUSE_M_SKIP)) {
                        clauseMSkipList.add(a.getClauseId());
                    }
                }
            });
        }

        List<GroupProgressInfo> groupProgressList = Lists.newArrayList();
        List<String> memberNameList = Lists.newArrayList();
        HosPlanUserInfoVO userHosPlanUserInfoVO = null;
        boolean isLeader = false;
        boolean isMoveClause = false;
        String leaderUserId = null;
        for (HosPlanUserInfoVO hosPlanUserInfoVO : memberList) {
            if (this.checkIsLeader(hosPlanUserInfoVO)) {
                memberNameList.add(hosPlanUserInfoVO.getName() + "(组长)");
                // 组长名称
                autSaAudList.setLeaderName(hosPlanUserInfoVO.getName());
                leaderUserId = hosPlanUserInfoVO.getAccountId();
            } else {
                memberNameList.add(hosPlanUserInfoVO.getName());
            }

            if (StringUtils.isNotBlank(hosPlanUserInfoVO.getAccountId()) && hosPlanUserInfoVO.getAccountId().equals(req.getAccountId())) {
                userHosPlanUserInfoVO = hosPlanUserInfoVO;
                //  是否为组长
                isLeader = this.checkIsLeader(hosPlanUserInfoVO);
                // 已分配条款id列表   组长复查列表和验证评审员  查询时返回空列表（实际是所有条款都可处理）  如果没有分配信息返回空列表
                if ((isLeader && isVPage) ||
                        AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(req.getRoleKey(), AutSaAudRoleEnum.SENIOR_ASSESSOR) ||
                        CollectionUtils.isEmpty(hosPlanUserInfoVO.getCstCertificationStandardVOList())
                ) {
                    List<CstCertificationStandards> cstCertificationStandards = cstCertificationStandardsMapper.selectAllClauseIdByVersionId(req.getAutSaRelation().getAutCsId());
                    List<String> distributeClauseIdList = cstCertificationStandards.stream().map(o -> o.getClauseId().toString()).collect(Collectors.toList());

                    autSaAudList.setDistributeClauseIdList(distributeClauseIdList);
                } else {
                    // 当只获取个人款则加入挪动逻辑
                    isMoveClause = true;
                    autSaAudList.setDistributeClauseIdList(hosPlanUserInfoVO.getCstCertificationStandardVOList().stream().filter(b -> b.getClauseId() != null).map(aa -> aa.getClauseId().toString()).collect(Collectors.toList()));
                }
            }
            //封装拒绝或驳回款Id
            this.packRejectIds(req, autSaAuds, autSaAudList);
            // 审核员 + 评审员 小组进度信息
            GroupProgressInfo groupProgressInfo = this.getGroupProgressInfo(autSaAudList, req, firstAudList, secondAudList, hosPlanUserInfoVO, totalCount, frClauseConfirmFlag);
            //当有不适用款，对应待审核款数量需要排除（当前款是否属于当前评审用户，属于排除）
            this.checkRemoveReviewFitMoveClause(req, hosPlanUserInfoVO, groupProgressInfo, autSaAudList, totalCount);

            // 款项挪动
            clauseMove(autSaAudList, isMoveClause, req);

            groupProgressList.add(groupProgressInfo);
        }
        autSaAudList.setIsLeader(isLeader ? Constants.HospitalConstants.STR_NUM_1 : Constants.HospitalConstants.STR_NUM_2);
        // 组长返回组员信息
        if (isLeader) {
            // 组员信息
            autSaAudList.setMemberName(String.join(",", memberNameList));
        }
        // 小组进度
        if (CollectionUtils.isNotEmpty(groupProgressList)) {
            autSaAudList.setGroupProgressList(groupProgressList);
        }

        if (AutSaAudPageTypeEnum.checkIsAutSaAudPageTypeEnum(req.getPageType(), AutSaAudPageTypeEnum.FAR_CLAUSE_W_M_PAGE)) {
            // 待修改评审列表页      需要返回 事实准确性审查修改下项 或者 验证评审员修改项
            if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autSaRelation.getAutStatus(), AutSaAudStatusEnum.FAR_CLAUSE_W_M, AutSaAudStatusEnum.FAR_CLAUSE_M, AutSaAudStatusEnum.FAR_V_CLAUSE_M)) {
                // 解析待修改信息
                this.parseWaitModifyInfo(autSaAudList, reportAudList, reviewerModificationReviewlist, userHosPlanUserInfoVO, memberList, clauseMSkipList);
            } else if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autSaRelation.getAutStatus(), AutSaAudStatusEnum.TR_CLAUSE_W_M, AutSaAudStatusEnum.TR_CLAUSE_M, AutSaAudStatusEnum.TR_V_CLAUSE_M)) {
                // 解析待修改信息
                this.parseWaitModifyInfo(autSaAudList, seniorReviewerAudList, reviewerModificationSeniorReviewerReviewAudList, userHosPlanUserInfoVO, memberList, clauseMSkipList);
            }
        }
        // 设置自评总结提交相关数据
        this.setSaAudSubmitTimeInfo(autSaAudList, autSaAuds);

        //封装文件信息
        this.packFileDetailMap(leaderUserId, autSaRelation, autSaAudList, autSaAuds);
    }

    private void packFileDetailMap(String leaderUserId, AutSaRelation autSaRelation, AutSaAudList autSaAudList, List<AutSaAud> autSaAuds) {
        //统一查询业务数据表
        List<String> businessCodeList = new ArrayList<>(Arrays.asList(AutSaAudBusinessCodeEnum.AUT_SA_AUD_REPORT.getCode(),
                AutSaAudBusinessCodeEnum.REVIEW_REPORT.getCode(), AutSaAudBusinessCodeEnum.AUD_SA_REPORT.getCode()));

        //(谁可以下载验证评审报告：中心管理员、审查组长、评审组长,加验证评审员)
        SysUser sysUser = SecurityUtils.getSysUser();
        if ((ObjectUtil.equal(leaderUserId, String.valueOf(sysUser.getUserId())) && AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(sysUser.getRoleKey(), AutSaAudRoleEnum.ASSESSOR, AutSaAudRoleEnum.INSPECTOR)) ||
                AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(sysUser.getRoleKey(), AutSaAudRoleEnum.ADMIN, AutSaAudRoleEnum.COMMON_ADMIN, AutSaAudRoleEnum.SENIOR_ASSESSOR)) {
            businessCodeList.add(AutSaAudBusinessCodeEnum.VERIFY_REVIEW_REPORT.getCode());
        }
        if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(sysUser.getRoleKey(), AutSaAudRoleEnum.INSPECTOR)) {
            businessCodeList.add(AutSaAudBusinessCodeEnum.MEET_REPORT.getCode());
        }

        List<AutSaAudBusinessData> autSaAudBusinessData = autSaAudBusinessDataService.selectAutSaAudBusinessData(autSaRelation.getAutCode(), String.join(",", businessCodeList));
        Map<String, AutSaAudBusinessData> businessDataMap = autSaAudBusinessData.stream().collect(Collectors.toMap(AutSaAudBusinessData::getBusinessCode, o -> o));

        // 评审结果
        AutSaAudBusinessData autSaAudReportBusinessData = businessDataMap.get(AutSaAudBusinessCodeEnum.AUT_SA_AUD_REPORT.getCode());
        log.info("查询到评审报告结果信息：{}", JSON.toJSONString(autSaAudReportBusinessData));
        if (ObjectUtil.isNotEmpty(autSaAudReportBusinessData) && StringUtils.isNotBlank(autSaAudReportBusinessData.getData())) {
            autSaAudList.setAutSaAudReport(JSON.parseObject(autSaAudReportBusinessData.getData(), AutSaAudReport.class));
        }

        // 评审报告生成时间
        AutSaAudBusinessData reviewReportBusinessData = businessDataMap.get(AutSaAudBusinessCodeEnum.REVIEW_REPORT.getCode());
        log.info("查询到评审报告信息：{}", JSON.toJSONString(reviewReportBusinessData));
        if (ObjectUtil.isNotEmpty(reviewReportBusinessData) && StringUtils.isNotBlank(reviewReportBusinessData.getData())) {
            autSaAudList.setReportSubmitTime(reviewReportBusinessData.getUpdateTime());
            Map<String, String> dataMap = JSON.parseObject(reviewReportBusinessData.getData(), Map.class);
            String filePath = MapUtils.getString(dataMap, "url");
            String fileId = MapUtils.getString(dataMap, "fileId");
            int index = filePath.indexOf("upload");
            String url = resourceUrl + filePath.substring(index + 6);
            List<FileInfoDTO> uploadFileInfoByIds = uploadFileInfoService.getUploadFileInfoByIds(fileId);
            autSaAudList.setReviewReportPdfUrl(url);
            autSaAudList.setReviewReportPdfFileId(fileId);
            autSaAudList.setReviewReportPdfFileName(uploadFileInfoByIds.stream().map(FileInfoDTO::getOrigin).collect(Collectors.joining()));
        }
        Map<String, List<FileInfoVO>> fileDetailMap = new HashMap<>();

        //封装自评报告终稿文件数据
        AutSaAudBusinessData audSaReportBusinessData = businessDataMap.get(AutSaAudBusinessCodeEnum.AUD_SA_REPORT.getCode());
        log.info("查询自评报告终稿信息：{}", JSON.toJSONString(audSaReportBusinessData));
        if (ObjectUtil.isNotEmpty(audSaReportBusinessData) && StringUtils.isNotBlank(audSaReportBusinessData.getData())) {
            FileInfoVO fileInfoVO = JSON.parseObject(audSaReportBusinessData.getData(), FileInfoVO.class);
            fileInfoVO.setFileType(AutSaAudBusinessCodeEnum.AUD_SA_REPORT.getCode());
            List<FileInfoVO> fileInfoVOS = commonService.fileInfoDtoToVo(uploadFileInfoService.getUploadFileInfoByIds(fileInfoVO.getFileId()));
            fileInfoVOS.forEach(o -> {
                o.setFileType(AutSaAudBusinessCodeEnum.AUD_SA_REPORT.getCode());
            });
            fileDetailMap.put(fileInfoVO.getFileId(), fileInfoVOS);
        }

        //审查组长获取事实准确性查询表文件
        String fileIds = autSaAuds.stream().filter(o -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(o.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SUBMIT) &&
                StringUtils.isNotBlank(o.getFileIds())).map(AutSaAud::getFileIds).collect(Collectors.joining(","));
        // 获取所有的文件列表信息
        //AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SUBMIT提交类型对应far_report_reviewer事实准确性报告
        if (StringUtils.isNotBlank(fileIds)) {
            List<FileInfoVO> fileInfoVOList = commonService.fileInfoDtoToVo(uploadFileInfoService.getUploadFileInfoByIds(fileIds));
            fileDetailMap.putAll(
                    fileInfoVOList.stream().map(o -> {
                        o.setFileType(AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SUBMIT.getSubmitType());
                        return o;
                    }).collect(Collectors.groupingBy(FileInfoVO::getFileId))
            );
        }
        //封装验证评审报告文件数据
        AutSaAudBusinessData verifyReviewReportBusinessData = businessDataMap.get(AutSaAudBusinessCodeEnum.VERIFY_REVIEW_REPORT.getCode());
        log.info("查询验证评审报告信息：{}", JSON.toJSONString(verifyReviewReportBusinessData));
        if (ObjectUtil.isNotEmpty(verifyReviewReportBusinessData) && StringUtils.isNotBlank(verifyReviewReportBusinessData.getData())) {
            FileInfoVO fileInfoVO = JSON.parseObject(verifyReviewReportBusinessData.getData(), FileInfoVO.class);
            List<FileInfoVO> fileInfoVOList = commonService.fileInfoDtoToVo(uploadFileInfoService.getUploadFileInfoByIds(fileInfoVO.getFileId()));
            fileInfoVOList.stream().forEach(o -> {
                o.setFileType(AutSaAudBusinessCodeEnum.VERIFY_REVIEW_REPORT.getCode());
            });
            fileDetailMap.put(fileInfoVO.getFileId(), fileInfoVOList);
        }
        //上会纪要
        AutSaAudBusinessData meetReportBusinessData = businessDataMap.get(AutSaAudBusinessCodeEnum.MEET_REPORT.getCode());
        if (ObjectUtil.isNotEmpty(meetReportBusinessData) && StringUtils.isNotBlank(meetReportBusinessData.getData())) {
            String fileId = meetReportBusinessData.getData();
            List<FileInfoDTO> fileInfoDTOList = iUploadFileInfoService.getUploadFileInfoByIds(fileId);
            List<FileInfoVO> fileInfoVOList = commonService.fileInfoDtoToVo(fileInfoDTOList);
            fileInfoVOList.get(0).setFileType(AutSaAudBusinessCodeEnum.MEET_REPORT.getCode());
            fileDetailMap.putAll(fileInfoVOList.stream().collect(Collectors.groupingBy(FileInfoVO::getFileId)));
        }

        // 认证审核信息文件列表
        autSaAudList.setFileDetailMap(fileDetailMap);
    }

    /**
     * @param req          请求参数
     * @param autSaAuds    对应节点查询提交类型数据
     * @param autSaAudList 返回对象
     */
    private void packRejectIds(AutSaAudQueryDTO req, List<AutSaAud> autSaAuds, AutSaAudList autSaAudList) {
        // 如果节点为驳回节点且驳回状态记录为【确认初审结果】，对应返回驳回信息
        List<AutSaAudBusinessData> rejectBusinessDatas = autSaAudBusinessDataService.selectAutSaAudBusinessData(
                req.getAutSaRelation().getAutCode(), AutSaAudBusinessCodeEnum.AUT_SA_AUD_REJECT.getCode());
        List<AutSaAudBusinessData> rejectStatus = autSaAudBusinessDataService.selectAutSaAudBusinessData(
                req.getAutSaRelation().getAutCode(), AutSaAudBusinessCodeEnum.AUT_SA_AUD_REJECT_STATUS.getCode());
        if (CollectionUtils.isNotEmpty(rejectBusinessDatas) && StringUtils.isNotBlank(rejectBusinessDatas.get(0).getData()) &&
                (
                        (CollectionUtils.isNotEmpty(rejectStatus) && StringUtils.isNotBlank(rejectStatus.get(0).getData()) &&
                                AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(rejectStatus.get(0).getData(), AutSaAudStatusEnum.FR_CLAUSE_CONFIRM)) ||
                                AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.FAR_CLAUSE_PROCESS, AutSaAudStatusEnum.FAR_CLAUSE_M,
                                        AutSaAudStatusEnum.FAR_V_CLAUSE_M, AutSaAudStatusEnum.TR_CLAUSE_PROCESS)
                )
        ) {
            log.info("查询到驳回信息：rejectBusinessDatas:{},rejectStatus:{}", JSON.toJSONString(rejectBusinessDatas), JSON.toJSONString(rejectStatus));
            log.info("自评编码为：{}，当前节点返回驳回数据：{}", req.getAutSaRelation().getAutCode(), req.getAutSaRelation().getAutStatus());
            List<AutSaAud> rejectInfo = JSON.parseArray(rejectBusinessDatas.get(0).getData(), AutSaAud.class);
            autSaAudList.setRejectIds(rejectInfo.stream().filter(a ->
                    StringUtils.isNotBlank(a.getClauseId())).map(AutSaAud::getClauseId).distinct().collect(Collectors.toList()));
        }
        //列表接口 在020401节点 封装返回驳回款id数据
        if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.SR_REPORT_M)) {
            AutSaAud autSaAud = new AutSaAud();
            autSaAud.setAutCode(req.getAutSaRelation().getAutCode());
            autSaAud.setSubmitType(AutSaAudSubmitTypeEnum.SR_REPORT_M.getSubmitType());
            autSaAud.setStatus(Constants.INT_ZERO);
            List<AutSaAud> rjAutSaAudList = autSaAudMapper.selectAutSaAudList(autSaAud);
            autSaAudList.setRejectIds(rjAutSaAudList.stream().map(AutSaAud::getClauseId).distinct().collect(Collectors.toList()));
        }
        //评审中，封装共享修改款数据
        if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.SR_CLAUSE_PROCESS)) {
            //获取sr_*类型数据且需共享修改且是当前操作人对应的数据
            List<AutSaAud> updateAutSaAudList = autSaAuds.stream().filter(o -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(o.getSubmitType(),
                    AutSaAudSubmitTypeEnum.SR_CLAUSE) && ObjectUtil.equal(o.getIsShareUpdate(), Constants.INT_ONE) &&
                    ObjectUtil.equal(o.getAccountId(), req.getAccountId())).collect(Collectors.toList());
            autSaAudList.setRejectIds(updateAutSaAudList.stream().map(AutSaAud::getClauseId).collect(Collectors.toList()));
        } else if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.TR_CLAUSE_PROCESS)) {
            //当前节点为040102时，查询审查驳回款封装
            List<String> rjClauseIds = autSaAuds.stream().filter(o -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(o.getSubmitType(), AutSaAudSubmitTypeEnum.TR_CLAUSE_RJ_E))
                    .map(AutSaAud::getClauseId).collect(Collectors.toList());
            autSaAudList.setRejectIds(rjClauseIds);
        } else if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.FAR_SUMMARY_CONFIRM,
                AutSaAudStatusEnum.TR_SUMMARY_CONFIRM, AutSaAudStatusEnum.FAR_SUMMARY_CONFIRM_M)) {
            //为020301,0203011或020302节点时，查询业务数据表获取驳回提交类型数据返回，用于前端过滤只回显当前驳回款
            List<AutSaAudBusinessData> examinerRjDataList = autSaAudBusinessDataService.selectAutSaAudBusinessData(
                    req.getAutSaRelation().getAutCode(), AutSaAudBusinessCodeEnum.EXAMINER_RJ.getCode());
            if (CollectionUtils.isNotEmpty(examinerRjDataList)) {
                List<AutSaAud> rejectInfo = JSON.parseArray(examinerRjDataList.get(0).getData(), AutSaAud.class);
                autSaAudList.setRejectIds(rejectInfo.stream().filter(a ->
                        StringUtils.isNotBlank(a.getClauseId())).map(AutSaAud::getClauseId).collect(Collectors.toList()));
            }
        }

    }

    private void clauseMove(AutSaAudList autSaAudList, boolean isMoveClause, AutSaAudQueryDTO req) {
        if (!isMoveClause) {
            return;
        }

        if (CollectionUtils.isEmpty(autSaAudList.getDistributeClauseIdList())) {
            return;
        }

        // 查询当前评审员所属的需要评审的组
        HospitalReviewer hospitalReviewer = hospitalReviewerMapper.selectHospitalReviewerByApplyNoAndAccountId(req.getAutSaRelation().getHospitalApplyNo(), req.getAccountId());

        // 如果查询为空或者为验证评审员或者评审学员不处理
        if (hospitalReviewer == null
                || StringUtils.isEmpty(hospitalReviewer.getFieldIdList())
                || hospitalReviewer.getFieldIdList().equals(HospitalConstants.SENIOR_REVIEW)
                || hospitalReviewer.getFieldIdList().equals(HospitalConstants.TRAINEES_REVIEW)) {
            return;
        }

        // 当前评审员下的组
        Set<String> curGroupIdSet = new HashSet<>(Arrays.asList(hospitalReviewer.getFieldIdList().split(",")));

        // 查询挪动记录
        ReviewFitMoveClause selectParam = new ReviewFitMoveClause();
        selectParam.setAutCode(req.getAutSaRelation().getAutCode());
        selectParam.setMoveStatus(HospitalConstants.LONG_NUM_1);
        List<ReviewFitMoveClause> moveReviewFitMoveClauseList = reviewFitMoveClauseService.selectReviewFitMoveClauseList(selectParam);

        // 挪动clauseId
        Set<String> moveClauseIdSet = moveReviewFitMoveClauseList.stream()
                .map(ReviewFitMoveClause::getClauseId)
                .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(moveClauseIdSet)) {
            return;
        }

        // 查询不适用记录
        selectParam.setMoveStatus(null);
        selectParam.setFitStatus(HospitalConstants.LONG_NUM_1);
        List<ReviewFitMoveClause> notApplicableReviewFitMoveClauseList = reviewFitMoveClauseService.selectReviewFitMoveClauseList(selectParam);

        // 不适用clauseId
        Set<String> notApplicableClauseIdSet = notApplicableReviewFitMoveClauseList.stream()
                .map(ReviewFitMoveClause::getClauseId)
                .collect(Collectors.toSet());

        // 挪动groupId -> clauseIds 映射
        Map<String, Set<String>> groupIdMap = moveReviewFitMoveClauseList.stream()
                .collect(
                        Collectors.groupingBy(
                                ReviewFitMoveClause::getGroupId, Collectors.mapping(ReviewFitMoveClause::getClauseId, Collectors.toSet())
                        )
                );

        Set<String> distributeClauseIdSet = new HashSet<>(autSaAudList.getDistributeClauseIdList());

        // 减少款项
        distributeClauseIdSet.removeAll(moveClauseIdSet);

        // 增加款项
        for (String groupId : curGroupIdSet) {
            Set<String> addClauseIdSet = groupIdMap.get(groupId);
            if (CollectionUtils.isEmpty(addClauseIdSet)) {
                continue;
            }

            for (String clauseId : addClauseIdSet) {
                if (notApplicableClauseIdSet.contains(clauseId)) {
                    continue;
                }
                distributeClauseIdSet.add(clauseId);
            }
        }

        List<String> distributeClauseIdList = autSaAudList.getDistributeClauseIdList();
        distributeClauseIdList.clear();
        distributeClauseIdList.addAll(distributeClauseIdSet);
    }

    private void clauseMoveTraineesAssessor(AutSaAudQueryDTO req, List<String> distributeClauseIds) {
        log.info("挪动开始===>>自评编码：{},学员id：{},分组评审员id：{},挪动的款Ids数据为：{}", req.getAutSaRelation().getAutCode(), req.getAccountId(), req.getDomainAccountId(), distributeClauseIds);

        // 查询当前评审员所属的需要评审的组
        HospitalReviewer hospitalReviewer = hospitalReviewerMapper.selectHospitalReviewerByApplyNoAndAccountId(req.getAutSaRelation().getHospitalApplyNo(), req.getDomainAccountId());

        // 如果查询为空或者为验证评审员或者评审学员不处理
        if (hospitalReviewer == null
                || StringUtils.isEmpty(hospitalReviewer.getFieldIdList())
                || hospitalReviewer.getFieldIdList().equals(HospitalConstants.SENIOR_REVIEW)
                || hospitalReviewer.getFieldIdList().equals(HospitalConstants.TRAINEES_REVIEW)) {
            return;
        }

        // 当前评审员下的组
        Set<String> curGroupIdSet = new HashSet<>(Arrays.asList(hospitalReviewer.getFieldIdList().split(",")));

        // 查询挪动记录
        ReviewFitMoveClause selectParam = new ReviewFitMoveClause();
        selectParam.setAutCode(req.getAutSaRelation().getAutCode());
        selectParam.setMoveStatus(HospitalConstants.LONG_NUM_1);
        List<ReviewFitMoveClause> moveReviewFitMoveClauseList = reviewFitMoveClauseService.selectReviewFitMoveClauseList(selectParam);

        // 挪动clauseId
        Set<String> moveClauseIdSet = moveReviewFitMoveClauseList.stream()
                .map(ReviewFitMoveClause::getClauseId)
                .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(moveClauseIdSet)) {
            return;
        }

        // 查询不适用记录
        selectParam.setMoveStatus(null);
        selectParam.setFitStatus(HospitalConstants.LONG_NUM_1);
        List<ReviewFitMoveClause> notApplicableReviewFitMoveClauseList = reviewFitMoveClauseService.selectReviewFitMoveClauseList(selectParam);

        // 不适用clauseId
        Set<String> notApplicableClauseIdSet = notApplicableReviewFitMoveClauseList.stream()
                .map(ReviewFitMoveClause::getClauseId)
                .collect(Collectors.toSet());

        // 挪动groupId -> clauseIds 映射
        Map<String, Set<String>> groupIdMap = moveReviewFitMoveClauseList.stream()
                .collect(
                        Collectors.groupingBy(
                                ReviewFitMoveClause::getGroupId, Collectors.mapping(ReviewFitMoveClause::getClauseId, Collectors.toSet())
                        )
                );

        // 减少款项
        distributeClauseIds.removeAll(moveClauseIdSet);

        // 增加款项
        for (String groupId : curGroupIdSet) {
            Set<String> addClauseIdSet = groupIdMap.get(groupId);
            if (CollectionUtils.isEmpty(addClauseIdSet)) {
                continue;
            }

            for (String clauseId : addClauseIdSet) {
                if (notApplicableClauseIdSet.contains(clauseId)) {
                    continue;
                }
                distributeClauseIds.add(clauseId);
            }
        }

        log.info("挪动结束===>>自评编码：{},学员id：{},分组评审员id：{},挪动的款Ids数据变化为：{}", req.getAutSaRelation().getAutCode(), req.getAccountId(), req.getDomainAccountId(), distributeClauseIds);
    }

    private Boolean queryFrClauseConfirmFlag(AutSaAudQueryDTO req) {
        AtomicReference<Boolean> frClauseConfirmFlag = new AtomicReference<>(false);
        AutSaAud autSaAud = new AutSaAud();
        autSaAud.setAutCode(req.getAutSaRelation().getAutCode());
        autSaAud.setSubmitType(AutSaAudSubmitTypeEnum.FR_CLAUSE_CONFIRM.getSubmitType());
        List<AutSaAud> frClauseConfirmList = autSaAudMapper.selectAutSaAudList(autSaAud);
        if (CollectionUtils.isNotEmpty(frClauseConfirmList) && AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(req.getRoleKey(), AutSaAudRoleEnum.INSPECTOR)) {
            frClauseConfirmFlag.set(true);
        }
        return frClauseConfirmFlag.get();
    }

    /**
     * 获取小组进度信息
     *
     * @param autSaAudList        审核数据
     * @param req                 流程参数
     * @param firstAudList        初查数据
     * @param secondAudList       复查数据
     * @param hosPlanUserInfoVO   申请单小组分配用户信息
     * @param totalCount          总条款数
     * @param frClauseConfirmFlag 是否存在{确认初审结果}数据
     * @return 小组进度信息
     */
    private GroupProgressInfo getGroupProgressInfo(AutSaAudList autSaAudList, AutSaAudQueryDTO req, List<AutSaAud> firstAudList,
                                                   List<AutSaAud> secondAudList, HosPlanUserInfoVO hosPlanUserInfoVO, int totalCount, Boolean frClauseConfirmFlag) {
        GroupProgressInfo groupProgressInfo = new GroupProgressInfo();
        // 用户名称
        groupProgressInfo.setName(hosPlanUserInfoVO.getName());
        // 是否为组长 是否为组长 1是 2不是
        groupProgressInfo.setLeaderIs(hosPlanUserInfoVO.getLeaderIs());
        // 条款id列表
        int distributeCount = 0;
        int audNum = firstAudList.size();
        int audSecondNum = secondAudList.size();
        // 是否复查页面
        boolean isVPage = AutSaAudPageTypeEnum.checkIsAutSaAudPageTypeEnum(req.getPageType(), AutSaAudPageTypeEnum.FR_V_CLAUSE_PAGE, AutSaAudPageTypeEnum.SR_V_CLAUSE_PAGE);
        if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(req.getRoleKey(), AutSaAudRoleEnum.SENIOR_ASSESSOR)) {
            // 资深评审员
            distributeCount = totalCount;
        } else {
            // 评审员 + 审查员（各人员负责的对应款）
            Set<String> cstCertificationStandardSet = hosPlanUserInfoVO.getCstCertificationStandardVOList().stream().filter(a -> a.getClauseId() != null).map(a -> a.getClauseId().toString()).collect(Collectors.toSet());

            // 当角色是评审员的时候，加入挪动逻辑
            if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(req.getRoleKey(), AutSaAudRoleEnum.ASSESSOR)) {
                cstCertificationStandardSet = curReviewerClauseIdMove(req, hosPlanUserInfoVO);
                hosPlanUserInfoVO.setMoveClauseIdSet(cstCertificationStandardSet);
            }
            List<String> cstCertificationStandardList = new ArrayList<>(cstCertificationStandardSet);

            distributeCount = cstCertificationStandardList.size();
            // 用户已初查条数          待初审条数 =  获取到分配给审核员对应的条款总数 - 已初查条数
            audNum = firstAudList.stream().filter(a -> cstCertificationStandardList.contains(a.getClauseId())).collect(Collectors.toList()).size();
            if (isVPage) {
                // 用户已初审复查条数   待组长复查条数=  获取到分配给审核员对应的条款总数 - 已初审复查条数
                audSecondNum = secondAudList.size();
            } else {
                audSecondNum = secondAudList.stream().filter(a -> cstCertificationStandardList.contains(a.getClauseId())).collect(Collectors.toList()).size();
            }

            //审查驳回/待医院修改条数
            //获取当前审查员分配的条款，匹配驳回款是否属于此用户
            //如果属于待审查数量去掉，封装到驳回条款数字段
            AtomicReference<Integer> pendingHospitalUpdateCount = new AtomicReference<>(0);
            List<String> rejectIds = autSaAudList.getRejectIds();
            //等于这六个节点且有拒绝款且对应用户有款数据
            if (CollectionUtils.isNotEmpty(rejectIds) && CollectionUtils.isNotEmpty(cstCertificationStandardList) &&
                    AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.WAIT_FR_CLAUSE,
                            AutSaAudStatusEnum.FR_CLAUSE_PROC, AutSaAudStatusEnum.FR_SUMMARY_REJECT, AutSaAudStatusEnum.SA_CLAUSE_CONFIRM,
                            AutSaAudStatusEnum.SA_REPORT_DESC, AutSaAudStatusEnum.SA_SUMMARY)
            ) {
                rejectIds.forEach(o -> {
                    if (cstCertificationStandardList.contains(o)) {
                        pendingHospitalUpdateCount.getAndSet(pendingHospitalUpdateCount.get() + 1);
                    }
                });
            }
            groupProgressInfo.setPendingHospitalUpdateCount(pendingHospitalUpdateCount.get());
        }
        // 已审查
        groupProgressInfo.setCensoredCount(audNum);
        // 待审查
        groupProgressInfo.setPendingReviewCount(distributeCount - audNum - groupProgressInfo.getPendingHospitalUpdateCount());

        boolean isLeader = this.checkIsLeader(hosPlanUserInfoVO);
        if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(req.getRoleKey(), AutSaAudRoleEnum.INSPECTOR)) {
            //当提交类型有{确认初审结果}数据时，证明已审核过
            if (Boolean.TRUE.equals(frClauseConfirmFlag)) {
                //当初审结束翻转节点会删除驳回的数据，既这里驳回数量为0
                // 组长已审查(组员已审查-驳回数量)
                groupProgressInfo.setLeaderCensoredCount(audNum - groupProgressInfo.getPendingHospitalUpdateCount());
            }
            // 组长待审查（组员已审查-组长已审查 - 驳回款）
            groupProgressInfo.setLeaderPendingReviewCount(audNum - groupProgressInfo.getLeaderCensoredCount() - groupProgressInfo.getPendingHospitalUpdateCount());
            if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(req.getAutSaRelation().getAutStatus(), AutSaAudStatusEnum.FR_CLAUSE_CONFIRM)) {
                //当前节点为{020103-确认初审结果}，证明审查组长正在审查所有款
                groupProgressInfo.setLeaderPendingReviewCount(audNum);
            }
        } else {
            // 检验是否组长
            if (isLeader) {
                // 组长已审查
                groupProgressInfo.setLeaderCensoredCount(secondAudList.size());
                // 组长待审查
                groupProgressInfo.setLeaderPendingReviewCount(totalCount - secondAudList.size());
            }
        }

        if (req.getAccountId().equals(hosPlanUserInfoVO.getAccountId())) {
            // 待审核条数/待评审条数  待初查
            autSaAudList.setWaitAudNum(distributeCount - audNum);
            // 待组长复查条数  待组长复查
            autSaAudList.setWaitLeaderAudNum(distributeCount - audSecondNum);
            if (isLeader && isVPage) {
                // 页面是复查，仅展示作为组长的相关信息 如果该用户不是组长，不展示该条数据信息
                autSaAudList.setWaitLeaderAudNum(totalCount - audSecondNum);
            }
        }
        return groupProgressInfo;
    }

    /**
     * 接入挪动逻辑
     *
     * @param req               入参
     * @param hosPlanUserInfoVO 当前评审员信息
     */
    private Set<String> curReviewerClauseIdMove(AutSaAudQueryDTO req, HosPlanUserInfoVO hosPlanUserInfoVO) {
        return reviewFitMoveClauseService.moveClause(req.getAutSaRelation().getAutCode(), hosPlanUserInfoVO);
    }

    private void checkRemoveReviewFitMoveClause(AutSaAudQueryDTO req, HosPlanUserInfoVO hosPlanUserInfoVO, GroupProgressInfo groupProgressInfo, AutSaAudList autSaAudList, int totalCount) {
        //查询不适用款
        ReviewFitMoveClause reviewFitMoveClause = new ReviewFitMoveClause();
        reviewFitMoveClause.setAutCode(req.getAutSaRelation().getAutCode());
        reviewFitMoveClause.setFitStatus(Long.valueOf(Constants.STR_NUM_1));
        reviewFitMoveClause.setStatus(Long.valueOf(Constants.STR_NUM_1));
        List<ReviewFitMoveClause> reviewFitMoveClauseList = reviewFitMoveClauseMapper.selectReviewFitMoveClauseList(reviewFitMoveClause);

        if (CollectionUtils.isNotEmpty(reviewFitMoveClauseList)) {
            List<String> allFitClauseIds = reviewFitMoveClauseList.stream().map(ReviewFitMoveClause::getClauseId).collect(Collectors.toList());
            log.info("不适用款排除：当前自评编码：【{}】有不适用款【{}】", req.getAutSaRelation().getAutCode(), allFitClauseIds);
            // 是否复查页面
            boolean isVPage = AutSaAudPageTypeEnum.checkIsAutSaAudPageTypeEnum(req.getPageType(), AutSaAudPageTypeEnum.FR_V_CLAUSE_PAGE, AutSaAudPageTypeEnum.SR_V_CLAUSE_PAGE);
            // 评审员 + 审查员（各人员负责的对应款）
            List<String> cstCertificationStandardList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(hosPlanUserInfoVO.getMoveClauseIdSet())) {
                // cstCertificationStandardList只是为了拿当前评审员的款id集合，如果hosPlanUserInfoVO.getMoveClauseIdSet()有值，
                // 则使用hosPlanUserInfoVO.getMoveClauseIdSet()，hosPlanUserInfoVO.getMoveClauseIdSet()为添加挪动逻辑之后的款id集合
                cstCertificationStandardList.addAll(hosPlanUserInfoVO.getMoveClauseIdSet());
            } else if (CollectionUtils.isNotEmpty(hosPlanUserInfoVO.getCstCertificationStandardVOList())) {
                cstCertificationStandardList = hosPlanUserInfoVO.getCstCertificationStandardVOList().stream().
                        filter(a -> a.getClauseId() != null).map(a -> a.getClauseId().toString()).collect(Collectors.toList());
            }
            // 获取两者匹配款数量，证明此款属于此用户，然后排除;
            List<String> finalCstCertificationStandardList = cstCertificationStandardList;
            List<String> matchClauseIds = reviewFitMoveClauseList.stream().filter(o -> finalCstCertificationStandardList.contains(o.getClauseId()))
                    .map(ReviewFitMoveClause::getClauseId).collect(Collectors.toList());
            int matchSize = matchClauseIds.size();

            //评审员
            if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(req.getRoleKey(), AutSaAudRoleEnum.ASSESSOR)) {
                //已初查数量
                groupProgressInfo.setCensoredCount(groupProgressInfo.getCensoredCount() == 0 ? 0 : groupProgressInfo.getCensoredCount() - matchSize);
                //待初查数量
                groupProgressInfo.setPendingReviewCount(groupProgressInfo.getPendingReviewCount() == cstCertificationStandardList.size() ?
                        groupProgressInfo.getPendingReviewCount() - matchSize : groupProgressInfo.getPendingReviewCount());
                //组长
                boolean isLeader = ObjectUtil.equal(groupProgressInfo.getLeaderIs(), Constants.INT_ONE);
                if (isLeader) {
                    //组长已审查条数
                    groupProgressInfo.setLeaderCensoredCount(groupProgressInfo.getLeaderCensoredCount() == 0 ?
                            0 : groupProgressInfo.getLeaderCensoredCount() - reviewFitMoveClauseList.size());
                    //组长待审查条数
                    groupProgressInfo.setLeaderPendingReviewCount(groupProgressInfo.getLeaderPendingReviewCount() == totalCount ?
                            groupProgressInfo.getLeaderPendingReviewCount() - reviewFitMoveClauseList.size() : groupProgressInfo.getLeaderPendingReviewCount()
                    );
                }
                //当前登录用户对应自己负责的款
                if (req.getAccountId().equals(hosPlanUserInfoVO.getAccountId())) {
                    // 待审核条数/待评审条数  待初查
                    autSaAudList.setWaitAudNum(autSaAudList.getWaitAudNum() == cstCertificationStandardList.size() ?
                            autSaAudList.getWaitAudNum() - matchSize : autSaAudList.getWaitAudNum());

                    if (isLeader && isVPage) {
                        // 页面是复查，仅展示作为组长的相关信息 如果该用户不是组长，不展示该条数据信息
                        autSaAudList.setWaitLeaderAudNum(autSaAudList.getWaitLeaderAudNum() == totalCount ?
                                autSaAudList.getWaitLeaderAudNum() - reviewFitMoveClauseList.size() : autSaAudList.getWaitLeaderAudNum()
                        );
                    } else {
                        // 待组长复查条数  待组长复查
                        autSaAudList.setWaitLeaderAudNum(autSaAudList.getWaitLeaderAudNum() == cstCertificationStandardList.size() ?
                                autSaAudList.getWaitLeaderAudNum() - matchSize : autSaAudList.getWaitLeaderAudNum()
                        );
                    }
                }
            }

            //排除当前用户负责的款项
            if (CollectionUtils.isNotEmpty(autSaAudList.getDistributeClauseIdList())) {
                autSaAudList.getDistributeClauseIdList().removeAll(allFitClauseIds);
            }
            //返回当前登录人员不适用款ID，用于过滤不适用
            if (StringUtils.isNotBlank(hosPlanUserInfoVO.getAccountId()) && hosPlanUserInfoVO.getAccountId().equals(req.getAccountId())) {
                autSaAudList.setFitClauseIds(matchClauseIds);
            }
            autSaAudList.setAllFitClauseIds(allFitClauseIds);
        }
    }

    private void checkRemoveReviewFitMoveClauseTraineesAssessor(AutSaAudQueryDTO req, List<String> cstCertificationStandardList) {
        log.info("不适应用款移除开始===>>自评编码{},学员id：{},分组评审员id：{},款Ids数据为：{}", req.getAutSaRelation().getAutCode(),
                req.getAccountId(), req.getDomainAccountId(), cstCertificationStandardList);
        //查询不适用款
        ReviewFitMoveClause reviewFitMoveClause = new ReviewFitMoveClause();
        reviewFitMoveClause.setAutCode(req.getAutSaRelation().getAutCode());
        reviewFitMoveClause.setFitStatus(Long.valueOf(Constants.STR_NUM_1));
        reviewFitMoveClause.setStatus(Long.valueOf(Constants.STR_NUM_1));
        List<ReviewFitMoveClause> reviewFitMoveClauseList = reviewFitMoveClauseMapper.selectReviewFitMoveClauseList(reviewFitMoveClause);

        if (CollectionUtils.isNotEmpty(reviewFitMoveClauseList)) {

            List<String> allFitClauseIds = reviewFitMoveClauseList.stream().map(ReviewFitMoveClause::getClauseId).collect(Collectors.toList());
            log.info("不适用款排除：当前自评编码：【{}】有不适用款【{}】", req.getAutSaRelation().getAutCode(), allFitClauseIds);
            // 获取两者匹配款数量，证明此款属于此用户，然后排除;
            cstCertificationStandardList.removeAll(allFitClauseIds);
        }
        log.info("不适应用款移除结束===>>自评编码{},学员id：{},分组评审员id：{},款Ids数据为：{}", req.getAutSaRelation().getAutCode(),
                req.getAccountId(), req.getDomainAccountId(), cstCertificationStandardList);

    }

    /**
     * 解析待修改信息
     *
     * @param autSaAudList
     * @param waitModifylist
     * @param historySubmitModificationReviewlist
     * @param userHosPlanUserInfoVO
     * @param memberList
     * @param clauseMSkipList                     ’拒绝修改‘的款
     */
    private void parseWaitModifyInfo(AutSaAudList autSaAudList, List<AutSaAud> waitModifylist, List<String> historySubmitModificationReviewlist,
                                     HosPlanUserInfoVO userHosPlanUserInfoVO, List<HosPlanUserInfoVO> memberList, List<String> clauseMSkipList) {
        if (CollectionUtils.isNotEmpty(waitModifylist)) {
            List<String> needModifyList = waitModifylist.stream().filter(a -> AutSaAudResultEnum.checkIsAutSaAudResultEnum(a.getAutResult(), AutSaAudResultEnum.DISAGREE)).
                    map(a -> a.getClauseId()).distinct().collect(Collectors.toList());
            List<String> realList = needModifyList.stream().
                    filter(a -> CollectionUtils.isEmpty(historySubmitModificationReviewlist) || !historySubmitModificationReviewlist.contains(a)).collect(Collectors.toList());
            //排除拒绝修改的款
            realList.removeAll(clauseMSkipList);
            // 待修改条款id列表(所属用户)
            List<CstCertificationStandardVO> cstCertificationStandardVOList = userHosPlanUserInfoVO.getCstCertificationStandardVOList();
            if (CollectionUtils.isNotEmpty(cstCertificationStandardVOList)) {
                // List<String> userClauseIds = cstCertificationStandardVOList.stream().map(a -> a.getClauseId().toString()).collect(Collectors.toList());
                // autSaAudList.setWaitModifyClauseIdList(realList.stream().filter(a -> userClauseIds.contains(a)).collect(Collectors.toList()));
                //全部由评审组长修改
                autSaAudList.setWaitModifyClauseIdList(realList);
            } else {
                autSaAudList.setWaitModifyClauseIdList(Lists.newArrayList());
            }
            // 待修改条款id列表(所有)
            autSaAudList.setAllWaitModifyClauseIdList(realList);
            // 待修改条款id数（所有）
            autSaAudList.setWaitModifySum(realList.size());
            if (StringUtils.equals(autSaAudList.getIsLeader(), Constants.HospitalConstants.STR_NUM_1)) {
                // 用户是组长，返回修改信息
                List<Map<String, String>> modifyClauseInfoLists = Lists.newArrayList();
                memberList.stream().forEach(a ->
                        a.getCstCertificationStandardVOList().stream().forEach(aa -> {
                            if (needModifyList.contains(aa.getClauseId().toString())) {
                                Map<String, String> modifyClauseInfo = new HashMap<>();
                                modifyClauseInfo.put("clauseNo", aa.getClauseNo());
                                modifyClauseInfo.put("name", a.getName());
                                boolean modify = historySubmitModificationReviewlist.contains(aa.getClauseId().toString());
                                modifyClauseInfo.put("modify", modify ? Constants.FLAG_VALUE_Y : Constants.FLAG_VALUE_N);
                                modifyClauseInfoLists.add(modifyClauseInfo);
                            }
                        })
                );
                autSaAudList.setModifyClauseInfoLists(modifyClauseInfoLists);
            }
        }
    }

    /**
     * 设置自评总结提交相关数据
     *
     * @param autSaAudList
     * @param autSaAuds
     */
    private void setSaAudSubmitTimeInfo(AutSaAudList autSaAudList, List<AutSaAud> autSaAuds) {
        autSaAuds.forEach(k -> {
            if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(k.getSubmitType(), AutSaAudSubmitTypeEnum.SA_SUMMARY)) {
                // 自评时间
                autSaAudList.setSaAudSubmitTime(k.getSubmitDate());
            } else if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(k.getSubmitType(), AutSaAudSubmitTypeEnum.SR_SUMMARY)) {
                // 评审时间
                autSaAudList.setAudSecondSubmitTime(k.getSubmitDate());
            } else if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(k.getSubmitType(), AutSaAudSubmitTypeEnum.FR_REPORT_SUBMIT)) {
                // 初审时间
                autSaAudList.setAudSubmitTime(k.getSubmitDate());
            } else if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(k.getSubmitType(), AutSaAudSubmitTypeEnum.FR_REPORT_R_SUMMARY)) {
                // 评审报告审查总结
                autSaAudList.setReviewReviewReportSubmitTime(k.getSubmitDate());
            } else if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(k.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_SUMMARY)) {
                // 事实准确性审查总结
                autSaAudList.setAccuracyReviewSubmitTime(k.getSubmitDate());
            } else if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(k.getSubmitType(), AutSaAudSubmitTypeEnum.TR_SUMMARY)) {
                // 验证评审员评审总结
                autSaAudList.setSeniorReviewerReviewSubmitTime(k.getSubmitDate());
            }
        });
    }

    /**
     * 组织查询详情反参
     *
     * @param req 流程参数
     * @return 详情反参
     */
    @Override
    public AutSaAudDetailVO tissueDetailResult(AutSaAudQueryDTO req) {
        // 获取查询用提交类型
        String submitTypes = this.getQuerySubmitType(req, false);
        List<AutSaAud> autSaAuds = autSaAudMapper.selectAutSaAudListByAutCodeAndTypes(req.getAutSaRelation().getAutCode(), submitTypes);
        // 查询出当前自评编码对应的自评审核信息
        return this.tissueDetailResult(autSaAuds, req);
    }

    /**
     * 组织查询详情反参
     *
     * @param autSaAuds 审核数据
     * @param req       流程参数
     * @return 详情反参
     */
    @Override
    public AutSaAudDetailVO tissueDetailResult(List<AutSaAud> autSaAuds, AutSaAudQueryDTO req) {
        AutSaAudDetailVO autSaAudDetail = new AutSaAudDetailVO();
        AutSaRelation autSaRelation = req.getAutSaRelation();
        // 自评节点状态
        autSaAudDetail.setAutSaAudStatus(autSaRelation.getAutStatus());
        autSaAudDetail.setVersionId(autSaRelation.getAutCsId());
        // 医院名称
        autSaAudDetail.setHospitalName(req.getHospitalName());
//        List<AutSaAud> autSaAudSums = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(autSaAuds)) {
            // 认证审核信息列表 将审核总结信息排除
            List<AutSaAud> autSaAudListWithOutSum = Lists.newArrayList();
            // 文件fileId
            List<String> fileIds = Lists.newArrayList();
            // <文件fileId,文件类型>
            Map<String, String> fileIdMap = new HashMap<>();
            for (AutSaAud autSaAud : autSaAuds) {
                if (StringUtils.isNotBlank(autSaAud.getFileIds())) {
                    fileIds.add(autSaAud.getFileIds());
                    Arrays.asList(autSaAud.getFileIds()).forEach(fileId -> {
                        fileIdMap.put(fileId, autSaAud.getSubmitType());
                    });

                }
//                if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(autSaAud.getSubmitType(), AutSaAudSubmitTypeEnum.SR_SUMMARY,
//                        AutSaAudSubmitTypeEnum.SR_CLAUSE_M_SUMMARY, AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SUMMARY,
//                        AutSaAudSubmitTypeEnum.TR_CLAUSE_M_SUMMARY, AutSaAudSubmitTypeEnum.SR_REPORT_M_SUMMARY)) {
//                    autSaAudSums.add(autSaAud);
//                }
                if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(autSaAud.getSubmitType(), AutSaAudSubmitTypeEnum.SA_SUMMARY)) {
                    // 自评总结
                    autSaAudDetail.setAutSaAudSum(autSaAud);
                } else if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(autSaAud.getSubmitType(), AutSaAudSubmitTypeEnum.SR_SUMMARY)) {
                    // 评审总结
                    autSaAudDetail.setAudSecondTrialSum(autSaAud);
                } else if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(autSaAud.getSubmitType(), AutSaAudSubmitTypeEnum.FR_REPORT_R_SUMMARY)) {
                    // 评审报告审查总结
                    autSaAudDetail.setReviewReviewReportSum(autSaAud);
                } else if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(autSaAud.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_SUMMARY)) {
                    // 事实准确性审查总结
                    autSaAudDetail.setFactualAccuracyReviewSum(autSaAud);
                } else if (AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(autSaAud.getSubmitType(), AutSaAudSubmitTypeEnum.TR_SUMMARY)) {
                    // TR_SUMMARY
                    autSaAudDetail.setSeniorReviewerReviewSum(autSaAud);
                } else {
                    // 认证审核信息列表 将审核总结信息排除
                    autSaAudListWithOutSum.add(autSaAud);
                }
            }

            List<AutSaAud> autSaAudClausesLists = autSaAuds.stream().filter(a -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType(),
                    AutSaAudSubmitTypeEnum.SR_CLAUSE, AutSaAudSubmitTypeEnum.SR_CLAUSE_M, AutSaAudSubmitTypeEnum.FAR_CLAUSE_M, AutSaAudSubmitTypeEnum.TR_CLAUSE_M,
                    AutSaAudSubmitTypeEnum.SR_REPORT_M)).collect(Collectors.toList());

            if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autSaRelation.getAutStatus(), AutSaAudStatusEnum.FAR_CLAUSE_M_SUMMARY)) {
                List<String> farClauseIds = autSaAuds.stream().filter(a -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType()
                        , AutSaAudSubmitTypeEnum.FAR_CLAUSE_M)).map(a -> a.getClauseId()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(farClauseIds)) {
                    List<AutSaAud> autSaAudClausesLists1 = autSaAuds.stream().filter(a -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(a.getSubmitType(),
                            AutSaAudSubmitTypeEnum.SR_CLAUSE, AutSaAudSubmitTypeEnum.SR_CLAUSE_M) && farClauseIds.contains(a.getClauseId()))
                            .collect(Collectors.groupingBy(AutSaAud::getClauseId)).entrySet().stream().
                                    map(a -> a.getValue().stream().sorted(Comparator.comparing(AutSaAud::getUpdateTime).reversed()).
                                            collect(Collectors.toList()).get(0)).collect(Collectors.toList());
                    autSaAudDetail.setReviewerEvaluateBeforeLastTime(autSaAudClausesLists1);
                }
            }
            List<AutSaAud> latestAutSaAudsList = autSaAudClausesLists.stream().collect(Collectors.groupingBy(AutSaAud::getClauseId)).entrySet().stream().
                    map(a -> a.getValue().stream().sorted(Comparator.comparing(AutSaAud::getUpdateTime).reversed()).
                            collect(Collectors.toList()).get(0)).collect(Collectors.toList());

            // 获取当前版本分组情况，封装分组对应款的’亮点‘，’不足‘，’整改建议‘，’改进机会‘信息，按分组返回
            if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autSaRelation.getAutStatus(), AutSaAudStatusEnum.SR_SUMMARY, AutSaAudStatusEnum.FR_REPORT_F_M_SUMMARY,
                    AutSaAudStatusEnum.FAR_CLAUSE_M_SUMMARY, AutSaAudStatusEnum.TR_CLAUSE_M_SUMMARY, AutSaAudStatusEnum.SR_REPORT_M_SUMMARY)
                    || AutSaAudStatusEnum.checkIsAutSaAudStatusEnum1(autSaRelation.getAutStatus(), temProcessEnums)
            ) {
                List<DomainGroupNode> domainGroupNodes = packGroupClauseDesc(autSaRelation, latestAutSaAudsList, autSaAudDetail);
                autSaAudDetail.setDomainGroupNodes(domainGroupNodes);
            }
            autSaAudDetail.setLatestAutSaAudList(latestAutSaAudsList);
//            if (CollectionUtils.isNotEmpty(autSaAudSums)) {
//                autSaAudDetail.setLatestAutSaAudSum(autSaAudSums.stream().sorted(Comparator.comparing(AutSaAud::getUpdateTime).reversed()).collect(Collectors.toList()).get(0));
//            }
            // 如果节点为驳回节点，对应返回驳回信息
            List<AutSaAudBusinessData> rejectBusinessDatas = autSaAudBusinessDataService.selectAutSaAudBusinessData(autSaRelation.getAutCode(), AutSaAudBusinessCodeEnum.AUT_SA_AUD_REJECT.getCode());
            log.info("查询到驳回信息：{}", JSON.toJSONString(rejectBusinessDatas));
            if (CollectionUtils.isNotEmpty(rejectBusinessDatas) && StringUtils.isNotBlank(rejectBusinessDatas.get(0).getData())) {
                List<AutSaAud> rejectInfo = JSON.parseArray(rejectBusinessDatas.get(0).getData(), AutSaAud.class);
                List<String> fileIds1 = rejectInfo.stream().filter(a -> StringUtils.isNotBlank(a.getFileIds())).map(a -> {
                    Arrays.asList(a.getFileIds()).forEach(fileId -> {
                        fileIdMap.put(fileId, a.getSubmitType());
                    });
                    return a.getFileIds();
                }).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(fileIds1)) {
                    fileIds.addAll(fileIds1);
                }
                autSaAudDetail.setRejectAutSaAudListMap(rejectInfo.stream().collect(Collectors.groupingBy(AutSaAud::getSubmitType)));
            }

            // 医院进行事实准确性确认，需要可以下载整份评审报告
            if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(req.getRoleKey(), AutSaAudRoleEnum.HOSPITAL)) {
                List<AutSaAudBusinessData> reviewReportFileData = autSaAudBusinessDataService.selectAutSaAudBusinessData(
                        autSaRelation.getAutCode(), AutSaAudBusinessCodeEnum.REVIEW_REPORT.getCode());
                if (CollectionUtils.isNotEmpty(reviewReportFileData) && StringUtils.isNotBlank(reviewReportFileData.get(0).getData())) {
                    Map<String, String> reviewReportFileMap = JSON.parseObject(reviewReportFileData.get(0).getData(), Map.class);
                    String fileId = reviewReportFileMap.get("fileId");
                    if (StringUtils.isNotBlank(fileId)) {
                        fileIds.add(fileId);
                        autSaAudDetail.setReviewReportFileId(fileId);
                        fileIdMap.put(fileId, FtlToPdfEnum.REVIEW_REPORT.getCode());
                    }
                }
            }


            //020401-修改评审节点，返回拒绝最新修改的数据
            if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autSaRelation.getAutStatus(), AutSaAudStatusEnum.SR_REPORT_M)) {
                AutSaAud autSaAud = new AutSaAud();
                autSaAud.setAutCode(req.getAutSaRelation().getAutCode());
                autSaAud.setSubmitType(AutSaAudSubmitTypeEnum.SR_REPORT_M.getSubmitType());
                autSaAud.setStatus(Constants.INT_ZERO);
                List<AutSaAud> srReportMList = autSaAudMapper.selectAutSaAudList(autSaAud);
                if (CollectionUtils.isNotEmpty(srReportMList)) {
                    //MAP<提交类型，对应数据>
                    Map<String, List<AutSaAud>> collect = srReportMList.stream().filter(o -> StringUtils.isNotBlank(o.getClauseId()))
                            .collect(Collectors.groupingBy(AutSaAud::getClauseId)).entrySet()
                            .stream().map(a -> a.getValue().stream().sorted(Comparator.comparing(AutSaAud::getSubmitDate).reversed())
                                    .collect(Collectors.toList()).get(0)).collect(Collectors.groupingBy(AutSaAud::getSubmitType));
                    autSaAudDetail.setRejectAutSaAudListMap(collect);
                }
            }

            // 获取所有的文件列表信息
            if (CollectionUtils.isNotEmpty(fileIds)) {
                fileIds = Arrays.asList(String.join(",", fileIds).split(",")).stream().distinct().collect(Collectors.toList());
            }
            List<FileInfoVO> fileInfoVOList = commonService.fileInfoDtoToVo(uploadFileInfoService.getUploadFileInfoByIds(String.join(",", fileIds)));
            Map<String, List<FileInfoVO>> fileDetailMap = fileInfoVOList.stream().map(o -> {
                o.setFileType(fileIdMap.get(o.getFileId()));
                return o;
            }).collect(Collectors.groupingBy(FileInfoVO::getFileId));

            //封装自评报告终稿文件数据
            List<AutSaAudBusinessData> audSaReportBusinessData = autSaAudBusinessDataService.selectAutSaAudBusinessData(
                    autSaRelation.getAutCode(), AutSaAudBusinessCodeEnum.AUD_SA_REPORT.getCode());
            log.info("查询自评报告终稿信息：{}", JSON.toJSONString(audSaReportBusinessData));
            if (CollectionUtils.isNotEmpty(audSaReportBusinessData) && StringUtils.isNotBlank(audSaReportBusinessData.get(0).getData())) {
                FileInfoVO fileInfoVO = JSON.parseObject(audSaReportBusinessData.get(0).getData(), FileInfoVO.class);

                List<FileInfoVO> fileInfoVOs = commonService.fileInfoDtoToVo(uploadFileInfoService.getUploadFileInfoByIds(fileInfoVO.getFileId()));
                fileInfoVOs.stream().forEach(o -> {
                    o.setFileType(AutSaAudBusinessCodeEnum.AUD_SA_REPORT.getCode());
                });
                fileDetailMap.put(fileInfoVO.getFileId(), fileInfoVOs);
            }
            // 认证审核信息文件列表
            autSaAudDetail.setFileDetailMap(fileDetailMap);
            // 认证审核信息列表
            autSaAudDetail.setAutSaAudListMap(autSaAudListWithOutSum.stream().collect(Collectors.groupingBy(AutSaAud::getSubmitType)));
            // 业务数据
            this.getBusinessDatas(autSaAudDetail, autSaRelation.getAutCode());
        }
        return autSaAudDetail;
    }

    private List<DomainGroupNode> packGroupClauseDesc(AutSaRelation autSaRelation, List<AutSaAud> latestAutSaAudsList, AutSaAudDetailVO autSaAudDetail) {
        //  提交评审总结 的亮点不足等信息需要后端组织
        List<CstCertificationStandards> allClauseIds = cstCertificationStandardsService.selectAllClauseIdByVersionId(autSaRelation.getAutCsId());
        Map<String, CstCertificationStandards> allClauseStars = allClauseIds.stream().collect(Collectors.toMap(a -> a.getClauseId().toString(), a -> a, (aa, bb) -> bb));
        // 评审中发现亮点 autAdvantage      优秀和良好只要一个输入框“亮点”【必填】；
        List<AutSaAud> autAdvantageLists = latestAutSaAudsList.stream().filter(a -> AutSaAudResultEnum.checkIsAutSaAudResultEnum(a.getAutResult(),
                AutSaAudResultEnum.EXCELLENT, AutSaAudResultEnum.GOOD) && StringUtils.isNotBlank(a.getAutDesc()))
                .sorted(Comparator.comparing(AutSaAud::getClauseId)).collect(Collectors.toList());

        // 评审中发现不足及对受评医院的整改建议 autEvaluate     部分达标和不达标，需要两个输入框“不足及建议”【必填】、“改进机会”【非必填】  不足及建议 是 reviewerRespond；  其余的全部都是desc
        List<AutSaAud> autEvaluateLists = latestAutSaAudsList.stream().filter(a -> AutSaAudResultEnum.checkIsAutSaAudResultEnum(a.getAutResult(),
                AutSaAudResultEnum.PARTIAL_COMPLIANCE, AutSaAudResultEnum.NOT_STANDARD) && StringUtils.isNotBlank(a.getAutDesc()))
                .sorted(Comparator.comparing(AutSaAud::getClauseId)).collect(Collectors.toList());

        // 受评医院需考虑的改进机会      autImprove     部分达标和不达标，需要两个输入框“不足及建议”【必填】、“改进机会”【非必填】      达标需要一个输入框“改进机会”【非必填】；
        List<AutSaAud> autImproveLists = Lists.newArrayList();
        List<AutSaAud> autImproveLists1 = latestAutSaAudsList.stream().filter(a -> AutSaAudResultEnum.checkIsAutSaAudResultEnum(a.getAutResult(),
                AutSaAudResultEnum.PARTIAL_COMPLIANCE, AutSaAudResultEnum.NOT_STANDARD) && StringUtils.isNotBlank(a.getAutDesc())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(autImproveLists1)) {
            autImproveLists.addAll(autImproveLists1);
        }
        List<AutSaAud> autImproveLists2 = latestAutSaAudsList.stream().filter(a -> AutSaAudResultEnum.checkIsAutSaAudResultEnum(a.getAutResult(),
                AutSaAudResultEnum.REACH_STANDARD) && StringUtils.isNotBlank(a.getAutDesc())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(autImproveLists2)) {
            autImproveLists.addAll(autImproveLists2);
        }
        if (CollectionUtils.isNotEmpty(autImproveLists)) {
            autImproveLists.stream().sorted(Comparator.comparing(AutSaAud::getClauseId)).collect(Collectors.toList());
        }

        //获取当前版本分组情况，亮点，不足，整个检验，改进机会，按分组返回
        //分组详情字段是款Id字段，选好分组，筛选当前组是否有款ID封装
        //1-根据版本号，查询cst_domain-group_id_list分组ID字段，
        List<DomainGroupNode> domainGroupNodeList = hospitalDomainGroupService.selectDomainGroup("", autSaRelation.getAutCsId());
        StringBuilder autAdvantage = new StringBuilder();
        StringBuilder autEvaluate = new StringBuilder();
        StringBuilder autProposal = new StringBuilder();
        StringBuilder autImprove = new StringBuilder();
        domainGroupNodeList.forEach(o -> {
            //2-通过分组ID查找对应分组表的分组详情字段（分组详情相当于款Id）数据
            List<String> groupDetails = Lists.newArrayList();
            //字段groupDetail，第一层父类封装的是分组名称，子类的是分组详情（款Id）
            String groupName = o.getGroupDetail();
            this.groupToTree1(groupDetails, o.getChildren(), o.getGroupId());

            autAdvantage.append(groupName + ":\n");
            autAdvantageLists.stream().forEach(a -> {
                //3-匹配亮点数据是否在分组中，封装返回
                if (groupDetails.contains(a.getClauseId())) {
                    CstCertificationStandards cstCertificationStandards = allClauseStars.get(a.getClauseId());
                    String clauseAutAdvantage = MapUtils.getString(JSON.parseObject(a.getAutDesc(), Map.class), "autAdvantage", "")
                            .replace("<", "&lt;").replace(">", "&gt;");
                    if (StringUtils.isNotBlank(clauseAutAdvantage)) {
                        autAdvantage.append(cstCertificationStandards.getClauseNo()).append(":").append(clauseAutAdvantage).append(";\n");
                    }
                }
            });

            // 评审中发现不足及对受评医院的整改建议
            autEvaluate.append(groupName + ":\n");
            autProposal.append(groupName + ":\n");
            autEvaluateLists.stream().forEach(a -> {
                if (groupDetails.contains(a.getClauseId())) {
                    CstCertificationStandards cstCertificationStandards = allClauseStars.get(a.getClauseId());
                    String autEvaluateDate = MapUtils.getString(JSON.parseObject(a.getAutDesc(), Map.class), AUT_EVALUATE, "")
                            .replace("<", "&lt;").replace(">", "&gt;");
                    String autProposalDate = MapUtils.getString(JSON.parseObject(a.getAutDesc(), Map.class), AUT_PROPOSAL, "")
                            .replace("<", "&lt;").replace(">", "&gt;");
                    if (StringUtils.isNotBlank(autEvaluateDate)) {
                        autEvaluate.append(cstCertificationStandards.getClauseNo()).append(":").append(autEvaluateDate).append(";\n");
                    }
                    if (StringUtils.isNotBlank(autProposalDate)) {
                        autProposal.append(cstCertificationStandards.getClauseNo()).append(":").append(autProposalDate).append(";\n");
                    }
                }
            });

            // 受评医院需考虑的改进机会
            autImprove.append(groupName + ":\n");
            autImproveLists.stream().forEach(a -> {
                if (groupDetails.contains(a.getClauseId())) {
                    CstCertificationStandards cstCertificationStandards = allClauseStars.get(a.getClauseId());
                    String autImproveStr = MapUtils.getString(JSON.parseObject(a.getAutDesc(), Map.class), "autImprove", "")
                            .replace("<", "&lt;").replace(">", "&gt;");
                    if (StringUtils.isNotBlank(autImproveStr)) {
                        autImprove.append(cstCertificationStandards.getClauseNo()).append(":").append(autImproveStr).append(";\n");
                    }
                }
            });
        });
        autSaAudDetail.setAutAdvantage(autAdvantage.toString());
        autSaAudDetail.setAutEvaluate(autEvaluate.toString());
        autSaAudDetail.setAutProposal(autProposal.toString());
        autSaAudDetail.setAutImprove(autImprove.toString());

        return domainGroupNodeList;

    }

    private void groupToTree1(List<String> groupDetails, List<DomainGroupNode> domainGroupNodeList, String parentId) {
        domainGroupNodeList.forEach(o -> {
            if (parentId.equals(o.getParentId())) {
                groupDetails.add(o.getGroupDetail());
                if (CollectionUtils.isNotEmpty(o.getChildren())) {
                    groupToTree1(groupDetails, o.getChildren(), o.getGroupId());
                }
            }
        });
    }

    /**
     * 获取业务数据
     *
     * @param autSaAudDetail 详情
     * @param autCode        自评编码
     */
    private void getBusinessDatas(AutSaAudDetailVO autSaAudDetail, String autCode) {
        // 自评报告
        List<AutSaAudBusinessData> saReportBusinessDatas = autSaAudBusinessDataService.selectAutSaAudBusinessData(autCode, AutSaAudBusinessCodeEnum.AUT_SA_AUD_SA_REPORT.getCode());
        log.info("查询到自评结果信息：{}", JSON.toJSONString(saReportBusinessDatas));
        if (CollectionUtils.isNotEmpty(saReportBusinessDatas) && StringUtils.isNotBlank(saReportBusinessDatas.get(0).getData())) {
            autSaAudDetail.setAutSaAudSaReport(JSON.parseObject(saReportBusinessDatas.get(0).getData(), AutSaAudReport.class));
        }
        // 评审报告
        List<AutSaAudBusinessData> reportBusinessDatas = autSaAudBusinessDataService.selectAutSaAudBusinessData(autCode, AutSaAudBusinessCodeEnum.AUT_SA_AUD_REPORT.getCode());
        log.info("查询到评审报告信息：{}", JSON.toJSONString(reportBusinessDatas));
        if (CollectionUtils.isNotEmpty(reportBusinessDatas) && StringUtils.isNotBlank(reportBusinessDatas.get(0).getData())) {
            autSaAudDetail.setAutSaAudReport(JSON.parseObject(reportBusinessDatas.get(0).getData(), AutSaAudReport.class));
        }

        //在此节点如果有‘030302修改评审报告’数据就返回
        if (AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(autSaAudDetail.getAutSaAudStatus(), AutSaAudStatusEnum.WAIT_FR_REPORT_R_CLAUSE, AutSaAudStatusEnum.FR_REPORT_R_CLAUSE_PROCESS)) {
            //将业务数据表对应数据，封装到autSaAudListMap字段，前端根据此字段过滤
            List<AutSaAudBusinessData> srClauseMBusinessDataList = autSaAudBusinessDataService.selectAutSaAudBusinessData(autCode, AutSaAudBusinessCodeEnum.SR_CLAUSE_M_DATA.getCode());
            if (CollectionUtils.isNotEmpty(srClauseMBusinessDataList) && StringUtils.isNotBlank(srClauseMBusinessDataList.get(0).getData())) {
                log.info("查询业务数据-修改评审报告自评信息为：{}", JSON.toJSONString(srClauseMBusinessDataList));
                List<AutSaAud> srClauseMList = JSON.parseArray(srClauseMBusinessDataList.get(0).getData(), AutSaAud.class);
                Map<String, List<AutSaAud>> autSaAudListMap = autSaAudDetail.getRejectAutSaAudListMap();
                autSaAudListMap = MapUtils.isNotEmpty(autSaAudListMap) ? autSaAudListMap : new HashMap<>();
                autSaAudListMap.put(srClauseMList.get(0).getSubmitType(), srClauseMList);
                autSaAudDetail.setRejectAutSaAudListMap(autSaAudListMap);
            }
        }

    }

    /**
     * 获取该用户的分配信息
     *
     * @param accountId  账户
     * @param memberList 组员分配列表
     * @return 当前用户的分配信息
     */
    @Override
    public HosPlanUserInfoVO getHosPlanUserInfoVOFromList(String accountId, List<HosPlanUserInfoVO> memberList) {
        if (CollectionUtils.isEmpty(memberList)) {
            log.info("用户：{} 获取对应组员列表信息为空", accountId);
            throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000002);
        }
        // 复查页面仅组长 可以查询   fr_v_clause    sr_v_clause
        List<HosPlanUserInfoVO> hosPlanUserInfoVOs = memberList.stream().filter(a -> accountId.equals(a.getAccountId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(hosPlanUserInfoVOs)) {
            log.info("用户：{} 获取对应组员信息为空", accountId);
            throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000002);
        }
        return hosPlanUserInfoVOs.get(0);
    }

    /**
     * 获取该用户的分配信息
     *
     * @param accountId       账户
     * @param hosReviewPlanVO 分配信息
     * @param roleKey         角色权限字符串
     * @return 当前用户的分配信息
     */
    @Override
    public HosPlanUserInfoVO getHosPlanUserInfoVOFromList(String accountId, HosReviewPlanVO hosReviewPlanVO, String roleKey) {
        return this.getHosPlanUserInfoVOFromList(accountId, this.getHosPlanMemberList(hosReviewPlanVO, roleKey));
    }

    /**
     * 根据类和方法名得到方法
     *
     * @param clazz      class对象
     * @param methodName 方法名
     * @return 方法对象
     */
    @Override
    public Method getMethodByClassAndName(Class clazz, String methodName) {
        Method[] methods = clazz.getDeclaredMethods();
        for (Method method : methods) {
            if (method.getName().equals(methodName)) {
                return method;
            }
        }
        return null;
    }

    /**
     * 生成自评报告
     *
     * @param autCode      自评编码
     * @param versionId    版本号
     * @param submitTypes  提交类型
     * @param businessCode 业务编码
     * @return 自评报告
     */
    @Override
    public AutSaAudReport generateReport(String autCode, String versionId, String submitTypes, String businessCode, List<AutSaAud> autSaAuds) {
        if (CollectionUtils.isEmpty(autSaAuds)) {
            autSaAuds = autSaAudMapper.selectAutSaAudListByAutCodeAndTypes(autCode, submitTypes);
        }
        List<AutSaAud> autSaAudsLists = autSaAuds.stream().filter(o -> StringUtils.isNotBlank(o.getClauseId()))
                .collect(Collectors.groupingBy(AutSaAud::getClauseId)).entrySet()
                .stream().map(a -> a.getValue().stream().sorted(Comparator.comparing(AutSaAud::getUpdateTime).reversed())
                        .collect(Collectors.toList()).get(0)).collect(Collectors.toList());
        //{020402-修改评审报告总结}数据
        List<AutSaAud> srReportMSummaryAutSaAud = autSaAuds.stream().filter(o -> AutSaAudSubmitTypeEnum.checkIsAutSaAudSubmitTypeEnum(o.getSubmitType(), AutSaAudSubmitTypeEnum.SR_REPORT_M_SUMMARY)).collect(Collectors.toList());
        AutSaAudReport autSaAudReport = this.getAudReport(autSaAudsLists, versionId, srReportMSummaryAutSaAud);
        // 保存报告信息
        this.saveAutSaAudBusinessData(autCode, businessCode, JSON.toJSONString(autSaAudReport));
        return autSaAudReport;
    }

    /**
     * 获取评审报告信息
     *
     * @param audSecondTrialInquires   审核数据
     * @param versionId                版本号
     * @param srReportMSummaryAutSaAud
     * @return 报告信息
     */
    private AutSaAudReport getAudReport(List<AutSaAud> audSecondTrialInquires, String versionId, List<AutSaAud> srReportMSummaryAutSaAud) {
        if (CollectionUtils.isEmpty(audSecondTrialInquires)) {
            log.info("获取报告时，关联数据为空");
            throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000002);
        }
        AutSaAudReport autSaAudReport = new AutSaAudReport();
        // 初始化评审报告数据
        List<AutSaAudReportListVo> autSaAudReportLists = this.initAutSaAudReportLists(audSecondTrialInquires, versionId);
        autSaAudReport.setAutSaAudReportListVos(autSaAudReportLists);

//        //{02040-修改评审报告总结}
//        //最终审查时，修改条款后认证授予应相应更新
//        if (CollectionUtils.isNotEmpty(srReportMSummaryAutSaAud)) {
//            AutSaAud autSaAud = srReportMSummaryAutSaAud.get(0);
//            autSaAudReport.setAutSaAudResult(Integer.valueOf(autSaAud.getAutResult()));
//            return autSaAudReport;
//        }

        // 获取评审报告结果
        AutSaAudReportListVo basicReportListVo = autSaAudReportLists.get(0);
        AutSaAudReportListVo nonBasicReportListVo = autSaAudReportLists.get(1);
        AutSaAudReportListVo.BaseReportList basicAboveStandardReportList = basicReportListVo.getReportList().stream().filter(a -> a.getAutSaAudResult() == AutSaAudResultEnum.ABOVE_STANDARD.getCode()).collect(Collectors.toList()).get(0);
        AutSaAudReportListVo.BaseReportList nonBasicAboveStandardReportList = nonBasicReportListVo.getReportList().stream().filter(a -> a.getAutSaAudResult() == AutSaAudResultEnum.ABOVE_STANDARD.getCode()).collect(Collectors.toList()).get(0);
        List<AutSaAudReportListVo.BaseChapterReportList> nonBasicChapterReportList = nonBasicReportListVo.getChapterReportList();

        //排除不适用款，不做认证评估结果运算
        AutSaAudReportListVo.BaseReportList basicNotApplicableReportList = basicReportListVo.getReportList().stream().filter(a ->
                AutSaAudResultEnum.checkIsAutSaAudResultEnum(String.valueOf(a.getAutSaAudResult()), AutSaAudResultEnum.NOT_APPLICABLE, AutSaAudResultEnum.ADMIN_NOT_APPLICABLE)).
                findAny().orElse(new AutSaAudReportListVo.BaseReportList());

        AutSaAudReportListVo.BaseReportList nonBasicNotApplicableReportList = nonBasicReportListVo.getReportList().stream().filter(a ->
                AutSaAudResultEnum.checkIsAutSaAudResultEnum(String.valueOf(a.getAutSaAudResult()), AutSaAudResultEnum.NOT_APPLICABLE, AutSaAudResultEnum.ADMIN_NOT_APPLICABLE)).
                findAny().orElse(new AutSaAudReportListVo.BaseReportList());

        int basicSumCount = basicReportListVo.getSumCount() - basicNotApplicableReportList.getCount();
        int nonBasicSumCount = nonBasicReportListVo.getSumCount() - nonBasicNotApplicableReportList.getCount();


        //非基本款总核算结果：部分达标以上数量/非基本款总数 >=0.6 && 达标之上数量/非基本款总数 >=0.4 为true
        //1.2 改为：非基本款，达标款的数目占非基本款的60%及以上；达标之上数量/非基本款总数 >=0.6 为true
        //boolean nonBasicCheckReulst = NumberUtil.div(nonBasicAbovePartCompReportList.getCount(), nonBasicSumCount) >= 0.6d && NumberUtil.div(nonBasicAboveStandardReportList.getCount(), nonBasicSumCount) >= 0.4d;
        boolean nonBasicCheckReulst = NumberUtil.div(nonBasicAboveStandardReportList.getCount(), nonBasicSumCount) >= 0.6d;
        boolean nonBasicChapterCheckReulst = true;
        //非基本款章核算结果：达标及以上数量/章节总数 < 0.4 || 部分达标及以上数量/章节总数 < 0.3 任何一章为true时，结果封装为false;
        //1.3 改为：每章的非基本款达标款的数目占本章节非基本款的50%及以上。本章达标数/本章总数 >=0.5
        for (AutSaAudReportListVo.BaseChapterReportList baseChapterReportList : nonBasicChapterReportList) {
            if (nonBasicChapterCheckReulst) {
                nonBasicChapterCheckReulst = checkChapterReport(baseChapterReportList, 0.0d, 0.5d);
            }

        }

        // 1. 通过认证
        // 1.1 基本款100%达标
        // 1.2 非基本款，达标款的数目与部分达标款的数目总和占非基本款的60%及以上，其中达标款数据不低于非基本款的40%
        //1.2 改为：非基本款，达标款的数目占非基本款的60%及以上；
        // 1.3 每章的非基本款达标款的数目与部分达标款的数目总和占本章节非基本款的40%及以上，其中达标款数目不低于本章节非基本款的30%
        //1.3 改为：每章的非基本款达标款的数目占本章节非基本款的50%及以上。
        if (basicAboveStandardReportList.getCount() == basicSumCount && nonBasicCheckReulst && nonBasicChapterCheckReulst) {
            log.info("通过认证: 1.1.1 基本款100%达标");
            log.info("通过认证: 1.1.2 非基本款，达标款的数目与部分达标款的数目总和占非基本款的60%及以上，其中达标款数据不低于非基本款的40%");
            log.info("通过认证: 1.1.3 每章的非基本款达标款的数目与部分达标款的数目总和占本章节非基本款的40%及以上，其中达标款数目不低于本章节非基本款的30%");
            autSaAudReport.setAutSaAudResult(AutSaAudResultEnum.CERTIFIED.getCode());
            return autSaAudReport;
        }

        // 2. 有条件通过认证 未能满足“通过认证”条件，但能满足以下条件之一时，可授予“有条件通过认证”：
        // 2.1  非基本款达标与部分达标款的数目符合“通过认证”的条件，但有三个及以下“基本款”不达标，且风险是中风险及以下风险的医院。

        // 改为：“有条件通过认证”必须符合下列所有条件，
        //--（1）基本款不达标或部分达标条款≤六款，其中不达标条款≤三款，且风险等级均为中低风险；
        //不达标数量
        int basicNotStandardCount = basicReportListVo.getReportList().stream().filter(a -> a.getAutSaAudResult() == AutSaAudResultEnum.NOT_STANDARD.getCode()).collect(Collectors.toList()).get(0).getCount();
        //部分达标数量
        int basicPartialComplianceCount = basicReportListVo.getReportList().stream().filter(a -> a.getAutSaAudResult() == AutSaAudResultEnum.PARTIAL_COMPLIANCE.getCode()).collect(Collectors.toList()).get(0).getCount();
        //--（2）非基本款，达标款(达标及以上)的数目占非基本款的50%及以上；
        nonBasicCheckReulst = NumberUtil.div(nonBasicAboveStandardReportList.getCount(), nonBasicSumCount) >= 0.5d;
        //--（3）每章的非基本款达标款的数目占本章节非基本款的40%及以上。
        nonBasicChapterCheckReulst = true;
        for (AutSaAudReportListVo.BaseChapterReportList baseChapterReportList : nonBasicChapterReportList) {
            if (nonBasicChapterCheckReulst) {
                nonBasicChapterCheckReulst = checkChapterReport(baseChapterReportList, 0.0d, 0.4d);
            }
        }

        if (nonBasicCheckReulst && nonBasicChapterCheckReulst && (basicNotStandardCount + basicPartialComplianceCount) <= 6 && basicNotStandardCount <= 3) {
            Map<String, Integer> basicRiskLevelMap = basicReportListVo.getRiskLevelMap();
            boolean basicRiskLevelResult = false;
            if (!org.springframework.util.CollectionUtils.isEmpty(basicRiskLevelMap)) {
                for (Map.Entry<String, Integer> entry : basicRiskLevelMap.entrySet()) {
                    if (AutSaAudRiskLevelEnum.RISK_LEVEL_CRITICAL.getLevel().intValue() == entry.getValue() || AutSaAudRiskLevelEnum.RISK_ILEVEL_HIGH.getLevel().intValue() == entry.getValue()) {
                        log.info("基本款id：{}，不达标，风险等级：{} 是中风险以上风险的医院", entry.getKey(), entry.getValue());
                        basicRiskLevelResult = true;
                    }
                }
            }
            //没中高风险项为false
            if (!basicRiskLevelResult) {
                // log.info("有条件通过认证: 2.1.1 非基本款达标与部分达标款的数目符合“通过认证”的条件，但有三个及以下“基本款”不达标，且风险是中风险及以下风险的医院 ");
                autSaAudReport.setAutSaAudResult(AutSaAudResultEnum.CONDITIONAL_CERTIFICATION.getCode());
                return autSaAudReport;
            }
        }

        // 2.2  非基本款未达到“通过认证”的条件，但在基本款100%达标的情况下，能满足以下二个条件：
        // •	非基本款达标款的数目与部分达标款的数目总和占非基本款的50%及以上，其中达标款的数目不低于非基本款的30%；\
        // •	每章的非基本款达标款的数目与部分达标款的数目总和占本章节非基本款的30%及以上，其中达标款的数目不低于本章节非基本款的20%。

        //改：达到下列条件之一的，为“不通过认证”
        //--（1）受评医院没有达到“有条件通过认证”要求的，为“不通过认证”；
        //--（2）达到“有条件通过认证”的受评医院，在规定时间内，整改结果被“中心”审定为未达到规范要求的。

//        nonBasicCheckReulst = NumberUtil.div(nonBasicAbovePartCompReportList.getCount(), nonBasicSumCount) >= 0.5d && NumberUtil.div(nonBasicAboveStandardReportList.getCount(), nonBasicSumCount) >= 0.3d;
//        nonBasicChapterCheckReulst = true;
//        for (AutSaAudReportListVo.BaseChapterReportList baseChapterReportList : nonBasicChapterReportList) {
//            if (this.checkChapterReport(baseChapterReportList, 0.3d, 0.2d)) {
//                nonBasicChapterCheckReulst = false;
//            }
//        }
//        if (basicAboveStandardReportList.getCount() == basicSumCount && nonBasicCheckReulst && nonBasicChapterCheckReulst) {
//            log.info("有条件通过认证: 2.2.1 非基本款达标款的数目与部分达标款的数目总和占非基本款的50%及以上，其中达标款的数目不低于非基本款的30%");
//            log.info("有条件通过认证: 2.2.2 每章的非基本款达标款的数目与部分达标款的数目总和占本章节非基本款的30%及以上，其中达标款的数目不低于本章节非基本款的20%");
//            autSaAudReport.setAutSaAudResult(AutSaAudResultEnum.CONDITIONAL_CERTIFICATION.getCode());
//            return autSaAudReport;
//        }
        // 3. 不通过认证  。
        log.info("不通过认证: 3.1.1 未能满足“通过认证”和“有条件通过认证”的条件时，授予“不通过认证”");
        autSaAudReport.setAutSaAudResult(AutSaAudResultEnum.NOT_CERTIFIED.getCode());
        return autSaAudReport;
    }

    /**
     * 校验报告的章节信息
     *
     * @param chapterReportList  章节信息
     * @param abovePartCompLimlt 部分达标之上限值
     * @param aboveStandardLimit 达标之上限值
     * @return 校验结果
     */
    private boolean checkChapterReport(AutSaAudReportListVo.BaseChapterReportList chapterReportList, double abovePartCompLimlt, double aboveStandardLimit) {
        //如果总数减去不适用款为0时，即本章总数量为0，不需要再算占比判断评估结果
        if (ObjectUtil.equal(chapterReportList.getSumCount(), Constants.INT_ZERO)) {
            return true;
        }

        //    AutSaAudReportListVo.BaseReportList abovePartCompReportList = chapterReportList.getReportList().stream().filter(a -> a.getAutSaAudResult() == AutSaAudResultEnum.ABOVE_PARTIAL_COMPLIANCE.getCode()).findAny().orElse(new AutSaAudReportListVo.BaseReportList());
        AutSaAudReportListVo.BaseReportList aboveStandardReportList = chapterReportList.getReportList().stream().filter(a -> a.getAutSaAudResult() == AutSaAudResultEnum.ABOVE_STANDARD.getCode()).findAny().orElse(new AutSaAudReportListVo.BaseReportList());
        //排除不适用款，不做认证评估结果运算
        AutSaAudReportListVo.BaseReportList notApplicableReportList = chapterReportList.getReportList().stream().filter(a ->
                AutSaAudResultEnum.checkIsAutSaAudResultEnum(String.valueOf(a.getAutSaAudResult()), AutSaAudResultEnum.NOT_APPLICABLE, AutSaAudResultEnum.ADMIN_NOT_APPLICABLE)).
                findAny().orElse(new AutSaAudReportListVo.BaseReportList());
        int sumCount = chapterReportList.getSumCount() - notApplicableReportList.getCount();

//        return NumberUtil.div(abovePartCompReportList.getCount(), sumCount) < abovePartCompLimlt
//                || NumberUtil.div(aboveStandardReportList.getCount(), sumCount) < aboveStandardLimit;

        return NumberUtil.div(aboveStandardReportList.getCount(), sumCount) >= aboveStandardLimit;
    }

    /**
     * 初始化评审报告数据
     *
     * @param audSecondTrialInquires
     * @param versionId              版本信息
     * @return 报告基础数据
     */
    private List<AutSaAudReportListVo> initAutSaAudReportLists(List<AutSaAud> audSecondTrialInquires, String versionId) {
        // 查询版本对应的款id
        List<CstCertificationStandards> allClauseIds = cstCertificationStandardsService.selectAllClauseIdByVersionId(versionId);
        Map<String, CstCertificationStandards> allClauseStars = allClauseIds.stream().collect(Collectors.toMap(a -> a.getClauseId().toString(), a -> a, (aa, bb) -> bb));
        //MAP<章节,MAP<AutResult评价结果,数量>>
        Map<String, Map<String, Integer>> basicReportMap = new HashMap<>();
        Map<String, Map<String, Integer>> nonBasicReportMap = new HashMap<>();
        Map<String, Integer> basicRiskLevelMap = new HashMap<>();
        for (AutSaAud autSaAud : audSecondTrialInquires) {
            CstCertificationStandards cstCertificationStandards = allClauseStars.get(autSaAud.getClauseId());
            if (null == cstCertificationStandards) {
                log.info("自评数据条款id：{}，在版本：{}内不存在", autSaAud.getClauseId(), versionId);
                throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000002);
            }
            // 是否带*(0否 1是)  基本款
            boolean isStar = StringUtils.equals(Constants.HospitalConstants.STR_NUM_1, cstCertificationStandards.getIsStar());
            Map<String, Integer> chapterMap = MapUtils.getMap((isStar) ? basicReportMap : nonBasicReportMap, cstCertificationStandards.getChapterId().toString(), new HashMap<>());
            chapterMap.put(autSaAud.getAutResult(), MapUtils.getIntValue(chapterMap, autSaAud.getAutResult(), 0) + 1);
            if (isStar) {
                basicReportMap.put(cstCertificationStandards.getChapterId().toString(), chapterMap);
                if (AutSaAudResultEnum.checkIsAutSaAudResultEnum(autSaAud.getAutResult(), AutSaAudResultEnum.NOT_STANDARD, AutSaAudResultEnum.PARTIAL_COMPLIANCE)) {
                    // 风险结果 = 风险可能性 * 风险影响
                    basicRiskLevelMap.put(autSaAud.getClauseId(), AutSaAudRiskLevelEnum.getAutSaAudRiskLevelEnum(NumberUtil.mul(autSaAud.getRiskPossibility(), autSaAud.getRiskImpact()).intValue()).getLevel());
                }
            } else {
                nonBasicReportMap.put(cstCertificationStandards.getChapterId().toString(), chapterMap);
            }
        }
        List<AutSaAudReportListVo> reportList = Lists.newArrayList();
        // 基本款列表信息
        AutSaAudReportListVo basicReportListVo = this.fillAutSaAudReportList(basicReportMap, 0);
        basicReportListVo.setRiskLevelMap(basicRiskLevelMap);
        reportList.add(basicReportListVo);
        // 非基本款列表信息
        AutSaAudReportListVo nonBasicReportListVo = this.fillAutSaAudReportList(nonBasicReportMap, 1);
        reportList.add(nonBasicReportListVo);

        //算所有款占比
        AutSaAudReportListVo allReportListVo = packAllReportListVo(basicReportListVo, nonBasicReportListVo);
        reportList.add(allReportListVo);

        return reportList;
    }

    private AutSaAudReportListVo packAllReportListVo(AutSaAudReportListVo basicReportListVo, AutSaAudReportListVo nonBasicReportListVo) {
        //1.所有款统计数据和算占比，‘不适用’包含管理员不适应款
        //2.占比计算，算占比时，其他评分数量排除不适用的数量，分母减去不适用数量
        AutSaAudReportListVo allReportListVo = new AutSaAudReportListVo();
        if (basicReportListVo != null && nonBasicReportListVo != null) {
            allReportListVo.setIsBasic(Constants.INT_TWO);
            allReportListVo.setSumCount(basicReportListVo.getSumCount() + nonBasicReportListVo.getSumCount());

            List<AutSaAudReportListVo.BaseReportList> allBaseReportList = new ArrayList<>();
            //基本，非基本款数据
            List<AutSaAudReportListVo.BaseReportList> basicReportListVos = basicReportListVo.getReportList();
            List<AutSaAudReportListVo.BaseReportList> nonBasicReportListVos = nonBasicReportListVo.getReportList();
            if (CollectionUtils.isNotEmpty(basicReportListVos) && CollectionUtils.isNotEmpty(nonBasicReportListVos)) {
                //获取不适用总数
                int notApplicableBasicSum = basicReportListVos.stream().filter(o -> AutSaAudResultEnum.checkIsAutSaAudResultEnum(String.valueOf(o.getAutSaAudResult()),
                        AutSaAudResultEnum.NOT_APPLICABLE, AutSaAudResultEnum.ADMIN_NOT_APPLICABLE)).mapToInt(AutSaAudReportListVo.BaseReportList::getCount).sum();
                int notApplicableNotBasicSum = nonBasicReportListVos.stream().filter(o -> AutSaAudResultEnum.checkIsAutSaAudResultEnum(String.valueOf(o.getAutSaAudResult()),
                        AutSaAudResultEnum.NOT_APPLICABLE, AutSaAudResultEnum.ADMIN_NOT_APPLICABLE)).mapToInt(AutSaAudReportListVo.BaseReportList::getCount).sum();
                int notApplicableSum = notApplicableBasicSum + notApplicableNotBasicSum;
                //封装对应评分所有款数; 部分达标以上-8，排除不封装数据
                basicReportListVos.stream().filter(o -> !AutSaAudResultEnum.checkIsAutSaAudResultEnum(String.valueOf(o.getAutSaAudResult()),
                        AutSaAudResultEnum.ABOVE_PARTIAL_COMPLIANCE)).forEach(basicReport -> {
                    nonBasicReportListVos.forEach(nonBasicReport -> {
                        if (ObjectUtil.equal(basicReport.getAutSaAudResult(), nonBasicReport.getAutSaAudResult())) {
                            AutSaAudReportListVo.BaseReportList allBaseReport = new AutSaAudReportListVo.BaseReportList();
                            allBaseReport.setAutSaAudResult(basicReport.getAutSaAudResult());
                            allBaseReport.setCount(basicReport.getCount() + nonBasicReport.getCount());

                            //2.占比计算，算占比时，其他评分数量排除不适用的数量，分母减去不适用数量
                            String sumCount = String.valueOf(allReportListVo.getSumCount());
                            if (!AutSaAudResultEnum.checkIsAutSaAudResultEnum(String.valueOf(nonBasicReport.getAutSaAudResult()),
                                    AutSaAudResultEnum.NOT_APPLICABLE, AutSaAudResultEnum.ADMIN_NOT_APPLICABLE)) {
                                sumCount = String.valueOf(allReportListVo.getSumCount() - notApplicableSum);
                            }
                            allBaseReport.setRate(getRate(String.valueOf(allBaseReport.getCount()), sumCount));
                            allBaseReportList.add(allBaseReport);
                        }
                    });
                });

                //9合并到6不适用，并且重新算占比
                AutSaAudReportListVo.BaseReportList adminNotApplicableReport = allBaseReportList.stream().filter(o -> AutSaAudResultEnum.checkIsAutSaAudResultEnum(
                        String.valueOf(o.getAutSaAudResult()), AutSaAudResultEnum.ADMIN_NOT_APPLICABLE)).findFirst().orElse(null);
                if (adminNotApplicableReport != null) {
                    for (AutSaAudReportListVo.BaseReportList o : allBaseReportList) {
                        if (AutSaAudResultEnum.checkIsAutSaAudResultEnum(String.valueOf(o.getAutSaAudResult()), AutSaAudResultEnum.NOT_APPLICABLE)) {
                            o.setCount(o.getCount() + adminNotApplicableReport.getCount());
                            o.setRate(getRate(String.valueOf(o.getCount()), String.valueOf(allReportListVo.getSumCount())));
                            break;
                        }
                    }
                    allBaseReportList.remove(adminNotApplicableReport);
                }

            }
            allReportListVo.setReportList(allBaseReportList);
        }

        return allReportListVo;
    }

    /**
     * @param divisor   除数
     * @param isDivided 被除数
     * @return
     */
    @Override
    public double getRate(String isDivided, String divisor) {
        double rate = 0d;
        if (isDivided.equals("0") || divisor.equals("0")) {
            return 0;
        } else {
            BigDecimal div = NumberUtil.div(isDivided, divisor).multiply(new BigDecimal("100"));
            rate = div.setScale(2, RoundingMode.HALF_UP).doubleValue();
        }
        return rate;
    }

    /**
     * 封装自评报告审核结果数据
     *
     * @param reportMap 审核信息
     * @param isBasic   基本款标识
     */
    private AutSaAudReportListVo fillAutSaAudReportList(Map<String, Map<String, Integer>> reportMap, int isBasic) {
        AutSaAudReportListVo reportListVo = new AutSaAudReportListVo();
        reportListVo.setIsBasic(isBasic);
        // 基本款、非基本款总数
        int totalSumCount = 0;
        // 基本款、非基本款总的审核结果
        Map<String, Integer> totalAudResultMap = new HashMap<>();
        // 获取条款基础审核信息
        List<AutSaAudReportListVo.BaseChapterReportList> chapterReportLists = Lists.newArrayList();
        for (Map.Entry<String, Map<String, Integer>> entry : reportMap.entrySet()) {
            AutSaAudReportListVo.BaseChapterReportList baseChapterReportList = new AutSaAudReportListVo.BaseChapterReportList();
            baseChapterReportList.setChapterId(entry.getKey());
            Map<String, Integer> audResultMap = entry.getValue();
//            //如果为管理员不适用款，跳过不累加统计总数
//            if (audResultMap.containsKey(AutSaAudResultEnum.ADMIN_NOT_APPLICABLE.getCode().toString())) {
//                audResultMap.remove(AutSaAudResultEnum.ADMIN_NOT_APPLICABLE.getCode().toString());
//            }

            // 章总数
            int chapterSumCount = 0;
            if (!org.springframework.util.CollectionUtils.isEmpty(audResultMap)) {
                chapterSumCount = audResultMap.values().stream().mapToInt(b -> b.intValue()).sum();
                audResultMap.forEach((key, value) -> totalAudResultMap.merge(key, value, Integer::sum));
            }
            totalSumCount = totalSumCount + chapterSumCount;
            baseChapterReportList.setSumCount(chapterSumCount);
            baseChapterReportList.setReportList(this.getBaseReportLists(audResultMap, chapterSumCount));
            chapterReportLists.add(baseChapterReportList);
        }
        reportListVo.setSumCount(totalSumCount);
        reportListVo.setChapterReportList(chapterReportLists);
        reportListVo.setReportList(this.getBaseReportLists(totalAudResultMap, totalSumCount));
        return reportListVo;
    }

    /**
     * 获取审核结果类型的自评报告列表
     *
     * @param audResultMap 审核结果信息
     * @param sumCount     总数
     * @return
     */
    private List<AutSaAudReportListVo.BaseReportList> getBaseReportLists(Map<String, Integer> audResultMap, int sumCount) {
        // 6: 不适用；将管理员不适用合并到6
        int notApplicableNum = MapUtils.getIntValue(audResultMap, AutSaAudResultEnum.NOT_APPLICABLE.getCode().toString(), 0);
        int adminNotApplicableNum = MapUtils.getIntValue(audResultMap, AutSaAudResultEnum.ADMIN_NOT_APPLICABLE.getCode().toString(), 0);
        int notApplicableCon = notApplicableNum + adminNotApplicableNum;
        //1、非‘不适用’款的分母是剔除‘不适用’款后的和；‘不适用’款的分母是所有款
        //2、涉及页面，报告模板内(保存到评审数据表，页面、报告从此表获取数据)
        //过滤不适应款的总数
        int filterNotApplicableSumCount = sumCount - notApplicableCon;

        List<AutSaAudReportListVo.BaseReportList> baseReportLists = Lists.newArrayList();
        // 1: 优秀
        baseReportLists.add(this.getBaseReportList(AutSaAudResultEnum.EXCELLENT.getCode().toString(), MapUtils.getIntValue(audResultMap, AutSaAudResultEnum.EXCELLENT.getCode().toString(), 0), filterNotApplicableSumCount));
        // 2: 良好
        baseReportLists.add(this.getBaseReportList(AutSaAudResultEnum.GOOD.getCode().toString(), MapUtils.getIntValue(audResultMap, AutSaAudResultEnum.GOOD.getCode().toString(), 0), filterNotApplicableSumCount));
        // 3: 达标
        baseReportLists.add(this.getBaseReportList(AutSaAudResultEnum.REACH_STANDARD.getCode().toString(), MapUtils.getIntValue(audResultMap, AutSaAudResultEnum.REACH_STANDARD.getCode().toString(), 0), filterNotApplicableSumCount));
        // 4: 部分达标
        baseReportLists.add(this.getBaseReportList(AutSaAudResultEnum.PARTIAL_COMPLIANCE.getCode().toString(), MapUtils.getIntValue(audResultMap, AutSaAudResultEnum.PARTIAL_COMPLIANCE.getCode().toString(), 0), filterNotApplicableSumCount));
        // 5: 不达标
        baseReportLists.add(this.getBaseReportList(AutSaAudResultEnum.NOT_STANDARD.getCode().toString(), MapUtils.getIntValue(audResultMap, AutSaAudResultEnum.NOT_STANDARD.getCode().toString(), 0), filterNotApplicableSumCount));
        // 6: 不适用；将管理员不适用合并到6
        baseReportLists.add(this.getBaseReportList(AutSaAudResultEnum.NOT_APPLICABLE.getCode().toString(), notApplicableCon, sumCount));
        // 7: 达标之上（1,2,3）
        int underReachStandard = MapUtils.getIntValue(audResultMap, AutSaAudResultEnum.EXCELLENT.getCode().toString(), 0) + MapUtils.getIntValue(audResultMap, AutSaAudResultEnum.GOOD.getCode().toString(), 0) + MapUtils.getIntValue(audResultMap, AutSaAudResultEnum.REACH_STANDARD.getCode().toString(), 0);
        baseReportLists.add(this.getBaseReportList(AutSaAudResultEnum.ABOVE_STANDARD.getCode().toString(), underReachStandard, filterNotApplicableSumCount));
        // 8：部分达标以上（1,2,3,4）
        int underPartialCompliance = underReachStandard + MapUtils.getIntValue(audResultMap, "4", 0);
        baseReportLists.add(this.getBaseReportList(AutSaAudResultEnum.ABOVE_PARTIAL_COMPLIANCE.getCode().toString(), underPartialCompliance, filterNotApplicableSumCount));
        return baseReportLists;
    }

    /**
     * 获取某审核结果类型的自评报告
     *
     * @param autSaAudResult 审核结果
     * @param count          数量
     * @param sumCount       总数
     * @return
     */
    private AutSaAudReportListVo.BaseReportList getBaseReportList(String autSaAudResult, int count, int sumCount) {
        AutSaAudReportListVo.BaseReportList baseReportList = new AutSaAudReportListVo.BaseReportList();
        baseReportList.setAutSaAudResult(Integer.valueOf(autSaAudResult));
        baseReportList.setCount(count);
        if (sumCount == 0 || count == 0) {
            baseReportList.setRate(0d);
        } else {
            baseReportList.setRate(NumberUtil.round(NumberUtil.div(count, sumCount) * 100d, 2, RoundingMode.HALF_UP).doubleValue());
        }
        return baseReportList;
    }

    /**
     * 获取查询用提交类型
     *
     * @param req         流程参数
     * @param isQueryList 是否查询列表
     * @return 结果
     */
    @Override
    public String getQuerySubmitType(AutSaAudQueryDTO req, boolean isQueryList) {
        String querySubmitType = "";
        String autStatus = req.getAutSaRelation().getAutStatus();
        if (isQueryList) {
            // 查询列表
            if (AutSaAudPageTypeEnum.checkIsAutSaAudPageTypeEnum(req.getPageType(), AutSaAudPageTypeEnum.FR_CLAUSE_PAGE, AutSaAudPageTypeEnum.FR_V_CLAUSE_PAGE)) {
                // 初审初查 + 初审复查
                querySubmitType = String.join(",", Arrays.asList(AutSaAudSubmitTypeEnum.SA_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.FR_CLAUSE.getSubmitType()
                        , AutSaAudSubmitTypeEnum.FR_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.FR_REPORT_SUBMIT.getSubmitType()));
            } else if (AutSaAudPageTypeEnum.checkIsAutSaAudPageTypeEnum(req.getPageType(), AutSaAudPageTypeEnum.SR_CLAUSE_PAGE, AutSaAudPageTypeEnum.SR_V_CLAUSE_PAGE)) {
                // 评审初查 + 评审复查
                querySubmitType = String.join(",", Arrays.asList(
                        AutSaAudSubmitTypeEnum.SA_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.SR_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.SR_V_CLAUSE.getSubmitType(),
                        AutSaAudSubmitTypeEnum.SR_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.FR_REPORT_SUBMIT.getSubmitType()));
            } else if (AutSaAudPageTypeEnum.checkIsAutSaAudPageTypeEnum(req.getPageType(), AutSaAudPageTypeEnum.FR_REPORT_R_CLAUSE_PAGE, AutSaAudPageTypeEnum.TR_CLAUSE_PAGE)) {
                // 评审报告审查页 + 验证评审员页面
                querySubmitType = String.join(",", Arrays.asList(AutSaAudSubmitTypeEnum.SR_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.FR_REPORT_R_SUMMARY.getSubmitType(),
                        AutSaAudSubmitTypeEnum.FAR_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.TR_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.FR_REPORT_SUBMIT.getSubmitType(),
                        AutSaAudSubmitTypeEnum.TR_CLAUSE_RJ_E.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SUBMIT.getSubmitType()));
            } else if (AutSaAudPageTypeEnum.checkIsAutSaAudPageTypeEnum(req.getPageType(), AutSaAudPageTypeEnum.FAR_CLAUSE_W_M_PAGE)) {
                // 待修改评审列表页
                if (StringUtils.isNotBlank(autStatus) && autStatus.startsWith("0303")) {
                    // 第一次修改
                    querySubmitType = String.join(",", Arrays.asList(AutSaAudSubmitTypeEnum.SR_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.FR_REPORT_R_SUMMARY.getSubmitType(),
                            AutSaAudSubmitTypeEnum.FAR_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.TR_SUMMARY.getSubmitType(),
                            AutSaAudSubmitTypeEnum.FR_REPORT_R_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.SR_CLAUSE_M.getSubmitType(), AutSaAudSubmitTypeEnum.FR_REPORT_SUBMIT.getSubmitType()));
                } else if (StringUtils.isNotBlank(autStatus) && autStatus.startsWith("0304")) {
                    // 第二次修改
                    querySubmitType = String.join(",", Arrays.asList(AutSaAudSubmitTypeEnum.SR_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.FR_REPORT_R_SUMMARY.getSubmitType(),
                            AutSaAudSubmitTypeEnum.FAR_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.TR_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_M.getSubmitType(),
                            AutSaAudSubmitTypeEnum.FAR_V_CLAUSE_M.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.FR_REPORT_SUBMIT.getSubmitType(),
                            AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SKIP.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_RJ_E_M.getSubmitType()));
                } else {
                    // 第三次修改
                    querySubmitType = String.join(",", Arrays.asList(AutSaAudSubmitTypeEnum.SR_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.FR_REPORT_R_SUMMARY.getSubmitType(),
                            AutSaAudSubmitTypeEnum.FAR_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.TR_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.TR_CLAUSE_M.getSubmitType(),
                            AutSaAudSubmitTypeEnum.TR_V_CLAUSE_M.getSubmitType(), AutSaAudSubmitTypeEnum.TR_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.FR_REPORT_SUBMIT.getSubmitType(),
                            AutSaAudSubmitTypeEnum.TR_CLAUSE_M_SKIP.getSubmitType()));
                }
            } else {
                // 其他页面
                log.info("当前页面类型未配置对应查询提交类型,查询全部类型数据", req.getPageType());
            }
        } else {
            // 查询详情
            if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(req.getRoleKey(), AutSaAudRoleEnum.HOSPITAL)) {
                // 医院端
                if (StringUtils.isNotBlank(autStatus) && autStatus.startsWith("0103")) {
                    // 事实准确性审查
                    querySubmitType = String.join(",", Arrays.asList(AutSaAudSubmitTypeEnum.SA_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.SA_REPORT_DESC.getSubmitType(),
                            AutSaAudSubmitTypeEnum.SR_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.SR_BT_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.SR_GROUP_SUMMARY.getSubmitType(),
                            AutSaAudSubmitTypeEnum.SR_CLAUSE_M.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_SUMMARY.getSubmitType(),
                            AutSaAudSubmitTypeEnum.SR_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.SR_CLAUSE_M_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SUMMARY.getSubmitType(),
                            AutSaAudSubmitTypeEnum.SA_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.TR_CLAUSE_M_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.SR_REPORT_M_SUMMARY.getSubmitType(),
                            AutSaAudSubmitTypeEnum.FAR_CLAUSE_RJ_E.getSubmitType()
                    ));
                } else {
                    // 其他节点
                    querySubmitType = String.join(",", Arrays.asList(AutSaAudSubmitTypeEnum.SA_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.SA_REPORT_DESC.getSubmitType(),
                            AutSaAudSubmitTypeEnum.SA_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.SR_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.SR_CLAUSE_M_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SUMMARY.getSubmitType(),
                            AutSaAudSubmitTypeEnum.TR_CLAUSE_M_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.SR_REPORT_M_SUMMARY.getSubmitType()));
                }
            } else if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(req.getRoleKey(), AutSaAudRoleEnum.INSPECTOR)) {
                // 审查员
                if (StringUtils.isNotBlank(autStatus) && autStatus.startsWith("0202")) {
                    // 评审报告审查
                    querySubmitType = String.join(",", Arrays.asList(AutSaAudSubmitTypeEnum.SA_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.SA_REPORT_DESC.getSubmitType(),
                            AutSaAudSubmitTypeEnum.SA_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.SR_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.SR_BT_SUMMARY.getSubmitType(),
                            AutSaAudSubmitTypeEnum.SR_GROUP_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.SR_CLAUSE_M.getSubmitType(),
                            AutSaAudSubmitTypeEnum.FR_REPORT_R_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.SR_SUMMARY.getSubmitType(),
                            AutSaAudSubmitTypeEnum.SR_CLAUSE_M_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SUMMARY.getSubmitType(),
                            AutSaAudSubmitTypeEnum.TR_CLAUSE_M_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.SR_REPORT_M_SUMMARY.getSubmitType()));
                } else if (StringUtils.isNotBlank(autStatus) && autStatus.startsWith("0203")) {
                    // 事实准确性审查完成待确认
                    querySubmitType = String.join(",", Arrays.asList(AutSaAudSubmitTypeEnum.SA_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.SA_REPORT_DESC.getSubmitType(),
                            AutSaAudSubmitTypeEnum.SA_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.SR_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.SR_BT_SUMMARY.getSubmitType(),
                            AutSaAudSubmitTypeEnum.SR_GROUP_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.SR_CLAUSE_M.getSubmitType(),
                            AutSaAudSubmitTypeEnum.FAR_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_SUMMARY.getSubmitType(),
                            AutSaAudSubmitTypeEnum.SR_SUMMARY.getSubmitType(),
                            AutSaAudSubmitTypeEnum.SR_CLAUSE_M_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SUMMARY.getSubmitType(),
                            AutSaAudSubmitTypeEnum.TR_CLAUSE_M_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.SR_REPORT_M_SUMMARY.getSubmitType(),
                            AutSaAudSubmitTypeEnum.FAR_V_CLAUSE_M.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_M.getSubmitType(),
                            AutSaAudSubmitTypeEnum.TR_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_RJ_E.getSubmitType(),
                            AutSaAudSubmitTypeEnum.TR_CLAUSE_RJ_E.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_RJ_E_M.getSubmitType(),
                            AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SUBMIT.getSubmitType()));
                } else if (StringUtils.isNotBlank(autStatus) && autStatus.startsWith("0204")) {
                    // 修改评审报告
                    querySubmitType = String.join(",", Arrays.asList(AutSaAudSubmitTypeEnum.SA_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.SA_REPORT_DESC.getSubmitType(),
                            AutSaAudSubmitTypeEnum.SA_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.SR_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.SR_BT_SUMMARY.getSubmitType(),
                            AutSaAudSubmitTypeEnum.SR_GROUP_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.SR_CLAUSE_M.getSubmitType(),
                            AutSaAudSubmitTypeEnum.FAR_CLAUSE_M.getSubmitType(),
                            AutSaAudSubmitTypeEnum.TR_CLAUSE_M.getSubmitType(), AutSaAudSubmitTypeEnum.SR_REPORT_M.getSubmitType(),
                            AutSaAudSubmitTypeEnum.SR_SUMMARY.getSubmitType(),
                            AutSaAudSubmitTypeEnum.SR_CLAUSE_M_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SUMMARY.getSubmitType(),
                            AutSaAudSubmitTypeEnum.TR_CLAUSE_M_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.SR_REPORT_M_SUMMARY.getSubmitType()));
                } else {
                    // 其他节点
                    querySubmitType = String.join(",", Arrays.asList(AutSaAudSubmitTypeEnum.SA_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.SA_REPORT_DESC.getSubmitType(),
                            AutSaAudSubmitTypeEnum.SA_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.FR_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.SR_SUMMARY.getSubmitType(),
                            AutSaAudSubmitTypeEnum.SR_CLAUSE_M_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SUMMARY.getSubmitType(),
                            AutSaAudSubmitTypeEnum.TR_CLAUSE_M_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.SR_REPORT_M_SUMMARY.getSubmitType()));
                }
            } else if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(req.getRoleKey(), AutSaAudRoleEnum.ASSESSOR)) {
                // 评审员
                if (StringUtils.isNotBlank(autStatus) && autStatus.startsWith("0303")) {
                    // 第一次待修改条款
                    querySubmitType = String.join(",", Arrays.asList(AutSaAudSubmitTypeEnum.SA_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.SR_CLAUSE.getSubmitType(),
                            AutSaAudSubmitTypeEnum.FR_REPORT_R_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.SR_CLAUSE_M.getSubmitType(), AutSaAudSubmitTypeEnum.SR_SUMMARY.getSubmitType(),
                            AutSaAudSubmitTypeEnum.SR_CLAUSE_M_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.TR_CLAUSE_M_SUMMARY.getSubmitType(),
                            AutSaAudSubmitTypeEnum.SR_REPORT_M_SUMMARY.getSubmitType()));
                } else if (StringUtils.isNotBlank(autStatus) && autStatus.startsWith("0304")) {
                    // 第二次待修改条款
                    querySubmitType = String.join(",", Arrays.asList(AutSaAudSubmitTypeEnum.SA_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.SR_CLAUSE.getSubmitType(),
                            AutSaAudSubmitTypeEnum.SR_CLAUSE_M.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_M.getSubmitType(),
                            AutSaAudSubmitTypeEnum.FAR_V_CLAUSE_M.getSubmitType(), AutSaAudSubmitTypeEnum.SR_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.SR_CLAUSE_M_SUMMARY.getSubmitType(),
                            AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.TR_CLAUSE_M_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.SR_REPORT_M_SUMMARY.getSubmitType(),
                            AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SKIP.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_RJ_E_M.getSubmitType()
                    ));
                } else if (StringUtils.isNotBlank(autStatus) && autStatus.startsWith("0305")) {
                    // 第三次待修改条款
                    querySubmitType = String.join(",", Arrays.asList(AutSaAudSubmitTypeEnum.SA_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.SR_CLAUSE.getSubmitType(),
                            AutSaAudSubmitTypeEnum.SR_CLAUSE_M.getSubmitType(), AutSaAudSubmitTypeEnum.SR_CLAUSE_M_SUMMARY.getSubmitType(),
                            AutSaAudSubmitTypeEnum.FAR_CLAUSE_M.getSubmitType(), AutSaAudSubmitTypeEnum.TR_CLAUSE.getSubmitType(),
                            AutSaAudSubmitTypeEnum.TR_CLAUSE_M.getSubmitType(), AutSaAudSubmitTypeEnum.TR_V_CLAUSE_M.getSubmitType(), AutSaAudSubmitTypeEnum.SR_SUMMARY.getSubmitType(),
                            AutSaAudSubmitTypeEnum.SR_CLAUSE_M_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SUMMARY.getSubmitType(),
                            AutSaAudSubmitTypeEnum.TR_CLAUSE_M_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.SR_REPORT_M_SUMMARY.getSubmitType(),
                            AutSaAudSubmitTypeEnum.TR_CLAUSE_M_SKIP.getSubmitType()
                    ));
                } else {
                    // 其他节点
                    querySubmitType = String.join(",", Arrays.asList(AutSaAudSubmitTypeEnum.SA_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.SA_REPORT_DESC.getSubmitType(),
                            AutSaAudSubmitTypeEnum.SA_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.SR_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.SR_BT_SUMMARY.getSubmitType(),
                            AutSaAudSubmitTypeEnum.SR_GROUP_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.SR_V_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.SR_SUMMARY.getSubmitType(),
                            AutSaAudSubmitTypeEnum.SR_CLAUSE_M_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SUMMARY.getSubmitType(),
                            AutSaAudSubmitTypeEnum.TR_CLAUSE_M_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.SR_REPORT_M_SUMMARY.getSubmitType()));
                }
            } else if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(req.getRoleKey(), AutSaAudRoleEnum.SENIOR_ASSESSOR)) {
                // 验证评审员
                querySubmitType = String.join(",", Arrays.asList(AutSaAudSubmitTypeEnum.SA_CLAUSE.getSubmitType(), AutSaAudSubmitTypeEnum.SR_CLAUSE.getSubmitType(),
                        AutSaAudSubmitTypeEnum.SR_CLAUSE_M.getSubmitType(), AutSaAudSubmitTypeEnum.FAR_CLAUSE_M.getSubmitType(), AutSaAudSubmitTypeEnum.TR_CLAUSE.getSubmitType(),
                        AutSaAudSubmitTypeEnum.SR_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.SR_CLAUSE_M_SUMMARY.getSubmitType(),
                        AutSaAudSubmitTypeEnum.FAR_CLAUSE_M_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.TR_CLAUSE_M_SUMMARY.getSubmitType(),
                        AutSaAudSubmitTypeEnum.SR_REPORT_M_SUMMARY.getSubmitType(), AutSaAudSubmitTypeEnum.TR_CLAUSE_RJ_E.getSubmitType(),
                        AutSaAudSubmitTypeEnum.TR_SUMMARY.getSubmitType()
                ));
            } else {
                // 其他角色
                log.info("当前查询详情未配置对应查询提交类型,查询全部类型数据", req.getRoleKey());
            }
        }
        return querySubmitType;
    }


    /**
     * 处理节点直接翻转场景
     *
     * @param req 流程参数
     */
    @Override
    public void processNodeFilpScene(AutSaAudSaveDTO req) {
        StatusProcessEnum statusProcessEnum = StatusProcessEnum.getStatusProcessEnum(req.getStatusProcess());
        if (null != statusProcessEnum) {
            String nextStatus = this.getNextStatus(req.getAutSaAudStatusConfig().getNextStatusConfig(), statusProcessEnum);
            log.info("processNodeFilpScene -- 入参statusProcess：{},翻转到下一节点：{}", req.getStatusProcess(), nextStatus);
            if (StringUtils.isNotBlank(nextStatus)) {
                AutSaAud autSaAud = new AutSaAud();
                autSaAud.setSubmitType(req.getSubmitType());
                autSaAud.setAutCode(req.getAutCode());
                autSaAud.setAutDesc(req.getAutSaAudStatusConfig().getCurrentStatus() + "|" + req.getStatusProcess());
                autSaAud.setAccountId(req.getAccountId());
                req.setAutSaAudLists(Arrays.asList(autSaAud));
                this.batchInsertAutSaAud(req.getAutSaAudLists());
                this.updateAutSaRelation(nextStatus, req.getAutCode());
                return;
            }
        }
        log.error("传入的参数不正确或者节点配置不正确，节点未正确翻转");
        throw new ServiceException(ServiceExceptionEnum.AUT_RECORD_ERROR_1000002);
    }


    /**
     * 评审报告形式审查驳回后，邮件通知，只通知评审组长、和对应条款的评审员，抄送审查组长、所有的管理员；
     *
     * @param frReportRClauses 评审报告形式审查（FR_REPORT_R_CLAUSE提交类型数据）
     * @param req              认证自评审核
     */
    @Override
    public void frReportEmailToUsers(List<AutSaAud> frReportRClauses, AutSaAudSaveDTO req) {
        if (req == null || req.getAutCode() == null || req.getAutSaRelation() == null ||
                StringUtils.isEmpty(req.getAutSaRelation().getHospitalApplyNo()) || StringUtils.isEmpty(req.getAutSaRelation().getAutCsId())) {
            throw new ServiceException(String.format("认证自评审核入参数据为空，发送邮件通知失败,入参：[%s]", req));
        }
        //评审报告形式审查驳回后，邮件通知，只通知评审组长、和对应条款的评审员，抄送审查组长、所有的管理员；
        List<Long> rejectClauseIds = frReportRClauses.stream().filter(o -> StringUtils.isNotEmpty(o.getAutResult()) &&
                AutSaAudResultEnum.checkIsAutSaAudResultEnum(o.getAutResult(), AutSaAudResultEnum.DISAGREE))
                .map(o -> Long.valueOf(o.getClauseId())).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(rejectClauseIds)) {
            //查询医院名称
            QueryBaseConditionDTO queryBaseConditionDTO = new QueryBaseConditionDTO();
            queryBaseConditionDTO.setCommonId(req.getAutSaRelation().getHospitalApplyNo());
            HospitalBaseInfo hospitalBaseInfo = hospitalBaseInfoMapper.selectHospitalByApplyNo(queryBaseConditionDTO);
            if (hospitalBaseInfo == null || StringUtils.isEmpty(hospitalBaseInfo.getHospitalName())) {
                throw new ServiceException(String.format("查询医院名称为空，发送邮件通知失败,入参：[%s]", req));
            }

            //获取评审组长，审查组长，管理员名称
            List<String> userIds = new ArrayList<>();
            HospitalPreExam preExamLeader = iHospitalPreExamService.selectHosPreExamByLeader(req.getAutSaRelation().getHospitalApplyNo(), Constants.INT_ONE);
            if (preExamLeader == null || StringUtils.isEmpty(preExamLeader.getPreExamId())) {
                throw new ServiceException(MessageFormat.format("根据医院编号：[{0}]，获取医疗机构认证信息对应初审员组长信息为空",
                        req.getAutSaRelation().getHospitalApplyNo()));
            }

            //评审组长
            List<HospitalReviewer> hospitalReviewerList = hospitalReviewerMapper.selectHospitalReviewerByApplyNo(req.getAutSaRelation().getHospitalApplyNo());
            List<String> reviewerLeaderId = hospitalReviewerList.stream().filter(o -> o.getLeaderIs() == Constants.INT_ONE).map(HospitalReviewer::getReviewerId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(reviewerLeaderId) || reviewerLeaderId.size() > 1) {
                throw new ServiceException(MessageFormat.format("根据医院编号：[{0}]，获取医疗机构认证信息对应评审员组长信息错误",
                        req.getAutSaRelation().getHospitalApplyNo()));
            }

            //查询评审员名称和对应的条款
            //MAP<评审员名称，ClauseNos>(ClauseNos为：管理1.1.1、管理1.1.2、XXX、XXX)
            Map<String, String> nameAndClauseNosMap = new HashMap<>();

            //根据拒绝条款和SubmitType为sr_clause，查询AutSaAud表，获取对应评审员Id
            AutSaAud autSaAud = new AutSaAud();
            autSaAud.setAutCode(req.getAutCode());
            autSaAud.setSubmitType(AutSaAudSubmitTypeEnum.SR_CLAUSE.getSubmitType());
            autSaAud.setClauseId(StringUtils.join(rejectClauseIds, ","));
            autSaAud.setStatus(Constants.INT_ONE);
            List<AutSaAud> rjAutSaAudList = autSaAudMapper.selectAutSaAudList(autSaAud);
            if (CollectionUtils.isEmpty(rjAutSaAudList)) {
                throw new ServiceException(MessageFormat.format("根据自评编码：[{0}]和提交类型：[{1}],查询认证自评审核对象信息为空！",
                        req.getAutCode(), AutSaAudSubmitTypeEnum.SR_CLAUSE.getSubmitType()));
            }
            userIds.add(preExamLeader.getPreExamId());
            userIds.add(reviewerLeaderId.get(0));
            userIds.addAll(rjAutSaAudList.stream().filter(o -> ObjectUtil.isNotEmpty(o.getCId())).map(o -> o.getCId().toString()).collect(Collectors.toList()));

            //获取评审组长，审查组长，评审员，管理员信息
            List<UserVo> userList = sysUserMapper.selectUserByIds(userIds);
            //管理员(抄送邮件的用户)
            List<String> roleKeys = Lists.newArrayList();
            roleKeys.add(AutSaAudRoleEnum.COMMON_ADMIN.getRoleKey());
            List<UserVo> ccUserVoList = sysUserMapper.selectUsersByRoleKeys(roleKeys);
            //删除系统管理不用发送短信
            List<UserVo> removeCcUserVoList = new ArrayList<>();
            userList.stream().forEach(o -> {
                if (ObjectUtil.equal(preExamLeader.getPreExamId(), String.valueOf(o.getUserId()))) {
                    ccUserVoList.add(o);
                    removeCcUserVoList.add(o);
                }
            });
            userList.removeAll(removeCcUserVoList);

            //根据条款，获取条款No内容
            SelectStandardsByClauseIdsDTO selectStandardsByClauseIdsDTO = new SelectStandardsByClauseIdsDTO();
            selectStandardsByClauseIdsDTO.setClauseIds(rejectClauseIds);
            selectStandardsByClauseIdsDTO.setVersionId(Long.valueOf(req.getAutSaRelation().getAutCsId()));
            List<CstCertificationStandards> cstCertificationStandards = cstCertificationStandardsService.selectByClauseIdsAndVersionId(selectStandardsByClauseIdsDTO);
            if (CollectionUtils.isEmpty(cstCertificationStandards)) {
                throw new ServiceException(MessageFormat.format("根据条款：[{0}]和版本号：[{1}],查询认证标准模板对象信息为空！",
                        rejectClauseIds, req.getAutSaRelation().getAutCsId()));
            }
            //MAP<ClauseId，ClauseNo>,驳回的款Id和No
            Map<String, String> rejectClauseMap = cstCertificationStandards.stream().filter(o -> StringUtils.isNotBlank(o.getClauseNo()))
                    .collect(Collectors.toMap(o -> String.valueOf(o.getClauseId()), CstCertificationStandards::getClauseNo));
            //MAP<AccountId，ClauseIds>
            Map<String, List<AutSaAud>> rjAutSaAudMap = rjAutSaAudList.stream().collect(Collectors.groupingBy(AutSaAud::getAccountId));
            //Map<UserId,NickName>
            Map<String, String> userNameMap = userList.stream().collect(Collectors.toMap(o -> String.valueOf(o.getUserId()), UserVo::getNickName));
            rjAutSaAudMap.forEach((accountId, autSaAudList) -> {
                List<String> clauseNos = autSaAudList.stream().filter(o -> StringUtils.isNotBlank(o.getClauseId())).map(o -> rejectClauseMap.get(o.getClauseId())).collect(Collectors.toList());
                String clauseNosStr = StringUtils.join(clauseNos, "、");
                //有可能账号失效为空
                if (StringUtils.isNotBlank(userNameMap.get(accountId))) {
                    nameAndClauseNosMap.put(userNameMap.get(accountId), clauseNosStr);
                }
            });

            //邮件标题
            String emailTitle = MessageFormat.format("{0}评审-评审报告审查驳回通知", hospitalBaseInfo.getHospitalName());
            if (MapUtil.isEmpty(nameAndClauseNosMap)) {
                throw new ServiceException("评审员驳回信息为空，发送邮件通知失败！");
            }
            //邮件内容
            String spaces = MessageContentFormatEnum.getSpaces(2);
            StringBuilder content = new StringBuilder();
            content.append(MessageContentFormatEnum.packContentNewline("各评审成员好:"));
            content.append(MessageContentFormatEnum.packContentNewline(spaces + MessageFormat.format("您参与的{0}评审，如下评价条款被审查驳回:", hospitalBaseInfo.getHospitalName())));
            //邮件内容-评审员名称和对应款信息
            for (Map.Entry<String, String> entry : nameAndClauseNosMap.entrySet()) {
                content.append(MessageContentFormatEnum.packContentNewline(entry.getKey() + ":"));
                content.append(MessageContentFormatEnum.packContentNewline(spaces + MessageFormat.format("条款:{0}", entry.getValue())));
            }
            content.append(MessageContentFormatEnum.packContentNewline("请登录《深圳卫健医院评审评价平台》确认是否修改，谢谢!"));
            if (ObjectUtils.isNotEmpty(userList)) {
                //发送短信与邮箱
                MessageSendRecordDTO messageSendRecordDTO = new MessageSendRecordDTO();
                messageSendRecordDTO.setContent(content.toString());
                messageSendRecordDTO.setSendType(Constants.STR_NUM_1);
                messageSendRecordDTO.setEmailTitle(emailTitle);
                messageSendRecordDTO.setUserVoList(userList);
                messageSendRecordDTO.setCcUserVoList(ccUserVoList);
                try {
                    long time1 = System.currentTimeMillis();
                    messageSendRecordService.sendMessageToUser(messageSendRecordDTO);
                    log.info("评审报告形式审查驳回-发送邮箱耗时：" + (System.currentTimeMillis() - time1) + "ms");
                } catch (GeneralSecurityException e) {
                    log.error("评审报告形式审查驳回-发送短信链接客户端异常:" + e.getMessage());
                } catch (MessagingException e) {
                    log.error("评审报告形式审查驳回-发送邮箱异常:" + e.getMessage());
                }
            } else {
                throw new ServiceException(String.format("组员账号:{%s},已停用，不发送邮箱与短信", userIds.toString()));
            }
        }
    }

}
