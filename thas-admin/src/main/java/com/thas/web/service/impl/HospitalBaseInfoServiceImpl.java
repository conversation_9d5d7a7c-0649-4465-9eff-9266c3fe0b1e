package com.thas.web.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.thas.common.constant.Constants;
import com.thas.common.constant.Constants.HospitalConstants;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.core.domain.entity.SysDictData;
import com.thas.common.core.domain.entity.SysRole;
import com.thas.common.core.domain.entity.SysUser;
import com.thas.common.core.domain.model.LoginUser;
import com.thas.common.core.redis.RedisCache;
import com.thas.common.enums.*;
import com.thas.common.exception.ServiceException;
import com.thas.common.utils.PageUtils;
import com.thas.common.utils.RegexUtil;
import com.thas.common.utils.SecurityUtils;
import com.thas.generator.util.NumberGenUtils;
import com.thas.system.domain.SysUserHospital;
import com.thas.system.domain.vo.SendEmailCodeVo;
import com.thas.system.mapper.SysDictDataMapper;
import com.thas.system.mapper.SysUserHospitalMapper;
import com.thas.system.mapper.SysUserMapper;
import com.thas.system.service.ISysConfigService;
import com.thas.system.service.ISysUserService;
import com.thas.web.domain.*;
import com.thas.web.domain.dto.HospitalBaseInfosDTO;
import com.thas.web.domain.dto.ReviewFitMoveClauseReq;
import com.thas.web.domain.vo.DomainGroupNode;
import com.thas.web.domain.vo.FileInfoVO;
import com.thas.web.domain.vo.HospitalReviewerAndAutSaRelationVO;
import com.thas.web.domain.vo.ModifyTheReviewCycleVo;
import com.thas.web.dto.*;
import com.thas.web.mapper.CertificateAbilityMapper;
import com.thas.web.mapper.CstCertificationStandardsMapper;
import com.thas.web.mapper.CstDomainMapper;
import com.thas.web.mapper.HospitalAuthContactMapper;
import com.thas.web.mapper.HospitalBaseInfoMapper;
import com.thas.web.mapper.HospitalDepartmentMapper;
import com.thas.web.mapper.HospitalLegalPersonMapper;
import com.thas.web.mapper.HospitalPreExamMapper;
import com.thas.web.mapper.HospitalReviewCycleMapper;
import com.thas.web.mapper.HospitalReviewerMapper;
import com.thas.web.mapper.MessageTemplateMapper;
import com.thas.web.mapper.ReviewFitMoveClauseMapper;
import com.thas.web.mapper.ReviewerBaseInfoMapper;
import com.thas.web.mapper.TrainingEvaluateResultMapper;
import com.thas.web.service.CommonService;
import com.thas.web.service.IAutSaAudBusinessDataService;
import com.thas.web.service.IAutSaRelationService;
import com.thas.web.service.ICstCertificationStandardsService;
import com.thas.web.service.ICstDomainService;
import com.thas.web.service.ICstVersioningService;
import com.thas.web.service.IHospitalBaseInfoService;
import com.thas.web.service.IHospitalDomainGroupService;
import com.thas.web.service.IHospitalPlannedDistributionService;
import com.thas.web.service.IHospitalPreExamService;
import com.thas.web.service.IHospitalReviewCycleService;
import com.thas.web.service.IHospitalReviewerService;
import com.thas.web.service.IMessageSendRecordService;
import com.thas.web.service.IReviewFitMoveClauseService;
import com.thas.web.service.ITrainingEvaluateResultService;
import com.thas.web.service.IUploadFileInfoService;
import com.thas.web.utils.HutoolExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.time.Instant;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.thas.common.utils.SecurityUtils.getLoginUser;

/**
 * 医疗机构详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-19
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class HospitalBaseInfoServiceImpl implements IHospitalBaseInfoService {

    @Autowired
    private HospitalBaseInfoMapper hospitalBaseInfoMapper;

    @Autowired
    private IMessageSendRecordService messageSendRecordService;

    @Autowired
    private HospitalAuthContactMapper hospitalAuthContactMapper;

    @Autowired
    private HospitalLegalPersonMapper hospitalLegalPersonMapper;

    @Autowired
    private MessageTemplateMapper messageTemplateMapper;

    @Autowired
    private HospitalDepartmentMapper hospitalDepartmentMapper;

    @Autowired
    private IHospitalDomainGroupService hospitalDomainGroupService;

    @Autowired
    private SysDictDataMapper sysDictDataMapper;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private CstDomainMapper cstDomainMapper;

    @Autowired
    private CertificateAbilityMapper certificateAbilityMapper;

    @Autowired
    private IUploadFileInfoService iUploadFileInfoService;

    @Autowired
    private IHospitalPreExamService iHospitalPreExamService;

    @Autowired
    private IHospitalReviewerService iHospitalReviewerService;

    @Autowired
    private HospitalPreExamMapper hospitalPreExamMapper;

    @Autowired
    private ICstCertificationStandardsService iCstCertificationStandardsService;

    @Autowired
    private HospitalReviewerMapper hospitalReviewerMapper;

    @Autowired
    private ISysUserService iSysUserService;

    @Autowired
    private SysUserHospitalMapper sysUserHospitalMapper;

    @Autowired
    private IHospitalPlannedDistributionService iHospitalPlannedDistributionService;

    @Autowired
    private IHospitalReviewCycleService iHospitalReviewCycleService;

    @Autowired
    private ICstDomainService iCstDomainService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private CommonService commonService;

    @Autowired
    private ICstVersioningService iCstVersioningService;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private HospitalReviewCycleMapper hospitalReviewCycleMapper;

    @Resource
    private IAutSaRelationService autSaRelationService;

    @Autowired
    private ITrainingEvaluateResultService iTrainingEvaluateResultService;

    @Autowired
    private ReviewerBaseInfoMapper reviewerBaseInfoMapper;

    @Autowired
    private TrainingEvaluateResultMapper trainingEvaluateResultMapper;

    @Autowired
    private ReviewFitMoveClauseMapper reviewFitMoveClauseMapper;
    @Autowired
    private CstCertificationStandardsMapper cstCertificationStandardsMapper;
    @Autowired
    private IReviewFitMoveClauseService reviewFitMoveClauseService;
    @Autowired
    private IUploadFileInfoService uploadFileInfoService;

    @Override
    public AjaxResult hospitalBaseInfoSubmit(HospitalBaseInfoDTO hospitalBaseInfoDTO) {
        // 如果applyNo不为空则说明是医院端提交医院基本信息PDF确认件，更新数据直接提交即可。
        HospitalBaseInfo hospitalBaseInfo = hospitalBaseInfoDTO.getHospitalBaseInfo();
        if (ObjectUtil.equal(hospitalBaseInfoDTO.getSignFlag(), Constants.STR_NUM_1) && Objects.nonNull(hospitalBaseInfo)
                && CharSequenceUtil.isNotEmpty(hospitalBaseInfo.getApplyNo())) {
            if (Objects.isNull(hospitalBaseInfo.getSignConfirmStatus())
                    || CharSequenceUtil.isEmpty(hospitalBaseInfo.getSignFileId())) {
                throw new ServiceException(ServiceExceptionEnum.HOSPITAL_ERROR_1000013);
            }
            hospitalBaseInfoMapper.updateHospitalBaseInfo(hospitalBaseInfo);

            QueryBaseConditionDTO queryBaseConditionDTO = new QueryBaseConditionDTO();
            queryBaseConditionDTO.setCommonId(hospitalBaseInfo.getApplyNo());
            HospitalBaseInfo qryHospitalBaseInfo = hospitalBaseInfoMapper.selectHospitalByApplyNo(queryBaseConditionDTO);
            if (qryHospitalBaseInfo == null || StringUtils.isBlank(qryHospitalBaseInfo.getPracticeLicenseNo())) {
                log.error("根据【{}】查询医疗机构执业许可证登记号为空,医疗机构数据为：【{}】", hospitalBaseInfo.getApplyNo(), JSON.toJSONString(qryHospitalBaseInfo));
                throw new ServiceException("查询医疗机构数据为空，请联系管理员！");
            }
            //最后一步上传文件后，失效缓存验证，详情接口不能访问
            String k1 = Constants.TEMP_QRY_HOS_INFO_KEY_PRE + qryHospitalBaseInfo.getPracticeLicenseNo();
            redisCache.deleteObject(k1);

            return AjaxResult.success();
        }

        //免登接口提交的步骤标识(第二步-2，第三步-3，第四步-4)
        String stepFlag = hospitalBaseInfoDTO.getStepFlag();
        if (StringUtils.isBlank(stepFlag) || !Arrays.asList("2", "3", "4").contains(stepFlag)) {
            throw new ServiceException(String.format("医院申请提交时，步骤标识：[%s]入参错误", stepFlag));
        }
        if (Arrays.asList("2", "3", "4").contains(stepFlag) && (hospitalBaseInfoDTO.getHospitalBaseInfo() == null ||
                hospitalBaseInfoDTO.getHospitalLegalPerson() == null)) {
            //hospitalBaseInfo 与 hospitalLegalPerson是第二,三，四步必填
            throw new ServiceException("【hospitalBaseInfo】与【hospitalLegalPerson】入参第二,三，四步提交时必填");
        }
        if (Arrays.asList("3", "4").contains(stepFlag) && hospitalBaseInfoDTO.getHospitalDepartmentList() == null) {
            //hospitalDepartmentList 在第三，四步加上必填
            throw new ServiceException("【hospitalDepartmentList】入参第三，四步提交时必填");
        }
        if (ObjectUtil.equal(stepFlag, Constants.STR_NUM_4) && hospitalBaseInfoDTO.getHospitalDepartmentList() == null) {
            //certificateAbilityList在第四步加上必填
            throw new ServiceException("【certificateAbilityList】入参第四步提交时必填");
        }

        // 校验hospitalBaseInfo.getPracticeLicenseNo()当前'医疗机构执业许可证登记号'是否已经存在
        checkRequest(hospitalBaseInfoDTO);

        // 生成applyNo
        String applyNo = NumberGenUtils.hospitalApplyNoGen();
        log.info("医疗结构提交基本数据生成applyNo:{}", applyNo);
        // 插入hospital_base_info表
        hospitalBaseInfo.setApplyNo(applyNo);
        return this.doSaveHosBaseInfo(hospitalBaseInfoDTO);
    }

    /**
     * 校验当前入参
     *
     * @param hospitalBaseInfoDTO 入参
     */
    private void checkRequest(HospitalBaseInfoDTO hospitalBaseInfoDTO) {
        doCheckPracticeLicenseNo(hospitalBaseInfoDTO);
    }

    /**
     * 校验当前'医疗机构执业许可证登记号'是否已经存在
     *
     * @param hospitalBaseInfoDTO 入参
     */
    private void doCheckPracticeLicenseNo(HospitalBaseInfoDTO hospitalBaseInfoDTO) {
        HospitalBaseInfo hospitalBaseInfo = hospitalBaseInfoDTO.getHospitalBaseInfo();
        if (null == hospitalBaseInfo || StringUtils.isEmpty(hospitalBaseInfo.getPracticeLicenseNo())) {
            return;
        }

        String practiceLicenseNo = hospitalBaseInfo.getPracticeLicenseNo();
        QueryBaseConditionDTO queryBaseConditionDTO = new QueryBaseConditionDTO();
        queryBaseConditionDTO.setPracticeLicenseNo(practiceLicenseNo);
        if (selectHospitalByApplyNo(queryBaseConditionDTO) != null) {
            throw new ServiceException("医疗机构执业许可证登记号已存在", 500);
        }
    }

    private void checkHospitalBaseInfoDTO(HospitalBaseInfoDTO hospitalBaseInfoDTO) {
        HospitalBaseInfo hospitalBaseInfo = hospitalBaseInfoDTO.getHospitalBaseInfo();
        if (hospitalBaseInfo != null && StringUtils.isNotBlank(hospitalBaseInfo.getHospitalExtPhone()) &&
                !RegexUtil.checkNumberLength(hospitalBaseInfo.getHospitalExtPhone(), "8")) {
            throw new ServiceException("医院分机号码长度超过8位数，请确认后重试！");
        }
        //【医院端】医院申请流程：2. 面积需要支持小数点后2位；
        if (hospitalBaseInfo != null && StringUtils.isNotBlank(hospitalBaseInfo.getAreaCovered()) &&
                !RegexUtil.checkNumberBit(hospitalBaseInfo.getAreaCovered(), "2")) {
            throw new ServiceException("占地面积超过两位小数，请确认后重试！");
        }
        if (hospitalBaseInfo != null && StringUtils.isNotBlank(hospitalBaseInfo.getAreaArchitecture()) &&
                !RegexUtil.checkNumberBit(hospitalBaseInfo.getAreaArchitecture(), "2")) {
            throw new ServiceException("总建筑面积超过两位小数，请确认后重试！");
        }
        if (hospitalBaseInfo != null && StringUtils.isNotBlank(hospitalBaseInfo.getAreaBusinessArchitecture()) &&
                !RegexUtil.checkNumberBit(hospitalBaseInfo.getAreaBusinessArchitecture(), "2")) {
            throw new ServiceException("业务用房建筑面积超过两位小数，请确认后重试！");
        }
        HospitalLegalPerson hospitalLegalPerson = hospitalBaseInfoDTO.getHospitalLegalPerson();
        if (hospitalLegalPerson != null && StringUtils.isNotBlank(hospitalLegalPerson.getLegalPersonExtPhone()) &&
                !RegexUtil.checkNumberLength(hospitalLegalPerson.getLegalPersonExtPhone(), "8")) {
            throw new ServiceException("法人分机号码长度超过8位数，请确认后重试！");
        }
        //【医院端】14. 医院申请流程，评审员申请流程，法人性别均去掉“未知”；0男 1女
        if (hospitalLegalPerson != null && hospitalLegalPerson.getGender() != null &&
                !(hospitalLegalPerson.getGender() == Constants.INT_ZERO || hospitalLegalPerson.getGender() == Constants.INT_ONE)) {
            throw new ServiceException("法人性别不能为“未知”，请确认后重试！");
        }

    }

    /**
     * 保存数据
     */
    private AjaxResult doSaveHosBaseInfo(HospitalBaseInfoDTO hospitalBaseInfoDTO) {

        //增加所有分机号码，非必填，长度可限制为8位；
        // 【医院端】14. 医院申请流程，评审员申请流程，性别均去掉“未知”；
        //【医院端】医院申请流程：2. 面积需要支持小数点后2位；
        this.checkHospitalBaseInfoDTO(hospitalBaseInfoDTO);

        String applyNo = hospitalBaseInfoDTO.getHospitalBaseInfo().getApplyNo();
        //查询是否有数据有数据更新，没数据新增
        QueryBaseConditionDTO queryBaseConditionDTO = new QueryBaseConditionDTO();
        queryBaseConditionDTO.setCommonId(applyNo);
        HospitalBaseInfo hospitalBaseInfo = hospitalBaseInfoMapper.selectHospitalByApplyNo(queryBaseConditionDTO);
        if (hospitalBaseInfo == null) {
            int hosInfoInsRes = hospitalBaseInfoMapper.insertHospitalBaseInfo(hospitalBaseInfoDTO.getHospitalBaseInfo());
            if (hosInfoInsRes <= 0) {
                throw new ServiceException(ServiceExceptionEnum.HOSPITAL_ERROR_1000000);
            }
        } else {
            hospitalBaseInfoMapper.updateHospitalBaseInfo(hospitalBaseInfoDTO.getHospitalBaseInfo());
        }

        // 插入授权人信息表 hospital_auth_contact
        HospitalAuthContact hospitalAuthContact = hospitalBaseInfoDTO.getHospitalAuthContact();
        if (Objects.nonNull(hospitalAuthContact)) {
            HospitalAuthContact qryHospitalAuthContact = hospitalAuthContactMapper.selectHospitalAuthContactByApplyNo(applyNo);
            hospitalAuthContact.setApplyNo(applyNo);
            if (qryHospitalAuthContact == null) {
                int hosAuConRes = hospitalAuthContactMapper.insertHospitalAuthContact(hospitalAuthContact);
                if (hosAuConRes <= 0) {
                    throw new ServiceException(ServiceExceptionEnum.HOSPITAL_ERROR_1000001);
                }
            } else {
                hospitalAuthContactMapper.updateHospitalAuthContact(hospitalAuthContact);
            }
        }

        // 插入法人信息表
        HospitalLegalPerson hospitalLegalPerson = hospitalBaseInfoDTO.getHospitalLegalPerson();
        if (hospitalLegalPerson != null) {
            HospitalLegalPerson qryHospitalLegalPerson = hospitalLegalPersonMapper.selectHospitalLegalPersonByApplyNo(applyNo);
            hospitalLegalPerson.setApplyNo(applyNo);
            if (qryHospitalLegalPerson == null) {
                int hosLegPerRes = hospitalLegalPersonMapper.insertHospitalLegalPerson(hospitalLegalPerson);
                if (hosLegPerRes <= 0) {
                    throw new ServiceException(ServiceExceptionEnum.HOSPITAL_ERROR_1000002);
                }
            } else {
                hospitalLegalPersonMapper.updateHospitalLegalPerson(hospitalLegalPerson);
            }
        }

        // 统计医疗机构与科室关系
        List<HospitalDepartment> hospitalDepartmentList = hospitalBaseInfoDTO.getHospitalDepartmentList();
        if (CollUtil.isNotEmpty(hospitalDepartmentList)) {
            //先删除后新增，逻辑不能修改
            hospitalDepartmentMapper.deleteHospitalDepartmentByApplyNo(applyNo);
            hospitalDepartmentMapper.deleteHospitalExclusiveDepartmentByApplyNo(applyNo);
            // 将医疗机构科室关系提交
            hospitalDepartmentList.forEach(hospitalDepartment -> {
                if (StringUtils.equals(Constants.HospitalConstants.OTHER_NEW_DEPARTMENTS_DICT_VALUE, hospitalDepartment.getDepartmentDictValue())) {
                    // 如果字典值为 otherNewDepartments ，说明该科室为新增的专属于这个医院的科室 需要存入 另外的科室表中
                    this.processHosExclusiveDepartment(hospitalDepartment, applyNo);
                } else {
                    // 没有对应字典键值的的项，将对应的新增的添加到字典表
                    if (CharSequenceUtil.isEmpty(hospitalDepartment.getDepartmentDictValue())) {
                        // 根据字段类型查出的数据是已经升序排列
                        String dictType = hospitalDepartment.getDepartmentDictType();
                        List<SysDictData> dictTypeList = sysDictDataMapper.selectDictDataByType(dictType);
                        if (CollUtil.isEmpty(dictTypeList)) {
                            throw new ServiceException(ServiceExceptionEnum.HOSPITAL_ERROR_1000005);
                        }
                        // 获取最后一个对应的排序值
                        long lastSortNum = dictTypeList.get(dictTypeList.size() - 1).getDictSort() + 1;
                        // 获取对应类型列表中对应的最后的键值
                        dictTypeList.sort(Comparator.comparingInt(x -> Integer.parseInt(x.getDictValue())));
                        String lastDictVal = String.valueOf(Integer.parseInt(dictTypeList.get(dictTypeList.size() - 1).getDictValue()) + 1);
                        SysDictData sd = new SysDictData();
                        sd.setDictType(dictType);
                        sd.setDictLabel(hospitalDepartment.getDepartmentName());
                        sd.setDictValue(lastDictVal);
                        sd.setDictSort(lastSortNum);
                        int sdInRes = sysDictDataMapper.insertDictData(sd);
                        if (sdInRes <= 0) {
                            throw new ServiceException(ServiceExceptionEnum.HOSPITAL_ERROR_1000005);
                        }
                        hospitalDepartment.setDepartmentDictValue(lastDictVal);
                    }
                    hospitalDepartment.setApplyNo(applyNo);
                    int hosDepRes = hospitalDepartmentMapper.insertHospitalDepartment(hospitalDepartment);
                    if (hosDepRes < 0) {
                        throw new ServiceException(ServiceExceptionEnum.HOSPITAL_ERROR_1000006);
                    }
                }
            });
        }

        // 医疗机构对应证书存表
        List<CertificateAbility> certificateAbilityList = hospitalBaseInfoDTO.getCertificateAbilityList();
        if (CollectionUtils.isNotEmpty(certificateAbilityList)) {
            certificateAbilityMapper.deleteCertificateAbilityByProcessCode(applyNo);
            certificateAbilityList.forEach(certificateAbility -> {
                // 使用applyNo作为医疗结构在表中对应的关联
                certificateAbility.setProcessCode(applyNo);
                int cerAbiRes = certificateAbilityMapper.insertCertificateAbility(certificateAbility);
                if (cerAbiRes <= 0) {
                    throw new ServiceException(ServiceExceptionEnum.HOSPITAL_ERROR_1000007);
                }
            });

        }

        String verifyCode = UUID.randomUUID().toString();
        String verifyKey = applyNo + ":" + verifyCode;
        redisCache.setCacheObject(verifyKey, verifyCode, 2, TimeUnit.HOURS);

        Map<String, String> data = new HashMap<>();
        data.put("applyNo", applyNo);
        data.put("verifyCode", verifyCode);
        return AjaxResult.success(data);
    }

    /**
     * 处理医院专属科室信息
     *
     * @param hospitalDepartment
     */
    private void processHosExclusiveDepartment(HospitalDepartment hospitalDepartment, String applyNo) {
        String dictType = hospitalDepartment.getDepartmentDictType();
        if (StringUtils.isBlank(dictType)) {
            log.error("新增医院专属科室：{} 信息时，入参科室类型为空", hospitalDepartment.getDepartmentName());
            throw new ServiceException(ServiceExceptionEnum.HOSPITAL_ERROR_1000012);
        }
        // 查询医疗科室专属科室信息
        List<HospitalDepartment> hospitalExclusiveDepartments = hospitalDepartmentMapper.selectHospitalExclusiveDepartmentByApplyNo(applyNo);
        String lastDictVal = "otherNewDepartments";
        long num;
        if (CollectionUtils.isEmpty(hospitalExclusiveDepartments) || !hospitalExclusiveDepartments.stream().anyMatch(a -> StringUtils.equals(dictType, a.getDepartmentDictType()))) {
            // 专属科室信息为空 或者当前科室对应类型为空
            num = 1;
        } else {
            num = hospitalExclusiveDepartments.stream().filter(a -> StringUtils.equals(dictType, a.getDepartmentDictType())).count() + 1;
        }
        lastDictVal = lastDictVal + "_" + num;
        hospitalDepartment.setDepartmentDictValue(lastDictVal);
        hospitalDepartment.setApplyNo(applyNo);
        int hosDepRes = hospitalDepartmentMapper.insertHospitalExclusiveDepartment(hospitalDepartment);
        if (hosDepRes < 0) {
            throw new ServiceException(ServiceExceptionEnum.HOSPITAL_ERROR_1000006);
        }
    }


    @Override
    public List<QueryHospitalListDTO> queryHospitalList(QueryHospitalListDTO queryHospitalListDTO) {
        return hospitalBaseInfoMapper.queryHospitalList(queryHospitalListDTO);
    }


    @Override
    public HospitalBaseInfoDTO queryHospitalDetail(QueryBaseConditionDTO queryBaseConditionDTO) {
        String applyNo = queryBaseConditionDTO.getCommonId();
        String practiceLicenseNo = queryBaseConditionDTO.getPracticeLicenseNo();
        if (CharSequenceUtil.isEmpty(applyNo) && CharSequenceUtil.isEmpty(practiceLicenseNo)) {
            return null;
        }
        StringBuilder ids = new StringBuilder();
        // 查询医疗基本信息
        HospitalBaseInfo hospitalBaseInfo = hospitalBaseInfoMapper.selectHospitalByApplyNo(queryBaseConditionDTO);

        HospitalBaseInfoDetailVO hospitalBaseInfoDetailVO = new HospitalBaseInfoDetailVO();
        if (Objects.isNull(hospitalBaseInfo)) {
            return hospitalBaseInfoDetailVO;
        }
        if (CharSequenceUtil.isEmpty(applyNo)) {
            applyNo = hospitalBaseInfo.getApplyNo();
        }
        // 工商营业执照
        if (CharSequenceUtil.isNotEmpty(hospitalBaseInfo.getBusinessLicenseId())) {
            ids.append(hospitalBaseInfo.getBusinessLicenseId());
        }

        // 添加PDF文件id
        if (CharSequenceUtil.isNotEmpty(hospitalBaseInfo.getSignFileId())) {
            ids.append(",");
            ids.append(hospitalBaseInfo.getSignFileId());
        }

        // 查询被授权人信息
        HospitalAuthContact hospitalAuthContact = hospitalAuthContactMapper.selectHospitalAuthContactByApplyNo(applyNo);
        if (Objects.nonNull(hospitalAuthContact)) {
            // 证件照附件
            if (CharSequenceUtil.isNotEmpty(hospitalAuthContact.getAuthContactPhoto())) {
                ids.append(",");
                ids.append(hospitalAuthContact.getAuthContactPhoto());
            }
            // 医院授权书附件
            if (CharSequenceUtil.isNotEmpty(hospitalAuthContact.getHospitalCertificateAuth())) {
                ids.append(",");
                ids.append(hospitalAuthContact.getHospitalCertificateAuth());
            }
        }

        // 查询法人信息
        HospitalLegalPerson hospitalLegalPerson = hospitalLegalPersonMapper.selectHospitalLegalPersonByApplyNo(applyNo);
        if (Objects.nonNull(hospitalLegalPerson)
                && hospitalLegalPerson.getLegalPersonPhoto() != null) {
            ids.append(",");
            ids.append(hospitalLegalPerson.getLegalPersonPhoto());
        }

        // 查询科室信息
        List<HospitalDepartment> hospitalDepartmentList = hospitalDepartmentMapper.selectHospitalDepartmentByApplyNo(applyNo);

        // 查询专属科室信息
        List<HospitalDepartment> hospitalExclusiveDepartmentList = hospitalDepartmentMapper.selectHospitalExclusiveDepartmentByApplyNo(applyNo);
        if (CollectionUtils.isNotEmpty(hospitalExclusiveDepartmentList)) {
            hospitalDepartmentList.addAll(hospitalExclusiveDepartmentList);
        }

        // 查询证书信息
        List<CertificateAbility> certificateAbilityList = certificateAbilityMapper.selectCertificateAbilityByProcessCode(applyNo);
        certificateAbilityList.forEach(certificateAbility -> {
            if (certificateAbility.getFileId() != null) {
                ids.append(",");
                ids.append(certificateAbility.getFileId());
            }
        });

        // 基本数据PDF文件id
        if (CharSequenceUtil.isNotEmpty(hospitalBaseInfo.getSignFileId())) {
            ids.append(",");
            ids.append(hospitalBaseInfo.getSignFileId());
        }

        List<FileInfoDTO> fileInfoDTOList = iUploadFileInfoService.getUploadFileInfoByIds(ids.toString());
        List<FileInfoVO> fileInfoVOList = commonService.fileInfoDtoToVo(fileInfoDTOList);
        Map<String, List<FileInfoVO>> fileDetailMap = fileInfoVOList.stream().collect(Collectors.groupingBy(FileInfoVO::getFileId));


        hospitalBaseInfoDetailVO.setHospitalBaseInfo(hospitalBaseInfo);
        hospitalBaseInfoDetailVO.setHospitalAuthContact(hospitalAuthContact);
        hospitalBaseInfoDetailVO.setHospitalLegalPerson(hospitalLegalPerson);
        hospitalBaseInfoDetailVO.setHospitalDepartmentList(hospitalDepartmentList);
        hospitalBaseInfoDetailVO.setCertificateAbilityList(certificateAbilityList);
        hospitalBaseInfoDetailVO.setFileDetails(fileDetailMap);
        return hospitalBaseInfoDetailVO;
    }

    @Override
    public HospitalBaseInfoDTO bingTangHospitalDetail(TempHosInfoRequest request) {
        // 免登查询，获取redis校验码校验权限
        this.checkTempCode(request);
        QueryBaseConditionDTO conditionDTO = new QueryBaseConditionDTO();
        conditionDTO.setPracticeLicenseNo(request.getPracticeLicenseNo());
        return queryHospitalDetail(conditionDTO);
    }

    private void checkTempCode(AgainHosInfoSubmitRequest request) {
        boolean checkFlag = true;
        try {
            checkTempCode(request.getTempHosInfo());
        } catch (Exception exception) {
            checkFlag = false;
        }

        if (checkFlag) {
            return;
        }

        HospitalBaseInfo hospitalBaseInfo = request.getHospitalBaseInfo();
        String verifyCode = request.getTempHosInfo().getVerifyCode();
        String verifyKey = null;
        if (null != hospitalBaseInfo && StringUtils.isNotBlank(hospitalBaseInfo.getApplyNo()) && StringUtils.isNotBlank(verifyCode)) {
            verifyKey = hospitalBaseInfo.getApplyNo() + ":" + verifyCode;
            Object cacheObject = redisCache.getCacheObject(verifyKey);
            if (verifyCode.equals(cacheObject)) {
                return;
            }
        }
        log.error("非法操作! request.getHospitalBaseInfo()不能为空或hospitalBaseInfo.getApplyNo()或request.getTempHosInfo().getVerifyCode()不能为空" +
                "或根据verifyKey：hospitalBaseInfo.getApplyNo() + : + verifyCode【{}】获取缓存数据不能为空；入参为：【{}】", verifyKey, JSON.toJSON(request));
        throw new ServiceException("非法操作!");
    }

    /**
     * 临时查询详情，校验code
     *
     * @param request 入参
     */
    private void checkTempCode(TempHosInfoRequest request) {
        String queryHosInfoCode = request.getQueryHosInfoCode();

        if (StringUtils.isNotBlank(queryHosInfoCode)) {
            // 获取redis保存的临时code
            String practiceLicenseNo = request.getPracticeLicenseNo();
            String redisKey = Constants.TEMP_QRY_HOS_INFO_KEY_PRE + practiceLicenseNo;
            String redisTempQueryCode = redisCache.getCacheObject(redisKey);
            if (queryHosInfoCode.equals(redisTempQueryCode)) {
                return;
            }
        }

        // 增加医院通过邮箱验证码获取验证码查询医院详情
        // 判断当前医院登记号时候和邮箱匹配
        QueryBaseConditionDTO conditionDTO = new QueryBaseConditionDTO();
        conditionDTO.setPracticeLicenseNo(request.getPracticeLicenseNo());
        String verifyKey = null;
        HospitalBaseInfo hosInfo = hospitalBaseInfoMapper.selectHospitalByApplyNo(conditionDTO);
        if (hosInfo != null && StringUtils.equals(request.getEmail(), hosInfo.getContactsEmail())) {
            verifyKey = Constants.EMAIL_CODE_KEY + request.getEmail() + request.getEmailCode();
            Object captcha = redisCache.getCacheObject(verifyKey);

            if (captcha != null) {
                return;
            }
        }
        log.error("非法操作!查询医院信息【{}】不能为空，或入参【{}】与医院信息【hosInfo.getEmail】邮箱不匹配," +
                        "或根据verifyKey:Constants.EMAIL_CODE_KEY + request.getEmail() + request.getEmailCode()【{}】获取缓存验证码为空",
                JSON.toJSONString(hosInfo), request.getEmail(), verifyKey);
        throw new ServiceException("非法操作!");
    }

    @Override
    public void hospitalListExport(QueryHospitalListDTO queryHospitalListDTO, HttpServletResponse response) {
        long time = System.currentTimeMillis();
        //创建sheet集合，用来存储sheet
        List<SheetDTO> sheetList = new ArrayList<>();
        //查询医院列表信息，封装医院applyNo
        List<QueryHospitalListDTO> list = this.queryHospitalList(queryHospitalListDTO);
        if (CollectionUtil.isEmpty(list)) {
            log.info("医院信息审核列表导出功能-查询医院列表信息为空！");
            throw new ServiceException("查询的数据为空，不导出文件！");
        }
        List<String> applyNoList = list.stream().map(QueryHospitalListDTO::getApplyNo).collect(Collectors.toList());
        //清空内存
        list.removeAll(list);

        //查询列表相关医院的基本信息
        List<HospitalBaseInfosDTO> hospitalBaseInfoList = hospitalBaseInfoMapper.selectBatchHospitalBaseInfoList(applyNoList);
        //查询相关医院临床服务信息
        List<HospitalDepartment> clinicalServicesList = hospitalDepartmentMapper.selectClinicalServicesByApplyNos(applyNoList);
        //查询相关医院医技服务信息
        List<HospitalDepartment> medicalServiceList = hospitalDepartmentMapper.selectMedicalServiceByApplyNos(applyNoList);
        log.info("医院信息审核列表导出功能-获取数据库数据耗时：{}ms", (System.currentTimeMillis() - time));
        if (CollectionUtil.isEmpty(hospitalBaseInfoList) && CollectionUtil.isEmpty(clinicalServicesList) && CollectionUtil.isEmpty(medicalServiceList)) {
            log.info("医院信息审核列表导出功能-查询列表相关医院信息为空！");
            throw new ServiceException("查询的数据为空，不导出文件！");
        }

        time = System.currentTimeMillis();
        hospitalBaseInfoList.forEach(hospitalBaseInfo -> {
            //医疗结构类别:1:综合医院 2:专科医院
            hospitalBaseInfo.setHospitalTypeCZ(HospitalBaseInfosDTO.hospitalTypeMap.get(hospitalBaseInfo.getHospitalType().toString()));
            //所有制形式:1全民 2集体 3私人 4股份 5中外合资（合作）6其他
            hospitalBaseInfo.setFormOwnershipCZ(HospitalBaseInfosDTO.formOwnershipMap.get(hospitalBaseInfo.getFormOwnership().toString()));
            //经营性质:1政府举办非营利性 2非政府举办非营利性 3营利性
            hospitalBaseInfo.setNatureOperationCZ(HospitalBaseInfosDTO.natureOperationMap.get(hospitalBaseInfo.getNatureOperation().toString()));
            //管理隶属关系:1部属（管）2省自治区、直辖市属 3省辖市区、地辖市属 4县(旗)属 5其他
            hospitalBaseInfo.setManagementAffiliationCZ(HospitalBaseInfosDTO.managementAffiliationMap.get(hospitalBaseInfo.getManagementAffiliation().toString()));
            //医院级别:1一级医院 2二级医院 3三级医院
            hospitalBaseInfo.setHospitalLevelCZ(HospitalBaseInfosDTO.hospitalLevelMap.get(hospitalBaseInfo.getHospitalLevel().toString()));
            //教学类别:1大学直属附属医院 2大学非直属附属医院 3教学医院 4其他
            hospitalBaseInfo.setTeachingCategoryCZ(HospitalBaseInfosDTO.teachingCategoryMap.get(hospitalBaseInfo.getTeachingCategory().toString()));
        });
        clinicalServicesList.forEach(clinicalServices -> {
            clinicalServices.setInpatientServiceCZ(HospitalDepartment.serviceMap.get(clinicalServices.getInpatientService()));
            clinicalServices.setOutpatientServiceCZ(HospitalDepartment.serviceMap.get(clinicalServices.getOutpatientService()));
        });
        medicalServiceList.forEach(medicalService -> {
            medicalService.setInpatientServiceCZ(HospitalDepartment.serviceMap.get(medicalService.getInpatientService()));
            medicalService.setOutpatientServiceCZ(HospitalDepartment.serviceMap.get(medicalService.getOutpatientService()));
        });

        log.info("医院信息审核列表导出功能-遍历封装中文数据耗时：{}ms", (System.currentTimeMillis() - time));

        //封装医院基本信息数据到sheet1
        // 设置只导出有hospitalBaseInfo别名的字段
        LinkedHashMap<String, String> map = new LinkedHashMap<>();
        map.put("hospitalName", "医疗机构名称");
        map.put("applyNo", "认证编号");
        map.put("businessTime", "开业日期");
        map.put("practiceLicenseNo", "医疗机构执业许可证登记号");
        map.put("hospitalAddress", "医疗机构地址");
        map.put("hospitalPhone", "医疗机构电话");
        map.put("hospitalFax", "医疗机构传真");
        map.put("hospitalOfficialWebsite", "医疗机构官网");
        map.put("postalOde", "邮政编码");
        map.put("hospitalContacts", "联系人");
        map.put("contactsPhone", "联系人电话");
        map.put("contactsEmail", "联系人邮箱");
        map.put("postalAddress", "通讯地址");
        map.put("hospitalTypeCZ", "医疗结构类别:1:综合医院 2:专科医院");
        map.put("formOwnershipCZ", "所有制形式:1全民 2集体 3私人 4股份 5中外合资（合作）6其他");
        map.put("formOwnershipOther", "所有制形式:其他");
        map.put("natureOperationCZ", "经营性质:1政府举办非营利性 2非政府举办非营利性 3营利性");
        map.put("managementAffiliationCZ", "管理隶属关系:1部属（管）2省自治区、直辖市属 3省辖市区、地辖市属 4县(旗)属 5其他");
        map.put("managementAffiliationOther", "管理隶属关系:其他");
        map.put("competentAuthorityName", "主管单位名称");
        map.put("hospitalLevelCZ", "医院级别:1一级医院 2二级医院 3三级医院");
        map.put("teachingCategoryCZ", "教学类别:1大学直属附属医院 2大学非直属附属医院 3教学医院 4其他");
        map.put("teachingCategoryOther", "教学类别:其他");
        map.put("clinicalDepartmentNum", "临床科室总数（个）");
        map.put("inpatientAreaNum", "病区总数（个）");
        map.put("medicalTechnologyNum", "医技科室总数（个）");
        map.put("areaCovered", "占地面积：m2");
        map.put("areaArchitecture", "总建筑面积：m2");
        map.put("areaBusinessArchitecture", "业务用房建筑面积m2");
        map.put("preparationBed", "编制床位（《医疗机构执业许可证》时核准的床位数（个）");
        map.put("actualBed", "实际开放总床位数:（个）");
        map.put("bedUtilization", "病床使用率：%");
        map.put("onDutyNum", "全院在岗人员（人）");
        map.put("healthTechnologyNum", "卫生技术人员（人）");
//        map.put("businessLicenseId", "工商营业执照附件");
//        map.put("hospitalCertificate", "医疗机构证书");
//        map.put("first", "是否初次认证:1是 2不是");
//        map.put("beforeReviewerDate", "上次评审时间");
//        map.put("beforeReviewerConclusionGrade", "评审结果:1一级医院 2二级医院 3三级医院");
//        map.put("beforeReviewerConclusionLevel", "评审结果:1甲 2乙 3丙 4未定");
//        map.put("expectReviewDate", "期望评审日期");
//        map.put("signConfirmStatus", "医院端PDF签章确认状态0:未确认 1已确认");
//        map.put("signFileId", "医院端PDF签章文件id");
//        map.put("authStatus", "认证状态:1待审核 2审核通过 3审核拒绝");
//        map.put("authDesc", "评审说明");
//        map.put("authPersonId", "审核人id");
//        map.put("authPersonName", "审核人姓名");
//        map.put("status", "状态:1可用 2不可用");
        //法人信息
        map.put("legalPersonName", "法人姓名");
        map.put("gender", "法人性别(1:男；2:女；3:其他)");
        map.put("legalPersonPost", "法人职务");
        map.put("legalPersonTitle", "法人职称");
        map.put("legalPersonPhone", "法人电话");
        map.put("legalPersonFax", "法人传真");
        map.put("legalPersonMobile", "法人手机");
        map.put("legalPersonEmail", "法人邮箱");
        //医院被授权人信息
        map.put("authContactName", "被授权人姓名");
        map.put("authContactMobile", "被授权人手机号");
        map.put("authContactEmail", "被授权人邮箱");
        map.put("contactAddress", "被授权人联系地址");
        String name = "医院基本信息";
        SheetDTO sheet1 = new SheetDTO();
        sheet1.setFieldAndAlias(map);
        sheet1.setSheetName(name);
        sheet1.setCollection(hospitalBaseInfoList);
        sheetList.add(sheet1);

        //封装医院基本临床服务和医技服务信息到sheet2
        LinkedHashMap<String, String> map2 = new LinkedHashMap<>();
        map2.put("hospitalName", "医疗机构名称");
        map2.put("departmentName", "科室名称（临床服务）");
        map2.put("outpatientServiceCZ", "是否提供门诊服务:1提供 2不提供");
        map2.put("inpatientServiceCZ", "是否提供住院服务:1提供 2不提供");
        map2.put("departmentDesc", "备注（开设少于3年的科室请注明）");
        String name2 = "临床服务信息";
        SheetDTO sheet2 = new SheetDTO();
        sheet2.setFieldAndAlias(map2);
        sheet2.setSheetName(name2);
        sheet2.setCollection(clinicalServicesList);
        sheetList.add(sheet2);

        //封装医院基本临床服务和医技服务信息到sheet3
        LinkedHashMap<String, String> map3 = new LinkedHashMap<>();
        map3.put("hospitalName", "医疗机构名称");
        map3.put("departmentName", "科室名称（医技服务）");
        map3.put("outpatientServiceCZ", "是否提供门诊服务:1提供 2不提供");
        map3.put("inpatientServiceCZ", "是否提供住院服务:1提供 2不提供");
        map3.put("departmentDesc", "备注（开设少于3年的科室请注明）");
        String name3 = "医技服务信息";
        SheetDTO sheet3 = new SheetDTO();
        sheet3.setFieldAndAlias(map3);
        sheet3.setSheetName(name3);
        sheet3.setCollection(medicalServiceList);
        sheetList.add(sheet3);

        time = System.currentTimeMillis();
        HutoolExcelUtils.exportExcel(response, sheetList, "认证申请医院名单");
        log.info("医院信息审核列表导出功能-导出文件耗时：{}ms", (System.currentTimeMillis() - time));
    }

    @Override
    public int updateAuthStatusByApplyNo(AutRecord autRecord) {
        // 如果为审核通过，则通过文件不能为空
        Integer sc = 2;
        if (sc.equals(autRecord.getAudResult()) && StringUtils.isEmpty(autRecord.getSignFileId())) {
            throw new ServiceException("审核通过signFileId不能为空", 400);
        }

        // 审核结果，邮件和短信通知客户
        this.notifyCustomer(autRecord);

        HospitalBaseInfo hospitalBaseInfo = new HospitalBaseInfo();
        hospitalBaseInfo.setApplyNo(autRecord.getAccountId());
        hospitalBaseInfo.setAuthStatus(autRecord.getAudResult());
        hospitalBaseInfo.setAuthDate(DateUtil.now());
        hospitalBaseInfo.setAuthPersonId(autRecord.getAudId());
        hospitalBaseInfo.setAuthDesc(autRecord.getAudDesc());

        // 当审核文件不为空的时候则覆盖之前的文件
        if (StringUtils.isNotEmpty(autRecord.getSignFileId())) {
            hospitalBaseInfo.setSignFileId(autRecord.getSignFileId());
        }

        // 获取当前管理员姓名
        String nickName = SecurityUtils.getLoginUser().getUser().getNickName();
        hospitalBaseInfo.setAuthPersonName(nickName);
        return hospitalBaseInfoMapper.updateHospitalBaseInfo(hospitalBaseInfo);
    }

    @Override
    public int updateHospitalBaseInfo(HospitalBaseInfo hospitalBaseInfo) {
        return hospitalBaseInfoMapper.updateHospitalBaseInfo(hospitalBaseInfo);
    }

    @Override
    public void notifyHosAccount(String applyNo, String userName, String password) {
        // 查询邮箱和手机号码
        HospitalBaseInfo hospitalBaseInfo = this.selectHospitalBaseInfoByApplyNo(applyNo);
        String contactsEmail = hospitalBaseInfo.getContactsEmail();
        String contactsPhone = hospitalBaseInfo.getContactsPhone();

        // 查询消息模板message_template表，通过模板固定使用id 2查询
        Long id = 1L;
        MessageTemplate messageTemplate = messageTemplateMapper.selectMessageTemplateById(id);
        if (Objects.isNull(messageTemplate)) {
            throw new ServiceException(String.format("id[%s]消息模板数据为空", id), 500);
        }

        String content = messageTemplate.getContent();
        String msg = String.format(content, hospitalBaseInfo.getHospitalName(), userName, password);
        if (StringUtils.isNotEmpty(contactsEmail)) {
            messageSendRecordService.sendMsgFromEmail(msg, contactsEmail, "医院账号密码分配通知");
        }

        try {
            if (StringUtils.isNotEmpty(contactsPhone)) {
                messageSendRecordService.sendMsgFromSms(msg, contactsPhone);
            }
        } catch (Exception e) {
            log.error("创建【{}】医院账号时，发送手机短信失败，不抛异常正常创建账号，通知的手机号为：{}，内容为：{},异常信息e为:{}",
                    hospitalBaseInfo.getHospitalName(), contactsPhone, msg, e);
        }
    }

    private HospitalBaseInfo selectHospitalBaseInfoByApplyNo(String applyNo) {
        QueryBaseConditionDTO queryBaseConditionDTO = new QueryBaseConditionDTO();
        queryBaseConditionDTO.setCommonId(applyNo);
        HospitalBaseInfo hospitalBaseInfo = hospitalBaseInfoMapper.selectHospitalByApplyNo(queryBaseConditionDTO);
        if (Objects.isNull(hospitalBaseInfo)) {
            throw new ServiceException(String.format("applyNo[%s]查询医疗机构数据为空", applyNo));
        }
        return hospitalBaseInfo;
    }

    /**
     * 通知医院审核结果
     *
     * @param autRecord 入参
     */
    private void notifyCustomer(AutRecord autRecord) {
        // 审核通过为2
        Integer sc = 2;
        // 审核拒绝为3
        Integer rej = 3;

        // 查询邮箱和手机号码
        HospitalBaseInfo hospitalBaseInfo = this.selectHospitalBaseInfoByApplyNo(autRecord.getAccountId());
        String contactsEmail = hospitalBaseInfo.getContactsEmail();
        String contactsPhone = hospitalBaseInfo.getContactsPhone();
        // 审核成功
        if (sc.equals(autRecord.getAudResult())) {
            this.doNotifyCustomerHosPass(contactsEmail, contactsPhone);
        }
        // 审核拒绝
        if (rej.equals(autRecord.getAudResult())) {
            this.doNotifyCustomerHosRej(contactsEmail, contactsPhone, hospitalBaseInfo.getPracticeLicenseNo());
        }
    }

    /**
     * 通知医院审核成功
     *
     * @param contactsEmail 邮箱
     * @param contactsPhone 手机号码
     */
    private void doNotifyCustomerHosPass(String contactsEmail, String contactsPhone) {
        // 查询消息模板message_template表，通过模板固定使用id 1查询
        Long id = 2L;
        MessageTemplate messageTemplate = messageTemplateMapper.selectMessageTemplateById(id);
        if (Objects.isNull(messageTemplate)) {
            throw new ServiceException(String.format("id[%s]消息模板数据为空", id), 500);
        }
        String content = messageTemplate.getContent();
        if (StringUtils.isNotEmpty(contactsEmail)) {
            messageSendRecordService.sendMsgFromEmail(content, contactsEmail, "医院申请审核结果通知");
        }
        if (StringUtils.isNotEmpty(contactsPhone)) {
            messageSendRecordService.sendMsgFromSms(content, contactsPhone);
        }
    }

    /**
     * 通知医院审核被拒绝
     *
     * @param contactsEmail     邮箱
     * @param contactsPhone     联系方式
     * @param practiceLicenseNo 医疗机构社会信用代码
     */
    private void doNotifyCustomerHosRej(String contactsEmail, String contactsPhone, String practiceLicenseNo) {
        String code = NumberGenUtils.getVerificationCode();
        String msgContent = this.getMessageContent(code);
        // 存入redis
        String redisKey = Constants.TEMP_QRY_HOS_INFO_KEY_PRE + practiceLicenseNo;
        // 获取有效期
        String time = configService.selectConfigByKey("hos.rej.again.submit.time");
        time = StringUtils.isBlank(time) ? "7" : time;
        redisCache.setCacheObject(redisKey, code, Integer.valueOf(time), TimeUnit.DAYS);
        if (StringUtils.isNotEmpty(contactsEmail)) {
            messageSendRecordService.sendMsgFromEmail(msgContent, contactsEmail, "医院信息审核结果通知");
        }
        if (StringUtils.isNotEmpty(contactsPhone)) {
            messageSendRecordService.sendMsgFromSms(msgContent, contactsPhone);
        }
    }

    /**
     * 获取信息内容
     *
     * @return 信息内容
     */
    private String getMessageContent(String code) {
        // 您好，您在深圳卫健医院评审评价平台提交的评审人员申请已被拒绝，如需重新提交，请点击以下链接进行操作：xxx
        // 配置短信模板
        String msgTem = configService.selectConfigByKey("hos.rej.again.msg.tem");
        String path = configService.selectConfigByKey("hos.rej.again.path");
        path = path.endsWith("/") ? path + code : path + "/" + code;
        return String.format(msgTem, path);
    }

    @Override
    public Map<String, Object> qryHosStatus(QryHosStatusRequest request) {
        QueryBaseConditionDTO queryBaseConditionDTO = new QueryBaseConditionDTO();
        queryBaseConditionDTO.setPracticeLicenseNo(request.getPracticeLicenseNo());
        HospitalBaseInfo hospitalBaseInfo = hospitalBaseInfoMapper.selectHospitalByApplyNo(queryBaseConditionDTO);
        Map<String, Object> result = new HashMap<>();
        if (Objects.isNull(hospitalBaseInfo)) {
            return result;
        }
        result.put("applyNo", hospitalBaseInfo.getApplyNo());
        result.put("signConfirmStatus", hospitalBaseInfo.getSignConfirmStatus());
        result.put("authStatus", hospitalBaseInfo.getAuthStatus());
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult hosAgainSubmit(AgainHosInfoSubmitRequest request) {
        // 校验当前验证码是否正确
        this.checkTempCode(request);
        // 校验当前applyNo与社会信用编码是否匹配
        HospitalBaseInfo hospitalBaseInfo = request.getHospitalBaseInfo();
        if (Objects.isNull(hospitalBaseInfo) || StringUtils.isEmpty(hospitalBaseInfo.getApplyNo())) {
            throw new ServiceException("applyNo不能为空");
        }
        String applyNo = hospitalBaseInfo.getApplyNo();
        QueryBaseConditionDTO queryBaseConditionDTO = new QueryBaseConditionDTO();
        queryBaseConditionDTO.setCommonId(applyNo);
        HospitalBaseInfo hosInfo = hospitalBaseInfoMapper.selectHospitalByApplyNo(queryBaseConditionDTO);
        if (Objects.isNull(hosInfo)) {
            throw new ServiceException(String.format("applyNo[%s]查询医疗机构详情为空", applyNo));
        }
        if (!request.getTempHosInfo().getPracticeLicenseNo().equals(hosInfo.getPracticeLicenseNo())) {
            throw new ServiceException(String.
                    format("applyNo[%s] practiceLicenseNo[%s]不匹配", applyNo, request.getTempHosInfo().getPracticeLicenseNo()));
        }
        // 删除之前的旧数据
        this.delHosOldData(applyNo);
        // 保存数据
        AjaxResult ajaxResult = doSaveHosBaseInfo(request);

        // 保存成功删除redis验证码
        //提交接口，在最终上传完文件时，失效缓存校验数据，此时返回页面再操作时校验住
        if (ObjectUtil.equal(Constants.STR_NUM_1, request.getSignFlag())) {
            String k1 = Constants.TEMP_QRY_HOS_INFO_KEY_PRE + hosInfo.getPracticeLicenseNo();
            String k2 = Constants.EMAIL_CODE_KEY + request.getTempHosInfo().getEmail() + request.getTempHosInfo().getEmailCode();
            String k3 = applyNo + ":" + request.getTempHosInfo().getVerifyCode();
            redisCache.deleteObject(Arrays.asList(k1, k2, k3));
        }
        return ajaxResult;
    }

    @Override
    public HospitalBaseInfo queryCurHosBaseInfo() {
        // 获取当前登录用户id
        Long userId = SecurityUtils.getUserId();
        SysUserHospital sysUserHospital = sysUserHospitalMapper.selectSysUserHospitalByUserId(userId);
        if (Objects.isNull(sysUserHospital)) {
            throw new ServiceException("当前登录用户，无医院关联数据", 500);
        }
        String applyNo = sysUserHospital.getHospitalApplyNo();
        QueryBaseConditionDTO queryBaseConditionDTO = new QueryBaseConditionDTO();
        queryBaseConditionDTO.setCommonId(applyNo);
        return hospitalBaseInfoMapper.selectHospitalByApplyNo(queryBaseConditionDTO);
    }

    @Override
    public List<HospitalBaseInfo> qryPassHosInfo() {
        return hospitalBaseInfoMapper.qryPassHosInfo();
    }

    @Override
    public void sendEmailCode(SendEmailCodeVo sendEmailCodeVo) {
        if (StringUtils.isBlank(sendEmailCodeVo.getApplyNo())) {
            throw new ServiceException("医院编号不能为空");
        }
        if (StringUtils.isBlank(sendEmailCodeVo.getToEmailNo())) {
            throw new ServiceException("邮箱不能为空");
        }
        if (StringUtils.isBlank(sendEmailCodeVo.getEmailTitle())) {
            throw new ServiceException("邮箱title不能为空");
        }

        //邮箱验证的弹窗点击获取验证码判断输入的邮箱是否与登记证号有关联
        // 邮箱与登记证号无关联，提示"请输入正确的医院联系邮箱”;
        QueryBaseConditionDTO queryBaseConditionDTO = new QueryBaseConditionDTO();
        queryBaseConditionDTO.setCommonId(sendEmailCodeVo.getApplyNo());
        HospitalBaseInfo hospitalBaseInfo = hospitalBaseInfoMapper.selectHospitalByApplyNo(queryBaseConditionDTO);
        if (hospitalBaseInfo == null || ObjectUtil.notEqual(hospitalBaseInfo.getContactsEmail(), sendEmailCodeVo.getToEmailNo())) {
            throw new ServiceException("无法获取验证码，请输入医院申请时填写的联系邮箱");
        }

        sendEmailCodeVo.setRoleEnum(AutSaAudRoleEnum.HOSPITAL);
        iSysUserService.sendEmailCode(sendEmailCodeVo);

    }

    /**
     * 删除旧数据
     *
     * @param applyNo applyNo
     */
    private void delHosOldData(String applyNo) {
        hospitalBaseInfoMapper.deleteHospitalBaseInfoByApplyNo(applyNo);
        hospitalAuthContactMapper.deleteHospitalAuthContactByApplyNo(applyNo);
        hospitalLegalPersonMapper.deleteHospitalLegalPersonByApplyNo(applyNo);
        hospitalDepartmentMapper.deleteHospitalDepartmentByApplyNo(applyNo);
        hospitalDepartmentMapper.deleteHospitalExclusiveDepartmentByApplyNo(applyNo);
        certificateAbilityMapper.deleteCertificateAbilityByProcessCode(applyNo);
    }

    @Override
    public List<HosReviewPlanVO> queryReviewPlanList(QueryHosReviewPlanDTO queryHosReviewPlanDTO) {
        if (Objects.isNull(queryHosReviewPlanDTO)) {
            queryHosReviewPlanDTO = new QueryHosReviewPlanDTO();
        }
        // 查询已经可以分配的医疗机构信息
        // 获取当前登录用户角色
        SysRole sysRole = commonService.selectSysRole();
        log.info("当前登录角色key:{}", sysRole.getRoleKey());
        if (Constants.HospitalConstants.ROLE_HOSPITAL.equals(sysRole.getRoleKey())) {
            // 医院端
            String applyNo = selectApplyNoByUserId();
            queryHosReviewPlanDTO.setApplyNo(applyNo);
            return queryReviewPlan(queryHosReviewPlanDTO);
        }
        if (Constants.HospitalConstants.ROLE_ADMIN.equals(sysRole.getRoleKey()) ||
                Constants.HospitalConstants.ROLE_COMMON_ADMIN.equals(sysRole.getRoleKey())) {
            queryHosReviewPlanDTO.setRoleKey(HospitalConstants.ROLE_COMMON_ADMIN);
            return queryReviewPlan(queryHosReviewPlanDTO);
        }
        return new ArrayList<>();
    }


    private List<HosReviewPlanVO> queryReviewPlan(QueryHosReviewPlanDTO queryHosReviewPlanDTO) {
        PageUtils.startPage();
        List<HosReviewPlanVO> hosReviewPlanVOList = hospitalBaseInfoMapper.selectHosPlanList(queryHosReviewPlanDTO);
        hosReviewPlanVOList.forEach(hosReviewPlanVO -> {
            // 针对每一个医疗机构的信息查询其初审员和评审员信息
            String applyNo = hosReviewPlanVO.getApplyNo();
            // 查询评审周期
            List<HospitalReviewCycle> hospitalReviewCycleList = iHospitalReviewCycleService.selectHospitalReviewCycleByApplyNo(applyNo);
            if (CollUtil.isNotEmpty(hospitalReviewCycleList)) {
                hosReviewPlanVO.setCycleDisComplete(Constants.HospitalConstants.STR_NUM_1);
                hosReviewPlanVO.setHospitalReviewCycleList(hospitalReviewCycleList);
            } else {
                hosReviewPlanVO.setCycleDisComplete(Constants.HospitalConstants.STR_NUM_2);
            }

            // 查询初审员列表
            List<HosPlanUserInfoVO> preExamList =
                    iHospitalPreExamService.selectHosPlanUserInfoByApplyNo(applyNo);
            hosReviewPlanVO.setPreExamList(preExamList);

            // 查询评审员列表
            Map<String, List<HosPlanUserInfoVO>> reviewerListMap =
                    iHospitalReviewerService.selectHosPlanUserInfoByApplyNo(applyNo, null);
            hosReviewPlanVO.setReviewerList(reviewerListMap.get("review"));

            // 设置资深评审员
            hosReviewPlanVO.setSeniorReviewerUser(reviewerListMap.get("seniorReview"));

            // 设置评审学员
            hosReviewPlanVO.setTraineesList(reviewerListMap.get("traReview"));

            // 组装对应文件数据
            Map<String, List<FileInfoVO>> fileDetailMap = new HashMap<>();
            if (StrUtil.isNotEmpty(hosReviewPlanVO.getRevFileId())) {
                // 取最后一个
                String[] fileIs = hosReviewPlanVO.getRevFileId().split(",");
                String fileId = fileIs[fileIs.length - 1];
                List<FileInfoDTO> fileInfoDTOList = iUploadFileInfoService.getUploadFileInfoByIds(fileId);
                List<FileInfoVO> fileInfoVOList = commonService.fileInfoDtoToVo(fileInfoDTOList);
                fileDetailMap.putAll(fileInfoVOList.stream().map(o -> {
                    o.setFileType(FtlToPdfEnum.HOS_REW_PLAN.getCode());
                    return o;
                }).collect(Collectors.groupingBy(FileInfoVO::getFileId)));
            }

            // 返回医疗机构当前有效的审核状态信息
            AutSaRelation autSaRelation = autSaRelationService.selectAutSaRelationByHospitalApplyNo(applyNo, false);
            if (ObjectUtil.isNotNull(autSaRelation)) {
                hosReviewPlanVO.setAnuStatus(autSaRelation.getAutStatus());
                hosReviewPlanVO.setAutCode(autSaRelation.getAutCode());
            }

            //封装验证评审报告文件数据
            //(谁可以下载验证评审报告：中心管理员、审查组长、评审组长,加验证评审员)
            SysUser sysUser = SecurityUtils.getSysUser();
            if (StringUtils.isNotBlank(hosReviewPlanVO.getAutCode()) && AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(sysUser.getRoleKey(),
                    AutSaAudRoleEnum.ADMIN, AutSaAudRoleEnum.COMMON_ADMIN)) {
                List<AutSaAudBusinessData> autSaAudBusinessData = autSaAudBusinessDataService.selectAutSaAudBusinessData(hosReviewPlanVO.getAutCode(),
                        AutSaAudBusinessCodeEnum.VERIFY_REVIEW_REPORT.getCode());
                log.info("查询验证评审报告信息：{}", JSON.toJSONString(autSaAudBusinessData));
                if (CollectionUtils.isNotEmpty(autSaAudBusinessData) && StringUtils.isNotBlank(autSaAudBusinessData.get(0).getData())) {
                    FileInfoVO fileInfoVO = JSON.parseObject(autSaAudBusinessData.get(0).getData(), FileInfoVO.class);

                    List<FileInfoVO> fileInfoVOList = commonService.fileInfoDtoToVo(uploadFileInfoService.getUploadFileInfoByIds(fileInfoVO.getFileId()));
                    fileInfoVOList.stream().forEach(o -> {
                        o.setFileType(AutSaAudBusinessCodeEnum.VERIFY_REVIEW_REPORT.getCode());
                    });
                    fileDetailMap.put(fileInfoVO.getFileId(), fileInfoVOList);
                }
            }
            hosReviewPlanVO.setFileDetailMap(fileDetailMap);

            //需要周期校验，这部分逻辑统一放到/hos/plan/clause/detail接口
//            if (!Constants.HospitalConstants.STR_NUM_1.equals(hosReviewPlanVO.getSeniorReviewDisComplete())) {
//                // 如果资深评审员列表未分配 查询对应可分配列表
//                List<HosPlanUserInfoVO> seniorReviewerList = iHospitalReviewerService.selectSeniorReviewerList(Constants.HospitalConstants.ROLE_SENIOR_ASSESSOR);
//                hosReviewPlanVO.setSeniorReviewerList(seniorReviewerList);
//            }
        });

        queryAutRelation(queryHosReviewPlanDTO, hosReviewPlanVOList);

        return hosReviewPlanVOList;
    }

    /**
     * 整合认证自评关联列表
     *
     * @param queryHosReviewPlanDTO 入参
     * @param hosReviewPlanVOList   入参
     */
    private void queryAutRelation(QueryHosReviewPlanDTO queryHosReviewPlanDTO,
                                  List<HosReviewPlanVO> hosReviewPlanVOList) {
        if (!HospitalConstants.ROLE_COMMON_ADMIN.equals(queryHosReviewPlanDTO.getRoleKey())
                || CollectionUtils.isEmpty(hosReviewPlanVOList)) {
            return;
        }

        for (HosReviewPlanVO hosReviewPlanVO : hosReviewPlanVOList) {
            StringBuilder meetReportFileIds = new StringBuilder();

            AutSaRelation autSaRelation = new AutSaRelation();
            autSaRelation.setHospitalApplyNo(hosReviewPlanVO.getApplyNo());
            List<AutSaRelationList> autSaRelationList = autSaRelationService.selectAllAutSaRelationList(autSaRelation);

            // 查询aut_sa_aud_business_data
            queryMeetReportFileId(autSaRelationList, meetReportFileIds);

            List<FileInfoDTO> fileInfoDTOList = iUploadFileInfoService.getUploadFileInfoByIds(meetReportFileIds.toString());
            List<FileInfoVO> fileInfoVOList = commonService.fileInfoDtoToVo(fileInfoDTOList);
            Map<String, List<FileInfoVO>> fileDetailMap = fileInfoVOList.stream().collect(Collectors.groupingBy(FileInfoVO::getFileId));
            Map<String, List<FileInfoVO>> fileDetailMapAll = hosReviewPlanVO.getFileDetailMap();
            if (MapUtil.isEmpty(fileDetailMapAll)) {
                hosReviewPlanVO.setFileDetailMap(fileDetailMap);
            } else {
                fileDetailMapAll.putAll(fileDetailMap);
            }

            hosReviewPlanVO.setAutSaRelationList(autSaRelationList);
        }
    }

    @Autowired
    private IAutSaAudBusinessDataService autSaAudBusinessDataService;

    /**
     * 查询会议纪要fileID
     *
     * @param autSaRelationList autSaRelationList
     * @param meetReportFileIds meetReportFileIds
     */
    private void queryMeetReportFileId(List<AutSaRelationList> autSaRelationList, StringBuilder meetReportFileIds) {
        if (CollectionUtils.isEmpty(autSaRelationList)) {
            return;
        }

        StringBuilder autCodes = new StringBuilder();
        for (AutSaRelationList saRelation : autSaRelationList) {
            autCodes.append(saRelation.getAutCode()).append(",");
        }

        List<AutSaAudBusinessData> autSaAudBusinessDataList = autSaAudBusinessDataService
                .selectAutSaAudBusinessData(autCodes.toString(), AutSaAudBusinessCodeEnum.MEET_REPORT.getCode());

        if (CollectionUtils.isEmpty(autSaAudBusinessDataList)) {
            return;
        }

        Map<String, String> autSaAudBusinessDataMap = autSaAudBusinessDataList.stream()
                .peek(autSaAudBusinessData -> meetReportFileIds.append(autSaAudBusinessData.getData()))
                .collect(Collectors.toMap(AutSaAudBusinessData::getAutCode, AutSaAudBusinessData::getData, (v1, v2) -> v2));

        for (AutSaRelationList saRelation : autSaRelationList) {
            String meetReportFileId = autSaAudBusinessDataMap.get(saRelation.getAutCode());
            saRelation.setMeetReportFileId(meetReportFileId);
        }
    }

    @Override
    public List<CstCertificationStandardVO> queryHosPlanClauseDetail(HosPlanDetailDTO hosPlanDetailDTO) {

        List<CstCertificationStandardVO> cstCertificationStandardVOList;
        // 判断是初审员还是评审员
        Integer personForm = hosPlanDetailDTO.getPersonForm();
        String applyNo = hosPlanDetailDTO.getApplyNo();
        String accountId = hosPlanDetailDTO.getAccountId();
        String fieldId = hosPlanDetailDTO.getFieldId();

        HospitalPlannedDistribution hospitalPlannedDistribution
                = iHospitalPlannedDistributionService.selectHospitalPlannedDistributionByApplyNo(applyNo);
        Long versionId;
        if (Objects.isNull(hospitalPlannedDistribution)) {
            versionId = iCstVersioningService.selectEffCstVersion();
        } else {
            versionId = hospitalPlannedDistribution.getVersionId();
        }

        String autCode = null;
        if (StringUtils.isNotEmpty(hosPlanDetailDTO.getAutCode())) {
            autCode = hosPlanDetailDTO.getAutCode();
        } else {
            AutSaRelation autSaRelation = autSaRelationService.selectAutSaRelationByHospitalApplyNo(applyNo, false);
            if (ObjectUtil.isNotEmpty(autSaRelation)) {
                autCode = autSaRelation.getAutCode();
            }
            hosPlanDetailDTO.setAutCode(autCode);
        }


        // 查询分配计划详情
        if (Constants.HospitalConstants.NUM_1.equals(personForm)) {
            // 如果是初审员
            if (CharSequenceUtil.isNotEmpty(accountId)) {
                // 如果accountId不为空则针对此用户查询对应条款信息
                HospitalPreExam hospitalPreExam = iHospitalPreExamService.selectHospitalPreExamByApplyNoAndAccountId(applyNo, accountId);
                // 获取初审员对应的条款信息
                String clauses = hospitalPreExam.getClauseList();
                PageUtils.startPage();
                cstCertificationStandardVOList = iCstCertificationStandardsService.selectCstCertificationStandardVOByIds(clauses);
            } else if (CharSequenceUtil.isNotEmpty(fieldId)) {
                // 对应得fieldId不为空->分组id不为空
                // 对应领域不为空直接通过此领域查询
                PageUtils.startPage();
                cstCertificationStandardVOList = iCstCertificationStandardsService.selectCstCertificationStandardsByDomainId(fieldId, versionId);
            } else {
                // 否则查询未分配的条款信息
                List<HospitalPreExam> hospitalPreExamList = hospitalPreExamMapper.selectHospitalPreExamByApplyNo(applyNo);
                // 查询有效版本
                StringBuilder clauses = new StringBuilder();
                hospitalPreExamList.forEach(hospitalPreExam ->
                        clauses.append(hospitalPreExam.getClauseList()).append(","));

                PageUtils.startPage();
                cstCertificationStandardVOList = iCstCertificationStandardsService.selectCstCertificationStandardVONoInIds(clauses.toString(), versionId);
            }
        } else if (Constants.HospitalConstants.NUM_2.equals(personForm)) {

            cstCertificationStandardVOList = queryReviewClauseInfo(hosPlanDetailDTO, versionId);
        } else {
            // 其他则抛异常
            throw new ServiceException(ServiceExceptionEnum.HOSPITAL_ERROR_1000009);
        }

        //封装条款不适用状态
        if (CollectionUtils.isNotEmpty(cstCertificationStandardVOList) && StringUtils.isNotBlank(autCode)) {
            //根据自评编码和不适应状态，查询不适用表，根据不适应款封装到对应的返参款下
            ReviewFitMoveClauseReq reviewFitMoveClauseReq = new ReviewFitMoveClauseReq();
            reviewFitMoveClauseReq.setAutCode(autCode);
            List<ReviewFitMoveClause> qryReviewFitMoveClauseList = reviewFitMoveClauseMapper.qryReviewFitMoveClauseList(reviewFitMoveClauseReq);
            Map<String, ReviewFitMoveClause> ReviewFitMoveClauseMap = qryReviewFitMoveClauseList.stream().collect(Collectors.toMap(ReviewFitMoveClause::getClauseId, o -> o));
            cstCertificationStandardVOList.stream().forEach(cstCertificationStandardVO -> {
                //判断是否有不适用款数据，用就取值，没有默认为0;条款不适用状态（0:否,1：是）
                ReviewFitMoveClause fitMoveClause = ReviewFitMoveClauseMap.getOrDefault(cstCertificationStandardVO.getClauseId().toString(), null);
                cstCertificationStandardVO.setFitStatus(fitMoveClause == null ? Constants.STR_NUM_0 : fitMoveClause.getFitStatus().toString());
                //挪动款封装
                if (fitMoveClause != null) {
                    cstCertificationStandardVO.setSmallThemeId(fitMoveClause.getSmallThemeId());
                    cstCertificationStandardVO.setThemeId(fitMoveClause.getThemeId());
                    cstCertificationStandardVO.setGroupId(fitMoveClause.getGroupId());
                }
            });
        }
        return cstCertificationStandardVOList;

    }

    /**
     * 获取款项详情
     *
     * @param hosPlanDetailDTO 入参
     * @param versionId        版本id
     * @return CstCertificationStandardVO
     */
    public List<CstCertificationStandardVO> queryReviewClauseInfo(HosPlanDetailDTO hosPlanDetailDTO, Long versionId) {
        List<CstCertificationStandardVO> result = new ArrayList<>();

        List<GroupClauseInfo> groupClauseInfoList = new ArrayList<>();
        String applyNo = hosPlanDetailDTO.getApplyNo();
        String accountId = hosPlanDetailDTO.getAccountId();
        String fieldId = hosPlanDetailDTO.getFieldId();


        // 如果是评审员
        if (CharSequenceUtil.isNotEmpty(accountId)) {
            // 如果用户id不为空，则查对应的评审员对应的领域以及对应的条款
            HospitalReviewer hospitalReviewer = hospitalReviewerMapper.selectHospitalReviewerByApplyNoAndAccountId(applyNo, accountId);
            if (hospitalReviewer == null) {
                throw new ServiceException("当前评审员无分配记录");
            }
            // 查询出对应的领域id和版本查询对应的条款
            groupClauseInfoList = cstCertificationStandardsMapper.selectGroupClause(hospitalReviewer.getFieldIdList(), versionId);
        } else if (CharSequenceUtil.isNotEmpty(fieldId)) {
            // 对应领域不为空直接通过此领域查询
            groupClauseInfoList = cstCertificationStandardsMapper.selectGroupClause(fieldId, versionId);
            // cstCertificationStandardVOList = iCstCertificationStandardsService.selectCstCertificationStandardsByDomainId(fieldId, versionId);
        } else if(ObjectUtil.equal(hosPlanDetailDTO.getIsLeader(),Constants.STR_NUM_1)){
            //是否为组长:1是 2不是
            //获取所有分组信息
            groupClauseInfoList = cstCertificationStandardsMapper.selectGroupClause(null, versionId);
        }

        Map<String, Set<String>> groupClauseIdSetMap = groupClauseInfoList.stream()
                .collect(Collectors.groupingBy(GroupClauseInfo::getGroupId, Collectors.mapping(GroupClauseInfo::getClauseId, Collectors.toSet())));

        // 增加挪动逻辑
        reviewFitMoveClauseService.moveClause(hosPlanDetailDTO.getAutCode(), groupClauseIdSetMap);

        // 挪动逻辑，判断每组下个款有哪些。
        Set<String> allClauseIdSet = new HashSet<>();
        for (Entry<String, Set<String>> entry : groupClauseIdSetMap.entrySet()) {
            Set<String> clauseIdSet = entry.getValue();
            allClauseIdSet.addAll(clauseIdSet);
        }

        List<CstCertificationStandardVO> cstCertificationStandardVOList = cstCertificationStandardsMapper.selectCstCertificationStandardsByClauseIds(allClauseIdSet, versionId);
        Map<String, CstCertificationStandardVO> cstClauseIdMap = Optional.ofNullable(cstCertificationStandardVOList)
                .orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.toMap(cst -> String.valueOf(cst.getClauseId()), v -> v, (v1, v2) -> v2));

        for (Entry<String, Set<String>> entry : groupClauseIdSetMap.entrySet()) {
            String groupId = entry.getKey();
            Set<String> clauseIdSet = entry.getValue();
            for (String clauseId : clauseIdSet) {
                CstCertificationStandardVO cstCertificationStandardVO = cstClauseIdMap.get(clauseId);
                if (cstCertificationStandardVO != null) {
                    cstCertificationStandardVO.setDomainId(groupId);
                    result.add(cstCertificationStandardVO);
                }
            }
        }
        return result;
    }

    @Override
    public HosPlanDetailVO queryHosPlanDetail(HosPlanDetailDTO hosPlanDetailDTO) {
        HosPlanDetailVO hosPlanDetailVO = new HosPlanDetailVO();
        String applyNo = hosPlanDetailDTO.getApplyNo();
        Integer personForm = hosPlanDetailDTO.getPersonForm();
        // 根据传入类型查询对应分配情况
        // 查询分配计划详情
        HospitalPlannedDistribution hospitalPlannedDistribution
                = iHospitalPlannedDistributionService.selectHospitalPlannedDistributionByApplyNo(applyNo);
        //查询分配计划详情为空或分配评审周期不通过，返回异常信息
        if (Objects.isNull(hospitalPlannedDistribution)) {
//            String effCstVersion = iCstVersioningService.selectEffCstVersion();
//            if(StringUtils.isBlank(effCstVersion)){
//                log.error("启用的评估标准为空");
//                throw new ServiceException(ServiceExceptionEnum.CST_VERSIONING_ERROR_1000007);
//            }
//            versionId = Long.valueOf(effCstVersion);
            throw new ServiceException("请先进行分配评审周期！");
        }
        if (hospitalPlannedDistribution.getCycleStatus() != 1) {
            throw new ServiceException("分配评审周期" + (hospitalPlannedDistribution.getCycleStatus() == 0 ? "待审核！" : "被拒绝！"));
        }
        //查询类型为：资深评审员0
        if (Constants.HospitalConstants.NUM_0.equals(personForm)) {
            // 获取所有的已认证资深评审员
            List<SysUserBaseInfo> seniorReviewerList = iHospitalReviewerService.selectSeniorReviewerList(Constants.HospitalConstants.ROLE_SENIOR_ASSESSOR);
            hosPlanDetailVO.setSeniorAssessorList(seniorReviewerList);
        } else {
            Long versionId = hospitalPlannedDistribution.getVersionId();
            // 查询联系人信息
//            HospitalAuthContact hospitalAuthContact = hospitalAuthContactMapper.selectHospitalAuthContactByApplyNo(applyNo);
            HospitalAuthContact hospitalAuthContact = this.selectHospitalContactByApplyNo(applyNo);
            hosPlanDetailVO.setHospitalAuthContact(hospitalAuthContact);
            List<HosReviewClauseNumInfoVO> hosReviewClauseNumInfoVOList = new ArrayList<>();

            // 通过版本号查询出条款数
            Integer count = iCstCertificationStandardsService.selectCstCertificationStandardsCountByVersionId(versionId);
            AtomicReference<Integer> sum = new AtomicReference<>(0);
            if (Constants.HospitalConstants.NUM_1.equals(personForm)) {
                // 查询初审分配情况
                List<HospitalPreExam> hospitalPreExamList = hospitalPreExamMapper.selectHospitalPreExamByApplyNo(applyNo);
                hospitalPreExamList.forEach(hospitalPreExam -> {
                    // 查询对应的用户信息
                    SysUser sysUser = iSysUserService.selectUserById(Long.valueOf(hospitalPreExam.getPreExamId()));
                    HosReviewClauseNumInfoVO hosReviewClauseNumInfoVO = new HosReviewClauseNumInfoVO();
                    hosReviewClauseNumInfoVO.setAccountId(hospitalPreExam.getPreExamId());
                    hosReviewClauseNumInfoVO.setName(sysUser.getNickName());
                    String clauseNumStr = hospitalPreExam.getClauseList();
                    int clauseNum = clauseNumStr == null ? 0 : clauseNumStr.split(",").length;
                    hosReviewClauseNumInfoVO.setClauseNum(clauseNum);
                    hosReviewClauseNumInfoVOList.add(hosReviewClauseNumInfoVO);
                    sum.updateAndGet(v -> v + clauseNum);
                });
                if (!count.equals(sum.get())) {
                    HosReviewClauseNumInfoVO hosReviewClauseNumInfoVO = new HosReviewClauseNumInfoVO();
                    hosReviewClauseNumInfoVO.setName("待分配");
                    hosReviewClauseNumInfoVO.setClauseNum(count - sum.get());
                    hosReviewClauseNumInfoVOList.add(hosReviewClauseNumInfoVO);
                }
                hosPlanDetailVO.setHosReviewClauseNumInfoVOList(hosReviewClauseNumInfoVOList);

                // 判断初审员是否分配完成，只有当分配未完成才需要展示评审员列表
                if (!Constants.HospitalConstants.NUM_1.equals(hospitalPlannedDistribution.getPreDisComplete())) {
                    List<SysUser> sysUsers = commonService.selectUserByRoleKey(Constants.HospitalConstants.ROLE_INSPECTOR, true);
                    List<SysUserBaseInfo> userBaseInfoList = commonService.getBaseInfo(sysUsers);
                    hosPlanDetailVO.setPreExamList(userBaseInfoList);

                    // 需要添加未分配的组给到前端
                    StringBuilder domainIds = new StringBuilder();
                    for (HospitalPreExam hospitalPreExam : hospitalPreExamList) {
                        domainIds.append(hospitalPreExam.getGroupIdList());
                        domainIds.append(",");
                    }
                    List<DomainGroupNode> unDomainGroupNodeList = iCstDomainService.selectGroupNodeByNotInIds(domainIds.toString());
                    // 根据版本号查询出对应的领域(分组)id列表
                    List<String> domainIdList = iCstCertificationStandardsService.selectDomainIdListByVersionId(versionId);

                    // 将不在当前版本中的领域(分组)去除
                    unDomainGroupNodeList = unDomainGroupNodeList.stream()
                            .filter(domainGroupNode -> domainIdList.contains(domainGroupNode.getGroupId())).collect(Collectors.toList());
                    // 组装分组下的大主题 小主题 条款等详情
                    hospitalDomainGroupService.assembleGroupInfo(unDomainGroupNodeList);
                    hosPlanDetailVO.setUnDomainGroupNodeList(unDomainGroupNodeList);
                }
            } else if (Constants.HospitalConstants.NUM_2.equals(personForm)) {
                // 兼容不适用挪动
                List<HospitalReviewer> hospitalReviewerList = hospitalReviewerMapper.selectHospitalReviewerByApplyNoAndFieldId(applyNo, null);
                StringBuilder domainIdSb = new StringBuilder();
                for (HospitalReviewer hospitalReviewer : hospitalReviewerList) {
                    domainIdSb.append(hospitalReviewer.getFieldIdList()).append(",");
                }

                // 根据组查询所有款
                Map<String, Set<String>> groupClauseIdSetMap;
                Integer notApplicableCount = 0;
                if (domainIdSb.length() > 0) {
                    String autCode = hospitalReviewerList.get(0).getAutCode();
                    domainIdSb.deleteCharAt(domainIdSb.length() - 1);
                    List<GroupClauseInfo> groupClauseInfos = cstCertificationStandardsMapper.selectGroupClause(domainIdSb.toString(), versionId);

                    groupClauseIdSetMap = groupClauseInfos.stream()
                            .collect(Collectors.groupingBy(GroupClauseInfo::getGroupId,
                                    Collectors.mapping(GroupClauseInfo::getClauseId, Collectors.toSet()))
                            );

                    // 挪动
                    reviewFitMoveClauseService.moveClause(autCode, groupClauseIdSetMap);

                    // 不适用
                    notApplicableCount = reviewFitMoveClauseService.notApplicable(autCode, groupClauseIdSetMap);

                    // 总数减去不适用款数
                    count -= notApplicableCount;
                } else {
                    groupClauseIdSetMap = new HashMap<>();
                }
                //查询评估结论
                List<TrainingEvaluateResult> trainingEvaluateResultList = trainingEvaluateResultMapper.
                        selectTrainingEvaluateResultListByConclusion(TrainingEvaluateResultEnum.ConclusionEnum.SITE_FIT_NO_CONSIDER_21.getStrCode());
                List<String> traineesAssessorIdList = trainingEvaluateResultList.stream().map(o -> String.valueOf(o.getTraineesAssessorId())).collect(Collectors.toList());

                hospitalReviewerList.forEach(hospitalReviewer -> {
                    // 查询对应的用户信息
                    SysUser sysUser = iSysUserService.selectUserById(Long.valueOf(hospitalReviewer.getReviewerId()));
                    HosReviewClauseNumInfoVO hosReviewClauseNumInfoVO = new HosReviewClauseNumInfoVO();
                    hosReviewClauseNumInfoVO.setHospitalReviewerId(hospitalReviewer.getId());
                    hosReviewClauseNumInfoVO.setAccountId(hospitalReviewer.getReviewerId());
                    hosReviewClauseNumInfoVO.setInterestFileId(hospitalReviewer.getInterestFileId());
                    hosReviewClauseNumInfoVO.setAdminInterestFileId(hospitalReviewer.getAdminInterestFileId());
                    hosReviewClauseNumInfoVO.setHasInterest(hospitalReviewer.getHasInterest());
                    hosReviewClauseNumInfoVO.setInterestDesc(hospitalReviewer.getInterestDesc());
                    hosReviewClauseNumInfoVO.setFieldIds(hospitalReviewer.getFieldIdList());
                    hosReviewClauseNumInfoVO.setName(sysUser.getNickName());
                    Integer clauseNum = getClauseNum(hospitalReviewer.getFieldIdList(), groupClauseIdSetMap);
                    hosReviewClauseNumInfoVO.setClauseNum(clauseNum);
                    if (traineesAssessorIdList.contains(hospitalReviewer.getReviewerId())) {
                        hosReviewClauseNumInfoVO.setConclusion(TrainingEvaluateResultEnum.ConclusionEnum.SITE_FIT_NO_CONSIDER_21.getStrCode());
                    }
                    hosReviewClauseNumInfoVOList.add(hosReviewClauseNumInfoVO);
                    sum.updateAndGet(v -> v + clauseNum);
                });
                if (!count.equals(sum.get())) {
                    HosReviewClauseNumInfoVO hosReviewClauseNumInfoVO = new HosReviewClauseNumInfoVO();
                    hosReviewClauseNumInfoVO.setName("待分配");
                    hosReviewClauseNumInfoVO.setClauseNum(count - sum.get());
                    hosReviewClauseNumInfoVOList.add(hosReviewClauseNumInfoVO);
                }
                hosPlanDetailVO.setHosReviewClauseNumInfoVOList(hosReviewClauseNumInfoVOList);

                // 判断评审员是否分配完成，如没有分配完成，则需要带出对应的领域列表以及每个领域下对应的评审员
                Integer reviewDisComplete = hospitalPlannedDistribution.getReviewDisComplete();

                // 查询出当前版本所有的领域数据
                List<UnDomainListVO> allDomainListVOList = cstDomainMapper.selectCstDomainByVersionId(versionId);
                hosPlanDetailVO.setAllDomainListVOList(allDomainListVOList);
                if (Constants.HospitalConstants.NUM_2.equals(reviewDisComplete)) {
                    // 缓存已经分配了的领域id
                    StringBuilder domainIdsTemp = new StringBuilder();
                    hospitalReviewerList.forEach(hospitalReviewer ->
                            domainIdsTemp.append(hospitalReviewer.getFieldIdList()).append(","));

                    String domainIds = domainIdsTemp.toString();


                    // 将已经分配过的剔除掉。
                    List<UnDomainListVO> unDomainListVOList = allDomainListVOList.stream()
                            .filter(unDomainListVO -> !domainIds.contains(unDomainListVO.getId()))
                            .collect(Collectors.toList());

                    hosPlanDetailVO.setUnDomainListVOList(unDomainListVOList);
                }

                // 组装allDomainListVOList unDomainListVOList两个对象领域对应评审员信息并且剔除已经分配过领域的评审员
                this.findGroupReviewer(hosPlanDetailVO);
                eliminate(allDomainListVOList, applyNo);

                // 如果所有的利益冲突申报表都已经提交，则组装对应的数据提供给管理员查询
                StringBuilder ids = new StringBuilder();
                for (HospitalReviewer hospitalReviewer : hospitalReviewerList) {
                    String interestFileId = hospitalReviewer.getInterestFileId();
                    String adminInterestFileId = hospitalReviewer.getAdminInterestFileId();
                    if (CharSequenceUtil.isNotEmpty(interestFileId)) {
                        ids.append(interestFileId);
                        ids.append(",");
                    }
                    if (CharSequenceUtil.isNotEmpty(adminInterestFileId)) {
                        ids.append(adminInterestFileId);
                        ids.append(",");
                    }
                }
                if (ids.length() > 0) {
                    List<FileInfoDTO> fileInfoDTOList = iUploadFileInfoService.getUploadFileInfoByIds(ids.toString());
                    List<FileInfoVO> fileInfoVOList = commonService.fileInfoDtoToVo(fileInfoDTOList);
                    Map<String, List<FileInfoVO>> fileDetailMap = fileInfoVOList.stream().collect(Collectors.groupingBy(FileInfoVO::getFileId));
                    hosPlanDetailVO.setFileDetailMap(fileDetailMap);
                    hosPlanDetailVO.setReviewDisComplete(reviewDisComplete);
                }

            } else if (Constants.HospitalConstants.NUM_3.equals(personForm)) {
                // 查询学员未分配的学员列表
                List<HospitalReviewer> hospitalReviewerList = hospitalReviewerMapper.selectHospitalReviewerByApplyNoAndFieldId(applyNo, Constants.HospitalConstants.TRAINEES_REVIEW);
                Set<String> traAccSet = new HashSet<>();
                for (HospitalReviewer hospitalReviewer : hospitalReviewerList) {
                    SysUser sysUser = iSysUserService.selectUserById(Long.valueOf(hospitalReviewer.getReviewerId()));
                    HosReviewClauseNumInfoVO hosReviewClauseNumInfoVO = new HosReviewClauseNumInfoVO();
                    hosReviewClauseNumInfoVO.setAccountId(hospitalReviewer.getReviewerId());
                    hosReviewClauseNumInfoVO.setName(sysUser.getNickName());
                    hosReviewClauseNumInfoVOList.add(hosReviewClauseNumInfoVO);
                    traAccSet.add(hospitalReviewer.getReviewerId());
                }
                hosPlanDetailVO.setHosReviewClauseNumInfoVOList(hosReviewClauseNumInfoVOList);

                List<SysUser> sysUsers = commonService.selectUserByRoleKey(Constants.HospitalConstants.ROLE_TRAINEES_ASSESSOR, true);
                List<SysUserBaseInfo> baseInfoList = commonService.getBaseInfo(sysUsers);
                List<SysUserBaseInfo> baseInfos = baseInfoList.stream()
                        .filter(baseInfo -> !traAccSet.contains(baseInfo.getAccountId()))
                        .collect(Collectors.toList());
                hosPlanDetailVO.setTraineesList(baseInfos);
                //排除培训评审结果对应结论状态不展示评审员学员信息
                checkTrainingEvaluateResult(hosPlanDetailVO);

            } else {
                // 其他则抛异常
                throw new ServiceException(ServiceExceptionEnum.HOSPITAL_ERROR_1000009);
            }
        }
        //评审周期范围判断
        try {
            this.reviewCycleJudge(hosPlanDetailVO, applyNo, new ModifyTheReviewCycleVo());
        } catch (Exception e) {
            log.error("获取分配详情失败-评审周期范围判断异常信息为：{}", e.getMessage());
            throw new ServiceException("获取分配详情失败，请联系平台管理员");
        }

        //获取的评审员确认表
        if (StringUtils.isNotBlank(hospitalPlannedDistribution.getRevFileId())) {
            log.info("获取的评审员确认表--开始");
            List<FileInfoDTO> fileInfoDTOList = iUploadFileInfoService.getUploadFileInfoByIds(hospitalPlannedDistribution.getRevFileId());
            if (CollectionUtil.isNotEmpty(fileInfoDTOList)) {
                log.info("获取的评审员确认表--有数据");
                List<FileInfoVO> fileInfoVOList = commonService.fileInfoDtoToVo(fileInfoDTOList);
                Map<String, List<FileInfoVO>> fileDetailMap = fileInfoVOList.stream().collect(Collectors.groupingBy(FileInfoVO::getFileId));
                Map<String, List<FileInfoVO>> fileDetailMap1 = hosPlanDetailVO.getFileDetailMap();
                Map<String, List<FileInfoVO>> fileDetailMapT = new HashMap<>();
                fileDetailMapT.putAll(fileDetailMap);
                if (MapUtil.isNotEmpty(fileDetailMap1)) {
                    fileDetailMapT.putAll(fileDetailMap1);
                }
                hosPlanDetailVO.setFileDetailMap(fileDetailMapT);
            }
            hosPlanDetailVO.setRevFileId(hospitalPlannedDistribution.getRevFileId());
            log.info("获取的评审员确认表--revFileId数据有：{}", hosPlanDetailVO.getFileDetailMap());
        }

        return hosPlanDetailVO;
    }

    private Integer getClauseNum(String fieldIdList, Map<String, Set<String>> groupClauseIdSetMap) {
        if (StringUtils.isBlank(fieldIdList)) {
            return 0;
        }
        int result = 0;
        for (String groupId : fieldIdList.split(",")) {
            Set<String> clauseIdSet = groupClauseIdSetMap.get(groupId);
            if (CollectionUtils.isNotEmpty(clauseIdSet)) {
                result += clauseIdSet.size();
            }
        }
        return result;
    }

    /**
     * 剔除在途的评审员信息
     */
    private void eliminate(List<UnDomainListVO> domainListVOList, String applyNo) {
        HosPlanDetailVO hosPlanDetailVO = new HosPlanDetailVO();
        hosPlanDetailVO.setUnDomainListVOList(domainListVOList);
        reviewCycleJudge(hosPlanDetailVO, applyNo, new ModifyTheReviewCycleVo());
    }

    /**
     * 查询医院联系人信息
     *
     * @param applyNo 申请号
     * @return HospitalAuthContact
     */
    private HospitalAuthContact selectHospitalContactByApplyNo(String applyNo) {
        HospitalAuthContact hospitalAuthContact = new HospitalAuthContact();
        QueryBaseConditionDTO queryBaseConditionDTO = new QueryBaseConditionDTO();
        queryBaseConditionDTO.setCommonId(applyNo);
        HospitalBaseInfo hosInfo = hospitalBaseInfoMapper.selectHospitalByApplyNo(queryBaseConditionDTO);
        if (Objects.isNull(hosInfo)) {
            throw new ServiceException(String.format("applyNo[%s]查询医疗机构详情为空", applyNo));
        }
        hospitalAuthContact.setAuthContactName(hosInfo.getHospitalContacts());
        hospitalAuthContact.setAuthContactMobile(hosInfo.getContactsPhone());
        hospitalAuthContact.setAuthContactEmail(hosInfo.getContactsEmail());
        return hospitalAuthContact;
    }

    /**
     * 找出未分配的组对应的评审员(剔除已经分配过的评审员)
     *
     * @param hosPlanDetailVO 出参
     */
    private void findGroupReviewer(HosPlanDetailVO hosPlanDetailVO) {

        // 未分配的分组。
        List<UnDomainListVO> allDomainListVOList = hosPlanDetailVO.getAllDomainListVOList();
        if (CollectionUtils.isEmpty(allDomainListVOList)) {
            return;
        }

        // 未分配分组ids
        List<String> domainIdList = allDomainListVOList.stream()
                .map(UnDomainListVO::getId)
                .collect(Collectors.toList());

        // 获取已经分配过的评审员
        List<HosReviewClauseNumInfoVO> hosReviewClauseNumInfoVOList = hosPlanDetailVO.getHosReviewClauseNumInfoVOList();
        Set<String> accountIdSet = hosReviewClauseNumInfoVOList.stream()
                .map(HosReviewClauseNumInfoVO::getAccountId)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toSet());

        List<SysUserBaseInfo> sysUserBaseInfoList = hospitalReviewerMapper.selectUserByField(domainIdList);
        if (CollectionUtils.isEmpty(sysUserBaseInfoList)) {
            return;
        }

        // 剔除已经分配的评审员
        Map<String, List<SysUserBaseInfo>> domainIdMap = sysUserBaseInfoList.stream()
                .filter(sysUserBaseInfo -> !accountIdSet.contains(sysUserBaseInfo.getAccountId()))
                .collect(Collectors.groupingBy(SysUserBaseInfo::getDomainId));

        for (UnDomainListVO domainListVO : allDomainListVOList) {
            String domainId = domainListVO.getId();
            domainListVO.setReviewerList(domainIdMap.get(domainId));
        }

        List<UnDomainListVO> unDomainListVOList = hosPlanDetailVO.getUnDomainListVOList();
        if (CollectionUtils.isEmpty(unDomainListVOList)) {
            return;
        }

        for (UnDomainListVO unDomainListVO : unDomainListVOList) {
            String domainId = unDomainListVO.getId();
            unDomainListVO.setReviewerList(domainIdMap.get(domainId));
        }
    }

    private void checkTrainingEvaluateResult(HosPlanDetailVO hosPlanDetailVO) {
        log.info("checkTrainingEvaluateResult-入参:[{}]", JSON.toJSONString(hosPlanDetailVO));
        List<SysUserBaseInfo> traineesList = hosPlanDetailVO.getTraineesList();
        if (CollectionUtils.isEmpty(traineesList)) {
            return;
        }
        List<String> accountIds = traineesList.stream().map(SysUserBaseInfo::getAccountId).collect(Collectors.toList());

        //评审结果筛选
        String strCodeConclusion = TrainingEvaluateResultEnum.ConclusionEnum.ASSIGN_EXCLUDE.getStrCode();
        List<TrainingEvaluateResult> trainingEvaluateResultList = iTrainingEvaluateResultService.selectTrainingEvaluateResultListByConclusion(strCodeConclusion);

        //刷选 reviewer_base_info评审员基本信息表，auth_status状态审核拒绝
        List<ReviewerBaseInfo> reviewerBaseInfoList =
                reviewerBaseInfoMapper.selectReviewerBaseInfoByAccountIdList(accountIds, QueryReviewerListDTO.REVIEW_REJECTION);

        //排除的评审学员
        List<SysUserBaseInfo> delTraineesList = new ArrayList<>();
        //如果查询为空，证明没有需要排除的学员
        if (CollectionUtils.isNotEmpty(trainingEvaluateResultList)) {
            //评审学员Ids
            List<String> delTraineesIds = trainingEvaluateResultList.stream().map(a -> a.getTraineesAssessorId().toString()).collect(Collectors.toList());
            //评审员基本信息排除的学员用户
            if (CollectionUtils.isNotEmpty(reviewerBaseInfoList)) {
                List<String> delAccountIds = reviewerBaseInfoList.stream().map(a -> a.getAccountId()).collect(Collectors.toList());
                delTraineesIds.addAll(delAccountIds);
            }
            traineesList.forEach(trainees -> {
                if (delTraineesIds.contains(trainees.getAccountId())) {
                    delTraineesList.add(trainees);
                }
            });
        }
        if (CollectionUtils.isNotEmpty(delTraineesList)) {
            traineesList.removeAll(delTraineesList);
        }

        //该学员已有分配的现场评审 且 走完了现场评审，且 已评估，就能评
        //有现场评审不能分配，没走完现场评审 不能，走完但没评估 不能
        //有评估可以分配,拿出不能分配的排除
        this.checkTraineesReviewStatus(traineesList);

    }

    /**
     * 根据逻辑判断不能分配的学员
     *
     * @param traineesList 可分配的学员
     */
    private void checkTraineesReviewStatus(List<SysUserBaseInfo> traineesList) {
        if (CollectionUtils.isEmpty(traineesList)) {
            return;
        }
        List<String> accountIds = traineesList.stream().map(o -> o.getAccountId()).collect(Collectors.toList());

        //有现场评审，没走完现场评审不能分配
        List<HospitalReviewerAndAutSaRelationVO> voList = hospitalReviewerMapper.selectHospitalReviewerAndAutSaRelationByReviewerIds(accountIds);
        //有现场评审，没走完现场评审的学员，需排除
        List<String> delReviewerIds = voList.stream().filter(o -> o.getEvaluateFlag().equals(Constants.INT_ZERO)).distinct().map(o -> o.getReviewerId()).collect(Collectors.toList());
        //有现场评审，走完现场评审流程的学员，需再校验是否有带教评估结果
        List<String> checkReviewerIds = voList.stream().filter(o -> o.getEvaluateFlag().equals(Constants.INT_ONE)).distinct().map(o -> o.getReviewerId()).collect(Collectors.toList());

        //新用户需完成理论培训评估结果才能分配
        if (CollectionUtils.isNotEmpty(voList)) {
            List<String> reviewerIds = voList.stream().distinct().map(o -> o.getReviewerId()).collect(Collectors.toList());
            //过滤没参加过医院评审的用户为新用户
            List<String> newReviewerId = accountIds.stream().filter(o -> !reviewerIds.contains(o)).collect(Collectors.toList());
            //需校验是否有理论结果
            this.checkNewReviewerIds(newReviewerId, delReviewerIds);
        } else {
            //如果为空，证明全是新用户，未分配过医院的,需校验是否有理论结果
            this.checkNewReviewerIds(accountIds, delReviewerIds);
        }

        //有评估可以分配
        if (CollectionUtils.isNotEmpty(checkReviewerIds)) {
            //评估数据
            List<TrainingEvaluateResult> trainingEvaluateResultList = trainingEvaluateResultMapper.selectTrainingEvaluateResultByTraineesAssessorIdList(checkReviewerIds);
            //不为空，有带教评估结果的学员可分配
            if (CollectionUtils.isNotEmpty(trainingEvaluateResultList)) {
                List<String> traineesAssessorIds = trainingEvaluateResultList.stream().filter(o -> Constants.STR_NUM_2.equals(o.getReviewResultType().toString()))
                        .map(o -> o.getTraineesAssessorId().toString()).collect(Collectors.toList());
                checkReviewerIds.removeAll(traineesAssessorIds);
            }
            delReviewerIds.addAll(checkReviewerIds);
        }

        //排除的评审学员
        List<SysUserBaseInfo> delTraineesList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(delReviewerIds)) {
            traineesList.forEach(trainees -> {
                if (delReviewerIds.contains(trainees.getAccountId())) {
                    delTraineesList.add(trainees);
                }
            });
        }
        if (CollectionUtils.isNotEmpty(delTraineesList)) {
            traineesList.removeAll(delTraineesList);
        }
    }

    /**
     * 校验新用户是否有理论培训，有就给分配
     *
     * @param newReviewerId
     * @param delReviewerIds
     */
    private void checkNewReviewerIds(List<String> newReviewerId, List<String> delReviewerIds) {
        if (CollectionUtil.isNotEmpty(newReviewerId)) {
            List<TrainingEvaluateResult> trainingEvaluateResultList = trainingEvaluateResultMapper.selectTrainingEvaluateResultByTraineesAssessorIdList(newReviewerId);
            if (CollectionUtils.isNotEmpty(trainingEvaluateResultList)) {
                List<String> traineesAssessorIds = trainingEvaluateResultList.stream().filter(o -> Constants.STR_NUM_1.equals(o.getReviewResultType().toString()))
                        .map(o -> o.getTraineesAssessorId().toString()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(traineesAssessorIds)) {
                    //筛选没有理论结果，不分配
                    newReviewerId = newReviewerId.stream().filter(o -> !traineesAssessorIds.contains(o)).collect(Collectors.toList());
                    delReviewerIds.addAll(newReviewerId);
                } else {
                    //为空，没有理论结果，不分配
                    delReviewerIds.addAll(newReviewerId);
                }
            } else {
                //为空，没有理论结果，不分配
                delReviewerIds.addAll(newReviewerId);
            }
        }
    }

    @Override
    public Boolean reviewCycleJudge(HosPlanDetailVO hosPlanDetailVO, String applyNo, ModifyTheReviewCycleVo modifyTheReviewCycleVo) {

        Boolean flag = false;
        //改：2.释放评审分配人员逻辑
        //2-1审查员，查询获取在途的人员，排除在途人员不能再分配
        //2-2评审员和学员，现场评审完成后（提交评审报告、评审拒绝或有条件通过认证节点）释放；
        //3-3审查、评审组长和验证评审员，整体流程结束后释放；
        //自身医院已分配的人员需排除
        //审查员
        if (CollectionUtils.isNotEmpty(hosPlanDetailVO.getPreExamList())) {
            List<String> preExamIds = hosPlanDetailVO.getPreExamList().stream().map(SysUserBaseInfo::getAccountId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(preExamIds)) {
                log.info("分配审查员为空，不用刷选过滤");
                return flag;
            }
            List<ReviewCycleJudgeVo> workPreExamList = hospitalPreExamMapper.workPreExam(preExamIds);
            //刷选组员的周期值<3,和所有查询出来的组长都是在途，需排除   是否为组长1:是 2不是
            List<String> reviewIds = workPreExamList.stream().filter(o -> (ObjectUtil.equal(o.getLeaderIs(), Constants.STR_NUM_2) && Integer.parseInt(o.getCycleStage()) < 3) ||
                    ObjectUtil.equal(o.getLeaderIs(), Constants.STR_NUM_1)).map(ReviewCycleJudgeVo::getReviewId).collect(Collectors.toList());
            //排除
            hosPlanDetailVO.setPreExamList(
                    hosPlanDetailVO.getPreExamList().stream().filter(preExam -> !reviewIds.contains(preExam.getAccountId())).collect(Collectors.toList())
            );
            flag = CollUtil.isNotEmpty(hosPlanDetailVO.getPreExamList());
        }
        //评审员
        if (CollectionUtils.isNotEmpty(hosPlanDetailVO.getUnDomainListVOList())) {
            List<String> reviewerIds = new ArrayList<>();
            hosPlanDetailVO.getUnDomainListVOList().forEach(unDomainList -> unDomainList.getReviewerList().forEach(
                    o -> reviewerIds.add(o.getAccountId())
            ));
            List<String> disReviewerIds = reviewerIds.stream().distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(disReviewerIds)) {
                log.info("分配评审员为空，不用刷选过滤");
                return flag;
            }
            List<ReviewCycleJudgeVo> workReviewerList = hospitalReviewerMapper.workReviewer(disReviewerIds, null);
            //刷选组员的周期值<5(结束现场评审),和所有查询出来的组长都是在途，需排除   是否为组长1:是 2不是
            //组员，周期值为4，且不包括在030205-待提交评审结果节点 排除
            List<String> reviewIds = workReviewerList.stream().filter(o -> (ObjectUtil.equal(o.getLeaderIs(), Constants.STR_NUM_2) && Integer.parseInt(o.getCycleStage()) < 5 &&
                    !AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(o.getAutStatus(), AutSaAudStatusEnum.SR_SUMMARY)) || ObjectUtil.equal(o.getLeaderIs(), Constants.STR_NUM_1)).
                    map(ReviewCycleJudgeVo::getReviewId).collect(Collectors.toList());
            hosPlanDetailVO.getUnDomainListVOList().forEach(unDomainListVO -> {
                unDomainListVO.setReviewerList(
                        unDomainListVO.getReviewerList().stream().filter(reviewer -> !reviewIds.contains(reviewer.getAccountId())).collect(Collectors.toList())
                );
            });
            flag = CollUtil.isNotEmpty(hosPlanDetailVO.getUnDomainListVOList());
        }
        //评审学员
        if (CollectionUtils.isNotEmpty(hosPlanDetailVO.getTraineesList())) {
            List<String> traineesIds = hosPlanDetailVO.getTraineesList().stream().map(SysUserBaseInfo::getAccountId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(traineesIds)) {
                log.info("分配评审学员为空，不用刷选过滤");
                return flag;
            }
            List<ReviewCycleJudgeVo> traineesIdsList = hospitalReviewerMapper.workReviewer(traineesIds, Constants.HospitalConstants.TRAINEES_REVIEW);
            List<String> reviewIds = traineesIdsList.stream().filter(o -> Integer.parseInt(o.getCycleStage()) < 5 &&
                    !AutSaAudStatusEnum.checkIsAutSaAudStatusEnum(o.getAutStatus(), AutSaAudStatusEnum.SR_SUMMARY)).map(ReviewCycleJudgeVo::getReviewId).collect(Collectors.toList());
            //排除
            hosPlanDetailVO.setTraineesList(
                    hosPlanDetailVO.getTraineesList().stream().filter(trainees -> !reviewIds.contains(trainees.getAccountId())).collect(Collectors.toList())
            );
            flag = CollUtil.isNotEmpty(hosPlanDetailVO.getTraineesList());
        }
        //验证评审员
        if (CollectionUtils.isNotEmpty(hosPlanDetailVO.getSeniorAssessorList())) {
            List<String> seniorAssessorIds = hosPlanDetailVO.getSeniorAssessorList().stream().map(SysUserBaseInfo::getAccountId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(seniorAssessorIds)) {
                log.info("分配验证评审员为空，不用刷选过滤");
                return flag;
            }
            //sql已排除结束流程，既不用再过滤周期值
            List<ReviewCycleJudgeVo> seniorAssessorList = hospitalReviewerMapper.workReviewer(seniorAssessorIds, Constants.HospitalConstants.SENIOR_REVIEW);
            List<String> reviewIds = seniorAssessorList.stream().map(ReviewCycleJudgeVo::getReviewId).collect(Collectors.toList());
            //排除
            hosPlanDetailVO.setSeniorAssessorList(
                    hosPlanDetailVO.getSeniorAssessorList().stream().filter(seniorAssessor -> !reviewIds.contains(seniorAssessor.getAccountId())).collect(Collectors.toList())
            );
            flag = CollUtil.isNotEmpty(hosPlanDetailVO.getSeniorAssessorList());
        }

        return flag;

        //这段按周期时间过滤
//        //审查员ids
//        List<String> preExamIds = new ArrayList<>();
//        Map<String, SysUserBaseInfo> preExamMap = new HashMap<>();
//        //评审员ids
//        List<String> reviewerIds = new ArrayList<>();
//        //资深评审员ids
//        List<String> seniorAssessorIds = new ArrayList<>();
//        Map<String, SysUserBaseInfo> seniorAssessorMap = new HashMap<>();
//
//        if (CollectionUtils.isNotEmpty(hosPlanDetailVO.getPreExamList())) {
//            preExamIds = hosPlanDetailVO.getPreExamList().stream().distinct().map(SysUserBaseInfo::getAccountId).collect(Collectors.toList());
//            preExamMap = hosPlanDetailVO.getPreExamList().stream().distinct().collect(Collectors.toMap(SysUserBaseInfo::getAccountId, o -> o));
//        } else if (CollectionUtils.isNotEmpty(hosPlanDetailVO.getUnDomainListVOList())) {
//            List<SysUserBaseInfo> seniorReviewerList = iHospitalReviewerService.selectSeniorReviewerList(Constants.HospitalConstants.ROLE_SENIOR_ASSESSOR);
//            //需排除的id
//            List<String> detIds = seniorReviewerList.stream().map(SysUserBaseInfo::getAccountId).collect(Collectors.toList());
//            Set<SysUserBaseInfo> detInfoList = new HashSet<>();
//
//            for (UnDomainListVO UnDomainListVOList : hosPlanDetailVO.getUnDomainListVOList()) {
//                for (SysUserBaseInfo reviewerInfo : UnDomainListVOList.getReviewerList()) {
//                    if (!reviewerIds.contains(reviewerInfo.getAccountId())) {
//                        reviewerIds.add(reviewerInfo.getAccountId());
//                    }
//                    //领域获取的用户会有资深评审员，要排除
//                    if (CollectionUtils.isNotEmpty(detIds) && detIds.contains(reviewerInfo.getAccountId())) {
//                        detInfoList.add(reviewerInfo);
//                    }
//                }
//                UnDomainListVOList.getReviewerList().removeAll(detInfoList);
//            }
//
//            Set<String> unDomainReviewerIdList = new HashSet<>();
//            hosPlanDetailVO.getUnDomainListVOList().forEach(o -> o.getReviewerList().forEach(o1 -> unDomainReviewerIdList.add(o1.getAccountId())));
//            if (CollectionUtils.isNotEmpty(unDomainReviewerIdList)) {
//                //排除刚转为评审员且还有做为学员参与评审的人员
//                List<String> selectTraineesReviewHospitalReviewerIds = hospitalReviewerMapper.selectTraineesReviewHospitalReviewerByReviewerIds(unDomainReviewerIdList);
//                if (CollectionUtils.isNotEmpty(selectTraineesReviewHospitalReviewerIds)) {
//                    //如果排除了，且已分配过一次需走周期校验逻辑
//                    List<String> selectExcludedReviewerIds = hospitalReviewerMapper.selectExcludedReviewHospitalReviewerByReviewerIds(selectTraineesReviewHospitalReviewerIds);
//                    selectTraineesReviewHospitalReviewerIds.removeAll(selectExcludedReviewerIds);
//                }
//                reviewerIds.removeAll(selectTraineesReviewHospitalReviewerIds);
//            }
//
//
//        } else if (CollectionUtils.isNotEmpty(hosPlanDetailVO.getSeniorAssessorList())) {
//            seniorAssessorIds = hosPlanDetailVO.getSeniorAssessorList().stream().distinct().map(SysUserBaseInfo::getAccountId).collect(Collectors.toList());
//            seniorAssessorMap = hosPlanDetailVO.getSeniorAssessorList().stream().distinct().collect(Collectors.toMap(SysUserBaseInfo::getAccountId, o -> o));
//        }
//        //修改周期操作，对象不为空则做修改的逻辑
//        boolean b = checkObjAllFieldsIsNull(modifyTheReviewCycleVo);
//        if (!b) {
//            preExamIds = modifyTheReviewCycleVo.getPreExamIds();
//            reviewerIds = modifyTheReviewCycleVo.getReviewerIds();
//            seniorAssessorIds = modifyTheReviewCycleVo.getSeniorAssessorIds();
//        }
//        //获取有在途的数据进行周期校验
//        List<String> validIsIntransitByIds = new ArrayList<>();
//        //根据在途中的用户id批量查询评审或审批人员与医院的关联信息表，获取医院ids
//        //周期表的阶段名
//        String stageValue = "";
//        //根据医院ids,获取对应审批周期表数据
//        List<HospitalReviewCycleDTO> hospitalReviewCycleList = new ArrayList<>();
//
//        //查询审查员与审批员是否在途中
//        if (CollectionUtils.isNotEmpty(preExamIds)) {
//            //修改周期操作，不需要校验在途与当前已评审的用户
//            if (b) {
//                //获取当前医院已评审的用户，不做周期校验正常显示
//                List<String> PEIds = iHospitalPreExamService.selectHospitalPreExamIdsByApplyNo(applyNo);
//                if (CollectionUtil.isNotEmpty(PEIds)) {
//                    preExamIds.removeAll(PEIds);
//                }
//                //validIsIntransitByIds = sysUserMapper.getValidIsInTransitByIds(preExamIds,AutSaAudRoleEnum.INSPECTOR.getCode());
//                if (CollectionUtils.isNotEmpty(preExamIds)) {
//                    validIsIntransitByIds = sysUserMapper.batchValidIsInTransit(preExamIds)
//                            .stream().filter(o -> StringUtils.isNotBlank(o)).collect(Collectors.toList());
//                }
//            } else {
//                validIsIntransitByIds = preExamIds;
//            }
//            if (CollectionUtils.isNotEmpty(validIsIntransitByIds)) {
//                stageValue = AutSaAudCycleEnum.INITIAL_REVIEW_CYCLE.getCycleStageValue();
//                hospitalReviewCycleList = hospitalReviewCycleMapper.getHospitalReviewCycleByPreExamIds(validIsIntransitByIds, stageValue, applyNo);
//            }
//
//        } else if (CollectionUtils.isNotEmpty(reviewerIds)) {
//            if (b) {
//                List<String> RIds = iHospitalReviewerService.selectHospitalReviewerIdsByApplyNo(applyNo);
//                if (CollectionUtil.isNotEmpty(RIds)) {
//                    reviewerIds.removeAll(RIds);
//                }
//                if (CollectionUtils.isNotEmpty(reviewerIds)) {
//                    validIsIntransitByIds = sysUserMapper.batchValidIsInTransit(reviewerIds)
//                            .stream().filter(o -> StringUtils.isNotBlank(o)).collect(Collectors.toList());
//                }
//            } else {
//                validIsIntransitByIds = reviewerIds;
//            }
//            if (CollectionUtils.isNotEmpty(validIsIntransitByIds)) {
//                List<String> stageValueList = new ArrayList<>();
//                stageValue = AutSaAudCycleEnum.REVIEW_CYCLE.getCycleStageValue();
//                String stageValue2 = AutSaAudCycleEnum.VALIDATION_REVIEWER_REVIEW_CYCLE.getCycleStageValue();
//                stageValueList.add(stageValue);
//                stageValueList.add(stageValue2);
//                hospitalReviewCycleList = hospitalReviewCycleMapper.getHospitalReviewCycleByReviewerIds(validIsIntransitByIds, stageValueList, applyNo);
//            }
//
//        } else if (CollectionUtils.isNotEmpty(seniorAssessorIds)) {
//            if (b) {
//                List<String> RIds = iHospitalReviewerService.selectHospitalReviewerIdsByApplyNo(applyNo);
//                if (CollectionUtil.isNotEmpty(RIds)) {
//                    seniorAssessorIds.removeAll(RIds);
//                }
//                if (CollectionUtils.isNotEmpty(seniorAssessorIds)) {
//                    validIsIntransitByIds = sysUserMapper.batchValidIsInTransit(seniorAssessorIds)
//                            .stream().filter(o -> StringUtils.isNotBlank(o)).collect(Collectors.toList());
//                }
//            } else {
//                validIsIntransitByIds = seniorAssessorIds;
//            }
//            if (CollectionUtils.isNotEmpty(validIsIntransitByIds)) {
//                stageValue = AutSaAudCycleEnum.VALIDATION_REVIEWER_REVIEW_CYCLE.getCycleStageValue();
//                hospitalReviewCycleList = hospitalReviewCycleMapper.getHospitalReviewCycleByReviewerIds(validIsIntransitByIds, Collections.singletonList(stageValue), applyNo);
//            }
//        }
//        log.info("hospitalReviewCycleList-对应的评审周期表数据为{}", hospitalReviewCycleList);
//        if (CollectionUtils.isEmpty(validIsIntransitByIds)) {
//            log.info("认证自评审核-周期阶段值{},审核人员没有在途任务!", stageValue);
//            return;
//        }
//        if (!AutSaAudCycleEnum.getPartCycleStageValue().contains(stageValue)) {
//            throw new ServiceException("认证自评审核-周期阶段值为{" + stageValue + "},不符合认证自评审核周期阶段!");
//        }
//
//        //根据入参医院id，获取对应审批周期表数据
//        HospitalReviewCycle hospitalReviewCycle = new HospitalReviewCycle();
//        if (StringUtils.isEmpty(modifyTheReviewCycleVo.getCycleValue())) {
//            hospitalReviewCycle = hospitalReviewCycleMapper.selectByApplyNoAndStageValue(applyNo, stageValue);
//        } else {
//            hospitalReviewCycle.setCycle(modifyTheReviewCycleVo.getCycleValue());
//        }
//
//        if (StringUtils.isEmpty(hospitalReviewCycle.getCycle())) {
//            //先分配周期，没有数据报异常
//            throw new ServiceException(ServiceExceptionEnum.HOSPITAL_ERROR_1000014);
//        }
//        if (CollectionUtil.isEmpty(hospitalReviewCycleList)) {
//            log.info("当前所有分配人员与医院关联关系不存在,不用校验评审周期！");
//            return;
//        }
//        //比较入参id获取的周期表是否不在”在途用户“的周期时间内，不在则可分配，在就不能分配
//        String startTime1 = hospitalReviewCycle.getCycle().substring(0, hospitalReviewCycle.getCycle().indexOf(","));
//        String endTime1 = hospitalReviewCycle.getCycle().substring(hospitalReviewCycle.getCycle().indexOf(",") + 1);
//
//        //修改周期操作，封装冲突的人员id
//        List<String> conflictingInformationPeople = new ArrayList<>();
//        StringBuffer conflictingMessage = new StringBuffer();
//
//        String finalStageValue = stageValue;
//        Map<String, SysUserBaseInfo> finalPreExamMap = preExamMap;
//        Map<String, SysUserBaseInfo> finalSeniorAssessorMap = seniorAssessorMap;
//        hospitalReviewCycleList.forEach(hospitalReviewCycleDTO -> {
//            if (hospitalReviewCycleDTO.getSysUserBaseInfo() == null || StringUtils.isBlank(hospitalReviewCycleDTO.getSysUserBaseInfo().getAccountId())) {
//                throw new ServiceException("获取评审周期对应人员的信息为空！");
//            }
//
//            //是否为评审员，评审员检验开始的周期时间为阶段周期4，结束的周期时间为阶段周期9，审查员，资深评审员不变
//            String startTime2 = "";
//            String endTime2 = "";
//            if (AutSaAudCycleEnum.REVIEW_CYCLE.getCycleStageValue().equals(finalStageValue)) {
//                //time1>time2,time1入参周期开始时间大于time2用户评审完时间，证明用户time1时间段没有任务，可以分配 -1 ,0 1
//                //startTime2<startTime1<endTime2 =f && startTime2<endTime1<endTime2 =f 不能分配
//                startTime2 = hospitalReviewCycleDTO.getCycle().substring(0, hospitalReviewCycleDTO.getCycle().indexOf(","));
//                endTime2 = hospitalReviewCycleDTO.getNineCycleTime().substring(hospitalReviewCycleDTO.getCycle().indexOf(",") + 1);
//            } else {
//                startTime2 = hospitalReviewCycleDTO.getCycle().substring(0, hospitalReviewCycleDTO.getCycle().indexOf(","));
//                endTime2 = hospitalReviewCycleDTO.getCycle().substring(hospitalReviewCycleDTO.getCycle().indexOf(",") + 1);
//            }
//            if ((startTime1.compareTo(endTime2) <= 0 && startTime1.compareTo(startTime2) >= 0)
//                    || (endTime1.compareTo(endTime2) <= 0 && endTime1.compareTo(startTime2) >= 0)
//                    || endTime2.compareTo(endTime1) <= 0 && endTime2.compareTo(startTime1) >= 0) {
//                //不能分配,删除
//                if (AutSaAudCycleEnum.INITIAL_REVIEW_CYCLE.getCycleStageValue().equals(finalStageValue)) {
//                    //当前需要周期校验的人员对应的医院是否评审完，评审完就不校验
//                    //审查员
//                    hosPlanDetailVO.getPreExamList().remove(finalPreExamMap.get(hospitalReviewCycleDTO.getSysUserBaseInfo().getAccountId()));
//                    //修改周期操作，封装不能分配的人员id(冲突)
//                    conflictingInformationPeople.add(hospitalReviewCycleDTO.getSysUserBaseInfo().getAccountId());
//                    conflictingMessage.append("当前修改周期的审查员：" + hospitalReviewCycleDTO.getSysUserBaseInfo().getUserName() + "存在冲突，已删除相关分配审查员信息，请重新分配！\n");
//                    log.info("不能分配的审查员为{}", hospitalReviewCycleDTO.getSysUserBaseInfo().getUserName());
//
//                } else if (AutSaAudCycleEnum.REVIEW_CYCLE.getCycleStageValue().equals(finalStageValue)) {
//                    //评审员
//                    hosPlanDetailVO.getUnDomainListVOList().forEach(unDomainListVOList -> {
//                        List<SysUserBaseInfo> reviewerList = unDomainListVOList.getReviewerList().stream()
//                                .filter(o -> ObjectUtil.equal(o.getAccountId(), hospitalReviewCycleDTO.getSysUserBaseInfo().getAccountId())).collect(Collectors.toList());
//                        if (CollectionUtils.isNotEmpty(reviewerList)) {
//                            unDomainListVOList.getReviewerList().removeAll(reviewerList);
//                        }
//                        conflictingInformationPeople.add(hospitalReviewCycleDTO.getSysUserBaseInfo().getAccountId());
//                        conflictingMessage.append("当前修改周期的评审员：" + hospitalReviewCycleDTO.getSysUserBaseInfo().getUserName() + "存在冲突，已删除相关分配评审员信息，请重新分配！\n");
//                        log.info("不能分配的评审员为{}", hospitalReviewCycleDTO.getSysUserBaseInfo().getUserName());
//                    });
//
//                } else if (AutSaAudCycleEnum.VALIDATION_REVIEWER_REVIEW_CYCLE.getCycleStageValue().equals(finalStageValue)) {
//                    //资深评审员
//                    hosPlanDetailVO.getSeniorAssessorList().remove(finalSeniorAssessorMap.get(hospitalReviewCycleDTO.getSysUserBaseInfo().getAccountId()));
//                    conflictingInformationPeople.add(hospitalReviewCycleDTO.getSysUserBaseInfo().getAccountId());
//                    conflictingMessage.append("当前修改周期的资深评审员：" + hospitalReviewCycleDTO.getSysUserBaseInfo().getUserName() + "存在冲突，已删除相关分配资深评审员信息，请重新分配！\n");
//                    log.info("不能分配的资深评审员为{}", hospitalReviewCycleDTO.getSysUserBaseInfo().getUserName());
//                }
//            }
//        });
//        modifyTheReviewCycleVo.setConflictingPeople(conflictingInformationPeople);
//        modifyTheReviewCycleVo.setConflictingMessage(conflictingMessage.toString());

    }

    @Override
    public void checkUserWork(SysUser user) {

        LoginUser loginUser = getLoginUser();
        SysUser sysUser = loginUser.getUser();
        //判断用户是否在途，有就不能请假;如果请假操作才校验,状态发生改变
        if (!sysUser.getIsLeave().equals(user.getIsLeave()) && StringUtils.isNotEmpty(String.valueOf(user.getUserId()))) {

            HosPlanDetailVO hosPlanDetailVO = new HosPlanDetailVO();

            List<SysUserBaseInfo> sysUserBaseInfos = new ArrayList<>();
            SysUserBaseInfo sysUserBaseInfo = new SysUserBaseInfo();
            sysUserBaseInfo.setAccountId(String.valueOf(user.getUserId()));
            sysUserBaseInfos.add(sysUserBaseInfo);

            if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(sysUser.getRoleKey(), AutSaAudRoleEnum.ASSESSOR)) {
                //评审员
                List<UnDomainListVO> unDomainListVOS = new ArrayList<>();
                UnDomainListVO unDomainListVO = new UnDomainListVO();
                unDomainListVO.setReviewerList(sysUserBaseInfos);
                unDomainListVOS.add(unDomainListVO);
                hosPlanDetailVO.setUnDomainListVOList(unDomainListVOS);

            } else if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(sysUser.getRoleKey(), AutSaAudRoleEnum.SENIOR_ASSESSOR)) {
                //验证评审员
                hosPlanDetailVO.setSeniorAssessorList(sysUserBaseInfos);
            } else if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(sysUser.getRoleKey(), AutSaAudRoleEnum.TRAINEES_ASSESSOR)) {
                //评审学员
                hosPlanDetailVO.setTraineesList(sysUserBaseInfos);
            } else if (AutSaAudRoleEnum.checkIsAutSaAudRoleEnum(sysUser.getRoleKey(), AutSaAudRoleEnum.INSPECTOR)) {
                //审查员
                hosPlanDetailVO.setPreExamList(sysUserBaseInfos);
            } else {
                log.info("当前角色暂时不需要校验，入参为{}", JSON.toJSONString(user));
            }

            //在途的排除，如果为空，代表有在途任务,true不为空
            if (Boolean.FALSE.equals(reviewCycleJudge(hosPlanDetailVO, null, null))) {
                throw new ServiceException("在途任务未完成，用户：'" + user.getUserName() + "'请假失败");
            }
        }
    }

    /**
     * 判断对象中属性值是否全为空(目前仅支持List集合和String类型检验)
     *
     * @param object
     * @return
     */
    public static boolean checkObjAllFieldsIsNull(Object object) {
        if (null == object) {
            return true;
        }
        try {
            for (Field f : object.getClass().getDeclaredFields()) {
                f.setAccessible(true);
//                System.out.print(f.getName() + ":");
//                System.out.println(f.get(object));
                if ("serialVersionUID".equals(f.getName())) {
                    continue;
                }
                //目前仅支持List集合和String类型检验(其他类型可能会报错需调试)
                if (ObjectUtils.isNotEmpty(f.get(object))) {
                    return false;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }

    @Override
    public void invalidHosPlan(String applyNo) {
        // 失效初审员分配
        HospitalPreExam hospitalPreExam = new HospitalPreExam();
        hospitalPreExam.setApplyNo(applyNo);
        hospitalPreExam.setStatus(2);
        iHospitalPreExamService.updateHospitalPreExamByApplyNo(hospitalPreExam);
        // 失效评审员分配
        HospitalReviewer hospitalReviewer = new HospitalReviewer();
        hospitalReviewer.setApplyNo(applyNo);
        hospitalReviewer.setStatus(2);
        iHospitalReviewerService.updateHospitalReviewerByApplyNo(hospitalReviewer);
        // 失效评审周期分配
        HospitalReviewCycle hospitalReviewCycle = new HospitalReviewCycle();
        hospitalReviewCycle.setApplyNo(applyNo);
        hospitalReviewCycle.setStatus(2);
        iHospitalReviewCycleService.updateHospitalReviewCycleByApplyNo(hospitalReviewCycle);
        // 失效分配计划表
        HospitalPlannedDistribution hospitalPlannedDistribution = new HospitalPlannedDistribution();
        hospitalPlannedDistribution.setApplyNo(applyNo);
        hospitalPlannedDistribution.setStatus(2);
        iHospitalPlannedDistributionService.updateHospitalPlannedDistributionByApplyNo(hospitalPlannedDistribution);
    }

    @Override
    public List<HosReviewPlanVO> selectHosPlanClauseDetail(String applyNo) {
        QueryHosReviewPlanDTO queryHosReviewPlanDTO = new QueryHosReviewPlanDTO();
        queryHosReviewPlanDTO.setApplyNo(applyNo);
        List<HosReviewPlanVO> hosReviewPlanVOList = queryReviewPlan(queryHosReviewPlanDTO);
        // 组装初审员 评审员的一些条款信息 通过applyNo查询的数据列表只有一条
        // 查询分配计划详情
        HospitalPlannedDistribution hospitalPlannedDistribution
                = iHospitalPlannedDistributionService.selectHospitalPlannedDistributionByApplyNo(applyNo);
        if (null == hospitalPlannedDistribution) {
            return null;
        }
        if (CollectionUtils.isNotEmpty(hosReviewPlanVOList)) {
            hosReviewPlanVOList.forEach(hosReviewPlanVO -> {
                long start = Instant.now().toEpochMilli();
                // 初审员
                List<HosPlanUserInfoVO> preExamList = hosReviewPlanVO.getPreExamList();

                // 将医疗认证编码下所有初审员查询
                List<HospitalPreExam> hospitalPreExamList = iHospitalPreExamService.selectHospitalPreExamByApplyNo(applyNo);
                // 组装所有评审员下对应条款id
                StringBuilder clauseIds = new StringBuilder();
                hospitalPreExamList.forEach(hospitalPreExam -> {
                    clauseIds.append(hospitalPreExam.getClauseList()).append(",");
                });
                // 将所有条款数据查询出
                List<CstCertificationStandardVO> hospitalPreExamCsAllList = iCstCertificationStandardsService.selectCstCertificationStandardVOByIds(clauseIds.toString());
                // 开始对初审员对应的条款信息开始组装
                if (CollectionUtils.isNotEmpty(preExamList)) {
                    preExamList.forEach(preExam -> {
                        // 获取当前初审员下对应的所有条款id
                        HospitalPreExam hos = hospitalPreExamList.stream().filter(hospitalPreExam ->
                                preExam.getAccountId().equals(hospitalPreExam.getPreExamId()))
                                .findAny()
                                .orElse(new HospitalPreExam());

                        List<CstCertificationStandardVO> hospitalPreExamCs = new ArrayList<>();
                        String clauseList = hos.getClauseList();
                        if (StrUtil.isNotEmpty(clauseList)) {
                            hospitalPreExamCsAllList.forEach(hospitalPreExamCsAll -> {
                                // 如果对应的条款id在当前的评审员负责条款名下
                                if (clauseList.contains(String.valueOf(hospitalPreExamCsAll.getId()))) {
                                    hospitalPreExamCs.add(hospitalPreExamCsAll);
                                }
                            });
                        }
                        preExam.setCstCertificationStandardVOList(hospitalPreExamCs);
                    });
                }

                // 评审员
                List<HosPlanUserInfoVO> reviewerList = hosReviewPlanVO.getReviewerList();

                // 查询出所有的评审员的信息
                List<HospitalReviewer> hospitalReviewerList = hospitalReviewerMapper.selectHospitalReviewerByApplyNo(applyNo);
                StringBuilder fieldIdList = new StringBuilder();
                if (CollectionUtil.isNotEmpty(hospitalReviewerList)) {
                    hospitalReviewerList.forEach(hospitalReviewer -> {
                        fieldIdList.append(hospitalReviewer.getFieldIdList()).append(",");
                    });
                }
                Long versionId = hospitalPlannedDistribution.getVersionId();
                // 查询出所有该版本下的条款
                List<CstCertificationStandardVO> hospitalReviewerCsAllList = iCstCertificationStandardsService.selectCstCertificationStandardsByDomainId(fieldIdList.toString(), versionId);
                // 组装评审员的条款信息
                if (CollectionUtils.isNotEmpty(reviewerList)) {
                    reviewerList.forEach(reviewer -> {
                        HospitalReviewer hos = hospitalReviewerList.stream().filter(hospitalReviewer ->
                                reviewer.getAccountId().equals(hospitalReviewer.getReviewerId()))
                                .findAny().orElse(new HospitalReviewer());

                        List<CstCertificationStandardVO> hospitalReviewerCs = new ArrayList<>();
                        String fieldIdListStr = hos.getFieldIdList();
                        if (StrUtil.isNotEmpty(fieldIdListStr) && CollectionUtils.isNotEmpty(hospitalReviewerCsAllList)) {
                            hospitalReviewerCsAllList.forEach(hospitalReviewerCsAll -> {
                                if (fieldIdListStr.contains(hospitalReviewerCsAll.getDomainId())) {
                                    hospitalReviewerCs.add(hospitalReviewerCsAll);
                                }
                            });
                        }
                        reviewer.setCstCertificationStandardVOList(hospitalReviewerCs);
                    });
                }
                hosReviewPlanVO.setVersionId(versionId.toString());
                long end = Instant.now().toEpochMilli();
                log.info("耗时:{}", end - start);
            });
        }
        return hosReviewPlanVOList;
    }

    @Override
    public String selectApplyNoByUserId() {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        return hospitalBaseInfoMapper.selectApplyNoByUserId(user.getUserId());
    }

    @Override
    public List<QueryUnassignedHosDTO> queryUnassignedHos(QueryBaseConditionDTO queryBaseConditionDTO) {
        return hospitalBaseInfoMapper.queryUnassignedHos(queryBaseConditionDTO);
    }

    @Override
    public AjaxResult hosPlanSubmit(HosPlanSubmitDTO hosPlanSubmitDTO) {
        String applyNo = hosPlanSubmitDTO.getApplyNo();
        String accountId = hosPlanSubmitDTO.getAccountId();
        Integer personForm = hosPlanSubmitDTO.getPersonForm();
        Integer leaderIs = hosPlanSubmitDTO.getLeaderIs();
        String autCode = hosPlanSubmitDTO.getAutCode();

        //当入参为评审学员且Id为空时，只更新医院评审计划表hos_status为待评审
        if (StringUtils.isBlank(accountId) && Constants.HospitalConstants.TRAINEES_REVIEW.equals(hosPlanSubmitDTO.getFieldIdList())) {
            HospitalPlannedDistribution hospitalPlannedDistribution = new HospitalPlannedDistribution();
            hospitalPlannedDistribution.setApplyNo(applyNo);
            hospitalPlannedDistribution.setHosStatus(Constants.HospitalConstants.NUM_0);
            iHospitalPlannedDistributionService.insertOrUpdateHospitalPlannedDistribution(hospitalPlannedDistribution);
            return AjaxResult.success();
        }
        //分配评审员，验证评审员时，用户Id不能为空，为【确认操作】CompleteIs为3时可为空
        if (StringUtils.isBlank(accountId) && !Constants.HospitalConstants.NUM_3.equals(hosPlanSubmitDTO.getCompleteIs())) {
            throw new ServiceException("入参accountId不能为空！");
        }

        //校验是否重复提交数据
        this.checkSubmitData(hosPlanSubmitDTO);

        // 判断当前初审员/评审员是否请假
        if (iSysUserService.isLeaveByAccountId(accountId)) {
            throw new ServiceException(ServiceExceptionEnum.HOSPITAL_ERROR_1000011);
        }
        // 获取当前版本号，如果为空则查询分配计划表
        Long versionId = hosPlanSubmitDTO.getVersionId();
        if (Objects.isNull(versionId)) {
            // 查询分配计划详情
            versionId = iCstVersioningService.selectVersionId(applyNo);
            hosPlanSubmitDTO.setVersionId(versionId);
        }
        HospitalPlannedDistribution hospitalPlannedDistribution = new HospitalPlannedDistribution();
        hospitalPlannedDistribution.setApplyNo(applyNo);
        hospitalPlannedDistribution.setVersionId(hosPlanSubmitDTO.getVersionId());
        hospitalPlannedDistribution.setHosStatus(Constants.HospitalConstants.NUM_0);
        if (Constants.HospitalConstants.NUM_1.equals(personForm)) {
            // 初审员提交
            HospitalPreExam hospitalPreExam = new HospitalPreExam();
            hospitalPreExam.setApplyNo(applyNo);
            hospitalPreExam.setPreExamId(accountId);
            hospitalPreExam.setClauseList(hosPlanSubmitDTO.getClauseList());
            hospitalPreExam.setGroupIdList(hosPlanSubmitDTO.getFieldIdList());
            hospitalPreExam.setLeaderIs(leaderIs);
            // 如果当前提交人员是组长，则需要判断之前是否已经有组长存在，如果有则替换
            //0629v05改为不能替换
            if (Constants.HospitalConstants.NUM_1.equals(leaderIs)) {
                HospitalPreExam hosPreExamLeader = iHospitalPreExamService.selectHosPreExamByLeader(applyNo, leaderIs);
                if (Objects.nonNull(hosPreExamLeader)) {
                    throw new ServiceException("其他管理员已分配审查组长，请勿重复操作");
//                    hosPreExamLeader.setLeaderIs(Constants.HospitalConstants.NUM_2);
//                    iHospitalPreExamService.updateHospitalPreExam(hosPreExamLeader);
                }
            }
            iHospitalPreExamService.insertOrUpdateHospitalPreExam(hospitalPreExam);
            hospitalPlannedDistribution.setPreDisComplete(hosPlanSubmitDTO.getCompleteIs());
            iHospitalPlannedDistributionService.insertOrUpdateHospitalPlannedDistribution(hospitalPlannedDistribution);
        } else if (Constants.HospitalConstants.NUM_2.equals(personForm)) {
            // 评审员
            // 组长逻辑和初审员一致
            if (ObjectUtil.equal(Constants.HospitalConstants.NUM_1, leaderIs)) {
                HospitalReviewer hosReLeader = iHospitalReviewerService.selectHospitalReviewerByLeader(applyNo, leaderIs);
                if (Objects.nonNull(hosReLeader)) {
                    throw new ServiceException("其他管理员已分配评审组长，请勿重复操作");
//                    hosReLeader.setLeaderIs(Constants.HospitalConstants.NUM_2);
//                    iHospitalReviewerService.updateHospitalReviewer(hosReLeader);
                }
            }
            // hosPlanSubmitDTO.getCompleteIs() 如果为3，则说明已经分配完毕了，
            // 则需要判断一下所有的评审员是否已经上传了利益冲突表。
            // 改为：【确认操作】时，入参传3，转下个节点，没有医院评审信息更新；
            if (Constants.HospitalConstants.NUM_3.equals(hosPlanSubmitDTO.getCompleteIs())) {
                // 目前确认操作不需要判断是否全部上传利益冲突表，不管正向流程欢是医院驳回，都需确认操作肯定会有没上传的利益冲突表
                // iHospitalReviewerService.checkInterestFileIsFull(applyNo);
            } else {
                HospitalReviewer hospitalReviewer = new HospitalReviewer();
                hospitalReviewer.setApplyNo(applyNo);
                hospitalReviewer.setReviewerId(accountId);
                hospitalReviewer.setFieldIdList(hosPlanSubmitDTO.getFieldIdList());
                hospitalReviewer.setLeaderIs(leaderIs);
                hospitalReviewer.setAutCode(autCode);
                iHospitalReviewerService.insertOrUpdateHospitalReviewer(hospitalReviewer);
            }

            // 判断是否为资深评审员分配
            if (Constants.HospitalConstants.SENIOR_REVIEW.equals(hosPlanSubmitDTO.getFieldIdList())) {
                // 如果是分配验证评审员，则不需要改动hosStatus
                hospitalPlannedDistribution.setHosStatus(null);
                hospitalPlannedDistribution.setSeniorReviewDisComplete(Constants.HospitalConstants.NUM_1);
            }
            hospitalPlannedDistribution.setReviewDisComplete(hosPlanSubmitDTO.getCompleteIs());
            iHospitalPlannedDistributionService.insertOrUpdateHospitalPlannedDistribution(hospitalPlannedDistribution);

        } else {
            throw new ServiceException(ServiceExceptionEnum.HOSPITAL_ERROR_1000009);
        }
        return AjaxResult.success();
    }

    private void checkSubmitData(HosPlanSubmitDTO hosPlanSubmitDTO) {
        //查询当前医院节点
        if (StringUtils.isBlank(hosPlanSubmitDTO.getApplyNo())) {
            throw new ServiceException("入参医院编码不能为空！");
        }
        String autStatus = autSaRelationService.selectAllAutSaRelationByHospitalApplyNo(hosPlanSubmitDTO.getApplyNo());

        //评审组长提交校验-如果提交’领域id列表为空‘且‘为评审员’且‘是组长’且‘分配完成’，但节点不是’确认评审员‘，不能提交
        if (StringUtils.isEmpty(hosPlanSubmitDTO.getFieldIdList()) && hosPlanSubmitDTO.getPersonForm() == 2 &&
                ObjectUtil.equal(Constants.HospitalConstants.NUM_1, hosPlanSubmitDTO.getLeaderIs()) && hosPlanSubmitDTO.getCompleteIs() == 1 &&
                !ObjectUtil.equal(AutSaAudStatusEnum.CONFIRM_REVIEWER.getStatus(), autStatus)
        ) {
            if (ObjectUtil.equal(AutSaAudStatusEnum.BENEFIT_CONFLICT_DECLARE.getStatus(), autStatus)) {
                throw new ServiceException("其他管理员已更换评审员，请刷新页面查看！");
            }
            throw new ServiceException("其他管理员已分配评审组长，请勿重复操作！");
        }
        //如果提交领域id列表为空，提交的是组长数据或【确认操作】提交，不校验
        if (StringUtils.isEmpty(hosPlanSubmitDTO.getFieldIdList())) {
            return;
        }
        /**
         * 审查员：已提交分组，不能重复提交
         评审员学员：已提交相同学员，不能重复提交
         评审员：已提交分配领域，不能重复提交
         验证评审员：已提交一个不允许提交第二个验证评审员
         */
        if (Constants.HospitalConstants.NUM_1.equals(hosPlanSubmitDTO.getPersonForm())) {
            //审查
            HospitalPreExam queryPreExam = new HospitalPreExam();
            queryPreExam.setApplyNo(hosPlanSubmitDTO.getApplyNo());
            queryPreExam.setStatus(Constants.HospitalConstants.NUM_1);
            List<HospitalPreExam> hospitalPreExams = hospitalPreExamMapper.selectHospitalPreExamList(queryPreExam);
            if (CollectionUtils.isNotEmpty(hospitalPreExams)) {
                List<List<String>> groupIdLists = hospitalPreExams.stream().filter(o -> StringUtils.isNotBlank(o.getGroupIdList()))
                        .map(o -> Arrays.asList(o.getGroupIdList().split(","))).collect(Collectors.toList());
                Set<String> groupIds = new TreeSet<>();
                groupIdLists.forEach(groupIds::addAll);
                if (groupIds.containsAll(Arrays.asList(hosPlanSubmitDTO.getFieldIdList().split(",")))) {
                    throw new ServiceException("该分组已由其他管理员分配审查员，请勿重复操作!");
                }
            }
        } else if (Constants.HospitalConstants.NUM_2.equals(hosPlanSubmitDTO.getPersonForm())) {
            HospitalReviewer queryReviewer = new HospitalReviewer();
            queryReviewer.setApplyNo(hosPlanSubmitDTO.getApplyNo());
            queryReviewer.setStatus(Constants.HospitalConstants.NUM_1);
            if (ObjectUtil.equal(Constants.HospitalConstants.TRAINEES_REVIEW, hosPlanSubmitDTO.getFieldIdList())) {
                //学员
                queryReviewer.setReviewerId(hosPlanSubmitDTO.getAccountId());
                List<HospitalReviewer> hospitalReviewers = hospitalReviewerMapper.selectHospitalReviewerList(queryReviewer);
                if (CollectionUtils.isNotEmpty(hospitalReviewers)) {
                    throw new ServiceException("其他管理员已分配评审学员，请勿重复操作!");
                }

            } else if (ObjectUtil.equal(Constants.HospitalConstants.SENIOR_REVIEW, hosPlanSubmitDTO.getFieldIdList())) {
                //验证评审员
                List<HospitalReviewer> hospitalReviewers = hospitalReviewerMapper.selectHospitalReviewerList(queryReviewer);
                if (CollectionUtils.isNotEmpty(hospitalReviewers) &&
                        hospitalReviewers.stream().filter(o -> Constants.HospitalConstants.SENIOR_REVIEW.equals(o.getFieldIdList())).count() > 0) {
                    throw new ServiceException("其他管理员已分配验证评审员，请勿重复操作!");
                }
            } else {
                //评审员 已提交的分组 且 入参的评审员Id与查询已有的评审员相同时，不能重复提交
                List<HospitalReviewer> hospitalReviewers = hospitalReviewerMapper.selectHospitalReviewerList(queryReviewer);
                if (CollectionUtils.isNotEmpty(hospitalReviewers)) {
//                    Set<String> fieldIdList = hospitalReviewers.stream().filter(o -> StringUtils.isNotBlank(o.getFieldIdList()))
//                            .map(HospitalReviewer::getFieldIdList).collect(Collectors.toSet());
//
//                    if (fieldIdList.containsAll(Arrays.asList(hosPlanSubmitDTO.getFieldIdList().split(",")))) {
//                        throw new ServiceException("该分组已由其他管理员分配评审员，请勿重复操作!");
//                    }

                    Set<String> reviewerIds = hospitalReviewers.stream().map(HospitalReviewer::getReviewerId).collect(Collectors.toSet());
                    if (reviewerIds.contains(hosPlanSubmitDTO.getAccountId())) {
                        throw new ServiceException("该评审员已由其他管理员分配分组，请勿重复操作!");
                    }

                }
            }
        }
    }

    /**
     * 查询医疗机构详情
     *
     * @param queryBaseConditionDTO 医疗机构详情认证编号
     * @return 医疗机构详情
     */
    @Override
    public HospitalBaseInfo selectHospitalByApplyNo(QueryBaseConditionDTO queryBaseConditionDTO) {
        return hospitalBaseInfoMapper.selectHospitalByApplyNo(queryBaseConditionDTO);
    }

    /**
     * 查询医院申请前，当年已申请的数量
     *
     * @param applyNO 医院编码
     * @return 医院申请前，当年已申请的数量
     */
    @Override
    public Map<String, Object> selectCurrentYearApplyCountByApplyNO(String applyNO) {
        return hospitalBaseInfoMapper.selectCurrentYearApplyCountByApplyNO(applyNO);
    }


}
