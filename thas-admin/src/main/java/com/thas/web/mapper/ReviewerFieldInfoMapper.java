package com.thas.web.mapper;

import com.thas.web.domain.ReviewerFieldInfo;
import com.thas.web.dto.ReviewerFieldInfoVO;
import com.thas.web.dto.SysUserBaseInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 评审员领域关联Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-19
 */
@Mapper
public interface ReviewerFieldInfoMapper
{
    /**
     * 查询评审员领域关联
     *
     * @param id 评审员领域关联主键
     * @return 评审员领域关联
     */
    public ReviewerFieldInfo selectReviewerFieldInfoById(Long id);

    /**
     * 查询评审员领域关联列表
     *
     * @param reviewerFieldInfo 评审员领域关联
     * @return 评审员领域关联集合
     */
    public List<ReviewerFieldInfo> selectReviewerFieldInfoList(ReviewerFieldInfo reviewerFieldInfo);

    List<ReviewerFieldInfoVO> selectReviewerFieldInfoByAccountId(String accountId);

    List<ReviewerFieldInfoVO> selectReviewerFieldInfoByAccountIdList(List<String> accountIdList);

    /**
     * 新增评审员领域关联
     *
     * @param reviewerFieldInfo 评审员领域关联
     * @return 结果
     */
    public int insertReviewerFieldInfo(ReviewerFieldInfo reviewerFieldInfo);

    int batchInsertReviewerFieldInfo(List<ReviewerFieldInfo> reviewerFieldInfoList);

    /**
     * 修改评审员领域关联
     *
     * @param reviewerFieldInfo 评审员领域关联
     * @return 结果
     */
    public int updateReviewerFieldInfo(ReviewerFieldInfo reviewerFieldInfo);

    /**
     * 删除评审员领域关联
     *
     * @param id 评审员领域关联主键
     * @return 结果
     */
    public int deleteReviewerFieldInfoById(Long id);

    /**
     * 批量删除评审员领域关联
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteReviewerFieldInfoByIds(Long[] ids);

    int updateStatusByAccountId(@Param("accountId") String accountId, @Param("status") int status);

    int updateStatusByFieldCode(@Param("fieldCode") String fieldCode, @Param("status") String status);

    void updateAccountId(@Param("userId") String userId, @Param("accountId")String accountId);
}
