package com.thas.web.mapper;

import com.thas.web.domain.TraQuestionnaireFeedBack;

import java.util.List;


/**
 * 反馈问卷Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-10-26
 */
public interface TraQuestionnaireFeedBackMapper 
{
    /**
     * 查询反馈问卷
     * 
     * @param feedBackId 反馈问卷主键
     * @return 反馈问卷
     */
    public TraQuestionnaireFeedBack selectTraQuestionnaireFeedBackByFeedBackId(Long feedBackId);

    /**
     * 查询反馈问卷列表
     * 
     * @param traQuestionnaireFeedBack 反馈问卷
     * @return 反馈问卷集合
     */
    public List<TraQuestionnaireFeedBack> selectTraQuestionnaireFeedBackList(TraQuestionnaireFeedBack traQuestionnaireFeedBack);

    /**
     * 新增反馈问卷
     * 
     * @param traQuestionnaireFeedBack 反馈问卷
     * @return 结果
     */
    public int insertTraQuestionnaireFeedBack(TraQuestionnaireFeedBack traQuestionnaireFeedBack);

    /**
     * 修改反馈问卷
     * 
     * @param traQuestionnaireFeedBack 反馈问卷
     * @return 结果
     */
    public int updateTraQuestionnaireFeedBack(TraQuestionnaireFeedBack traQuestionnaireFeedBack);

    /**
     * 删除反馈问卷
     * 
     * @param feedBackId 反馈问卷主键
     * @return 结果
     */
    public int deleteTraQuestionnaireFeedBackByFeedBackId(Long feedBackId);

    /**
     * 批量删除反馈问卷
     * 
     * @param feedBackIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTraQuestionnaireFeedBackByFeedBackIds(Long[] feedBackIds);
}
