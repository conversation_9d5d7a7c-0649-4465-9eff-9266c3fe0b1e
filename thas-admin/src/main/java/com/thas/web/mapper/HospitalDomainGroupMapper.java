package com.thas.web.mapper;

import com.thas.web.domain.HospitalDomainGroup;
import com.thas.web.domain.vo.DomainGroupNode;

import java.util.List;


/**
 * 领域对应分组Mapper接口
 *
 * <AUTHOR>
 * @date 2022-04-22
 */
public interface HospitalDomainGroupMapper {
    /**
     * 查询领域对应分组
     *
     * @param id 领域对应分组主键
     * @return 领域对应分组
     */
    HospitalDomainGroup selectHospitalDomainGroupById(Long id);

    /**
     * 查询领域对应分组列表
     *
     * @param hospitalDomainGroup 领域对应分组
     * @return 领域对应分组集合
     */
    List<HospitalDomainGroup> selectHospitalDomainGroupList(HospitalDomainGroup hospitalDomainGroup);

    /**
     * 新增领域对应分组
     *
     * @param hospitalDomainGroup 领域对应分组
     * @return 结果
     */
    int insertHospitalDomainGroup(HospitalDomainGroup hospitalDomainGroup);

    /**
     * 修改领域对应分组
     *
     * @param hospitalDomainGroup 领域对应分组
     * @return 结果
     */
    int updateHospitalDomainGroup(HospitalDomainGroup hospitalDomainGroup);


    int batchInsertByDomainGroupNode(List<DomainGroupNode> insertList);

    List<DomainGroupNode> selectHospitalDomainGroupAll(String groupIdList);

    int deleteHospitalDomainGroupByGroupIds(List<String> groupIds);
}
