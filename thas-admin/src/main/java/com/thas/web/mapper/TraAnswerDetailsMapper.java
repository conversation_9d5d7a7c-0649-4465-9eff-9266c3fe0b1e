package com.thas.web.mapper;

import com.thas.web.domain.TraAnswerDetails;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 答卷明细Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-27
 */
@Mapper
public interface TraAnswerDetailsMapper {
    /**
     * 查询答卷明细
     *
     * @param id 答卷明细主键
     * @return 答卷明细
     */
    TraAnswerDetails selectTraAnswerDetailsById(Long id);

    /**
     * 查询答卷明细列表
     *
     * @param traAnswerDetails 答卷明细
     * @return 答卷明细集合
     */
    List<TraAnswerDetails> selectTraAnswerDetailsList(TraAnswerDetails traAnswerDetails);

    /**
     * 新增答卷明细
     *
     * @param traAnswerDetails 答卷明细
     * @return 结果
     */
    int insertTraAnswerDetails(TraAnswerDetails traAnswerDetails);

    /**
     * 修改答卷明细
     *
     * @param traAnswerDetails 答卷明细
     * @return 结果
     */
    int updateTraAnswerDetails(TraAnswerDetails traAnswerDetails);

    /**
     * 删除答卷明细
     *
     * @param id 答卷明细主键
     * @return 结果
     */
    int deleteTraAnswerDetailsById(Long id);

    /**
     * 批量删除答卷明细
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteTraAnswerDetailsByIds(Long[] ids);
}
