package com.thas.web.mapper;


import com.thas.web.domain.TraQuestionnaireAnswer;
import com.thas.web.domain.vo.TraQuestionnaireAnswerVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 调查问卷答卷Mapper接口
 *
 * <AUTHOR>
 * @date 2022-02-14
 */
@Mapper
public interface TraQuestionnaireAnswerMapper {
    /**
     * 查询调查问卷答卷
     *
     * @param questionnaireId 调查问卷答卷主键
     * @return 调查问卷答卷
     */
    TraQuestionnaireAnswerVO selectTraQuestionnaireAnswerById(@Param("questionnaireId") String questionnaireId, @Param("answerId") String answerId, @Param("createId") String createId);

    /**
     * 查询调查问卷答卷列表
     *
     * @param traQuestionnaireAnswer 调查问卷答卷
     * @return 调查问卷答卷集合
     */
    List<TraQuestionnaireAnswer> selectTraQuestionnaireAnswerList(TraQuestionnaireAnswer traQuestionnaireAnswer);

    /**
     * 新增调查问卷答卷
     *
     * @param traQuestionnaireAnswer 调查问卷答卷
     * @return 结果
     */
    int insertTraQuestionnaireAnswer(TraQuestionnaireAnswer traQuestionnaireAnswer);

    /**
     * 修改调查问卷答卷
     *
     * @param traQuestionnaireAnswer 调查问卷答卷
     * @return 结果
     */
    int updateTraQuestionnaireAnswer(TraQuestionnaireAnswer traQuestionnaireAnswer);

    /**
     * 删除调查问卷答卷
     *
     * @param id 调查问卷答卷主键
     * @return 结果
     */
    int deleteTraQuestionnaireAnswerById(Long id);

    /**
     * 批量删除调查问卷答卷
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteTraQuestionnaireAnswerByIds(Long[] ids);

    int selectCountByQuestionnaireId(Long questionnaireId);

    int updateStatus(@Param("status") Integer status, @Param("id") Long id);

    int delete(@Param("createId") String createId, @Param("id") Long id);
}
