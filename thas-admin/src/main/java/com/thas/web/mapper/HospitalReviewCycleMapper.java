package com.thas.web.mapper;

import java.util.List;
import com.thas.web.domain.HospitalReviewCycle;
import com.thas.web.dto.HospitalReviewCycleDTO;
import org.apache.ibatis.annotations.Param;

/**
 * 医院评审阶段周期Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-27
 */
public interface HospitalReviewCycleMapper
{
    /**
     * 查询医院评审阶段周期
     *
     * @param id 医院评审阶段周期主键
     * @return 医院评审阶段周期
     */
    public HospitalReviewCycle selectHospitalReviewCycleById(Long id);


    List<HospitalReviewCycle> selectHospitalReviewCycleByApplyNo(String applyNo);
    /**
     * 查询医院评审阶段周期列表
     *
     * @param hospitalReviewCycle 医院评审阶段周期
     * @return 医院评审阶段周期集合
     */
    public List<HospitalReviewCycle> selectHospitalReviewCycleList(HospitalReviewCycle hospitalReviewCycle);

    /**
     * 根据医院ids，查询医院评审阶段周期列表
     *
     * @param applyNoList
     * @param cycleStageValue
     * @return 医院评审阶段周期集合
     */
    public List<HospitalReviewCycle> selectHospitalReviewCycleByApplyNos(@Param("list") List<String> applyNoList, @Param("cycleStageValue") String cycleStageValue);


    /**
     * 新增医院评审阶段周期
     *
     * @param hospitalReviewCycle 医院评审阶段周期
     * @return 结果
     */
    public int insertHospitalReviewCycle(HospitalReviewCycle hospitalReviewCycle);

    /**
     * 修改医院评审阶段周期
     *
     * @param hospitalReviewCycle 医院评审阶段周期
     * @return 结果
     */
    public int updateHospitalReviewCycle(HospitalReviewCycle hospitalReviewCycle);

    /**
     * 删除医院评审阶段周期
     *
     * @param id 医院评审阶段周期主键
     * @return 结果
     */
    public int deleteHospitalReviewCycleById(Long id);

    /**
     * 批量删除医院评审阶段周期
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHospitalReviewCycleByIds(Long[] ids);

    /**
     * 批量插入
     * @param hospitalReviewCycleList
     * @return
     */
    public int insertHospitalReviewCycleList(List<HospitalReviewCycle> hospitalReviewCycleList);


    public int deleteByApplyNoList(List<String> applyNo);

    /**
     * 按照applyNo和stageValue查询周期时间
     * @param applyNo
     * @param cycleStage
     * @return
     */
    HospitalReviewCycle selectByApplyNoAndStageValue(@Param("applyNo") String applyNo, @Param("cycleStage") String cycleStage);

    /**
     * 根据applyNo和stageValue查询周期时间（不加是否有效字段条件查询）
     * @param applyNo
     * @param cycleStage
     * @param autCode 自评编码
     * @return
     */
    List<HospitalReviewCycle> selectByApplyNoAndStageValue2(@Param("applyNo") String applyNo, @Param("cycleStage") String cycleStage, @Param("autCode") String autCode);


    int updateHospitalReviewCycleByApplyNo(HospitalReviewCycle hospitalReviewCycle);

    /**
     * 根据审查员ids，获取医院认证编码，获取对应查询周期时间(排除医院最终节点‘aut_status’为’评审流程结束‘的数据)
     * @param preExamIds 审查员preExamIds
     * @param stageValue 周期表的阶段名
     * @param applyNo 医院认证编码（排除此编码数据）
     * @return
     */
    List<HospitalReviewCycleDTO> getHospitalReviewCycleByPreExamIds(@Param("preExamIds") List<String> preExamIds, @Param("stageValue") String stageValue,@Param("applyNo") String applyNo);

    /**
     * 根据评审员ids，获取医院认证编码，获取对应查询周期时间(排除医院最终节点‘aut_status’为’评审流程结束‘的数据)
     * @param reviewerIds 审批员reviewerIds
     * @param stageValueList 周期表的阶段名
     * @return
     */
    List<HospitalReviewCycleDTO> getHospitalReviewCycleByReviewerIds(@Param("reviewerIds") List<String> reviewerIds,
                                                                     @Param("stageValueList") List<String> stageValueList,@Param("applyNo") String applyNo);

    /**
     * 根据周期表的阶段名，审查员和验证评审员是否分配完成状态，查询对应的评审周期信息
     * @param stageValue 周期表的阶段名
     * @param field 对应人员‘是否分配完成状态’字段
     * @return
     */
    List<HospitalReviewCycleDTO> getHospitalReviewCycleByStageValueAndStatus(@Param("stageValue") String stageValue,@Param("field") String field);

    int updateHospitalReviewCycleByApplyNoList(@Param("list") List<String> applyNoList);
}
