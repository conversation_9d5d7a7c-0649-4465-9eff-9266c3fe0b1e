package com.thas.web.mapper;


import com.thas.web.domain.TraQuestionnaireAnswerDetails;
import com.thas.web.domain.vo.AnswerCountVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 调查问卷答卷Mapper接口
 *
 * <AUTHOR>
 * @date 2022-02-14
 */
@Mapper
public interface TraQuestionnaireAnswerDetailsMapper {
    /**
     * 查询调查问卷答卷
     *
     * @param id 调查问卷答卷主键
     * @return 调查问卷答卷
     */
    TraQuestionnaireAnswerDetails selectTraQuestionnaireAnswerDetailsById(Long id);

    /**
     * 查询调查问卷答卷列表
     *
     * @param traQuestionnaireAnswerDetails 调查问卷答卷
     * @return 调查问卷答卷集合
     */
    List<TraQuestionnaireAnswerDetails> selectTraQuestionnaireAnswerDetailsList(TraQuestionnaireAnswerDetails traQuestionnaireAnswerDetails);

    /**
     * 新增调查问卷答卷
     *
     * @param traQuestionnaireAnswerDetails 调查问卷答卷
     * @return 结果
     */
    int insertTraQuestionnaireAnswerDetails(TraQuestionnaireAnswerDetails traQuestionnaireAnswerDetails);

    /**
     * 修改调查问卷答卷
     *
     * @param traQuestionnaireAnswerDetails 调查问卷答卷
     * @return 结果
     */
    int updateTraQuestionnaireAnswerDetails(TraQuestionnaireAnswerDetails traQuestionnaireAnswerDetails);

    /**
     * 删除调查问卷答卷
     *
     * @param id 调查问卷答卷主键
     * @return 结果
     */
    int deleteTraQuestionnaireAnswerDetailsById(Long id);

    /**
     * 批量删除调查问卷答卷
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteTraQuestionnaireAnswerDetailsByIds(Long[] ids);

    List<AnswerCountVO> getAnswerCountMap(Long questionnaireDetailsId);
}
