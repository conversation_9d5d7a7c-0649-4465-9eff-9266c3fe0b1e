package com.thas.web.mapper;

import com.thas.web.domain.CstReviewerOfflineTraining;
import com.thas.web.domain.vo.OfflineTrainingRegisteredVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 评审员端线下培训管理Mapper接口
 *
 * <AUTHOR>
 * @date 2022-02-17
 */
public interface CstReviewerOfflineTrainingMapper {
    /**
     * 查询评审员端线下培训管理
     *
     * @param id 评审员端线下培训管理主键
     * @return 评审员端线下培训管理
     */
    CstReviewerOfflineTraining selectCstReviewerOfflineTrainingById(Long id);

    /**
     * 查询评审员端线下培训管理列表
     *
     * @param cstReviewerOfflineTraining 评审员端线下培训管理
     * @return 评审员端线下培训管理集合
     */
    List<CstReviewerOfflineTraining> selectCstReviewerOfflineTrainingList(CstReviewerOfflineTraining cstReviewerOfflineTraining);

    /**
     * 新增评审员端线下培训管理
     *
     * @param cstReviewerOfflineTraining 评审员端线下培训管理
     * @return 结果
     */
    int insertCstReviewerOfflineTraining(CstReviewerOfflineTraining cstReviewerOfflineTraining);

    /**
     * 修改评审员端线下培训管理
     *
     * @param cstReviewerOfflineTraining 评审员端线下培训管理
     * @return 结果
     */
    int updateCstReviewerOfflineTraining(CstReviewerOfflineTraining cstReviewerOfflineTraining);

    int updateStatusByAccountId(@Param("accountId") String accountId, @Param("status") int status);

    int batchInsertOfflineTraining(List<CstReviewerOfflineTraining> cstReviewerOfflineTrainingList);

    List<CstReviewerOfflineTraining> selectCstReviewerOfflineTrainingByAccountId(String accountId);

    List<OfflineTrainingRegisteredVo> selectSysUserByTrainingId(@Param("trainingId") String trainingId);

    int selectCountByTrainingId(String trainingId);

    /**
     * 删除线下培训管理
     *
     * @param cstReviewerOfflineTraining 评审员端线下培训管理
     * @return 结果
     */
     int deleteCstReviewerOfflineTraining(CstReviewerOfflineTraining cstReviewerOfflineTraining);

    int updateInfoByAccountIdAndTraId(CstReviewerOfflineTraining cstReviewerOfflineTraining);
}
