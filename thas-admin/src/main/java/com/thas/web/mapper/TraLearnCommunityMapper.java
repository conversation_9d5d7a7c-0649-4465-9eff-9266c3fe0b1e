package com.thas.web.mapper;


import com.thas.web.domain.TraLearnCommunity;
import com.thas.web.domain.vo.TraLearnCommunityVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 学习社区文章详情Mapper接口
 *
 * <AUTHOR>
 * @date 2022-02-15
 */
@Mapper
public interface TraLearnCommunityMapper {
    /**
     * 查询学习社区文章详情
     *
     * @param id 学习社区文章详情主键
     * @return 学习社区文章详情
     */
    TraLearnCommunityVO selectTraLearnCommunityById(Long id);

    /**
     * 查询学习社区文章详情列表
     *
     * @param traLearnCommunity 学习社区文章详情
     * @return 学习社区文章详情集合
     */
    List<TraLearnCommunityVO> selectTraLearnCommunityList(TraLearnCommunity traLearnCommunity);

    /**
     * 新增学习社区文章详情
     *
     * @param traLearnCommunity 学习社区文章详情
     * @return 结果
     */
    int insertTraLearnCommunity(TraLearnCommunity traLearnCommunity);

    /**
     * 修改学习社区文章详情
     *
     * @param traLearnCommunity 学习社区文章详情
     * @return 结果
     */
    int updateTraLearnCommunity(TraLearnCommunity traLearnCommunity);

    /**
     * 删除学习社区文章详情
     *
     * @param id 学习社区文章详情主键
     * @return 结果
     */
    int deleteTraLearnCommunityById(Long id);

    /**
     * 批量删除学习社区文章详情
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteTraLearnCommunityByIds(Long[] ids);

    Integer getCountByCommunityId(Long communityId);
 }
