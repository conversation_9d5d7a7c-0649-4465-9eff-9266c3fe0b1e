package com.thas.web.mapper;

import com.thas.web.domain.CstEvaluationCriterion;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 评估标准模板Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-21
 */
public interface CstEvaluationCriterionMapper
{
    /**
     * 查询评估标准模板
     *
     * @param id 评估标准模板主键
     * @return 评估标准模板
     */
    public CstEvaluationCriterion selectCstEvaluationCriterionById(Long id);

    /**
     * 查询评估标准模板列表
     *
     * @param cstEvaluationCriterion 评估标准模板
     * @return 评估标准模板集合
     */
    public List<CstEvaluationCriterion> selectCstEvaluationCriterionList(CstEvaluationCriterion cstEvaluationCriterion);

    /**
     * 新增评估标准模板
     *
     * @param cstEvaluationCriterion 评估标准模板
     * @return 结果
     */
    public int insertCstEvaluationCriterion(CstEvaluationCriterion cstEvaluationCriterion);

    /**
     * 修改评估标准模板
     *
     * @param cstEvaluationCriterion 评估标准模板
     * @return 结果
     */
    public int updateCstEvaluationCriterion(CstEvaluationCriterion cstEvaluationCriterion);

    /**
     * 删除评估标准模板
     *
     * @param id 评估标准模板主键
     * @return 结果
     */
    public int deleteCstEvaluationCriterionById(Long id);

    /**
     * 批量删除评估标准模板
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCstEvaluationCriterionByIds(Long[] ids);

    /**
     * 通过版本id和款id多条插入插入
     * @param cstEvaluationCriteriaList
     * @return 结果
     */
    public int insertListByVersionIdAndClauseId(List<CstEvaluationCriterion> cstEvaluationCriteriaList);

    /**
     * 通过款id和版本id进行删除
     * @param versionId 版本id
     * @param clauseId 款id
     * @return
     */
    public int deleteByVersionIdAndClauseId(@Param("versionId") Long versionId,@Param("clauseId") Long clauseId);

    /**
     * 查询评估标准模板列表
     *
     * @param versionId versionId 版本id
     * @param clauseIds 款id列表 逗号拼接
     * @return 评估标准模板集合
     */
    List<CstEvaluationCriterion> selectByVersionIdAndClauseId(@Param("versionId") String versionId, @Param("clauseIds") String clauseIds);

}
