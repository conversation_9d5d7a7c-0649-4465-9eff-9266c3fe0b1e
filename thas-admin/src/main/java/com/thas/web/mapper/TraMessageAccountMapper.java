package com.thas.web.mapper;

import com.thas.web.domain.TraMessageAccount;
import org.apache.ibatis.annotations.Mapper;

/**
 * 留言管理Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-20
 */
@Mapper
public interface TraMessageAccountMapper {

    int delete(TraMessageAccount traMessageAccount);

    int add(TraMessageAccount traMessageAccount);

    TraMessageAccount getByAccount(TraMessageAccount traMessageAccount);
}
