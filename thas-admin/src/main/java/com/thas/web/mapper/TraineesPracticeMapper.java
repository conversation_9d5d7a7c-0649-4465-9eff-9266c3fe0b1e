package com.thas.web.mapper;

import com.thas.common.core.domain.AjaxResult;
import com.thas.web.domain.TraineesPractice;
import com.thas.web.dto.TraineesPracticeDTO;

import java.util.List;

/**
 * 评审学员实践记录Mapper接口
 *
 * <AUTHOR>
 * @date 2022-05-31
 */
public interface TraineesPracticeMapper {
    /**
     * 查询评审学员实践记录
     *
     * @param id 评审学员实践记录主键
     * @return 评审学员实践记录
     */
    TraineesPractice selectTraineesPracticeById(Long id);

    /**
     * 查询评审学员实践记录列表
     *
     * @param traineesPractice 评审学员实践记录
     * @return 评审学员实践记录集合
     */
    List<TraineesPractice> selectTraineesPracticeList(TraineesPractice traineesPractice);

    /**
     * 新增评审学员实践记录
     *
     * @param traineesPractice 评审学员实践记录
     * @return 结果
     */
    int insertTraineesPractice(TraineesPractice traineesPractice);

    /**
     * 修改评审学员实践记录
     *
     * @param traineesPractice 评审学员实践记录
     * @return 结果
     */
    int updateTraineesPractice(TraineesPractice traineesPractice);

    /**
     * 删除评审学员实践记录
     *
     * @param id 评审学员实践记录主键
     * @return 结果
     */
    int deleteTraineesPracticeById(Long id);

    /**
     * 通过实践记录id删除
     * @param praId 实践记录id
     * @return 结果
     */
    int deleteTraineesPracticeByPraId(String praId);

    /**
     * 批量删除评审学员实践记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteTraineesPracticeByIds(Long[] ids);

    int batchInsertTraineesPractice(List<TraineesPracticeDTO> traineesPracticeDTOList);

    List<TraineesPractice> selectTraineesPracticeByAccountId(String accountId);
}
