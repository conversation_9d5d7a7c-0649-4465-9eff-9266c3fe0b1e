package com.thas.web.mapper;

import java.util.List;
import com.thas.web.domain.MessageSendRecord;

/**
 * 消息发送记录Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-25
 */
public interface MessageSendRecordMapper
{
    /**
     * 查询消息发送记录
     *
     * @param id 消息发送记录主键
     * @return 消息发送记录
     */
    public MessageSendRecord selectMessageSendRecordById(Long id);

    /**
     * 查询消息发送记录列表
     *
     * @param messageSendRecord 消息发送记录
     * @return 消息发送记录集合
     */
    public List<MessageSendRecord> selectMessageSendRecordList(MessageSendRecord messageSendRecord);

    /**
     * 新增消息发送记录
     *
     * @param messageSendRecord 消息发送记录
     * @return 结果
     */
    public int insertMessageSendRecord(MessageSendRecord messageSendRecord);

    /**
     * 修改消息发送记录
     *
     * @param messageSendRecord 消息发送记录
     * @return 结果
     */
    public int updateMessageSendRecord(MessageSendRecord messageSendRecord);

    /**
     * 删除消息发送记录
     *
     * @param id 消息发送记录主键
     * @return 结果
     */
    public int deleteMessageSendRecordById(Long id);

    /**
     * 批量删除消息发送记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMessageSendRecordByIds(Long[] ids);
}
