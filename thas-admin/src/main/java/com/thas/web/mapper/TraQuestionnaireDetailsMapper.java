package com.thas.web.mapper;


import com.thas.web.domain.TraQuestionnaireDetails;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 调查问卷详细信息Mapper接口
 *
 * <AUTHOR>
 * @date 2022-02-14
 */
@Mapper
public interface TraQuestionnaireDetailsMapper {
    /**
     * 查询调查问卷详细信息
     *
     * @param id 调查问卷详细信息主键
     * @return 调查问卷详细信息
     */
    TraQuestionnaireDetails selectTraQuestionnaireDetailsById(Long id);

    /**
     * 查询调查问卷详细信息列表
     *
     * @param traQuestionnaireDetails 调查问卷详细信息
     * @return 调查问卷详细信息集合
     */
    List<TraQuestionnaireDetails> selectTraQuestionnaireDetailsList(TraQuestionnaireDetails traQuestionnaireDetails);

    /**
     * 新增调查问卷详细信息
     *
     * @param traQuestionnaireDetails 调查问卷详细信息
     * @return 结果
     */
    int insertTraQuestionnaireDetails(TraQuestionnaireDetails traQuestionnaireDetails);

    /**
     * 修改调查问卷详细信息
     *
     * @param traQuestionnaireDetails 调查问卷详细信息
     * @return 结果
     */
    int updateTraQuestionnaireDetails(TraQuestionnaireDetails traQuestionnaireDetails);

    /**
     * 删除调查问卷详细信息
     *
     * @param id 调查问卷详细信息主键
     * @return 结果
     */
    int deleteTraQuestionnaireDetailsById(Long id);

    /**
     * 批量删除调查问卷详细信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteTraQuestionnaireDetailsByIds(Long[] ids);

    /**
     * 根据问卷id删除调查问卷详细信息
     *
     * @param questionnaireId 问卷id
     * @return 结果
     */
    int deleteByQuestionnaireId(Long questionnaireId);

    List<TraQuestionnaireDetails> selectByQuestionnaireId(Long questionnaireId);
}
