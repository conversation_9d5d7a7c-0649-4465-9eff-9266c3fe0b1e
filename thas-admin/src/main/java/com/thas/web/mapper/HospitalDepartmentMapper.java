package com.thas.web.mapper;

import com.thas.web.domain.HospitalDepartment;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 医疗结构-临床服务/医技服务开放关联Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-19
 */
@Mapper
public interface HospitalDepartmentMapper
{
    /**
     * 查询医疗结构-临床服务/医技服务开放关联
     *
     * @param id 医疗结构-临床服务/医技服务开放关联主键
     * @return 医疗结构-临床服务/医技服务开放关联
     */
    HospitalDepartment selectHospitalDepartmentById(Long id);

    /**
     * 查询医疗结构-临床服务/医技服务开放关联
     *
     * @param applyNo 医疗结构-认证编号
     * @return 医疗结构-临床服务/医技服务开放关联
     */
    List<HospitalDepartment> selectHospitalDepartmentByApplyNo(String applyNo);

    /**
     * 查询医疗结构-临床服务/医技服务开放关联列表
     *
     * @param hospitalDepartment 医疗结构-临床服务/医技服务开放关联
     * @return 医疗结构-临床服务/医技服务开放关联集合
     */
    List<HospitalDepartment> selectHospitalDepartmentList(HospitalDepartment hospitalDepartment);

    /**
     * 新增医疗结构-临床服务/医技服务开放关联
     *
     * @param hospitalDepartment 医疗结构-临床服务/医技服务开放关联
     * @return 结果
     */
    int insertHospitalDepartment(HospitalDepartment hospitalDepartment);

    /**
     * 修改医疗结构-临床服务/医技服务开放关联
     *
     * @param hospitalDepartment 医疗结构-临床服务/医技服务开放关联
     * @return 结果
     */
    int updateHospitalDepartment(HospitalDepartment hospitalDepartment);


    /**
     * 查询医疗结构专属科室信息   --- hos_exclusive_department
     *
     * @param applyNo 医疗结构-认证编号
     * @return 医疗结构专属科室信息
     */
    List<HospitalDepartment> selectHospitalExclusiveDepartmentByApplyNo(String applyNo);

    /**
     * 新增医疗结构专属科室信息   --- hos_exclusive_department
     *
     * @param hospitalDepartment 医疗结构-临床服务/医技服务开放关联
     * @return 结果
     */
    int insertHospitalExclusiveDepartment(HospitalDepartment hospitalDepartment);

    /**
     * 根据applyNos,批量查询相关医院临床服务信息
     *
     * @param applyNos 医疗结构-认证编号
     * @return 医疗结构-临床服务
     */
    List<HospitalDepartment> selectClinicalServicesByApplyNos(List<String> applyNos);

    /**
     * 根据applyNos,批量查询相关医院医技服务信息
     *
     * @param applyNos 医疗结构-认证编号
     * @return 医疗结构-医技服务
     */
    List<HospitalDepartment> selectMedicalServiceByApplyNos(List<String> applyNos);

    void deleteHospitalDepartmentByApplyNo(String applyNo);

    void deleteHospitalExclusiveDepartmentByApplyNo(String applyNo);
}
