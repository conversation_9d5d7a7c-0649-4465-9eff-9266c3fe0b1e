package com.thas.web.mapper;

import com.thas.web.domain.HospitalBaseInfo;
import com.thas.web.domain.HospitalReviewer;
import com.thas.web.domain.vo.HospitalReviewerAndAutSaRelationVO;
import com.thas.web.domain.vo.HospitalReviewerVo;
import com.thas.web.domain.vo.ReviewInterestVO;
import com.thas.web.dto.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 医疗结构认证信息与评审员信息Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-25
 */
public interface HospitalReviewerMapper {
    /**
     * 查询医疗结构认证信息与评审员信息
     *
     * @param id 医疗结构认证信息与评审员信息主键
     * @return 医疗结构认证信息与评审员信息
     */
    public HospitalReviewer selectHospitalReviewerById(Long id);

    List<HospitalReviewer> selectHospitalReviewerByApplyNo(String applyNo);

    List<HospitalReviewer> selectHospitalReviewerByAutCode(String autCode);

    List<Map<String, Object>> selectReviewerUserInfoByApplyNo(String applyNo);

    HospitalReviewer selectHospitalReviewerByApplyNoAndAccountId(@Param("applyNo") String applyNo, @Param("accountId") String accountId);

    List<HosPlanUserInfoVO> selectHosPlanUserInfoByAccountId(List<HospitalReviewer> hospitalPreExamList);

    /**
     * 查询医疗结构认证信息与评审员信息列表
     *
     * @param hospitalReviewer 医疗结构认证信息与评审员信息
     * @return 医疗结构认证信息与评审员信息集合
     */
    public List<HospitalReviewer> selectHospitalReviewerList(HospitalReviewer hospitalReviewer);

    /**
     * 新增医疗结构认证信息与评审员信息
     *
     * @param hospitalReviewer 医疗结构认证信息与评审员信息
     * @return 结果
     */
    public int insertHospitalReviewer(HospitalReviewer hospitalReviewer);

    /**
     * 修改医疗结构认证信息与评审员信息
     *
     * @param hospitalReviewer 医疗结构认证信息与评审员信息
     * @return 结果
     */
    public int updateHospitalReviewer(HospitalReviewer hospitalReviewer);

    /**
     * 删除医疗结构认证信息与评审员信息
     *
     * @param id 医疗结构认证信息与评审员信息主键
     * @return 结果
     */
    public int deleteHospitalReviewerById(Long id);

    /**
     * 批量删除医疗结构认证信息与评审员信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHospitalReviewerByIds(Long[] ids);

    /**
     * 修改医疗结构评审计划中对应评审员的状态
     *
     * @param applyNo      医疗机构认证编码
     * @param reviewerFlag 评审员标识
     * @return 数据库影响条数
     */
    int updateReviewerStatusByApplyNoAndStatus(@Param("applyNo") String applyNo, @Param("reviewerFlag") String reviewerFlag);

    /**
     * 将待审核的状态改为同意
     *
     * @param applyNo      医疗机构认证编码
     * @param reviewerFlag 评审员标识
     * @return
     */
    public int updateReviewerStatusByApplyNo(@Param("applyNo") String applyNo, @Param("reviewerFlag") String reviewerFlag);

    HospitalReviewer selectHospitalReviewerByLeader(@Param("applyNo") String applyNo, @Param("leaderIs") Integer leaderIs);

    int isAllocation(@Param("applyNo") String applyNo, @Param("status") Integer status);

    /**
     * 根据评审员账户id获取关联的医疗机构编码
     *
     * @param reviewerId 评审员账户id
     * @return 结果
     */
    List<HospitalBaseInfo> selectApplyNosByReviewerId(@Param("reviewerId") String reviewerId, @Param("roleType") String roleType);

    List<ReviewManageVO> queryReviewManage(ReviewManageDTO reviewManageDTO);

    int updateHospitalReviewerByApplyNo(HospitalReviewer hospitalReviewer);

    int updateHosRecByAplNAndFil(HospitalReviewer hospitalReviewer);

    List<HospitalReviewer> selectHospitalSeniorReviewByApplyNo(String applyNo);

    List<SysUserBaseInfo> selectSeniorReviewerList(String roleKey);

    List<HospitalReviewer> selectHospitalReviewerByApplyNoAndFieldId(@Param("applyNo") String applyNo, @Param("fieldIdList") String fieldIdList);

    /**
     * 根据评审员账户id获取在途数量
     *
     * @param reviewerId 评审员账户id
     * @return 结果
     */
    int selectStatusByReviewerId(@Param("reviewerId") Long reviewerId);

    /**
     * 根据医院认证编码，获取对应的组员账户id
     *
     * @param applyNo 医院认证编码id
     * @return 结果
     */
    List<String> selectHospitalReviewerIsCrewIdByApplyNo(@Param("applyNo") String applyNo);

    /**
     * 根据评审员账户id和医院认证编码，判断评审员是否是组长
     *
     * @param reviewerId 评审员账户id
     * @param applyNo    医院认证编码id
     * @return 结果
     */
    int selectHospitalReviewerByReviewerIdAndApplyNo(@Param("reviewerId") String reviewerId, @Param("applyNo") String applyNo);

    /**
     * 根据医院applyNo,查询医疗机构认证信息对应评审员ids
     *
     * @param applyNo 医疗结构认证信息与评审员信息主键
     * @return 对应评审员ids
     */
    public List<String> selectHospitalReviewerReviewerIdsByApplyNo(String applyNo);

    /**
     * 根据评审员id,查询对应医疗机构认证信息apply_no
     *
     * @param reviewerId 医疗结构认证信息与评审员信息主键
     * @return 对应医疗机构认证信息apply_no
     */
    public List<String> selectHospitalReviewerApplyNosByReviewerId(@Param("reviewerId") String reviewerId, @Param("fieldIdList") String fieldIdList);


    /**
     * 通过申请号查询被拒绝的评审员的利益冲突文件
     *
     * @param applyNo 申请号
     * @return String
     */
    List<String> selectRejInterestFileId(String applyNo);

    /**
     * 根据医院applyNo和角色Key,获取对应角色的评审员ids
     *
     * @param applyNo 医疗结构认证信息与评审员信息主键
     * @param roleKey 角色Key
     * @return 对应评审员ids
     */
    public List<String> selectHospitalReviewerReviewerIdsByApplyNoAndRoleKey(@Param("applyNo") String applyNo, @Param("roleKey") String roleKey);

    /**
     * 根据评审员ids和医院applyNo, 批量删除医疗机构认证信息
     *
     * @param reviewerIds 评审员ids
     * @param applyNo
     * @return 结果
     */
    int deleteHospitalReviewerByReviewerIdsAndApplyNo(@Param("reviewerIds") List<String> reviewerIds, @Param("applyNo") String applyNo);

    List<ReviewInterestVO> selectReviewInterestVOByAccountId(String accountId);

    List<HospitalReviewer> selectCheckInterestFile(String applyNo);

    HospitalReviewer selectHosRevByAlnAndAct(@Param("applyNo") String applyNo, @Param("accountId") String accountId);

    List<HospitalReviewerAndAutSaRelationVO> selectHospitalReviewerAndAutSaRelationByReviewerIds(List<String> accountIds);

    /**
     * 通过领域id查询对应领域下所有的评审员
     *
     * @param domainIdList 领域idList
     * @return HospitalBaseInfo
     */
    List<SysUserBaseInfo> selectUserByField(List<String> domainIdList);

    /**
     * 入参评审员ID，查询为学员时且为结束流程且完成现场评审节点参加的医院，既评审员角色还有学员角色参与医院评审未结束的评审数据
     *
     * @param reviewerIds 评审员Ids
     * */
    List<String> selectTraineesReviewHospitalReviewerByReviewerIds(@Param("reviewerIds") Set<String> reviewerIds);

    /**
     * 查询已排除有学员数据的评审员是否已分配
     * 入参评审员ID，因上面sql(selectTraineesReviewHospitalReviewerByReviewerIds)返回的已排除评审员的周期校验，如果已分配了评审员，下面这条sql会有数据，那么就需求周期校验
     *
     * @param reviewerIds 评审员Ids(有做为学员时的在途数据)
     * */
    List<String> selectExcludedReviewHospitalReviewerByReviewerIds(@Param("reviewerIds") List<String> reviewerIds);

    int updateHospitalReviewerByApplyNoList(@Param("list") List<String> applyNoList);

    List<ReviewCycleJudgeVo> workReviewer(@Param("list")List<String> disReviewerIds,@Param("fieldIdList")String fieldIdList);

    List<HospitalReviewerVo> selectHospitalReviewerByTraineesAssessorReviewerId(@Param("reviewerId")String reviewerId);

    List<HospitalReviewerVo> selectHospitalReviewerAndCstDomainByAutCode(@Param("autCode") String autCode);
}
