package com.thas.web.mapper;

import com.thas.web.domain.TrainingEvaluateResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 培训评估结果Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-09-18
 */
public interface TrainingEvaluateResultMapper 
{
    /**
     * 查询培训评估结果
     * 
     * @param resultId 培训评估结果主键
     * @return 培训评估结果
     */
    public TrainingEvaluateResult selectTrainingEvaluateResultByResultId(Long resultId);

    /**
     * 查询培训评估结果列表
     * 
     * @param trainingEvaluateResult 培训评估结果
     * @return 培训评估结果集合
     */
    public List<TrainingEvaluateResult> selectTrainingEvaluateResultList(TrainingEvaluateResult trainingEvaluateResult);

    /**
     * 新增培训评估结果
     * 
     * @param trainingEvaluateResult 培训评估结果
     * @return 结果
     */
    public int insertTrainingEvaluateResult(TrainingEvaluateResult trainingEvaluateResult);

    /**
     * 修改培训评估结果
     * 
     * @param trainingEvaluateResult 培训评估结果
     * @return 结果
     */
    public int updateTrainingEvaluateResult(TrainingEvaluateResult trainingEvaluateResult);

    /**
     * 批量修改培训评估结果
     *
     * @param trainingEvaluateResultList 培训评估结果
     * @return 结果
     */
    public int batchUpdateTrainingEvaluateResult(@Param("list") List<TrainingEvaluateResult> trainingEvaluateResultList);

    /**
     * 删除培训评估结果
     * 
     * @param resultId 培训评估结果主键
     * @return 结果
     */
    public int deleteTrainingEvaluateResultByResultId(Long resultId);

    /**
     * 批量删除培训评估结果
     * 
     * @param resultIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTrainingEvaluateResultByResultIds(Long[] resultIds);

    List<TrainingEvaluateResult> selectTrainingEvaluateResultListByConclusion(@Param("conclusion") String conclusion);


    /**
     * 通过评审学员id查询培训评估结果表
     * @param traineesAssessorIdList id
     * @return TrainingEvaluateResult
     */
    List<TrainingEvaluateResult> selectTrainingEvaluateResultByTraineesAssessorIdList(List<String> traineesAssessorIdList);

    /**
     * 防止索引失效
     *
     * @param idList ids
     * @return TrainingEvaluateResult
     */
    List<TrainingEvaluateResult> selectByIdList(List<Long> idList);
}
