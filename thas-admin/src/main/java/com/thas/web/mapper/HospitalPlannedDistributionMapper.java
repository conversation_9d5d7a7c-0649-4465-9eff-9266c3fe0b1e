package com.thas.web.mapper;

import java.util.List;
import com.thas.web.domain.HospitalPlannedDistribution;
import com.thas.web.domain.vo.HospitalPlannedDistributionVo;
import org.apache.ibatis.annotations.Param;

/**
 * 医疗机构分配计划详情Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-27
 */
public interface HospitalPlannedDistributionMapper
{
    /**
     * 查询医疗机构分配计划详情
     *
     * @param id 医疗机构分配计划详情主键
     * @return 医疗机构分配计划详情
     */
    public HospitalPlannedDistribution selectHospitalPlannedDistributionById(Long id);

    /**
     * 查询医疗机构分配计划详情列表
     *
     * @param hospitalPlannedDistribution 医疗机构分配计划详情
     * @return 医疗机构分配计划详情集合
     */
    public List<HospitalPlannedDistribution> selectHospitalPlannedDistributionList(HospitalPlannedDistribution hospitalPlannedDistribution);

    /**
     * 新增医疗机构分配计划详情
     *
     * @param hospitalPlannedDistribution 医疗机构分配计划详情
     * @return 结果
     */
    public int insertHospitalPlannedDistribution(HospitalPlannedDistribution hospitalPlannedDistribution);

    /**
     * 修改医疗机构分配计划详情
     *
     * @param hospitalPlannedDistribution 医疗机构分配计划详情
     * @return 结果
     */
    public int updateHospitalPlannedDistribution(HospitalPlannedDistribution hospitalPlannedDistribution);

    /**
     * 删除医疗机构分配计划详情
     *
     * @param id 医疗机构分配计划详情主键
     * @return 结果
     */
    public int deleteHospitalPlannedDistributionById(Long id);

    /**
     * 批量删除医疗机构分配计划详情
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHospitalPlannedDistributionByIds(Long[] ids);

    /**
     * 查找医疗机构分配计划所有有关详情
     * @param applyNo 医疗结构认证编码
     * @return 分配详情
     */
    HospitalPlannedDistributionVo selectPlannedDistributionInfoByApplyNo(String applyNo);

    /**
     * 通过applyNo将cycleStatus改为1
     * @param applyNo
     * @return
     */
    public int updateCycleStatusByApplyNo(String applyNo);

    HospitalPlannedDistribution selectHospitalPlannedDistributionByApplyNo(String applyNo);

    /**
     * 查找最新的医疗机构分配计划所有有关详情
     * @return
     */
    public HospitalPlannedDistributionVo selectPlannedDistributionInfoByLast();

    int updateHospitalPlannedDistributionByApplyNo(HospitalPlannedDistribution hospitalPlannedDistribution);

    int updateHospitalPlannedDistributionByApplyNoList(@Param("list") List<String> applyNoList);
}
