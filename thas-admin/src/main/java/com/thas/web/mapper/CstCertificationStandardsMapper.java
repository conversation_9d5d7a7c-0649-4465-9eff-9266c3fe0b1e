package com.thas.web.mapper;

import com.thas.web.domain.CstCertificationStandards;
import com.thas.web.domain.dto.CstCertificationStandardsDetailQueryDTO;
import com.thas.web.domain.vo.ChapterVo;
import com.thas.web.dto.CstCertificationStandardVO;
import com.thas.web.dto.GroupClauseInfo;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;


/**
 * 认证标准模板Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-21
 */
public interface CstCertificationStandardsMapper {
    /**
     * 查询认证标准模板
     *
     * @param id 认证标准模板主键
     * @return 认证标准模板
     */
    public CstCertificationStandards selectCstCertificationStandardsById(Long id);

    /**
     * 查询认证标准模板列表
     *
     * @param cstCertificationStandards 认证标准模板
     * @return 认证标准模板集合
     */
    public List<CstCertificationStandards> selectCstCertificationStandardsList(CstCertificationStandards cstCertificationStandards);

    /**
     * 新增认证标准模板
     *
     * @param cstCertificationStandards 认证标准模板
     * @return 结果
     */
    public int insertCstCertificationStandards(CstCertificationStandards cstCertificationStandards);

    /**
     * 修改认证标准模板
     *
     * @param cstCertificationStandards 认证标准模板
     * @return 结果
     */
    public int updateCstCertificationStandards(CstCertificationStandards cstCertificationStandards);

    /**
     * 删除认证标准模板
     *
     * @param id 认证标准模板主键
     * @return 结果
     */
    public int deleteCstCertificationStandardsById(Long id);

    /**
     * 批量删除认证标准模板
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCstCertificationStandardsByIds(Long[] ids);

    /**
     * 获取最新版本标准
     *
     * @param versionId
     * @return
     */
    public List<ChapterVo> selectByVersionId(Long versionId);


    /**
     * 根据版本好和条款id获取对应版本标准
     *
     * @param versionId
     * @return
     */
    public List<ChapterVo> selectClauseDetailByVersionId(@Param(value = "versionId") Long versionId, @Param(value = "articleId") Long articleId);

    /**
     * 通过章id,节id,条id,款id，查找详情
     *
     * @param cstCertificationStandardsDetailQueryDTO
     * @return
     */
    public CstCertificationStandards selectDetailByVersionIdAndClauseId(CstCertificationStandardsDetailQueryDTO cstCertificationStandardsDetailQueryDTO);

    /**
     * 按照款id和版本id修改认证标准模板
     *
     * @param cstCertificationStandards 认证标准模板
     * @return 结果
     */
    public int updateByClauseIdAndVersionId(CstCertificationStandards cstCertificationStandards);

    /**
     * 按照款列表ids和版本修改认证标准
     *
     * @param paramMap
     * @return 结果
     */
    public int updateByClauseIdAndVersionIds(Map paramMap);

    /**
     * 按照领域id和版本修改认证标准
     *
     * @param cstCertificationStandards
     * @return 结果
     */
    public int updateByDomainIdAndVersionId(CstCertificationStandards cstCertificationStandards);


    /**
     * 关闭其他版本的认证标准
     *
     * @return
     */
    public int updateStatusDisableByOtherVersionId(Long versionId);

    /**
     * 通过版本id启用
     *
     * @param versionId
     * @return
     */
    public int updateStatusStartByVersionId(Long versionId);

    /**
     * 查询版本对应的总款数
     *
     * @param versionId
     * @return
     */
    Map<String, Integer> selectCountByVersionId(String versionId);

    /**
     * 查询版本id对应的领域id数
     *
     * @param versionId
     * @return
     */
    int selectCountDoMianIdByVersionId(Long versionId);


    /**
     * 根据status,del_flag,查询版本id对应的版本数据数
     *
     * @param versionId
     * @return
     */
    int selectCountVersioningByVersionId(Long versionId);

    List<CstCertificationStandards> selectByClauseIdsAndVersionId(@Param("versionId") Long versionId, @Param("clauseIds") List<Long> clauseIds);

    List<CstCertificationStandardVO> selectCstCertificationStandardVOByIds(String ids);

    List<CstCertificationStandardVO> selectCstCertificationStandardVONoInIds(@Param("ids") String ids, @Param("versionId") Long versionId);

    List<CstCertificationStandardVO> selectCstCertificationStandardsByDomainId(@Param("domainIds") String domainIds, @Param("versionId") Long versionId);

    List<CstCertificationStandardVO> selectCstCertificationStandardsByClauseIds(@Param("clauseIds") Collection<String> clauseIds, @Param("versionId") Long versionId);

    List<CstCertificationStandardVO> selectCstCertificationStandardsNotInDomainId(@Param("domainIds") String domainIds, @Param("versionId") Long versionId);

    List<GroupClauseInfo> selectGroupClause(@Param("domainIds") String domainIds, @Param("versionId") Long versionId);

    Integer selectCstCertificationStandardsCountByVersionId(Long versionId);

    Integer selectCstCertificationStandardsCountByDomainIds(@Param("domainIds") String fieldIdList, @Param("versionId") Long versionId);

    /**
     * 根据版本号查询所有的款id
     *
     * @param versionId
     * @return
     */
    List<CstCertificationStandards> selectAllClauseIdByVersionId(@Param("versionId") String versionId);

    List<String> selectDomainIdListByVersionId(Long versionId);

    /**
     * 通过版本号查询所有组id和款项id
     *
     * @param versionId 版本号
     * @return  List<Map<String, Object>>
     */
    List<CstCertificationStandards> selectDomainIdAndClauseIdByVersionId(Long versionId);

    /**
     * 获取版本号对应的认证标准模板数据
     *
     * @param versionId
     * @return
     */
    List<ChapterVo> selectCstCertificationStandardsByVersionId(@Param("versionId") String versionId);

    /**
     * 获取款id对应的款数
     *
     * @param clauseIds
     * @param autCsId 版本号
     * @return
     */
    List<String> selectClauseNosByClauseIds(@Param("clauseIds") List<String> clauseIds, @Param("autCsId") String autCsId);
}

