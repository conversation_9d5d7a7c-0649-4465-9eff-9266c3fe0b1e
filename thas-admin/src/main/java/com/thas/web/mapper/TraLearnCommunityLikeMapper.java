package com.thas.web.mapper;


import com.thas.web.domain.TraLearnCommunityLike;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 学习社区点赞Mapper接口
 *
 * <AUTHOR>
 * @date 2022-02-15
 */
@Mapper
public interface TraLearnCommunityLikeMapper {
    /**
     * 查询学习社区点赞
     *
     * @param id 学习社区点赞主键
     * @return 学习社区点赞
     */
    TraLearnCommunityLike selectTraLearnCommunityLikeById(Long id);

    /**
     * 查询学习社区点赞列表
     *
     * @param traLearnCommunityLike 学习社区点赞
     * @return 学习社区点赞集合
     */
    List<TraLearnCommunityLike> selectTraLearnCommunityLikeList(TraLearnCommunityLike traLearnCommunityLike);

    /**
     * 新增学习社区点赞
     *
     * @param traLearnCommunityLike 学习社区点赞
     * @return 结果
     */
    int insertTraLearnCommunityLike(TraLearnCommunityLike traLearnCommunityLike);

    /**
     * 修改学习社区点赞
     *
     * @param traLearnCommunityLike 学习社区点赞
     * @return 结果
     */
    int updateTraLearnCommunityLike(TraLearnCommunityLike traLearnCommunityLike);

    /**
     * 删除学习社区点赞
     *
     * @param id 学习社区点赞主键
     * @return 结果
     */
    int deleteTraLearnCommunityLikeById(Long id);

    /**
     * 批量删除学习社区点赞
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteTraLearnCommunityLikeByIds(Long[] ids);

    int getCountByCommunityId(Long communityId);

    int deleteTraLearnCommunityLike(TraLearnCommunityLike traLearnCommunityLike);

}
