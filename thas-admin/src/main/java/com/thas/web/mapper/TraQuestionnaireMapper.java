package com.thas.web.mapper;

import com.thas.web.domain.TraQuestionnaire;
import com.thas.web.domain.dto.TraQuestionnaireDTO;
import com.thas.web.domain.vo.TraQuestionnaireVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 调查问卷Mapper接口
 *
 * <AUTHOR>
 * @date 2022-02-14
 */
@Mapper
public interface TraQuestionnaireMapper {
    /**
     * 查询调查问卷
     *
     * @param id 调查问卷主键
     * @return 调查问卷
     */
    TraQuestionnaireVO selectTraQuestionnaireById(Long id);

    /**
     * 查询调查问卷列表
     *
     * @param traQuestionnaireDTO 调查问卷
     * @return 调查问卷集合
     */
    List<TraQuestionnaireVO> selectTraQuestionnaireList(TraQuestionnaireDTO traQuestionnaireDTO);

    /**
     * 新增调查问卷
     *
     * @param traQuestionnaire 调查问卷
     * @return 结果
     */
    int insertTraQuestionnaire(TraQuestionnaire traQuestionnaire);

    /**
     * 修改调查问卷
     *
     * @param traQuestionnaire 调查问卷
     * @return 结果
     */
    int updateTraQuestionnaire(TraQuestionnaire traQuestionnaire);

    /**
     * 删除调查问卷
     *
     * @param id 调查问卷主键
     * @return 结果
     */
    int deleteTraQuestionnaireById(Long id);

    /**
     * 批量删除调查问卷
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteTraQuestionnaireByIds(Long[] ids);

    int updateStatus(@Param("status") Integer status, @Param("id") Long id);
}
