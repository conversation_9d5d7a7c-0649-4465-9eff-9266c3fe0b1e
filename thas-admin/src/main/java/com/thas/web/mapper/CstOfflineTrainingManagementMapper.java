package com.thas.web.mapper;


import com.thas.web.domain.CstOfflineTrainingManagement;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 线下培训管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-01-25
 */

public interface CstOfflineTrainingManagementMapper 
{
    /**
     * 查询线下培训管理
     * 
     * @param id 线下培训管理主键
     * @return 线下培训管理
     */
    public CstOfflineTrainingManagement selectCstOfflineTrainingManagementById(Long id);

    /**
     * 查询线下培训管理列表
     * 
     * @param cstOfflineTrainingManagement 线下培训管理
     * @return 线下培训管理集合
     */
    public List<CstOfflineTrainingManagement> selectCstOfflineTrainingManagementList(CstOfflineTrainingManagement cstOfflineTrainingManagement);

    /**
     * 新增线下培训管理
     * 
     * @param cstOfflineTrainingManagement 线下培训管理
     * @return 结果
     */
    public int insertCstOfflineTrainingManagement(CstOfflineTrainingManagement cstOfflineTrainingManagement);

    /**
     * 修改线下培训管理
     * 
     * @param cstOfflineTrainingManagement 线下培训管理
     * @return 结果
     */
    public int updateCstOfflineTrainingManagement(CstOfflineTrainingManagement cstOfflineTrainingManagement);

    /**
     * 删除线下培训管理
     * 
     * @param id 线下培训管理主键
     * @return 结果
     */
    int deleteCstOfflineTrainingManagementById(Long id);

    /**
     * 批量删除线下培训管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteCstOfflineTrainingManagementByIds(Long[] ids);



    CstOfflineTrainingManagement getByManagementId(Long id);

    /**
     * 修改培训管理状态
     * @param cstOfflineTrainingManagement
     * @return
     */
    int updateStatus(CstOfflineTrainingManagement cstOfflineTrainingManagement);


    /**
     * 通过当前时间和表中的举办时间比较
     * 判断是否已过期，修改状态
     */
    void updateTrainingStatusByNowTime(List<Long> ids);

    List<CstOfflineTrainingManagement> selectCstOfflineTrainingManagementByAccountId(String accountId);

    /**
     * 根据当前登录用户id且为未报名和未参与状态,查询线下培训管理表
     *
     * */
    Long selectCstOfflineTrainingManagementByAccountIdAndStatus(@Param("accountId") String accountId);

    List<Long> selectIdsByTime();
}
