package com.thas.web.mapper;

import com.thas.web.domain.SendDetailRes;
import com.thas.web.domain.StatisticsReportVo;
import com.thas.web.domain.TraQuestionnaireFeedBackRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 反馈问卷填写统计记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-12-12
 */
public interface TraQuestionnaireFeedBackRecordMapper 
{
    /**
     * 查询反馈问卷填写统计记录
     * 
     * @param recordId 反馈问卷填写统计记录主键
     * @return 反馈问卷填写统计记录
     */
    public TraQuestionnaireFeedBackRecord selectTraQuestionnaireFeedBackRecordByRecordId(Long recordId);

    /**
     * 查询反馈问卷填写统计记录列表
     * 
     * @param traQuestionnaireFeedBackRecord 反馈问卷填写统计记录
     * @return 反馈问卷填写统计记录集合
     */
    public List<TraQuestionnaireFeedBackRecord> selectTraQuestionnaireFeedBackRecordList(TraQuestionnaireFeedBackRecord traQuestionnaireFeedBackRecord);

    /**
     * 查询反馈问卷填写统计记录列表包含用户信息
     *
     * @param traQuestionnaireFeedBackRecord 反馈问卷填写统计记录
     * @return 反馈问卷填写统计记录集合
     */
    public List<SendDetailRes> selectTraQuestionnaireFeedBackRecordAndUserInfoList(TraQuestionnaireFeedBackRecord traQuestionnaireFeedBackRecord);

    /**
     * 查询反馈问卷填写统计记录列表包含医院信息
     *
     * @param traQuestionnaireFeedBackRecord 反馈问卷填写统计记录
     * @return 反馈问卷填写统计记录集合
     */
    public List<SendDetailRes> selectTraQuestionnaireFeedBackRecordAndHospitalInfoList(TraQuestionnaireFeedBackRecord traQuestionnaireFeedBackRecord);

    /**
     * 查询反馈问卷填写统计记录列表（按创建时间正序排序）
     *
     * @param traQuestionnaireFeedBackRecord 反馈问卷填写统计记录
     * @return 反馈问卷填写统计记录集合
     */
    public List<TraQuestionnaireFeedBackRecord> selectTraQuestionnaireFeedBackRecordListByTimeASC(TraQuestionnaireFeedBackRecord traQuestionnaireFeedBackRecord);

    /**
     * 新增反馈问卷填写统计记录
     * 
     * @param traQuestionnaireFeedBackRecord 反馈问卷填写统计记录
     * @return 结果
     */
    public int insertTraQuestionnaireFeedBackRecord(TraQuestionnaireFeedBackRecord traQuestionnaireFeedBackRecord);

    /**
     * 批量新增反馈问卷填写统计记录
     *
     * @param traQuestionnaireFeedBackRecordList 反馈问卷填写统计记录
     * @param autCodeFlag 是否有自评编码数据 0-否 1-是
     * @return 结果
     */
    public int insertBatchTraQuestionnaireFeedBackRecord(@Param("list") List<TraQuestionnaireFeedBackRecord> traQuestionnaireFeedBackRecordList, @Param("autCodeFlag") Integer autCodeFlag);

    /**
     * 修改反馈问卷填写统计记录
     * 
     * @param traQuestionnaireFeedBackRecord 反馈问卷填写统计记录
     * @return 结果
     */
    public int updateTraQuestionnaireFeedBackRecord(TraQuestionnaireFeedBackRecord traQuestionnaireFeedBackRecord);

    /**
     * 批量修改反馈问卷填写统计记录
     *
     * @param traQuestionnaireFeedBackRecordList 反馈问卷填写统计记录
     * @return 结果
     */
    public int updateTraQuestionnaireFeedBackRecordList(List<TraQuestionnaireFeedBackRecord> traQuestionnaireFeedBackRecordList);

    /**
     * 删除反馈问卷填写统计记录
     * 
     * @param recordId 反馈问卷填写统计记录主键
     * @return 结果
     */
    public int deleteTraQuestionnaireFeedBackRecordByRecordId(Long recordId);

    /**
     * 批量删除反馈问卷填写统计记录
     * 
     * @param recordIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTraQuestionnaireFeedBackRecordByRecordIds(Long[] recordIds);


    /**
     * 查询评审相关的统计报表数据（统计报表-培训教员的培训工作量（培训的次数））
     *
     * @param
     * @return
     */
    List<StatisticsReportVo> selectTrainTeacherStatisticsReport();

    /**
     * 查询反馈表对应下发筛选条件数据
     *
     * @param
     * @param needIdList 需填写的用户Id(下发的用户Id)
     * @return 返回筛选的Id
     */
    List<String> selectTraQuestionnaireFeedBackRecordIdsBySendCondition(@Param("needIdList") List<String> needIdList,@Param("questionnaireId") Long questionnaireId );

    /**
     * 查询教员未填写对应学员的教员反馈表
     *
     * @param  needIdList 需要填写人员ids
     * @param  resultIdList 评估结果Ids
     * @return 返回用户昵称
     */
     List<String> selectTraQuestionnaireFeedBackRecordListByTrainerCondition(@Param("needIdList") List<String> needIdList,@Param("resultIdList") List<Long> resultIdList );
}
