package com.thas.web.mapper;

import java.util.List;
import com.thas.web.domain.HospitalAuthContact;
import org.apache.ibatis.annotations.Mapper;

/**
 * 医疗机构被授权人信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-01-19
 */
@Mapper
public interface HospitalAuthContactMapper 
{
    /**
     * 查询医疗机构被授权人信息
     * 
     * @param id 医疗机构被授权人信息主键
     * @return 医疗机构被授权人信息
     */
    HospitalAuthContact selectHospitalAuthContactById(Long id);


    /**
     * 查询医疗机构被授权人信息
     *
     * @param applyNo 医疗机构认证编号
     * @return 医疗机构被授权人信息
     */
    HospitalAuthContact selectHospitalAuthContactByApplyNo(String applyNo);

    /**
     * 查询医疗机构被授权人信息列表
     * 
     * @param hospitalAuthContact 医疗机构被授权人信息
     * @return 医疗机构被授权人信息集合
     */
    List<HospitalAuthContact> selectHospitalAuthContactList(HospitalAuthContact hospitalAuthContact);

    /**
     * 新增医疗机构被授权人信息
     * 
     * @param hospitalAuthContact 医疗机构被授权人信息
     * @return 结果
     */
    int insertHospitalAuthContact(HospitalAuthContact hospitalAuthContact);

    /**
     * 修改医疗机构被授权人信息
     * 
     * @param hospitalAuthContact 医疗机构被授权人信息
     * @return 结果
     */
    int updateHospitalAuthContact(HospitalAuthContact hospitalAuthContact);

    /**
     * 删除医疗机构被授权人信息
     * 
     * @param id 医疗机构被授权人信息主键
     * @return 结果
     */
    int deleteHospitalAuthContactById(Long id);

    /**
     * 批量删除医疗机构被授权人信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteHospitalAuthContactByIds(Long[] ids);

    void deleteHospitalAuthContactByApplyNo(String applyNo);
}
