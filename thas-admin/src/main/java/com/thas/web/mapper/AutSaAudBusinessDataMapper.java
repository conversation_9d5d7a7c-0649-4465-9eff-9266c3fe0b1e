package com.thas.web.mapper;

import com.thas.web.domain.AutSaAudBusinessData;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 自评审核业务数据 Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-18
 */
@Mapper
public interface AutSaAudBusinessDataMapper {

    /**
     * 查询自评审核业务数据
     *
     * @param autCodes 自评编码
     * @param businessCode 环节
     * @return 自评审核业务数据
     */
    List<AutSaAudBusinessData> selectAutSaAudBusinessData(@Param("autCodes") String autCodes,@Param("businessCode")  String businessCode);

    /**
     * 保存自评审核业务数据
     *
     * @param autSaAudBusinessData 自评审核业务数据
     * @return 结果
     */
    int saveAutSaAudBusinessData(AutSaAudBusinessData autSaAudBusinessData);

    /**
     * 删除自评审核业务数据
     *
     * @param autCode 自评编码
     * @param businessCode 环节
     * @return 自评审核业务数据
     * @return 结果
     */
    int deleteAutSaAudBusinessData(@Param("autCode") String autCode,@Param("businessCode")  String businessCode);

    /**
     * 更新自评审核业务数据
     *
     * @param autSaAudBusinessData 自评审核业务数据
     * @return 结果
     */
    int updateAutSaAudBusinessData(AutSaAudBusinessData autSaAudBusinessData);
}
