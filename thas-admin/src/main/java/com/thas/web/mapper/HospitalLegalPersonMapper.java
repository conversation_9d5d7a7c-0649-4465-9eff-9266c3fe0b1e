package com.thas.web.mapper;

import java.util.List;

import com.thas.web.domain.HospitalLegalPerson;
import org.apache.ibatis.annotations.Mapper;

/**
 * 医疗结构法人代详情Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-19
 */
@Mapper
public interface HospitalLegalPersonMapper {
    /**
     * 查询医疗结构法人代详情
     *
     * @param id 医疗结构法人代详情主键
     * @return 医疗结构法人代详情
     */
    HospitalLegalPerson selectHospitalLegalPersonById(Long id);

    HospitalLegalPerson selectHospitalLegalPersonByApplyNo(String applyNo);

    /**
     * 查询医疗结构法人代详情列表
     *
     * @param hospitalLegalPerson 医疗结构法人代详情
     * @return 医疗结构法人代详情集合
     */
    List<HospitalLegalPerson> selectHospitalLegalPersonList(HospitalLegalPerson hospitalLegalPerson);

    /**
     * 新增医疗结构法人代详情
     *
     * @param hospitalLegalPerson 医疗结构法人代详情
     * @return 结果
     */
    int insertHospitalLegalPerson(HospitalLegalPerson hospitalLegalPerson);

    /**
     * 新增医疗结构法人代详情
     *
     * @param hospitalLegalPerson 医疗结构法人代详情
     * @return 结果
     */
    int updateHospitalLegalPerson(HospitalLegalPerson hospitalLegalPerson);

    void deleteHospitalLegalPersonByApplyNo(String applyNo);
}
