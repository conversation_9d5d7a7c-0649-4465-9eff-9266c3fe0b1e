package com.thas.web.mapper;

import com.thas.web.domain.ReviewerBaseInfo;
import com.thas.web.dto.QueryReviewerListDTO;
import com.thas.web.dto.SysUserBaseInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 评审员基本信息Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-20
 */
@Mapper
public interface ReviewerBaseInfoMapper {

    /**
     * 新增评审员基本信息
     *
     * @param reviewerBaseInfo 评审员基本信息
     * @return 结果
     */
    int insertReviewerBaseInfo(ReviewerBaseInfo reviewerBaseInfo);

    int updateReviewerBaseInfoByAccountId(ReviewerBaseInfo reviewerBaseInfo);

    List<QueryReviewerListDTO> selectReviewerBaseInfos(QueryReviewerListDTO queryReviewerListDTO);

    ReviewerBaseInfo selectReviewerBaseInfoByAccountId(String accountId);

    List<ReviewerBaseInfo> selectReviewerBaseInfoList(ReviewerBaseInfo reviewerBaseInfo);

    List<ReviewerBaseInfo> selectReviewerBaseInfoByAccountIdList(@Param("list") List<String> accountIdList, @Param("authStatus") Integer authStatus);

    @Update(" update reviewer_base_info set submit_status = 1 where account_id = #{accountId}")
    int updateSubmitStatusByAccountId(String accountId);

    int selectCountByAccountId(String accountId);

    int selectCountByCertificateNumber(String certificateNumber);

    /**
     * 通过身份证号查询基本信息
     *
     * @param certificateNumber 身份证号
     * @return ReviewerBaseInfo
     */
    ReviewerBaseInfo selectByCertificateNumber(String certificateNumber);

    /**
     * 通过身份证号查询所有的评审员基本信息
     *
     * @param certificateNumber 身份证号码
     * @return ReviewerBaseInfo
     */
    List<ReviewerBaseInfo> selectReviewerBaseInfoByCertificateNumber(String certificateNumber);

    List<ReviewerBaseInfo> queryReviewerTmpList(ReviewerBaseInfo reviewerBaseInfo);

    void updateAccountId(@Param("userId") String userId, @Param("accountId") String accountId);

    List<SysUserBaseInfo> selectTraBaseInfoListByApplyNo(String applyNo);

    /**
     * 通过邮箱查询reviewer_base_info
     *
     * @param email 邮箱
     * @return ReviewerBaseInfo
     */
    List<ReviewerBaseInfo> qryReviewerBaseInfoByEmail(@Param("email") String email);
}
