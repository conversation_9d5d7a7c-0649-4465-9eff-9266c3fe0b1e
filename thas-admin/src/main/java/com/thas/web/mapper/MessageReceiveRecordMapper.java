package com.thas.web.mapper;

import java.util.List;
import com.thas.web.domain.MessageReceiveRecord;

/**
 * 消息接收记录Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-25
 */
public interface MessageReceiveRecordMapper
{
    /**
     * 查询消息接收记录
     *
     * @param id 消息接收记录主键
     * @return 消息接收记录
     */
    public MessageReceiveRecord selectMessageReceiveRecordById(Long id);

    /**
     * 查询消息接收记录列表
     *
     * @param messageReceiveRecord 消息接收记录
     * @return 消息接收记录集合
     */
    public List<MessageReceiveRecord> selectMessageReceiveRecordList(MessageReceiveRecord messageReceiveRecord);

    /**
     * 新增消息接收记录
     *
     * @param messageReceiveRecord 消息接收记录
     * @return 结果
     */
    public int insertMessageReceiveRecord(MessageReceiveRecord messageReceiveRecord);

    /**
     * 修改消息接收记录
     *
     * @param messageReceiveRecord 消息接收记录
     * @return 结果
     */
    public int updateMessageReceiveRecord(MessageReceiveRecord messageReceiveRecord);

    /**
     * 删除消息接收记录
     *
     * @param id 消息接收记录主键
     * @return 结果
     */
    public int deleteMessageReceiveRecordById(Long id);

    /**
     * 批量删除消息接收记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMessageReceiveRecordByIds(Long[] ids);

    /**
     * 批量插入
     * @param messageReceiveRecordList
     * @return
     */
    public int insertMessageReceiveRecordList(List<MessageReceiveRecord> messageReceiveRecordList);
}
