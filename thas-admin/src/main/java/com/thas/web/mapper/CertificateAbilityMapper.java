package com.thas.web.mapper;

import com.thas.web.domain.CertificateAbility;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 对应证书关联Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-01-19
 */
@Mapper
public interface CertificateAbilityMapper 
{
    /**
     * 查询对应证书关联
     * 
     * @param id 对应证书关联主键
     * @return 对应证书关联
     */
    CertificateAbility selectCertificateAbilityById(Long id);

    /**
     * 查询对应证书关联
     *
     * @param processCode 医疗结构认证编号
     * @return 对应证书关联
     */
    List<CertificateAbility> selectCertificateAbilityByProcessCode(String processCode);

    /**
     * 查询对应证书关联列表
     * 
     * @param certificateAbility 对应证书关联
     * @return 对应证书关联集合
     */
    public List<CertificateAbility> selectCertificateAbilityList(CertificateAbility certificateAbility);

    /**
     * 新增对应证书关联
     * 
     * @param certificateAbility 对应证书关联
     * @return 结果
     */
    public int insertCertificateAbility(CertificateAbility certificateAbility);

    /**
     * 修改对应证书关联
     * 
     * @param certificateAbility 对应证书关联
     * @return 结果
     */
    public int updateCertificateAbility(CertificateAbility certificateAbility);

    /**
     * 删除对应证书关联
     * 
     * @param id 对应证书关联主键
     * @return 结果
     */
    public int deleteCertificateAbilityById(Long id);

    /**
     * 批量删除对应证书关联
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCertificateAbilityByIds(Long[] ids);

    @Update("update certificate_ability set status = #{status} where process_code = #{accountId}")
    void updateStatusByProcessCode(@Param("accountId") String accountId, @Param("status") int status);

    void updateAccountId(@Param("userId")String valueOf, @Param("accountId")String accountId);

    void deleteCertificateAbilityByProcessCode(String applyNo);
}
