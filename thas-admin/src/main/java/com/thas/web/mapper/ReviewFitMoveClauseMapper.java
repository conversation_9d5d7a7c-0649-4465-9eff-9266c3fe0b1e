package com.thas.web.mapper;

import com.thas.web.domain.ReviewFitMoveClause;
import com.thas.web.domain.dto.ReviewFitMoveClauseReq;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 测试评审不适用与挪动款Mapper接口
 *
 * <AUTHOR>
 * @date 2023-03-28
 */
public interface ReviewFitMoveClauseMapper {
    /**
     * 查询测试评审不适用与挪动款
     *
     * @param id 测试评审不适用与挪动款主键
     * @return 测试评审不适用与挪动款
     */
    public ReviewFitMoveClause selectReviewFitMoveClauseById(Long id);

    /**
     * 查询测试评审不适用与挪动款列表
     *
     * @param reviewFitMoveClause 测试评审不适用与挪动款
     * @return 测试评审不适用与挪动款集合
     */
    public List<ReviewFitMoveClause> selectReviewFitMoveClauseList(ReviewFitMoveClause reviewFitMoveClause);

    /**
     * 新增测试评审不适用与挪动款
     *
     * @param reviewFitMoveClause 测试评审不适用与挪动款
     * @return 结果
     */
    public int insertReviewFitMoveClause(ReviewFitMoveClause reviewFitMoveClause);

    /**
     * 修改测试评审不适用与挪动款
     *
     * @param reviewFitMoveClause 测试评审不适用与挪动款
     * @return 结果
     */
    public int updateReviewFitMoveClause(ReviewFitMoveClause reviewFitMoveClause);

    /**
     * 删除测试评审不适用与挪动款
     *
     * @param id 测试评审不适用与挪动款主键
     * @return 结果
     */
    public int deleteReviewFitMoveClauseById(Long id);

    /**
     * 批量删除测试评审不适用与挪动款
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteReviewFitMoveClauseByIds(Long[] ids);

    List<ReviewFitMoveClause> qryReviewFitMoveClauseList(ReviewFitMoveClauseReq reviewFitMoveClause);

    /**
     * 批量新增评审不适用与挪动款
     *
     * @param reviewFitMoveClauseList 评审不适用与挪动款
     * @return 结果
     */
    public int batchInsertReviewFitMoveClause(List<ReviewFitMoveClause> reviewFitMoveClauseList);

    void moveClauseBatchInsert(List<ReviewFitMoveClause> reviewFitMoveClauseList);

    /**
     * 批量修改评审不适用与挪动款
     *
     * @param reviewFitMoveClauseList 评审不适用与挪动款
     * @return 结果
     */
    public int batchUpdateReviewFitMoveClause(@Param("list") List<ReviewFitMoveClause> reviewFitMoveClauseList);

    /**
     * 删除
     */
    void delByAutCodeAndTypeAndClauseId(@Param("autCode") String autCode, @Param("type") Long type, @Param("clauseIdList") List<String> clauseIdList);
}
