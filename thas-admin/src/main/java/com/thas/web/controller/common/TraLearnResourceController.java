package com.thas.web.controller.common;

import com.thas.common.annotation.Log;
import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.core.page.TableDataInfo;
import com.thas.common.enums.BusinessType;
import com.thas.web.domain.TraLearnResource;
import com.thas.web.service.ITraLearnResourceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 学习资源Controller
 *
 * <AUTHOR>
 * @date 2022-01-20
 */
@RestController
@RequestMapping("/system/resource")
public class TraLearnResourceController extends BaseController {
    @Autowired
    private ITraLearnResourceService traLearnResourceService;

    /**
     * 查询学习资源列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TraLearnResource traLearnResource) {
        startPage();
        List<TraLearnResource> list = traLearnResourceService.selectTraLearnResourceList(traLearnResource);
        return getDataTable(list);
    }

    /**
     * 获取学习资源详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(traLearnResourceService.selectTraLearnResourceById(id));
    }

    /**
     * 新增学习资源
     */
    @Log(title = "新增学习资源", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TraLearnResource traLearnResource) {
        traLearnResource.setCreateId(getUsername());
        traLearnResource.setUpdateId(getUsername());
        return toAjax(traLearnResourceService.insertTraLearnResource(traLearnResource));
    }

    /**
     * 修改学习资源
     */
    @Log(title = "修改学习资源", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TraLearnResource traLearnResource) {
        traLearnResource.setUpdateId(getUsername());
        return toAjax(traLearnResourceService.updateTraLearnResource(traLearnResource));
    }

    /**
     * 删除学习资源
     */
    @Log(title = "修改学习资源", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(traLearnResourceService.deleteTraLearnResourceByIds(ids));
    }

    /**
     * 学习资源学习次数加1
     */
    @Log(title = "学习资源学习次数加1", businessType = BusinessType.UPDATE)
    @GetMapping("/learn/num/{id}")
    public AjaxResult learnNumPlus(@PathVariable("id") Long id) {
        return toAjax(traLearnResourceService.learnNumPlus(id));
    }
}
