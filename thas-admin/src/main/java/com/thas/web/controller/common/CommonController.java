package com.thas.web.controller.common;

import com.thas.common.config.ThasConfig;
import com.thas.common.constant.Constants;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.utils.StringUtils;
import com.thas.common.utils.file.FileUploadUtils;
import com.thas.common.utils.file.FileUtils;
import com.thas.framework.config.ServerConfig;
import com.thas.web.service.CommonService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * 通用请求处理
 *
 * <AUTHOR>
 */
@RestController
public class CommonController {
    private static final Logger log = LoggerFactory.getLogger(CommonController.class);

    @Autowired
    private ServerConfig serverConfig;

    @Resource
    private CommonService commonService;

    /**
     * 通用下载请求
     *
     * @param fileName 文件名称
     * @param delete   是否删除
     */
    @GetMapping("common/download")
    public void fileDownload(String fileName, Boolean delete, HttpServletResponse response, HttpServletRequest request) {
        try {
            if (!FileUtils.checkAllowDownload(fileName)) {
                throw new Exception(StringUtils.format("文件名称({})非法，不允许下载。 ", fileName));
            }
            String realFileName = System.currentTimeMillis() + fileName.substring(fileName.indexOf("_") + 1);
            String filePath = ThasConfig.getDownloadPath() + fileName;

            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, realFileName);
            FileUtils.writeBytes(filePath, response.getOutputStream());
            if (delete) {
                FileUtils.deleteFile(filePath);
            }
        } catch (Exception e) {
            log.error("下载文件失败", e);
        }
    }

    /**
     * 通用上传请求
     */
    @PostMapping("/common/upload")
    public AjaxResult uploadFile(@Valid @RequestPart(value = "file") MultipartFile file) {
        try {
            // 上传文件路径
            String filePath = ThasConfig.getUploadPath();
            // 上传并返回新文件名称
            String fileName = FileUploadUtils.upload(filePath, file);
            String url = serverConfig.getUrl() + fileName;
            AjaxResult ajax = AjaxResult.success();
            ajax.put("fileName", fileName);
            ajax.put("url", url);
            return ajax;
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 本地资源通用下载
     */
    @GetMapping("/common/download/resource")
    public void resourceDownload(String resource, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        try {
            if (!FileUtils.checkAllowDownload(resource)) {
                throw new Exception(StringUtils.format("资源文件({})非法，不允许下载。 ", resource));
            }
            // 本地资源路径
            String localPath = ThasConfig.getProfile();
            // 数据库资源地址
            String downloadPath = localPath + StringUtils.substringAfter(resource, Constants.RESOURCE_PREFIX);
            // 下载名称
            String downloadName = StringUtils.substringAfterLast(downloadPath, "/");
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, downloadName);
            FileUtils.writeBytes(downloadPath, response.getOutputStream());
        } catch (Exception e) {
            log.error("下载文件失败", e);
        }
    }

    /**
     * 单个文件上传
     *
     * @param file
     * @param type
     * @param downLoadFileName 下载文件名(用于下载文件的命名)
     */
    @PostMapping(value = {"/common/uploadFile", "/binTang/common/uploadFile"})
    public AjaxResult uploadFiles(@Valid @RequestPart(value = "file") MultipartFile file,
                                  @RequestParam("type") String type,
                                  @RequestParam("downLoadFileName") String downLoadFileName) {
        log.info("单个文件上传入参:{}", file.getOriginalFilename());
        return AjaxResult.success(commonService.uploadFile(file, type,"",downLoadFileName));
    }

    /**
     * 文件下载
     *
     * @param fileId
     */
    @PostMapping(value = "/common/downloadFile")
    public void downloadFiles(@RequestParam String fileId, HttpServletResponse response) {
        log.info("文件下载 入参:{}", fileId);
        commonService.downloadFile(fileId, response);
    }

    /**
     * 文件模板下载
     *
     * @param fileTemplate 模板文件
     */
    @PostMapping(value = "/file/template/down/load")
    public void fileTemplateDownLoad(@RequestParam String fileTemplate, HttpServletResponse response) {
        log.info("文件模板下载:{}", fileTemplate);
        commonService.fileTemplateDownLoad(fileTemplate, response);
    }

    /**
     * 【免登】文件模板下载
     *
     * @param fileTemplate 模板文件
     */
    @PostMapping(value = "/bing/file/template/down/load")
    public void fileTemplateDownLoadBing(@RequestParam String fileTemplate, HttpServletResponse response) {
        log.info("文件模板下载:{}", fileTemplate);
        commonService.fileTemplateDownLoad(fileTemplate, response);
    }

    //    @PostMapping(value = {"/binTang/common/uploadFile1"})
    public AjaxResult uploadFiles1(@Valid @RequestPart(value = "file") MultipartFile file) {
        log.info("单个文件上传入参={}", file.getOriginalFilename());
        return AjaxResult.success(commonService.uploadFile1(file));
    }

    //    @PostMapping(value = "/common/downloadFile1")
    public void downloadFiles1(@RequestParam String fileId, HttpServletResponse response) {
        log.info("文件下载 入参={}", fileId);
        commonService.downloadFile1(fileId, response);
    }

//    /**
//     * 文件统一批量上传
//     *
//     * @param files
//     */
//    @PostMapping(value = "/common/uploadFiles")
//    public AjaxResult uploadFiles(@Valid @RequestPart(value = "file") MultipartFile[] files) {
//        log.info("批量文件上传 数量={}", files.length);
//        List<FileInfoVO> fileInfoVoList = commonService.uploadFiles(files);
//        log.info("文件上传返回结果={}", fileInfoVoList);
//        return AjaxResult.success(fileInfoVoList);
//    }

}
