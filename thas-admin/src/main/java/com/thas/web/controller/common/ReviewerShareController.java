package com.thas.web.controller.common;

import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.validate.ValidGroup;

import javax.annotation.Resource;

import com.thas.web.dto.ReviewerShareReq;
import com.thas.web.service.IReviewerShareService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/reviewer/share")
@Api(value = "ReviewerShareController", tags = "评审共享相关接口API")
public class ReviewerShareController extends BaseController {

    @Resource
    private IReviewerShareService reviewerShareService;

    @PostMapping(value = "/submit")
    @ApiOperation(httpMethod = "POST", value = "评审共享-提交操作")
    public AjaxResult submit(@Validated(ValidGroup.Submit.class) @RequestBody ReviewerShareReq reviewerShareReq) {
        reviewerShareService.submit(reviewerShareReq);
        return AjaxResult.success();
    }

    @PostMapping(value = "/list")
    @ApiOperation(httpMethod = "POST", value = "评审共享-列表操作")
    public AjaxResult list(@Validated(ValidGroup.Query.class) @RequestBody ReviewerShareReq reviewerShareReq) {
        return AjaxResult.success(reviewerShareService.list(reviewerShareReq));
    }
    //调用评审详情接口，没改动
    @PostMapping(value = "/detail")
    @ApiOperation(httpMethod = "POST", value = "评审共享-详情操作")
    public AjaxResult detail(@Validated(ValidGroup.Detail.class) @RequestBody ReviewerShareReq reviewerShareReq) {
        return AjaxResult.success(reviewerShareService.detail(reviewerShareReq));
    }
    //需求取消通过操作，这个接口可注释
//    @PostMapping(value = "/pass")
//    @ApiOperation(httpMethod = "POST", value = "评审共享-通过操作")
//    public AjaxResult pass(@Validated({ValidGroup.Query.class,ValidGroup.Detail.class}) @RequestBody ReviewerShareReq reviewerShareReq) {
//        reviewerShareService.pass(reviewerShareReq);
//        return AjaxResult.success();
//    }
    //这接口没有校验可随时转节点，需校验都提交共享后，给到组长触发操作
    @PostMapping(value = "/tem-pass")
    @ApiOperation(httpMethod = "POST", value = "评审共享-通过操作（结束共享操作-只能由评审组长触发）")
    public AjaxResult temPass(@Validated({ValidGroup.Query.class}) @RequestBody ReviewerShareReq reviewerShareReq) {
        reviewerShareService.temPass(reviewerShareReq);
        return AjaxResult.success();
    }

    @PostMapping(value = "/update")
    @ApiOperation(httpMethod = "POST", value = "评审共享-修改操作")
    public AjaxResult update(@Validated({ValidGroup.Query.class,ValidGroup.Group1.class}) @RequestBody ReviewerShareReq reviewerShareReq) {
        reviewerShareService.update(reviewerShareReq);
        return AjaxResult.success();
    }

    @PostMapping(value = "/report")
    @ApiOperation(httpMethod = "POST", value = "评审共享-预览临时报告操作")
    public AjaxResult report(@Validated({ValidGroup.Query.class}) @RequestBody ReviewerShareReq reviewerShareReq) {
        return AjaxResult.success(reviewerShareService.report(reviewerShareReq));
    }

    @PostMapping(value = "/submit-evaluate")
    @ApiOperation(httpMethod = "POST", value = "提交评审结果-提交操作")
    public AjaxResult submitEvaluate(@Validated(ValidGroup.SubmitEvaluate.class) @RequestBody ReviewerShareReq req) {

        return toAjax(reviewerShareService.submitEvaluate(req));

    }

}
