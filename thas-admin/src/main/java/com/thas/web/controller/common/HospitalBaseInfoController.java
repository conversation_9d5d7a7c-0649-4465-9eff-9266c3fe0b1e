package com.thas.web.controller.common;


import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.core.page.TableDataInfo;
import com.thas.system.domain.vo.SendEmailCodeVo;
import com.thas.web.domain.HospitalBaseInfo;
import com.thas.web.dto.AgainHosInfoSubmitRequest;
import com.thas.web.dto.CstCertificationStandardVO;
import com.thas.web.dto.HosPlanDetailDTO;
import com.thas.web.dto.HosPlanDetailVO;
import com.thas.web.dto.HosPlanSubmitDTO;
import com.thas.web.dto.HosReviewPlanVO;
import com.thas.web.dto.HospitalBaseInfoDTO;
import com.thas.web.dto.QryHosStatusRequest;
import com.thas.web.dto.QueryBaseConditionDTO;
import com.thas.web.dto.QueryHosReviewPlanDTO;
import com.thas.web.dto.QueryHospitalListDTO;
import com.thas.web.dto.QueryUnassignedHosDTO;
import com.thas.web.dto.TempHosInfoRequest;
import com.thas.web.service.IHospitalBaseInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.UnsupportedEncodingException;
import java.util.List;

/**
 * 医疗机构详情Controller
 *
 * <AUTHOR>
 * @date 2022-01-19
 */
@RestController
@RequestMapping("/hospital")
@Api(value = "API - HospitalBaseInfoController", tags = "医疗机构对应接口")
public class HospitalBaseInfoController extends BaseController {


    @Autowired
    private IHospitalBaseInfoService hospitalBaseInfoService;

    /**
     * 提交医疗机构基本信息
     *
     * @return 提交结果
     */
    @PostMapping("/base/info/submit")
    @ApiOperation(httpMethod = "POST", value = "医疗结构基本数据提交")
    public AjaxResult hospitalBaseInfoSubmit(@RequestBody HospitalBaseInfoDTO hospitalBaseInfoDTO) {
        logger.info("/submit 医疗机构提交基本信息入参:{}", hospitalBaseInfoDTO);
        AjaxResult ajaxResult = hospitalBaseInfoService.hospitalBaseInfoSubmit(hospitalBaseInfoDTO);
        logger.info("/submit 医疗机构提交基本信息出参:{}", hospitalBaseInfoDTO);
        return ajaxResult;
    }

    /**
     * 【免登】提交医疗机构基本信息
     *
     * @return 提交结果
     */
    @PostMapping("/bing/base/info/submit")
    @ApiOperation(httpMethod = "POST", value = "医疗结构基本数据提交")
    public AjaxResult hospitalBaseInfoSubmitBing(@RequestBody HospitalBaseInfoDTO hospitalBaseInfoDTO) {
        logger.info("/submit 医疗机构提交基本信息入参:{}", hospitalBaseInfoDTO);
        AjaxResult ajaxResult = hospitalBaseInfoService.hospitalBaseInfoSubmit(hospitalBaseInfoDTO);
        logger.info("/submit 医疗机构提交基本信息出参:{}", hospitalBaseInfoDTO);
        return ajaxResult;
    }

    /**
     * 拒绝之后再次提交
     *
     * @return 提交结果
     */
    @PostMapping("/bing/again/base/info/submit")
    @ApiOperation(httpMethod = "POST", value = "医疗结构基本数据拒绝后再次提交")
    public AjaxResult hosAgainSubmit(@RequestBody @Validated AgainHosInfoSubmitRequest request) {

        return hospitalBaseInfoService.hosAgainSubmit(request);
    }

    /**
     * 查询医疗机构数据
     *
     * @return 分页数据
     */
    @PostMapping("/query/hospital/list")
    @ApiOperation(httpMethod = "POST", value = "医疗结构基本数据列表查询")
    public TableDataInfo queryHospitalList(@RequestBody(required = false)
                                                   QueryHospitalListDTO queryHospitalListDTO) {
        logger.info("/query/hospital/list 查询医疗结构列表数据入参:{}", queryHospitalListDTO);
        startPage();
        List<QueryHospitalListDTO> list = hospitalBaseInfoService.queryHospitalList(queryHospitalListDTO);
        return getDataTable(list);
    }

    /**
     * 查询未被分配账号的医疗机构记录
     *
     * @return 医疗信息
     */
    @PostMapping("/query/Unassigned/info")
    @ApiOperation(httpMethod = "POST", value = "未被分配账号的医疗机构记录")
    public List<QueryUnassignedHosDTO> queryUnassignedHos(@RequestBody(required = false)
                                                                  QueryBaseConditionDTO queryBaseConditionDTO) {
        // 通过传入参数commonId 充当flag 判断查询结果
        return hospitalBaseInfoService.queryUnassignedHos(queryBaseConditionDTO);
    }

    /**
     * 增加通过医疗机构社会信用编码查询接口，需要免登
     */
    @PostMapping("/bing/hospital/detail")
    @ApiOperation(httpMethod = "POST", value = "医疗结构基本数据详情查询")
    public AjaxResult bingTangHospitalDetail(@RequestBody @Validated TempHosInfoRequest request) {
        logger.info("/query/bingtang/hospital/detail 免登获取医疗结构详情通过社会信用编码:{}", request);
        return AjaxResult.success(hospitalBaseInfoService.bingTangHospitalDetail(request));
    }

    @GetMapping("/bing/hospital/status")
    @ApiOperation(httpMethod = "POST", value = "查询医疗结构审核状态")
    public AjaxResult qryHosStatus(@Validated QryHosStatusRequest request) {
        logger.info("/bing/hospital/status 查询医疗结构审核状态:{}", request);
        return AjaxResult.success(hospitalBaseInfoService.qryHosStatus(request));
    }

    @PostMapping("/query/hospital/detail")
    @ApiOperation(httpMethod = "POST", value = "医疗结构基本数据详情查询")
    public HospitalBaseInfoDTO queryHospitalDetail(@ApiParam(name = "queryBaseConditionDTO", value = "查询条件入参", required = true)
                                                   @RequestBody QueryBaseConditionDTO queryBaseConditionDTO) {
        logger.info("/query/hospital/detail 医疗结构基本数据详情查询入参:{}", queryBaseConditionDTO);
        return hospitalBaseInfoService.queryHospitalDetail(queryBaseConditionDTO);
    }

    /**
     * 根据当前登录用户查询医院联系人信息
     *
     * @return HospitalBaseInfo
     */
    @PostMapping("/query/cur/hos/info")
    @ApiOperation(httpMethod = "POST", value = "根绝当前登录用户查询医院信息")
    public HospitalBaseInfo queryCurHosBaseInfo() {
        return hospitalBaseInfoService.queryCurHosBaseInfo();
    }


    /**
     * 查询已经分配账号且审核通过医院信息
     *
     * @return HospitalBaseInfo
     */
    @PostMapping("/query/pass/hos/info")
    @ApiOperation(httpMethod = "POST", value = "查询已经分配账号且审核通过医院信息")
    public List<HospitalBaseInfo> qryPassHosInfo() {
        return hospitalBaseInfoService.qryPassHosInfo();
    }

    /**
     * 医疗机构基本数据列表查询-导出
     *
     * @param queryHospitalListDTO 搜索查询条件
     */
    @PostMapping("list/export")
    @ResponseBody
    @ApiOperation(httpMethod = "POST", value = "医疗机构基本数据列表查询-导出")
    public void hospitalListExport(@ApiParam(name = "queryBaseConditionDTO", value = "查询条件入参")
                                   @RequestBody(required = false) QueryHospitalListDTO queryHospitalListDTO, HttpServletResponse response) throws UnsupportedEncodingException {
        hospitalBaseInfoService.hospitalListExport(queryHospitalListDTO, response);

    }

    @PostMapping("/review/plan/list")
    @ApiOperation(httpMethod = "POST", value = "查询医疗机构评审计划列表")
    public TableDataInfo queryReviewPlanList(@RequestBody(required = false)
                                                     QueryHosReviewPlanDTO queryHosReviewPlanDTO) {
        logger.info("/review/plan/list 查询医疗结构评审计划列表入参:{}", queryHosReviewPlanDTO);
        List<HosReviewPlanVO> list = hospitalBaseInfoService.queryReviewPlanList(queryHosReviewPlanDTO);
        return getDataTable(list);
    }

    /**
     * 查询医疗机构分配详情初审员，评审员对应款项详情
     *
     * @param hosPlanDetailDTO 查询入参
     * @return 详情
     */
    @PostMapping("/hos/plan/clause/detail")
    @ApiOperation(httpMethod = "POST", value = "查询医疗机构分配详情初审员，评审员对应款项详情")
    public TableDataInfo queryHosPlanClauseDetail(@RequestBody HosPlanDetailDTO hosPlanDetailDTO) {
        logger.info("/hos/plan/clause/detail 查询医疗机构分配详情入参:{}", hosPlanDetailDTO);
        List<CstCertificationStandardVO> list = hospitalBaseInfoService.queryHosPlanClauseDetail(hosPlanDetailDTO);
        return getDataTable(list);
    }

    @PostMapping("/hos/plan/detail")
    @ApiOperation(httpMethod = "POST", value = "医疗机构分配详情已分配初审员评审员条款数")
    public HosPlanDetailVO queryHosPlanDetail(@RequestBody HosPlanDetailDTO hosPlanDetailDTO) {
        logger.info("/hos/plan/detail 查询医疗机构分配详情入参:{}", hosPlanDetailDTO);
        return hospitalBaseInfoService.queryHosPlanDetail(hosPlanDetailDTO);
    }

    @PostMapping("/plan/submit")
    @ApiOperation(httpMethod = "POST", value = "医疗机构分配详情提交")
    public AjaxResult hosPlanSubmit(@Valid @RequestBody HosPlanSubmitDTO hosPlanSubmitDTO) {
        logger.info("/plan/submit 医疗机构分配详情提交入参:{}", hosPlanSubmitDTO);
        return hospitalBaseInfoService.hosPlanSubmit(hosPlanSubmitDTO);
    }

    @PostMapping("/bing/sendEmailCode")
    @ApiOperation(httpMethod = "POST", value = "医疗机构免登发送邮箱")
    public AjaxResult sendEmailCodeBing(@RequestBody SendEmailCodeVo sendEmailCodeVo) {
        hospitalBaseInfoService.sendEmailCode(sendEmailCodeVo);
        return success();
    }

}
