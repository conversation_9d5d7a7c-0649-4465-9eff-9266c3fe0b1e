package com.thas.web.controller.common;

import java.security.GeneralSecurityException;
import java.util.List;
import javax.mail.MessagingException;
import javax.servlet.http.HttpServletResponse;

import com.thas.web.domain.dto.MessageSendRecordDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.thas.common.annotation.Log;
import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.enums.BusinessType;
import com.thas.web.domain.MessageSendRecord;
import com.thas.web.service.IMessageSendRecordService;
import com.thas.common.utils.poi.ExcelUtil;
import com.thas.common.core.page.TableDataInfo;

/**
 * 消息发送记录Controller
 *
 * <AUTHOR>
 * @date 2022-01-25
 */
@Api(value="MessageSendRecordController",tags = "消息发送记录Controller")
@RestController
@RequestMapping("/system/messageSendRecord")
public class MessageSendRecordController extends BaseController
{
    @Autowired
    private IMessageSendRecordService messageSendRecordService;

    /**
     * 查询消息发送记录列表
     */
//    @PreAuthorize("@ss.hasPermi('system:record:list')")
    @ApiOperation("查询消息发送记录列表")
    @GetMapping("/list")
    public TableDataInfo list(MessageSendRecord messageSendRecord)
    {
        startPage();
        List<MessageSendRecord> list = messageSendRecordService.selectMessageSendRecordList(messageSendRecord);
        return getDataTable(list);
    }

    /**
     * 导出消息发送记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:record:export')")
    @Log(title = "消息发送记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MessageSendRecord messageSendRecord)
    {
        List<MessageSendRecord> list = messageSendRecordService.selectMessageSendRecordList(messageSendRecord);
        ExcelUtil<MessageSendRecord> util = new ExcelUtil<MessageSendRecord>(MessageSendRecord.class);
        util.exportExcel(response, list, "消息发送记录数据");
    }

    /**
     * 获取消息发送记录详细信息
     */
//    @PreAuthorize("@ss.hasPermi('system:record:query')")
    @ApiOperation("获取消息发送记录详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(messageSendRecordService.selectMessageSendRecordById(id));
    }

    /**
     * 新增消息发送记录
     */
//    @PreAuthorize("@ss.hasPermi('system:record:add')")
    @ApiOperation("新增消息发送记录")
    @Log(title = "消息发送记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MessageSendRecord messageSendRecord)
    {
        return toAjax(messageSendRecordService.insertMessageSendRecord(messageSendRecord));
    }

    /**
     * 修改消息发送记录
     */
//    @PreAuthorize("@ss.hasPermi('system:record:edit')")
    @ApiOperation("修改消息发送记录")
    @Log(title = "消息发送记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MessageSendRecord messageSendRecord)
    {
        return toAjax(messageSendRecordService.updateMessageSendRecord(messageSendRecord));
    }

    /**
     * 删除消息发送记录
     */
//    @PreAuthorize("@ss.hasPermi('system:record:remove')")
    @ApiOperation("删除消息发送记录")
    @Log(title = "消息发送记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(messageSendRecordService.deleteMessageSendRecordByIds(ids));
    }

    /**
     * 发送消息给指定的用户
     * @param messageSendRecordDTO
     * @return
     */
    @ApiOperation("发送消息给指定的用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "content", value = "发送内容", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "sendType", value = "发送方式（0系统发送 1邮件发送 2手机短信发送），可多选字符串拼接“0,1,2”", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "messageTemplateId", value = "模板id", dataType = "Long", dataTypeClass = Long.class),
            @ApiImplicitParam(name = "userVoList", value = "指定用户(数组)", dataType = "List", dataTypeClass = List.class),
            @ApiImplicitParam(name = "userId", value = "用户id", dataType = "Long", dataTypeClass = Long.class),
            @ApiImplicitParam(name = "email", value = "邮箱", dataType = "String", dataTypeClass = String.class),

    })
    @PostMapping("/sendMessageToUser")
    public AjaxResult sendMessageToUser(@RequestBody MessageSendRecordDTO messageSendRecordDTO) throws MessagingException, GeneralSecurityException {
        messageSendRecordService.sendMessageToUser(messageSendRecordDTO);
        return AjaxResult.success();
    }

//    @ApiOperation("sendMessag1eToUsertest")
//    @PostMapping("/sendMessag1eToUsertest")
//    public AjaxResult sendMessag1eToUsertest() throws MessagingException, GeneralSecurityException {
//        //MAP<评审员名称，ClauseNos>(ClauseNos为：管理1.1.1、管理1.1.2、XXX、XXX)
//        Map<String, String> nameAndClauseNosMap = new HashMap<>();
//        nameAndClauseNosMap.put("评审员1","管理1.1.1、管理1.1.2");
//        nameAndClauseNosMap.put("评审员2","管理1.1.4、管理1.1.5");
//
//        String spaces = MessageContentFormatEnum.getSpaces(2);
//        StringBuilder content = new StringBuilder();
//        content.append(MessageContentFormatEnum.packContentNewline("各评审成员好:"));
//        content.append(MessageContentFormatEnum.packContentNewline(MessageFormat.format(spaces+"您参与的{0}评审，如下评价条款被审查驳回:", "XXX医院")));
//        //邮件内容-评审员名称和对应款信息
//        for (Map.Entry<String, String> entry : nameAndClauseNosMap.entrySet()) {
//            content.append(MessageContentFormatEnum.packContentNewline(entry.getKey()+":"));
//            content.append(MessageContentFormatEnum.packContentNewline(MessageFormat.format(spaces+"条款:{0}", entry.getValue())));
//        }
//        content.append(MessageContentFormatEnum.packContentNewline("请登录《深圳卫健医院评审评价平台》确认是否修改，谢谢!"+ "<a href='http://www.baidu.com'>【百度一下】</a></body></html>"));
//
//        System.out.println("content = " + content.toString());
//        messageSendRecordService.sendMsgFromEmail(content.toString(),"<EMAIL>","测试发送格式");
//        return AjaxResult.success();
//    }

}
