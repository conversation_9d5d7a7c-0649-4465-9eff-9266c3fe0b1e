package com.thas.web.controller.common;

import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;
import com.thas.web.domain.AutRecord;
import com.thas.web.service.IAutRecordService;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 认证记录Controller
 *
 * <AUTHOR>
 * @date 2022-01-17
 */
@RestController
@RequestMapping("/aut/record/info")
public class AutRecordInfoController extends BaseController {

    @Resource
    private IAutRecordService autRecordService;

    /**
     * 提交医疗结构/评审员认证审核信息
     */
    @PostMapping(value = "/submitAutRecord")
    public AjaxResult saveAutRecord(@Validated @RequestBody AutRecord autRecord) {
        autRecordService.submitAutRecord(autRecord);
        return AjaxResult.success();
    }
}
