package com.thas.web.controller.common;

import com.thas.common.core.domain.AjaxResult;
import com.thas.web.dto.StatisticReportRequest;
import com.thas.web.service.StatisticService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022/10/28
 * <p>
 * 统计报表控制器
 */
@RestController
@RequestMapping("/statistic")
public class StatisticController {

    @Autowired
    private StatisticService statisticService;

    /**
     * 统计报表
     *
     * @param request 入参
     * @return AjaxResult
     */
    @PostMapping("report")
    public AjaxResult report(@RequestBody @Validated StatisticReportRequest request) {
        Map<String, Object> data = statisticService.report(request);
        return AjaxResult.success(data);
    }
}
