package com.thas.web.controller.common;

import com.thas.common.annotation.Log;
import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.core.page.TableDataInfo;
import com.thas.common.enums.BusinessType;
import com.thas.common.utils.poi.ExcelUtil;
import com.thas.web.domain.CstDomain;
import com.thas.web.domain.CstDomainVO;
import com.thas.web.service.ICstDomainService;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 领域Controller
 *
 * <AUTHOR>
 * @date 2022-01-21
 */
@RestController
@RequestMapping("/system/domain")
public class CstDomainController extends BaseController {
    @Autowired
    private ICstDomainService cstDomainService;

    /**
     * 查询领域列表
     */
    @GetMapping("/list")
    public TableDataInfo list(CstDomain cstDomain) {
        startPage();
        List<CstDomain> list = cstDomainService.selectCstDomainList(cstDomain);
        return getDataTable(list);
    }

    /**
     * 查询领域列表
     */
    @GetMapping("/bing/list")
    public TableDataInfo bingList(CstDomain cstDomain) {
        startPage();
        List<CstDomain> list = cstDomainService.selectCstDomainList(cstDomain);
        return getDataTable(list);
    }

    /**
     * 导出领域列表
     */
    @Log(title = "领域", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CstDomain cstDomain) {
        List<CstDomain> list = cstDomainService.selectCstDomainList(cstDomain);
        ExcelUtil<CstDomain> util = new ExcelUtil<CstDomain>(CstDomain.class);
        util.exportExcel(response, list, "领域数据");
    }

    /**
     * 获取领域详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id, String versionId) {
        return AjaxResult.success(cstDomainService.selectCstDomainById(id, versionId));
    }

    /**
     * 新增领域
     */
    @Log(title = "领域", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody CstDomainVO cstDomainVO) {
        return toAjax(cstDomainService.insertCstDomain(cstDomainVO));
    }

    /**
     * 修改领域
     */
    @Log(title = "领域", businessType = BusinessType.UPDATE)
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody CstDomainVO cstDomainVO) {
        return toAjax(cstDomainService.updateCstDomain(cstDomainVO));
    }

    /**
     * 删除领域
     */
    @Log(title = "领域", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        return toAjax(cstDomainService.deleteCstDomainById(id));
    }
}
