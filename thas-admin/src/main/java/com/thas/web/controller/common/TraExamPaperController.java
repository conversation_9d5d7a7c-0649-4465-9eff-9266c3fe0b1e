package com.thas.web.controller.common;

import com.thas.common.annotation.Log;
import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.core.page.TableDataInfo;
import com.thas.common.enums.BusinessType;
import com.thas.common.exception.ServiceException;
import com.thas.web.domain.TraExamPaper;
import com.thas.web.dto.TraExamPaperDTO;
import com.thas.web.service.ITraExamPaperService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 考卷Controller
 *
 * <AUTHOR>
 * @date 2022-01-25
 */
@RestController
@RequestMapping("/system/paper")
@Api(value = "API - TraExamPaperController", tags = "在线学习 - 考卷管理")
public class TraExamPaperController extends BaseController {
    @Autowired
    private ITraExamPaperService traExamPaperService;

    /**
     * 查询考卷列表
     */
    @GetMapping("/list")
    @ApiOperation(httpMethod = "GET", value = "查询考卷列表")
    public TableDataInfo list(TraExamPaper traExamPaper) {
        startPage();
        List<TraExamPaper> list = traExamPaperService.selectTraExamPaperList(traExamPaper);
        return getDataTable(list);
    }

    /**
     * 获取考卷详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation(httpMethod = "GET", value = "获取考卷详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(traExamPaperService.selectTraExamPaperById(id));
    }

    @GetMapping(value = "/edit/{id}")
    @ApiOperation(httpMethod = "GET", value = "编辑获取考卷详细信息")
    public AjaxResult getInfoEdit(@PathVariable("id") Long id) {
        return AjaxResult.success(traExamPaperService.editTraExamPaperById(id));
    }

    /**
     * 新增考卷
     */
    @Log(title = "新增考卷", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(httpMethod = "POST", value = "新增考卷")
    public AjaxResult add(@RequestBody @Validated TraExamPaperDTO traExamPaperDTO) {
        if (CollectionUtils.isNotEmpty(traExamPaperDTO.getDetails())) {
            traExamPaperDTO.getDetails().forEach(detail -> {
                if (detail.getExamQuestion().length() > 500) {
                    throw new ServiceException("题目描述不可超过500个字符");
                }
            });
        }
        return toAjax(traExamPaperService.insertTraExamPaper(traExamPaperDTO));
    }

    /**
     * 修改考卷
     */
    @Log(title = "修改考卷", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(httpMethod = "PUT", value = "修改考卷")
    public AjaxResult edit(@RequestBody TraExamPaperDTO traExamPaperDTO) {
        if (CollectionUtils.isNotEmpty(traExamPaperDTO.getDetails())) {
            traExamPaperDTO.getDetails().forEach(detail -> {
                if (detail.getExamQuestion().length() > 500) {
                    throw new ServiceException("题目描述不可超过500个字符");
                }
            });
        }
        return toAjax(traExamPaperService.updateTraExamPaper(traExamPaperDTO));
    }

    /**
     * 删除考卷
     */
    @Log(title = "删除考卷", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(httpMethod = "DELETE", value = "删除考卷")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(traExamPaperService.deleteTraExamPaperByIds(ids));
    }

    /**
     * 修改考卷状态
     */
    @Log(title = "修改考卷状态", businessType = BusinessType.UPDATE)
    @GetMapping("/status")
    @ApiOperation(httpMethod = "GET", value = "修改考卷状态")
    public AjaxResult updateStatus(Long id, Long learnResourceId, Integer status) {
        return toAjax(traExamPaperService.updateStatus(id, learnResourceId, status));
    }
}
