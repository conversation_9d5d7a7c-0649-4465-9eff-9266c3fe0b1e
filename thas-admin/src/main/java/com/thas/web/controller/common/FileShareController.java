package com.thas.web.controller.common;


import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.core.page.TableDataInfo;
import com.thas.web.dto.DelFileShareRequest;
import com.thas.web.dto.FileShareCreateRequest;
import com.thas.web.dto.QryFileShareRequest;
import com.thas.web.service.IFileShareService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 管理员文件分享关联Controller
 *
 * <AUTHOR>
 * @date 2022-10-27
 */
@RestController
@RequestMapping("/file/share")
@Api(value = "FileShareController", tags = "管理员文件分享关联Controller")
public class FileShareController extends BaseController {

    @Autowired
    private IFileShareService fileShareService;

    /**
     * 文件共享提交-免登
     */
    @PostMapping("bing-create")
    public AjaxResult fileShareBingCreate(@RequestBody @Validated FileShareCreateRequest request) {
        fileShareService.fileShareBingCreate(request);
        return AjaxResult.success();
    }

    /**
     * 文件共享提交
     */
    @PostMapping("create")
    public AjaxResult fileShareCreate(@RequestBody @Validated FileShareCreateRequest request) {
        fileShareService.fileShareCreate(request);
        return AjaxResult.success();
    }

    /**
     * 查询
     */
    @PostMapping("query")
    @ApiOperation("查询资源库")
    public TableDataInfo qryFileShare(@RequestBody(required = false) QryFileShareRequest request) {
        return fileShareService.qryFileShare(request);
    }

    /**
     * 删除
     */
    @PostMapping("delete")
    public AjaxResult fileShareDelete(@RequestBody @Validated DelFileShareRequest request) {
        fileShareService.delFileShareByFileId(request);
        return AjaxResult.success();
    }
}
