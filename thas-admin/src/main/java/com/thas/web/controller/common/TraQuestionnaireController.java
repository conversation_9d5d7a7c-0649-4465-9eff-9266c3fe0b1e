package com.thas.web.controller.common;

import com.thas.common.annotation.Log;
import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.core.page.TableDataInfo;
import com.thas.common.enums.BusinessType;
import com.thas.web.domain.SendDetailRes;
import com.thas.web.domain.SendInfoRes;
import com.thas.web.domain.TraQuestionnaire;
import com.thas.web.domain.TraQuestionnaireFeedBackRecord;
import com.thas.web.domain.dto.TraQuestionnaireDTO;
import com.thas.web.domain.vo.TraQuestionnaireVO;
import com.thas.web.service.ITraQuestionnaireService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 调查问卷Controller
 *
 * <AUTHOR>
 * @date 2022-02-14
 */
@RestController
@RequestMapping("/system/questionnaire")
@Api(value = "API - TraQuestionnaireController", tags = "培训 - 调查问卷")
public class TraQuestionnaireController extends BaseController {
    @Autowired
    private ITraQuestionnaireService traQuestionnaireService;

    /**
     * 查询调查问卷列表
     */
    @GetMapping("/list")
    @ApiOperation(httpMethod = "GET", value = "查询调查问卷列表")
    public TableDataInfo list(TraQuestionnaireDTO traQuestionnaireDTO) {
        List<TraQuestionnaireVO> list = traQuestionnaireService.selectTraQuestionnaireList(traQuestionnaireDTO);
        return getDataTable(list);
    }

    /**
     * 获取调查问卷详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation(httpMethod = "GET", value = "获取调查问卷详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(traQuestionnaireService.selectTraQuestionnaireById(id));
    }

    /**
     * 新增/修改调查问卷
     */
    @Log(title = "新增/修改调查问卷", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(httpMethod = "POST", value = "新增/修改调查问卷")
    public AjaxResult add(@RequestBody TraQuestionnaire traQuestionnaire) {
        traQuestionnaireService.insertOrUpdateTraQuestionnaire(traQuestionnaire);
        return AjaxResult.success();
    }

    /**
     * 删除调查问卷
     */
    @Log(title = "删除调查问卷", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(httpMethod = "DELETE", value = "删除调查问卷")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(traQuestionnaireService.deleteTraQuestionnaireByIds(ids));
    }

    /**
     * 修改调查问卷状态
     */
    @Log(title = "修改调查问卷状态", businessType = BusinessType.INSERT)
    @GetMapping("/status")
    @ApiOperation(httpMethod = "GET", value = "修改调查问卷状态")
    public AjaxResult updateStatus(@ApiParam(value = "0:停止, 1:发布", name = "status") @RequestParam Integer status, @ApiParam(value = "问卷id", name = "id") @RequestParam Long id) {
        return toAjax(traQuestionnaireService.updateStatus(status, id));
    }

    /**
     * 下发评审学员与对应培训教员下拉框信息（下发学员理论反馈表与培训教员反馈表，对应教员与学员理论评估表相关）
     */
    @GetMapping("/send/info")
    @ApiOperation(httpMethod = "GET", value = "下发弹窗下拉框评审人员信息")
    public TableDataInfo sendInfo (@NotNull(message = "问卷id不能为空") @ApiParam(value = "问卷id", name = "id") @RequestParam Long id) {
        List<SendInfoRes> list = traQuestionnaireService.sendInfo(id);
        return getDataTable(list);
    }

    /**
     * 下发提交操作
     */
    @PostMapping("/send/submit")
    @ApiOperation(httpMethod = "POST", value = "下发提交操作")
    public AjaxResult sendSubmit(@Validated @RequestBody List<SendInfoRes> sendInfoResList) {
        return AjaxResult.success(traQuestionnaireService.sendSubmit(sendInfoResList));

    }

    /**
     * 下发反馈表详情
     */
    @GetMapping("/send/detail")
    @ApiOperation(httpMethod = "GET", value = "下发反馈表详情")
    public TableDataInfo sendDetail (TraQuestionnaireFeedBackRecord req) {
        List<SendDetailRes> list = traQuestionnaireService.sendDetail(req);
        return getDataTable(list);
    }


}
