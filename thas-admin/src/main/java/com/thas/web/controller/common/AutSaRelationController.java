package com.thas.web.controller.common;

import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.core.page.TableDataInfo;
import com.thas.web.domain.AutSaRelation;
import com.thas.web.service.IAutSaRelationService;
import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 认证自评关联Controller
 *
 * <AUTHOR>
 * @date 2022-01-17
 */
@RestController
@RequestMapping("/aut/sa/relation")
@Api(value = "AutSaRelationController", tags = "管理端-所有评审相关API")
public class AutSaRelationController extends BaseController {

    @Resource
    private IAutSaRelationService autSaRelationService;

    /**
     * 根据条件查询认证自评关联信息
     * 非医院角色 需要直接使用自评编码查询相关信息
     * 医院角色: 只需要医院账户信息
     *
     * @param autSaRelation 认证自评关联信息查询条件
     * @return 认证自评关联信息
     */
    @ApiOperation("根据条件查询认证自评关联信息")
    @PostMapping(value = "/query")
    public AjaxResult queryAutSaRelation(@RequestBody AutSaRelation autSaRelation) {
        return autSaRelationService.queryAutSaRelation(autSaRelation);
    }

    /**
     * 查询认证自评关联列表  --- 管理员端所有评审列表
     *
     * @param autSaRelation 认证自评关联信息查询条件
     * @return 认证自评关联信息列表
     */
    @ApiOperation("管理员端所有评审列表")
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody AutSaRelation autSaRelation) {
        startPage();
        return getDataTable(autSaRelationService.selectAllAutSaRelationList(autSaRelation));
    }

}
