package com.thas.web.controller.common;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.thas.web.domain.CstVersioning;
import com.thas.web.service.ICstVersioningService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.thas.common.annotation.Log;
import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.enums.BusinessType;
import com.thas.common.utils.poi.ExcelUtil;
import com.thas.common.core.page.TableDataInfo;

/**
 * 版本管理模板
Controller
 *
 * <AUTHOR>
 * @date 2022-01-20
 */
@Api(value="CstVersioningController",tags = "版本管理模板")
@RestController
@RequestMapping("/system/versioning")
public class CstVersioningController extends BaseController
{
    @Autowired
    private ICstVersioningService cstVersioningService;

    /**
     * 查询版本管理模板
列表
     */
    //@PreAuthorize("@ss.hasPermi('system:versioning:list')")
    @ApiOperation("版本管理模板列表")
    @GetMapping("/list")
    public TableDataInfo list(CstVersioning cstVersioning)
    {
        startPage();
        List<CstVersioning> list = cstVersioningService.selectCstVersioningList(cstVersioning);
        return getDataTable(list);
    }

    /**
     * 导出版本管理模板
列表
     */
    //@PreAuthorize("@ss.hasPermi('system:versioning:export')")
    @Log(title = "版本管理模板 ", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CstVersioning cstVersioning)
    {
        List<CstVersioning> list = cstVersioningService.selectCstVersioningList(cstVersioning);
        ExcelUtil<CstVersioning> util = new ExcelUtil<CstVersioning>(CstVersioning.class);
        util.exportExcel(response, list, "版本管理模板数据");
    }

    /**
     * 获取版本管理模板
详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:versioning:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(cstVersioningService.selectCstVersioningById(id));
    }

    /**
     * 新增版本管理模板

     */
    //@PreAuthorize("@ss.hasPermi('system:versioning:add')")
    @ApiOperation("新增版本管理模板")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "versionName", value = "模板名称", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "status", value = "数据状态（0未启用 1已启用）", dataType = "String", dataTypeClass = String.class)
    })
    @Log(title = "版本管理模板", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CstVersioning cstVersioning)
    {
        return toAjax(cstVersioningService.insertCstVersioning(cstVersioning));
    }

    /**
     * 修改版本管理模板

     */
    @ApiOperation("修改版本管理模板")
    //@PreAuthorize("@ss.hasPermi('system:versioning:edit')")
    @Log(title = "版本管理模板", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CstVersioning cstVersioning)
    {
        return toAjax(cstVersioningService.updateCstVersioning(cstVersioning));
    }

    /**
     * 删除版本管理模板

     */
    //@PreAuthorize("@ss.hasPermi('system:versioning:remove')")
    @Log(title = "版本管理模板", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(cstVersioningService.deleteCstVersioningByIds(ids));
    }


    /**
     * 验证密码后删除版本管理模板
     */
    @ApiOperation("验证密码后删除版本管理模板")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "模板id", dataType = "Long", dataTypeClass = Long.class),
            @ApiImplicitParam(name = "password", value = "密码", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "delFlag", value = "删除状态（0未删除 1删除）", dataType = "String", dataTypeClass = String.class),
    })
    //@PreAuthorize("@ss.hasPermi('system:versioning:remove')")
    @Log(title = "版本管理模板", businessType = BusinessType.DELETE)
    @PostMapping("/deleteAndVerifyPwdById")
    public AjaxResult deleteAndVerifyPwdById(@RequestBody CstVersioning cstVersioning)
    {
        cstVersioningService.deleteAndVerifyPwdById(cstVersioning);
        return AjaxResult.success();
    }

    /**
     * 验证密码后通过id修改状态为启用
     */
    @ApiOperation("通过id修改状态为启用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "模板id", dataType = "Long", dataTypeClass = Long.class),
            @ApiImplicitParam(name = "versionId", value = "模板ID", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "password", value = "密码", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "status", value = "数据状态（1正常 0停用）", dataType = "String", dataTypeClass = String.class)
    })
    //@PreAuthorize("@ss.hasPermi('system:versioning:edit')")
    @Log(title = "版本管理模板", businessType = BusinessType.UPDATE)
    @PutMapping("/updateStatusStartById")
    public AjaxResult updateStatusStartById(@RequestBody CstVersioning cstVersioning)
    {
        return toAjax(cstVersioningService.updateStatusStartById(cstVersioning));
    }
}
