package com.thas.web.controller.common;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.thas.common.annotation.Log;
import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.enums.BusinessType;
import com.thas.web.domain.MessageSendUser;
import com.thas.web.service.IMessageSendUserService;
import com.thas.common.utils.poi.ExcelUtil;
import com.thas.common.core.page.TableDataInfo;

/**
 * 消息发送指定用户关联Controller
 *
 * <AUTHOR>
 * @date 2022-01-25
 */
@RestController
@RequestMapping("/system/messageSendUser")
public class MessageSendUserController extends BaseController
{
    @Autowired
    private IMessageSendUserService messageSendUserService;

    /**
     * 查询消息发送指定用户关联列表
     */
    @PreAuthorize("@ss.hasPermi('system:user:list')")
    @GetMapping("/list")
    public TableDataInfo list(MessageSendUser messageSendUser)
    {
        startPage();
        List<MessageSendUser> list = messageSendUserService.selectMessageSendUserList(messageSendUser);
        return getDataTable(list);
    }

    /**
     * 导出消息发送指定用户关联列表
     */
    @PreAuthorize("@ss.hasPermi('system:user:export')")
    @Log(title = "消息发送指定用户关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MessageSendUser messageSendUser)
    {
        List<MessageSendUser> list = messageSendUserService.selectMessageSendUserList(messageSendUser);
        ExcelUtil<MessageSendUser> util = new ExcelUtil<MessageSendUser>(MessageSendUser.class);
        util.exportExcel(response, list, "消息发送指定用户关联数据");
    }

    /**
     * 获取消息发送指定用户关联详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:user:query')")
    @GetMapping(value = "/{sendMessageId}")
    public AjaxResult getInfo(@PathVariable("sendMessageId") Long sendMessageId)
    {
        return AjaxResult.success(messageSendUserService.selectMessageSendUserBySendMessageId(sendMessageId));
    }

    /**
     * 新增消息发送指定用户关联
     */
    @PreAuthorize("@ss.hasPermi('system:user:add')")
    @Log(title = "消息发送指定用户关联", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MessageSendUser messageSendUser)
    {
        return toAjax(messageSendUserService.insertMessageSendUser(messageSendUser));
    }

    /**
     * 修改消息发送指定用户关联
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "消息发送指定用户关联", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MessageSendUser messageSendUser)
    {
        return toAjax(messageSendUserService.updateMessageSendUser(messageSendUser));
    }

    /**
     * 删除消息发送指定用户关联
     */
    @PreAuthorize("@ss.hasPermi('system:user:remove')")
    @Log(title = "消息发送指定用户关联", businessType = BusinessType.DELETE)
	@DeleteMapping("/{sendMessageIds}")
    public AjaxResult remove(@PathVariable Long[] sendMessageIds)
    {
        return toAjax(messageSendUserService.deleteMessageSendUserBySendMessageIds(sendMessageIds));
    }
}
