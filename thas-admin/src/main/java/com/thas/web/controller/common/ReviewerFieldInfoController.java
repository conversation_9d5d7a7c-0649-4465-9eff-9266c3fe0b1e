package com.thas.web.controller.common;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.thas.web.domain.ReviewerFieldInfo;
import com.thas.web.service.IReviewerFieldInfoService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.thas.common.annotation.Log;
import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.enums.BusinessType;
import com.thas.common.utils.poi.ExcelUtil;
import com.thas.common.core.page.TableDataInfo;

/**
 * 评审员领域关联Controller
 * 
 * <AUTHOR>
 * @date 2022-01-19
 */
@RestController
@RequestMapping("/system/info")
public class ReviewerFieldInfoController extends BaseController
{
    @Autowired
    private IReviewerFieldInfoService reviewerFieldInfoService;

    /**
     * 查询评审员领域关联列表
     */
    @PreAuthorize("@ss.hasPermi('system:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(ReviewerFieldInfo reviewerFieldInfo)
    {
        startPage();
        List<ReviewerFieldInfo> list = reviewerFieldInfoService.selectReviewerFieldInfoList(reviewerFieldInfo);
        return getDataTable(list);
    }

    /**
     * 导出评审员领域关联列表
     */
    @PreAuthorize("@ss.hasPermi('system:info:export')")
    @Log(title = "评审员领域关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ReviewerFieldInfo reviewerFieldInfo)
    {
        List<ReviewerFieldInfo> list = reviewerFieldInfoService.selectReviewerFieldInfoList(reviewerFieldInfo);
        ExcelUtil<ReviewerFieldInfo> util = new ExcelUtil<ReviewerFieldInfo>(ReviewerFieldInfo.class);
        util.exportExcel(response, list, "评审员领域关联数据");
    }

    /**
     * 获取评审员领域关联详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:info:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(reviewerFieldInfoService.selectReviewerFieldInfoById(id));
    }

    /**
     * 新增评审员领域关联
     */
    @PreAuthorize("@ss.hasPermi('system:info:add')")
    @Log(title = "评审员领域关联", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ReviewerFieldInfo reviewerFieldInfo)
    {
        return toAjax(reviewerFieldInfoService.insertReviewerFieldInfo(reviewerFieldInfo));
    }

    /**
     * 修改评审员领域关联
     */
    @PreAuthorize("@ss.hasPermi('system:info:edit')")
    @Log(title = "评审员领域关联", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ReviewerFieldInfo reviewerFieldInfo)
    {
        return toAjax(reviewerFieldInfoService.updateReviewerFieldInfo(reviewerFieldInfo));
    }

    /**
     * 删除评审员领域关联
     */
    @PreAuthorize("@ss.hasPermi('system:info:remove')")
    @Log(title = "评审员领域关联", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(reviewerFieldInfoService.deleteReviewerFieldInfoByIds(ids));
    }
}
