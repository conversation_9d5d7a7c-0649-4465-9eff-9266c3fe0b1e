package com.thas.web.controller.common;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import com.thas.web.domain.TraineesPractice;
import com.thas.web.domain.vo.ReviewerTraineesRecVO;
import com.thas.web.dto.TraineesPracticeDTO;
import com.thas.web.service.ITraineesPracticeService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.thas.common.annotation.Log;
import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.enums.BusinessType;
import com.thas.common.utils.poi.ExcelUtil;
import com.thas.common.core.page.TableDataInfo;

/**
 * 评审学员实践记录Controller
 *
 * <AUTHOR>
 * @date 2022-05-31
 */
@Validated
@RestController
@RequestMapping("/trainees/practice")
public class TraineesPracticeController extends BaseController {
    @Autowired
    private ITraineesPracticeService traineesPracticeService;

    /**
     * 查询评审学员实践记录列表
     */
    // @PreAuthorize("@ss.hasPermi('system:practice:list')")
    @GetMapping("/list")
    public TableDataInfo list(TraineesPractice traineesPractice) {
        startPage();
        List<TraineesPractice> list = traineesPracticeService.selectTraineesPracticeList(traineesPractice);
        return getDataTable(list);
    }

    /**
     * 导出评审学员实践记录列表
     */
    // @PreAuthorize("@ss.hasPermi('system:practice:export')")
    @Log(title = "评审学员实践记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TraineesPractice traineesPractice) {
        List<TraineesPractice> list = traineesPracticeService.selectTraineesPracticeList(traineesPractice);
        ExcelUtil<TraineesPractice> util = new ExcelUtil<TraineesPractice>(TraineesPractice.class);
        util.exportExcel(response, list, "评审学员实践记录数据");
    }

    /**
     * 通过id查询对应的附件详情和带教评审员
     */
    @GetMapping("/fileAndReviewer")
    public AjaxResult getFileInfoAndReviewerById(@NotNull(message = "id不能为空") Long id) {
        ReviewerTraineesRecVO reviewerTraineesRecVO = traineesPracticeService.getFileInfoAndReviewerById(id);
        return AjaxResult.success(reviewerTraineesRecVO);
    }

    /**
     * 获取评审学员实践记录详细信息
     */
    // @PreAuthorize("@ss.hasPermi('system:practice:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(traineesPracticeService.selectTraineesPracticeById(id));
    }

    /**
     * 新增评审学员实践记录
     */
    // @PreAuthorize("@ss.hasPermi('system:practice:add')")
    @Log(title = "评审学员实践记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TraineesPractice traineesPractice) {
        return toAjax(traineesPracticeService.insertTraineesPractice(traineesPractice));
    }

    /**
     * 对学员添加实践培训
     */
    @PostMapping("/insert")
    public AjaxResult insertPractice(@RequestBody @NotEmpty(message = "添加评审学员实践列表不能为空")
                                         @Valid List<TraineesPracticeDTO> traineesPracticeDTOList) {
        return traineesPracticeService.insertPractice(traineesPracticeDTOList);
    }

    /**
     * 修改评审学员实践记录
     */
    // @PreAuthorize("@ss.hasPermi('system:practice:edit')")
    @Log(title = "评审学员实践记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TraineesPractice traineesPractice) {
        return toAjax(traineesPracticeService.updateTraineesPractice(traineesPractice));
    }

    /**
     * 删除评审学员实践记录
     */
    // @PreAuthorize("@ss.hasPermi('system:practice:remove')")
    @Log(title = "评审学员实践记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(traineesPracticeService.deleteTraineesPracticeByIds(ids));
    }
}
