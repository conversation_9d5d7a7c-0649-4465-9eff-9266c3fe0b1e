package com.thas.web.controller.common;

import com.thas.common.annotation.Log;
import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.core.page.TableDataInfo;
import com.thas.common.enums.BusinessType;
import com.thas.common.utils.poi.ExcelUtil;
import com.thas.web.domain.HospitalReviewer;
import com.thas.web.domain.vo.FileInfoVO;
import com.thas.web.dto.DelHosRevDTO;
import com.thas.web.service.IHospitalReviewerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 医疗结构认证信息与评审员信息Controller
 *
 * <AUTHOR>
 * @date 2022-01-25
 */
@Api(value = "HospitalReviewerController", tags = "医疗结构认证信息与评审员信息Controller")
@RestController
@RequestMapping("/system/reviewer")
public class HospitalReviewerController extends BaseController {
    @Autowired
    private IHospitalReviewerService hospitalReviewerService;

    /**
     * 查询医疗结构认证信息与评审员信息列表
     */
    // @PreAuthorize("@ss.hasPermi('system:reviewer:list')")
    @GetMapping("/list")
    public TableDataInfo list(HospitalReviewer hospitalReviewer) {
        startPage();
        List<HospitalReviewer> list = hospitalReviewerService.selectHospitalReviewerList(hospitalReviewer);
        return getDataTable(list);
    }

    /**
     * 导出医疗结构认证信息与评审员信息列表
     */
    //@PreAuthorize("@ss.hasPermi('system:reviewer:export')")
    @Log(title = "医疗结构认证信息与评审员信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HospitalReviewer hospitalReviewer) {
        List<HospitalReviewer> list = hospitalReviewerService.selectHospitalReviewerList(hospitalReviewer);
        ExcelUtil<HospitalReviewer> util = new ExcelUtil<HospitalReviewer>(HospitalReviewer.class);
        util.exportExcel(response, list, "医疗结构认证信息与评审员信息数据");
    }

    /**
     * 获取医疗结构认证信息与评审员信息详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:reviewer:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(hospitalReviewerService.selectHospitalReviewerById(id));
    }

    /**
     * 新增医疗结构认证信息与评审员信息
     */
    //@PreAuthorize("@ss.hasPermi('system:reviewer:add')")
    @Log(title = "医疗结构认证信息与评审员信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HospitalReviewer hospitalReviewer) {
        return toAjax(hospitalReviewerService.insertHospitalReviewer(hospitalReviewer));
    }

    /**
     * 删除对应的评审计划中的评审员
     */
    @PostMapping("/del/hos/rev")
    public AjaxResult delHosReviewer(@RequestBody @Validated DelHosRevDTO delHosRevDTO) {
        return hospitalReviewerService.delHosReviewer(delHosRevDTO);
    }

    /**
     * 修改医疗结构认证信息与评审员信息
     */
    //@PreAuthorize("@ss.hasPermi('system:reviewer:edit')")
    @ApiOperation("修改医疗结构认证信息与评审员信息")
    @Log(title = "医疗结构认证信息与评审员信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HospitalReviewer hospitalReviewer) {
        return toAjax(hospitalReviewerService.updateHospitalReviewer(hospitalReviewer));
    }

    /**
     * 紧急更换评审员
     */
    @ApiOperation("紧急更换评审员")
    @PostMapping("urgent/update/reviewer")
    public AjaxResult urgentUpdateReviewer(@RequestBody HospitalReviewer hospitalReviewer) {
        return toAjax(hospitalReviewerService.urgentUpdateReviewer(hospitalReviewer));
    }

    @PostMapping("/update/interest")
    public AjaxResult updateInterestFileId(@RequestBody HospitalReviewer hospitalReviewer) {
        return toAjax(hospitalReviewerService.updateInterestFileId(hospitalReviewer));
    }

    /**
     * 删除医疗结构认证信息与评审员信息
     */
    //@PreAuthorize("@ss.hasPermi('system:reviewer:remove')")
    @Log(title = "医疗结构认证信息与评审员信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(hospitalReviewerService.deleteHospitalReviewerByIds(ids));
    }

    @ApiOperation("管理员上传利益冲突表")
    @PostMapping("/upload/adminInterestFile")
    public AjaxResult uploadAdminInterestFile(@Valid @RequestPart(value = "file") MultipartFile file,
                                              @NotNull(message = "applyNo医院编号不能为空") @RequestParam(value = "applyNo") String applyNo,
                                              @NotNull(message = "reviewerId评审员Id不能为空") @RequestParam(value = "reviewerId") String reviewerId,
                                              @ApiParam(name = "下载文件名", value = "downLoadFileName") @RequestParam(value = "downLoadFileName") String downLoadFileName) {
        FileInfoVO fileInfoVO = hospitalReviewerService.uploadAdminInterestFile(file, applyNo, reviewerId, downLoadFileName);
        return AjaxResult.success(fileInfoVO);

    }
}
