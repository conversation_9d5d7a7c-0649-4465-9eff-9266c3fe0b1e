package com.thas.web.controller.common;

import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;
import com.thas.web.domain.*;
import com.thas.web.service.process.StatisticsReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/statistics/report")
@Api(value = "StatisticsReportController", tags = "统计报表Controller")
public class StatisticsReportController extends BaseController {

    @Resource
    private StatisticsReportService statisticsReportService;


    @PostMapping(value = "/queryList")
    @ApiOperation("查询")
    public AjaxResult queryList(@RequestBody StatisticsReportReq statisticsReportReq) {
        //TableDataInfo
        //startPage();
        //return getDataTable(statisticsReportService.queryList(statisticsReportReq));
        StatisticsReportRes statisticsReportRes = statisticsReportService.queryList(statisticsReportReq);
        return AjaxResult.success(statisticsReportRes);
    }

    @PostMapping(value = "/detail")
    @ApiOperation("详情")
    public AjaxResult detail(@RequestBody StatisticsReportReq statisticsReportReq) {
        return AjaxResult.success(statisticsReportService.detail(statisticsReportReq));
    }

}
