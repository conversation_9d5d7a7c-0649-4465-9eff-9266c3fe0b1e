package com.thas.web.controller.common;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.thas.web.domain.ReviewTraPractice;
import com.thas.web.service.IReviewTraPracticeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.thas.common.annotation.Log;
import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.enums.BusinessType;
import com.thas.common.utils.poi.ExcelUtil;
import com.thas.common.core.page.TableDataInfo;

/**
 * 评审学员参与评审记录Controller
 *
 * <AUTHOR>
 * @date 2022-05-23
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/practice/subject")
public class ReviewTraPracticeController extends BaseController {

    @Autowired
    private IReviewTraPracticeService reviewTraPracticeService;

    /**
     * 查询评审学员参与评审记录列表
     */
    // @PreAuthorize("@ss.hasPermi('system:rec:list')")
    @GetMapping("/list")
    public TableDataInfo list(ReviewTraPractice reviewerTraineesRec) {
        startPage();
        List<ReviewTraPractice> list = reviewTraPracticeService.selectReviewTraPracticeList(reviewerTraineesRec);
        return getDataTable(list);
    }

    /**
     * 导出评审学员参与评审记录列表
     */
    // @PreAuthorize("@ss.hasPermi('system:rec:export')")
    @Log(title = "评审学员参与评审记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ReviewTraPractice reviewerTraineesRec) {
        List<ReviewTraPractice> list = reviewTraPracticeService.selectReviewTraPracticeList(reviewerTraineesRec);
        ExcelUtil<ReviewTraPractice> util = new ExcelUtil<ReviewTraPractice>(ReviewTraPractice.class);
        util.exportExcel(response, list, "评审学员参与评审记录数据");
    }

    /**
     * 获取评审学员参与评审记录详细信息
     */
    // @PreAuthorize("@ss.hasPermi('system:rec:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(reviewTraPracticeService.selectReviewTraPracticeById(id));
    }

    /**
     * 新增评审学员参与评审记录
     */
    // @PreAuthorize("@ss.hasPermi('system:rec:add')")
    @Log(title = "评审学员参与评审记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ReviewTraPractice reviewerTraineesRec) {
        return toAjax(reviewTraPracticeService.insertReviewTraPractice(reviewerTraineesRec));
    }


    /**
     * 修改评审学员参与评审记录
     */
    // @PreAuthorize("@ss.hasPermi('system:rec:edit')")
    @Log(title = "评审学员参与评审记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ReviewTraPractice reviewerTraineesRec) {
        return toAjax(reviewTraPracticeService.updateReviewTraPractice(reviewerTraineesRec));
    }

    /**
     * 删除评审学员参与评审记录
     */
    // @PreAuthorize("@ss.hasPermi('system:rec:remove')")
    @Log(title = "评审学员参与评审记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(reviewTraPracticeService.deleteReviewTraPracticeByIds(ids));
    }
}
