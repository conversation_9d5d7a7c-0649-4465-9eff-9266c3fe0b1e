package com.thas.web.controller.common;

import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.enums.ImportFileTypeEnum;
import com.thas.common.enums.ServiceExceptionEnum;
import com.thas.common.exception.ServiceException;
import com.thas.common.utils.StringUtils;
import com.thas.web.service.ImportService;
import java.io.InputStream;
import java.util.Arrays;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 通用导入excel Controller
 *
 * <AUTHOR>
 * @date 2022-01-17
 */
@Slf4j
@RestController
@RequestMapping("/common/import")
public class ImportController extends BaseController {

    @Resource
    private ImportService importService;

    /**
     * excel导入
     */
    @PostMapping(value = "/excel")
    public AjaxResult importFile(@Valid @RequestPart(value = "file") MultipartFile file,
                                 @Valid @RequestPart(value = "fileType") String fileType,
                                 @Valid @RequestPart(value = "title") String title
                                 ) {
        log.info("导入文件名:{}, 文件类型:{},标题:{}", file.getOriginalFilename(), fileType, title);
        InputStream in = null;
        try {
            // 校验参数 校验传入的文件类型是否和文件格式匹配
            ImportFileTypeEnum importFileTypeEnum = ImportFileTypeEnum.getFileTypeEnumByFileType(fileType);
            if (null == importFileTypeEnum) {
                log.info("导入的文件类型:{} 未配置", fileType);
                throw new ServiceException(StringUtils.format("文件类型({})未配置，导入失败。", fileType));
            }

            if (!Arrays.asList(importFileTypeEnum.getFormat().split(",")).contains(file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1))) {
                log.info("导入的文件格式:{} 和 配置的格式：{} 不匹配， 导入失败", fileType, importFileTypeEnum.getFormat());
                throw new ServiceException(StringUtils.format("文件类型({})未配置，上传失败。", fileType));
            }
            in = file.getInputStream();
            importService.importExcel(in, importFileTypeEnum, title);
            return AjaxResult.success();
        } catch (Exception e) {
            log.error("导入excel：{} 异常 e:{}", file.getOriginalFilename(), e.getMessage());
            return error(ServiceExceptionEnum.CST_VERSIONING_ERROR_1000006.getMessage());
        } finally {
            if (null != in) {
                try {
                    in.close();
                } catch (Exception e) {
                    log.error("输入流关闭异常");
                }
            }
        }
    }

    /**
     * 测试-导入-优化内容：（拆分到对应款，去掉内容前后空格，拆分后去除序号）然后优化数据实现导出
     */
    @PostMapping(value = "/testExcel")
    public AjaxResult testExcel(
            @Valid @RequestPart(value = "file") MultipartFile file,
            @Valid @RequestPart(value = "fileType") String fileType,
            @Valid @RequestPart(value = "title") String title,
            HttpServletResponse response
            ) {

        log.info("导入文件名:{}, 文件类型:{},标题:{}", file.getOriginalFilename(), fileType, title);
        InputStream in = null;
        try {
            // 校验参数 校验传入的文件类型是否和文件格式匹配
            ImportFileTypeEnum importFileTypeEnum = ImportFileTypeEnum.getFileTypeEnumByFileType(fileType);
            if (null == importFileTypeEnum) {
                log.info("导入的文件类型:{} 未配置", fileType);
                throw new ServiceException(StringUtils.format("文件类型({})未配置，导入失败。", fileType));
            }

            if (!Arrays.asList(importFileTypeEnum.getFormat().split(",")).contains(file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1))) {
                log.info("导入的文件格式:{} 和 配置的格式：{} 不匹配， 导入失败", fileType, importFileTypeEnum.getFormat());
                throw new ServiceException(StringUtils.format("文件类型({})未配置，上传失败。", fileType));
            }
            in = file.getInputStream();
            importService.testImportExcel(in, importFileTypeEnum, title,response);
            return AjaxResult.success();
        } catch (Exception e) {
            log.error("导入excel：{} 异常 e:{}", file.getOriginalFilename(), e.getMessage());
            return error();
        } finally {
            if (null != in) {
                try {
                    in.close();
                } catch (Exception e) {
                    log.error("输入流关闭异常");
                }
            }
        }
    }

}

