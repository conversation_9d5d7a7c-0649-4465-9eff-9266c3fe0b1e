package com.thas.web.controller.common;

import com.thas.web.domain.AsaStatusConfig;
import com.thas.web.service.IAsaStatusConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.thas.common.core.controller.BaseController;
import com.thas.common.core.page.TableDataInfo;

import java.util.List;

/**
 * 节点配置Controller
 * 
 * <AUTHOR>
 * @date 2023-08-17
 */
@RestController
@RequestMapping("/asa/status/config")
@Api(value = "AsaStatusConfigController", tags = "节点配置Controller")
public class AsaStatusConfigController extends BaseController
{
    @Autowired
    private IAsaStatusConfigService asaStatusConfigService;

    /**
     * 查询节点配置列表
     */
  //  @PreAuthorize("@ss.hasPermi('common:config:list')")
    @GetMapping("/list")
    @ApiOperation("查询节点配置列表")
    public TableDataInfo list(AsaStatusConfig asaStatusConfig)
    {
        //startPage();
        List<AsaStatusConfig> list = asaStatusConfigService.selectAsaStatusConfigList(asaStatusConfig);
        return getDataTable(list);
    }

}
