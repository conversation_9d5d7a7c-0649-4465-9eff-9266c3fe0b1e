package com.thas.web.controller.sched.task;

import com.thas.common.exception.ServiceException;
import com.thas.common.utils.StringUtils;
import com.thas.web.service.AdminTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 定时任务调度
 *
 */
@Component("AdminTask")
@Slf4j
public class AdminTask
{

    @Resource
    private AdminTaskService adminTaskService;


    /**
     * 分配验证评审员和审查员，支持站内信、邮件、短信提醒，提前15天，定时任务每天早上八点执行
     * @param date 提前通知的天数
     *
     * */
    public void noticeTask(String date)
    {
        log.info("定时任务开始-入参天数为：{}",date);
        try {
            Boolean aBoolean = adminTaskService.noticeTask(date);
            if(Boolean.TRUE.equals(aBoolean)){
                log.info("[医院是否有未分配人员-通知任务]定时任务执行成功！");
            }
        } catch (Exception e) {
            log.info("[医院是否有未分配人员-通知任务],业务异常{}",e.getMessage());
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 根据评审周期失效已过期的评审数据
     */
    public void invalidAutSaAudByCycle(){
        log.info("定时任务开始-----");
        try {
            adminTaskService.invalidAutSaAudByCycle();
            log.info("[根据评审周期失效当天过期的评审数据]定时任务执行成功！");
        } catch (Exception e) {
            log.info("[根据评审周期失效当天过期的评审数据],业务异常{}",e.getMessage());
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 评审到期通知；评审周期各阶段 结束日期到期前{3}天每天短信和邮箱通知一次
     */
    public void reviewerExpNotice(String date){
        if (StringUtils.isBlank(date)){
            date = "2";
        }
        log.info("评审到期通知-定时任务开始-----");
        try {
            adminTaskService.reviewerExpNotice(date);
            log.info("评审到期通知-定时任务完成-----");
        } catch (Exception e) {
            log.info("[评审到期通知-定时任务业务异常{}",e.getMessage());
            throw new ServiceException(e.getMessage());
        }
    }

}
