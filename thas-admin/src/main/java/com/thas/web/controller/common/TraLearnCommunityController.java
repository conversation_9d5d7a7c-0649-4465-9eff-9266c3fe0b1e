package com.thas.web.controller.common;


import com.thas.common.annotation.Log;
import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.core.page.TableDataInfo;
import com.thas.common.enums.BusinessType;
import com.thas.web.domain.TraLearnCommunity;
import com.thas.web.domain.vo.TraLearnCommunityVO;
import com.thas.web.service.ITraLearnCommunityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 学习社区文章详情Controller
 *
 * <AUTHOR>
 * @date 2022-02-15
 */
@RestController
@RequestMapping("/system/community")
@Api(value = "API - TraLearnCommunityController", tags = "学习社区")
public class TraLearnCommunityController extends BaseController {
    @Autowired
    private ITraLearnCommunityService traLearnCommunityService;

    /**
     * 查询学习社区文章详情列表
     */
    @GetMapping("/list")
    @ApiOperation(httpMethod = "GET", value = "查询学习社区文章详情列表")
    public TableDataInfo list(TraLearnCommunity traLearnCommunity) {
        startPage();
        List<TraLearnCommunityVO> list = traLearnCommunityService.selectTraLearnCommunityList(traLearnCommunity);
        return getDataTable(list);
    }

    /**
     * 获取学习社区文章详情详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation(httpMethod = "GET", value = "获取学习社区文章详情详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(traLearnCommunityService.selectTraLearnCommunityById(id));
    }

    /**
     * 新增学习社区文章详情
     */
    @Log(title = "新增学习社区文章详情", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(httpMethod = "POST", value = "新增学习社区文章详情")
    public AjaxResult addOrUpdate(@RequestBody TraLearnCommunity traLearnCommunity) {
        return toAjax(traLearnCommunityService.insertOrUpdateTraLearnCommunity(traLearnCommunity));
    }

    /**
     * 删除学习社区文章详情
     */
    @Log(title = "删除学习社区文章详情", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(httpMethod = "DELETE", value = "删除学习社区文章详情")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(traLearnCommunityService.deleteTraLearnCommunityByIds(ids));
    }

    /**
     * 学习社区文章点赞
     */
    @Log(title = "学习社区文章点赞", businessType = BusinessType.INSERT)
    @GetMapping
    @ApiOperation(httpMethod = "GET", value = "学习社区文章点赞")
    public AjaxResult doLike(@ApiParam(value = "文章id", name = "id") @RequestParam("id") Long id, @ApiParam(value = "0:取消点赞 1:点赞", name = "type") @RequestParam("type") Integer type) {
        return toAjax(traLearnCommunityService.doLike(id, type));
    }
}
