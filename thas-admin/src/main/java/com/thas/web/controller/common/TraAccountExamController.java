package com.thas.web.controller.common;


import cn.hutool.core.text.CharSequenceUtil;
import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.core.page.TableDataInfo;
import com.thas.web.domain.TraAccountExam;
import com.thas.web.service.ITraAccountExamService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 评审员学习资源考卷通过情况Controller
 *
 * <AUTHOR>
 * @date 2022-01-20
 */
@RestController
@RequestMapping("/system/exam")
public class TraAccountExamController extends BaseController {
    @Autowired
    private ITraAccountExamService traAccountExamService;

    /**
     * 查询评审员学习资源考卷通过情况列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TraAccountExam traAccountExam) {
        startPage();
        if (CharSequenceUtil.isEmpty(traAccountExam.getAccount())) {
            traAccountExam.setAccount(getUsername());
        }
        List<TraAccountExam> list = traAccountExamService.selectTraAccountExamList(traAccountExam);
        return getDataTable(list);
    }

    /**
     * 线上学习通过,初始化数据
     */
    @GetMapping("/pass")
    @ApiOperation(httpMethod = "GET", value = "在线学习通过,提交审核")
    public AjaxResult pass() {
        String account = getUsername();
        Long roleId = getLoginUser().getUser().getRoleId();
        return AjaxResult.success(traAccountExamService.pass(account, roleId));
    }
}
