package com.thas.web.controller.common;


import cn.hutool.json.JSONUtil;
import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.core.domain.entity.SysUser;
import com.thas.common.core.page.TableDataInfo;
import com.thas.web.domain.CstOfflineTrainingManagement;
import com.thas.web.domain.CstReviewerOfflineTraining;
import com.thas.web.domain.QueryCstOfflineTrainingDTO;
import com.thas.web.domain.ReviewerBaseInfo;
import com.thas.web.domain.vo.OfflineTrainingRegisteredVo;
import com.thas.web.domain.vo.ReviewInterestVO;
import com.thas.web.domain.vo.TraineesReviewRecVO;
import com.thas.web.dto.*;
import com.thas.web.service.IReviewerBaseInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;


/**
 * 评审员基本信息Controller
 *
 * <AUTHOR>
 * @date 2022-01-20
 */
@RestController
@RequestMapping("/reviewer")
@Api(value = "API - ReviewerBaseInfoController", tags = "评审员对应接口")
public class ReviewerBaseInfoController extends BaseController {


    @Autowired
    private IReviewerBaseInfoService iReviewerBaseInfoService;

    /**
     * 评审员基本信息提交
     *
     * @param reviewerBaseInfoDTO 提交数据
     * @return 提交结果
     */
    @PostMapping("/base/info/submit")
    @ApiOperation(httpMethod = "POST", value = "评审员基本数据提交")
    public AjaxResult reviewerBaseInfoSubmit(@RequestBody ReviewerBaseInfoDTO reviewerBaseInfoDTO) {
        logger.info("评审员基本数据提交入参:{}", JSONUtil.toJsonStr(reviewerBaseInfoDTO));
        AjaxResult ajaxResult = iReviewerBaseInfoService.reviewerBaseInfoSubmit(reviewerBaseInfoDTO, false);
        logger.info("评审员基本数据提交出参:{}", JSONUtil.toJsonStr(reviewerBaseInfoDTO));
        return ajaxResult;
    }

    /**
     * 评审员临时基本数据提交，免登
     *
     * @param reviewerBaseInfoDTO 基本数据
     * @return 提交结果
     */
    @PostMapping("/bing/base/info/submit")
    @ApiOperation(httpMethod = "POST", value = "评审员基本数据提交")
    public AjaxResult reviewerBaseInfoTmpSubmit(@RequestBody ReviewerBaseInfoDTO reviewerBaseInfoDTO) {
        logger.info("评审员基本数据免登提交入参:{}", JSONUtil.toJsonStr(reviewerBaseInfoDTO));
        AjaxResult ajaxResult = iReviewerBaseInfoService.reviewerBaseInfoTmpSubmit(reviewerBaseInfoDTO);
        logger.info("评审员基本数据免登提交出参:{}", JSONUtil.toJsonStr(reviewerBaseInfoDTO));
        return ajaxResult;
    }

    /**
     * 判断基本数据唯一性
     *
     * @param checkParamOnlyRequest 入参
     * @return 校验是否通过
     */
    @PostMapping("/check-param-only")
    @ApiOperation(httpMethod = "POST", value = "评审员基本数据提交")
    public AjaxResult checkParamOnly(@Validated @RequestBody CheckParamOnlyRequest checkParamOnlyRequest) {
        iReviewerBaseInfoService.checkParamOnly(checkParamOnlyRequest);
        return AjaxResult.success();
    }

    /**
     * 查询评审员的基本数据提交状态，免登
     *
     * @param queryBaseConditionDTO 查询条件
     * @return 提交结果
     */
    @PostMapping("/bing/query/submit/status")
    @ApiOperation(httpMethod = "POST", value = "评审员临时基本数据提交")
    public AjaxResult querySubmitStatus(@RequestBody QueryBaseConditionDTO queryBaseConditionDTO) {
        logger.info("评审员基本数据免登提交入参:{}", JSONUtil.toJsonStr(queryBaseConditionDTO));
        AjaxResult ajaxResult = iReviewerBaseInfoService.querySubmitStatus(queryBaseConditionDTO);
        logger.info("评审员基本数据免登提交出参:{}", JSONUtil.toJsonStr(queryBaseConditionDTO));
        return ajaxResult;
    }


    @PostMapping("/query/reviewer/list")
    @ApiOperation(httpMethod = "POST", value = "评审员列表查询")
    public TableDataInfo queryReviewerList(@RequestBody(required = false)
                                                   QueryReviewerListDTO queryReviewerListDTO) {
        logger.info("评审员基本数据提交入参:{}", JSONUtil.toJsonStr(queryReviewerListDTO));
        startPage();
        List<QueryReviewerListDTO> list = iReviewerBaseInfoService.queryReviewerList(queryReviewerListDTO);
        return getDataTable(list);
    }

    /**
     * 通过证件号码查询填写过的评审员信息 免登
     */
    @PostMapping("/bing/qry-reviewer-detail")
    @ApiOperation(httpMethod = "POST", value = "免登评审员详情查询")
    public ReviewerBaseInfoDTO bingQryReviewerDetail(@RequestBody @Validated TempRevInfoRequest tempRevInfoRequest) {
        return iReviewerBaseInfoService.bingQryReviewerDetail(tempRevInfoRequest);
    }


    /**
     * 拒绝之后再次提交
     *
     * @return 提交结果
     */
    @PostMapping("/bing/base-info-again-submit")
    @ApiOperation(httpMethod = "POST", value = "评审员基本数据拒绝后再次提交")
    public AjaxResult revAgainSubmit(@RequestBody @Validated AgainRevInfoSubmitRequest request) {

        return iReviewerBaseInfoService.revAgainSubmit(request);
    }

    /**
     * 管理员给评审员分配账号，查询对应审核通过并且没有账号数据
     */
    @PostMapping("/query/reviewer/tmp/list")
    @ApiOperation(httpMethod = "POST", value = "评审员临时列表查询")
    public List<ReviewerBaseInfo> queryReviewerTmpList(@RequestBody(required = false)
                                                                   ReviewerBaseInfo reviewerBaseInfo) {
        return iReviewerBaseInfoService.queryReviewerTmpList(reviewerBaseInfo);
    }

    /**
     * 分配账号提交
     */
    @PostMapping("/account/distribute")
    @ApiOperation(httpMethod = "POST", value = "评审员账号分配")
    public AjaxResult reviewerAccountDistribute(@RequestBody SysUser sysUser) {
        logger.info("评审员账号分配入参:{}", JSONUtil.toJsonStr(sysUser.getReviewerAcc()));
        AjaxResult ajaxResult = iReviewerBaseInfoService.reviewerAccountDistribute(sysUser);
        logger.info("评审员账号分配出参:{}", JSONUtil.toJsonStr(ajaxResult));
        return ajaxResult;
    }


    @PostMapping("/query/reviewer/detail")
    @ApiOperation(httpMethod = "POST", value = "评审员详情查询")
    public ReviewerBaseInfoDTO queryReviewerDetail(@ApiParam(name = "queryBaseConditionDTO", value = "查询条件入参", required = true)
                                                   @Valid @RequestBody QueryBaseConditionDTO queryBaseConditionDTO) {
        logger.info("/query/reviewer/detail 评审员基本数据详情查询入参:{}", JSONUtil.toJsonStr(queryBaseConditionDTO));
        return iReviewerBaseInfoService.queryReviewerDetail(queryBaseConditionDTO.getCommonId());
    }

    /**
     * 目前没用到
     */
    @PostMapping("/query/review/manage")
    @ApiOperation(httpMethod = "POST", value = "医疗机构初审/评审分配管理列表")
    public TableDataInfo queryReviewManage(@RequestBody ReviewManageDTO reviewManageDTO) {
        logger.info("/query/review/manage 医疗机构初审/评审分配管理列表入参:{}", JSONUtil.toJsonStr(reviewManageDTO));
        startPage();
        List<ReviewManageVO> list = iReviewerBaseInfoService.queryReviewManage(reviewManageDTO);
        return getDataTable(list);
    }

    /**
     * 评审员线下培训提交
     *
     * @param idList 对应已培训id列表
     * @return 提交结果
     */
    @PostMapping("/offline/training/submit")
    @ApiOperation(httpMethod = "POST", value = "评审员线下培训提交")
    public AjaxResult offlineTrainingSubmit(@RequestBody List<String> idList) {
        logger.info("/offline/training/submit 评审员线下培训提交入参:{}", JSONUtil.toJsonStr(idList));
        return iReviewerBaseInfoService.offlineTrainingSubmit(idList);
    }

    /**
     * 管理员针对线下培训给对应的参与的评审员
     * 添加签到表，并且标记为已参与。
     */
    @PostMapping("/rev/offline/training")
    public AjaxResult revOfflineTra(@RequestBody @Validated RevOfflineTraDTO revOfflineTraDTO) {
        logger.info("/rev/offline/training 入参:{}", JSONUtil.toJsonStr(revOfflineTraDTO));
        return iReviewerBaseInfoService.reviewOfflineTra(revOfflineTraDTO);
    }


    /**
     * 评审员线下培训列表查询
     *
     * @param queryCstOfflineTrainingDTO 查询条件
     * @return 查询结果
     */
    @PostMapping("/query/cstOffline/training")
    @ApiOperation(httpMethod = "POST", value = "评审员线下培训列表查询")
    public TableDataInfo queryCstOfflineTrainingList(@RequestBody QueryCstOfflineTrainingDTO queryCstOfflineTrainingDTO) {
        logger.info("/query/cstOffline/training 评审员线下培训列表查询入参:{}", JSONUtil.toJsonStr(queryCstOfflineTrainingDTO));
        List<CstOfflineTrainingManagement> list = iReviewerBaseInfoService.queryCstOfflineTrainingList(queryCstOfflineTrainingDTO);
        return getDataTable(list);
    }


    /**
     * 评审学员转评审员提交接口
     */
    @PostMapping(value = "/trainees/to/reviewer")
    public AjaxResult traineesToReviewer(@RequestBody QueryCstOfflineTrainingDTO queryCstOfflineTrainingDTO) {
        logger.info("/traineesToReviewer, 评审学员转评审员提交接口入参:{}", queryCstOfflineTrainingDTO.getAccountId());
        iReviewerBaseInfoService.traineesToReviewer(queryCstOfflineTrainingDTO);
        return AjaxResult.success();
    }



    /**
     * 查询出满足条件医疗机构评审记录以及对应学员列表
     */
    @PostMapping("/query/trainees/review/list")
    @ApiOperation(httpMethod = "POST", value = "查询出满足条件医疗机构评审记录以及对应学员列表")
    public TableDataInfo traineesReviewList() {
        startPage();
        List<TraineesReviewRecVO> list = iReviewerBaseInfoService.traineesReviewList();
        return getDataTable(list);
    }

    /**
     * 评审员端-评审员线下培训报名
     * @param cstReviewerOfflineTrainingList
     * @return 提交结果
     */
    @PostMapping("/offline/training/apply")
    @ApiOperation(httpMethod = "POST", value = "评审员线下培训报名")
    public AjaxResult offlineTrainingApply(@RequestBody List<CstReviewerOfflineTraining> cstReviewerOfflineTrainingList) {

        return iReviewerBaseInfoService.offlineTrainingApply(cstReviewerOfflineTrainingList);
    }

    /**
     * 管理员端-评审员线下培训已报名人员信息
     * @param trainingId 线下培训id
     *
     * @return 提交结果
     */
    @GetMapping("/offline/training/registered")
    @ApiOperation(httpMethod = "POST", value = "管理员端-评审员线下培训已报名人员信息")
    public TableDataInfo offlineTrainingRegistered(String trainingId) {
        startPage();
        List<OfflineTrainingRegisteredVo> list = iReviewerBaseInfoService.offlineTrainingRegistered(trainingId);
        return getDataTable(list);
    }

    /**
     * 评审员对应医疗机构评审计划利益冲突申报表列表
     */
    @PostMapping("/interest/list")
    @ApiOperation(httpMethod = "POST", value = "评审员对应医疗机构评审计划利益冲突申报表列表")
    public TableDataInfo interestList() {
        startPage();
        List<ReviewInterestVO> list = iReviewerBaseInfoService.interestList();
        return getDataTable(list);
    }

    @PostMapping("/list/export")
    @ApiOperation(httpMethod = "POST", value = "评审员基本信息导出")
    public AjaxResult reviewerListExport(@RequestBody @Validated ReviewerExportRequest request) {
        iReviewerBaseInfoService.reviewerListExport(request);
        return success();
    }

    @PostMapping("update-reviewer-role")
    @ApiOperation(httpMethod = "POST", value = "更新评审员或验证评审员角色转换")
    public AjaxResult updateReviewerRole(@RequestBody @Validated UpdateReviewerRoleReq request) {
        iReviewerBaseInfoService.updateReviewerRole(request);
        return success();
    }

}
