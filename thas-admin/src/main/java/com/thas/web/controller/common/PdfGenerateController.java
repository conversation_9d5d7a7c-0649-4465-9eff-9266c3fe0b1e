package com.thas.web.controller.common;

import com.thas.common.core.controller.BaseController;
import com.thas.web.domain.FtlToPdfDTO;
import com.thas.web.service.PdfGenParamService;
import com.thas.web.service.PdfGenerateService;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * pdf生成 Controller
 *
 * <AUTHOR>
 * @date 2022-04-19
 */
@Slf4j
@RestController
@RequestMapping("/pdf/generate")
public class PdfGenerateController extends BaseController {

    @Resource
    private PdfGenerateService pdfGenerateService;

    /**
     * 根据ftl模板生成对应的pdf文件
     */
    @PostMapping(value = "/ftlToPdf")
    public void ftlToPdf(@Validated @RequestBody FtlToPdfDTO ftlToPdfDTO, HttpServletResponse response) {
        pdfGenerateService.ftlToPdf(ftlToPdfDTO, response);
    }

    /**
     * 免登
     */
    @PostMapping(value = "/bing/ftlToPdf")
    public void bingFtlToPdf(@Validated @RequestBody FtlToPdfDTO ftlToPdfDTO, HttpServletResponse response) {
        pdfGenerateService.bingFtlToPdf(ftlToPdfDTO, response);
    }

    @Autowired
    private PdfGenParamService pdfGenParamService;

    @PostMapping(value = "/hosRewPlanParam")
    public Object hosRewPlanParam(@RequestParam String applyNo){
        return pdfGenParamService.hosRewPlanParam(applyNo);
    }
}

