package com.thas.web.controller.common;

import com.thas.common.annotation.Log;
import com.thas.common.core.controller.BaseController;
import com.thas.common.core.domain.AjaxResult;
import com.thas.common.core.page.TableDataInfo;
import com.thas.common.enums.BusinessType;
import com.thas.web.domain.TraAnswerSheet;
import com.thas.web.domain.dto.TraAnswerSheetDTO;
import com.thas.web.domain.vo.TraAnswerSheetVO;
import com.thas.web.service.ITraAnswerSheetService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 答卷Controller
 *
 * <AUTHOR>
 * @date 2022-01-27
 */
@RestController
@RequestMapping("/system/sheet")
@Api(value = "API - TraAnswerSheetController", tags = "在线学习 - 答卷管理")
public class TraAnswerSheetController extends BaseController {
    @Autowired
    private ITraAnswerSheetService traAnswerSheetService;

    /**
     * 查询答卷列表
     */
    @GetMapping("/list")
    @ApiOperation(httpMethod = "GET", value = "查询答卷列表")
    public TableDataInfo list(TraAnswerSheet traAnswerSheet) {
        startPage();
        List<TraAnswerSheetVO> list = traAnswerSheetService.selectTraAnswerSheetList(traAnswerSheet);
        return getDataTable(list);
    }

    /**
     * 获取答卷详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation(httpMethod = "GET", value = "获取答卷详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(traAnswerSheetService.selectTraAnswerSheetById(id));
    }

    /**
     * 新增答卷
     */
    @Log(title = "新增答卷", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(httpMethod = "POST", value = "新增答卷")
    public AjaxResult add(@RequestBody TraAnswerSheetDTO traAnswerSheetDTO) {
        return toAjax(traAnswerSheetService.insertTraAnswerSheet(traAnswerSheetDTO));
    }

    /**
     * 修改答卷
     */
    @Log(title = "修改答卷", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(httpMethod = "PUT", value = "修改答卷")
    public AjaxResult edit(@RequestBody TraAnswerSheetDTO traAnswerSheetDTO) {
        return toAjax(traAnswerSheetService.updateTraAnswerSheet(traAnswerSheetDTO));
    }

    /**
     * 删除答卷
     */
    @Log(title = "删除答卷", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(httpMethod = "DELETE", value = "删除答卷")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(traAnswerSheetService.deleteTraAnswerSheetByIds(ids));
    }
}
