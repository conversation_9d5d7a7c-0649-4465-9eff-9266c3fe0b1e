package com.thas.web.controller.common;

import com.thas.common.core.domain.AjaxResult;
import com.thas.common.validate.ValidGroup;
import com.thas.web.domain.UploadChunkInfo;
import com.thas.web.service.BigFileUploadService;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 大文件上传
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/bigFileUpload")
public class BigFileUploadController {

    @Resource
    private BigFileUploadService bigFileUploadService;

    /**
     * 校验文件已上传分片信息
     *
     * @param uploadChunkInfo 分片文件信息
     * @return 文件信息
     */
    @GetMapping
    public AjaxResult checkChunk(@Validated(ValidGroup.Group1.class) UploadChunkInfo uploadChunkInfo) {
        // 获取文件已上传分片信息
        return AjaxResult.success(bigFileUploadService.checkChunk(uploadChunkInfo));
    }

    /**
     * 上传分片文件
     *
     * @param uploadChunkInfo 分片文件信息
     * @return 文件信息
     */
    @PostMapping
    public AjaxResult chunkUpload(@Validated(ValidGroup.Group2.class) UploadChunkInfo uploadChunkInfo) {
        // 上传分片信息
        return AjaxResult.success(bigFileUploadService.chunkUpload(uploadChunkInfo));
    }

    /**
     * 合并文件
     *
     * @param uploadChunkInfo 分片文件信息
     * @return 文件信息
     */
    @PostMapping("/mergeFile")
    public AjaxResult mergeFile(@Validated(ValidGroup.Group3.class) UploadChunkInfo uploadChunkInfo){
        return AjaxResult.success(bigFileUploadService.mergeFile(uploadChunkInfo));
    }

}
